package auth

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// OAuthState represents the OAuth state with security measures
type OAuthState struct {
	UserID    string    `json:"user_id"`
	State     string    `json:"state"`
	Nonce     string    `json:"nonce"`
	Challenge string    `json:"challenge"`
	Method    string    `json:"method"`
	ExpiresAt time.Time `json:"expires_at"`
}

// SecureOAuthService handles secure OAuth operations
type SecureOAuthService struct {
	clientID     string
	clientSecret string
	redirectURI  string
	jwtSecret    []byte
	states       map[string]*OAuthState // In production, use Redis
}

// NewSecureOAuthService creates a new secure OAuth service
func NewSecureOAuthService(clientID, clientSecret, redirectURI string, jwtSecret []byte) *SecureOAuthService {
	return &SecureOAuthService{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURI:  redirectURI,
		jwtSecret:    jwtSecret,
		states:       make(map[string]*OAuthState),
	}
}

// generateSecureState creates a cryptographically secure state parameter
func (s *SecureOAuthService) generateSecureState() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// generateNonce creates a unique nonce for the request
func (s *SecureOAuthService) generateNonce() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generatePKCEChallenge creates PKCE challenge and verifier
func (s *SecureOAuthService) generatePKCEChallenge() (challenge, verifier string, err error) {
	// Generate code verifier (43-128 characters)
	verifierBytes := make([]byte, 32)
	if _, err := rand.Read(verifierBytes); err != nil {
		return "", "", err
	}
	verifier = base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(verifierBytes)
	
	// Generate code challenge (SHA256 hash of verifier)
	hash := sha256.Sum256([]byte(verifier))
	challenge = base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(hash[:])
	
	return challenge, verifier, nil
}

// createSecureToken creates a JWT token containing encrypted user context
func (s *SecureOAuthService) createSecureToken(userID string, state *OAuthState) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"state":   state.State,
		"nonce":   state.Nonce,
		"exp":     time.Now().Add(10 * time.Minute).Unix(), // Short expiry
		"iat":     time.Now().Unix(),
		"iss":     "vaultke-oauth",
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.jwtSecret)
}

// InitiateGoogleDriveAuth starts the secure OAuth flow
func (s *SecureOAuthService) InitiateGoogleDriveAuth(c *gin.Context) {
	// Extract user ID from authenticated context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "User not authenticated",
		})
		return
	}
	
	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Invalid user context",
		})
		return
	}
	
	// Generate secure parameters
	state, err := s.generateSecureState()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to generate secure state",
		})
		return
	}
	
	nonce, err := s.generateNonce()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to generate nonce",
		})
		return
	}
	
	challenge, verifier, err := s.generatePKCEChallenge()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to generate PKCE challenge",
		})
		return
	}
	
	// Create OAuth state object
	oauthState := &OAuthState{
		UserID:    userIDStr,
		State:     state,
		Nonce:     nonce,
		Challenge: challenge,
		Method:    "S256", // SHA256 for PKCE
		ExpiresAt: time.Now().Add(10 * time.Minute),
	}
	
	// Store state securely (use Redis in production)
	s.states[state] = oauthState
	
	// Create secure token for additional validation
	secureToken, err := s.createSecureToken(userIDStr, oauthState)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create secure token",
		})
		return
	}
	
	// Build secure OAuth URL
	authURL := s.buildSecureAuthURL(state, challenge, nonce)
	
	// Return response without exposing sensitive data
	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "Google Drive authorization URL generated",
		"auth_url":     authURL,
		"secure_token": secureToken, // Client should include this in callback validation
		"expires_in":   600,          // 10 minutes
	})
}

// buildSecureAuthURL constructs the OAuth URL with security parameters
func (s *SecureOAuthService) buildSecureAuthURL(state, challenge, nonce string) string {
	baseURL := "https://accounts.google.com/o/oauth2/v2/auth"
	
	params := url.Values{}
	params.Add("client_id", s.clientID)
	params.Add("redirect_uri", s.redirectURI)
	params.Add("response_type", "code")
	params.Add("scope", "https://www.googleapis.com/auth/drive.file")
	params.Add("access_type", "offline")
	params.Add("prompt", "consent")
	
	// Security parameters
	params.Add("state", state)                    // CSRF protection
	params.Add("nonce", nonce)                   // Replay attack protection
	params.Add("code_challenge", challenge)       // PKCE challenge
	params.Add("code_challenge_method", "S256")   // PKCE method
	
	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// HandleGoogleCallback processes the OAuth callback securely
func (s *SecureOAuthService) HandleGoogleCallback(c *gin.Context) {
	// Extract parameters
	code := c.Query("code")
	state := c.Query("state")
	receivedError := c.Query("error")
	
	// Check for OAuth errors
	if receivedError != "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": fmt.Sprintf("OAuth error: %s", receivedError),
		})
		return
	}
	
	// Validate required parameters
	if code == "" || state == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required OAuth parameters",
		})
		return
	}
	
	// Retrieve and validate state
	oauthState, exists := s.states[state]
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid or expired OAuth state",
		})
		return
	}
	
	// Check state expiration
	if time.Now().After(oauthState.ExpiresAt) {
		delete(s.states, state)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "OAuth state expired",
		})
		return
	}
	
	// Validate secure token if provided
	secureToken := c.GetHeader("X-Secure-Token")
	if secureToken != "" {
		if err := s.validateSecureToken(secureToken, oauthState); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Invalid secure token",
			})
			return
		}
	}
	
	// Exchange code for tokens using PKCE
	tokens, err := s.exchangeCodeForTokens(code, oauthState)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to exchange code for tokens",
		})
		return
	}
	
	// Clean up state
	delete(s.states, state)
	
	// Store tokens securely for the user
	if err := s.storeUserTokens(oauthState.UserID, tokens); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to store user tokens",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Google Drive connected successfully",
	})
}

// validateSecureToken validates the JWT secure token
func (s *SecureOAuthService) validateSecureToken(tokenString string, state *OAuthState) error {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtSecret, nil
	})
	
	if err != nil {
		return err
	}
	
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// Validate claims
		if claims["user_id"] != state.UserID ||
			claims["state"] != state.State ||
			claims["nonce"] != state.Nonce {
			return fmt.Errorf("token claims don't match state")
		}
		return nil
	}
	
	return fmt.Errorf("invalid token")
}

// exchangeCodeForTokens exchanges authorization code for access tokens
func (s *SecureOAuthService) exchangeCodeForTokens(code string, state *OAuthState) (map[string]interface{}, error) {
	// This would implement the actual token exchange with Google
	// Including PKCE code_verifier parameter
	
	tokenURL := "https://oauth2.googleapis.com/token"
	
	data := url.Values{}
	data.Set("client_id", s.clientID)
	data.Set("client_secret", s.clientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", s.redirectURI)
	data.Set("code_verifier", state.Challenge) // PKCE verifier
	
	// Make HTTP request to exchange code for tokens
	// Implementation details would go here
	
	return map[string]interface{}{
		"access_token":  "encrypted_access_token",
		"refresh_token": "encrypted_refresh_token",
		"expires_in":    3600,
	}, nil
}

// storeUserTokens securely stores user's OAuth tokens
func (s *SecureOAuthService) storeUserTokens(userID string, tokens map[string]interface{}) error {
	// Encrypt tokens before storing
	// Store in database with proper encryption
	// Implementation would include proper encryption/decryption
	return nil
}

// CleanupExpiredStates removes expired OAuth states
func (s *SecureOAuthService) CleanupExpiredStates() {
	now := time.Now()
	for state, oauthState := range s.states {
		if now.After(oauthState.ExpiresAt) {
			delete(s.states, state)
		}
	}
}
