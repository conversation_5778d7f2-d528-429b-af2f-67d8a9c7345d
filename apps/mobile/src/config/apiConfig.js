// API Configuration for VaultKe Mobile App
// Handles dynamic API URL resolution for different environments

/**
 * Get the appropriate API base URL based on current environment
 * Priority: Environment variable > Current hostname detection > Default
 */
export const getApiBaseUrl = () => {
  // Check for environment variable override
  if (process.env.REACT_NATIVE_API_URL) {
    console.log('🔧 Using API URL from environment variable:', process.env.REACT_NATIVE_API_URL);
    return process.env.REACT_NATIVE_API_URL;
  }

  // Detect current environment
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;
    
    console.log(`🌐 Detected environment: ${protocol}//${hostname}`);
    
    // If accessing via localhost/127.0.0.1, use localhost backend
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      console.log('🏠 Using localhost backend (development)');
      return 'http://localhost:8080/api/v1';
    }
    
    // If accessing via network IP, use network backend
    if (hostname.match(/^192\.168\.|^10\.|^172\./)) {
      console.log('🏠 Using network IP backend');
      return `http://${hostname.replace(/:\d+$/, '')}:8080/api/v1`;
    }
    
    // If accessing via tunnelmole, use dedicated backend tunnel
    if (hostname.includes('tunnelmole.net')) {
      console.log('🌍 Using dedicated tunnelmole backend tunnel');
      return 'https://jhkvn8-ip-41-72-200-10.tunnelmole.net/api/v1';
    }
    
    // If accessing via other tunnel services
    if (hostname.includes('ngrok.io') || hostname.includes('localtunnel.me') || hostname.includes('localhost.run')) {
      console.log('🌍 Using tunnel backend (same domain)');
      return `${protocol}//${hostname}/api/v1`;
    }
  }

  // Default fallback - current backend tunnel
  console.log('🚀 Using backend tunnel');
  return 'https://jhkvn8-ip-41-72-200-10.tunnelmole.net/api/v1';
};

/**
 * API Configuration object
 */
export const API_CONFIG = {
  // Base URL
  BASE_URL: getApiBaseUrl(),
  
  // Timeout settings (adjusted for tunnel latency)
  TIMEOUT: {
    DEFAULT: 15000, // 15 seconds
    UPLOAD: 30000,  // 30 seconds for file uploads
    DOWNLOAD: 60000, // 60 seconds for downloads
  },
  
  // Retry settings
  RETRY: {
    ATTEMPTS: 3,
    DELAY: 1000, // 1 second base delay
    BACKOFF: 2,  // Exponential backoff multiplier
  },
  
  // Cache settings
  CACHE: {
    DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
    LONG_TTL: 30 * 60 * 1000,   // 30 minutes
    SHORT_TTL: 1 * 60 * 1000,   // 1 minute
  }
};

/**
 * Check if current environment is using tunnels
 */
export const isTunnelEnvironment = () => {
  if (typeof window === 'undefined') return false;
  
  const hostname = window.location.hostname;
  return hostname.includes('tunnelmole.net') || 
         hostname.includes('ngrok.io') || 
         hostname.includes('localtunnel.me') || 
         hostname.includes('localhost.run');
};

/**
 * Get optimized timeout based on environment
 */
export const getOptimizedTimeout = (operation = 'default') => {
  const baseTimeout = API_CONFIG.TIMEOUT[operation.toUpperCase()] || API_CONFIG.TIMEOUT.DEFAULT;
  
  // Increase timeout for tunnel environments
  if (isTunnelEnvironment()) {
    return Math.floor(baseTimeout * 1.5); // 50% increase for tunnels
  }
  
  return baseTimeout;
};

/**
 * Log current API configuration for debugging
 */
export const logApiConfig = () => {
  console.log('📊 API Configuration:', {
    baseUrl: API_CONFIG.BASE_URL,
    isTunnel: isTunnelEnvironment(),
    timeout: getOptimizedTimeout(),
    environment: typeof window !== 'undefined' ? window.location.hostname : 'server-side'
  });
};
