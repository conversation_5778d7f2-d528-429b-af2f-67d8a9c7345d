import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService from '../services/api';

/**
 * Resolves avatar URL to a usable image source
 * Handles cached base64 images, relative paths, and absolute URLs
 * @param {string} avatarUrl - The avatar URL to resolve
 * @returns {Promise<string|null>} - Resolved avatar URL or null
 */
export const resolveAvatarUrl = async (avatarUrl) => {
  if (!avatarUrl || typeof avatarUrl !== 'string') {
    return null;
  }

  // Handle cached base64 image placeholder
  if (avatarUrl === 'avatar://cached-base64-image') {
    try {
      const cachedData = await AsyncStorage.getItem('cached_avatar_data');
      if (cachedData) {
        console.log('🖼️ Using cached base64 avatar data');
        return cachedData;
      } else {
        console.warn('🖼️ No cached avatar data found for placeholder');
        return null;
      }
    } catch (error) {
      console.error('🖼️ Failed to get cached avatar data:', error);
      return null;
    }
  }

  // Check if it's already a complete URL (http/https) or data URL
  if (avatarUrl.startsWith('http') || avatarUrl.startsWith('data:')) {
    return avatarUrl;
  }

  // If it's a relative path, make it absolute
  const fullAvatarUrl = `${apiService.baseURL}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`;
  return fullAvatarUrl;
};

/**
 * Resolves avatar URL synchronously (for cases where async is not possible)
 * Note: This won't handle cached base64 images properly
 * @param {string} avatarUrl - The avatar URL to resolve
 * @returns {string|null} - Resolved avatar URL or null
 */
export const resolveAvatarUrlSync = (avatarUrl) => {
  if (!avatarUrl || typeof avatarUrl !== 'string') {
    return null;
  }

  // Handle cached base64 image placeholder - return null since we can't access AsyncStorage synchronously
  if (avatarUrl === 'avatar://cached-base64-image') {
    console.warn('🖼️ Cannot resolve cached avatar synchronously, use resolveAvatarUrl instead');
    return null;
  }

  // Check if it's already a complete URL (http/https) or data URL
  if (avatarUrl.startsWith('http') || avatarUrl.startsWith('data:')) {
    return avatarUrl;
  }

  // If it's a relative path, make it absolute
  const fullAvatarUrl = `${apiService.baseURL}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`;
  return fullAvatarUrl;
};

/**
 * Hook to resolve avatar URL with React state management
 * @param {string} avatarUrl - The avatar URL to resolve
 * @returns {[string|null, boolean]} - [resolvedUrl, isLoading]
 */
export const useAvatarUrl = (avatarUrl) => {
  const [resolvedUrl, setResolvedUrl] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const resolveUrl = async () => {
      setIsLoading(true);
      try {
        const resolved = await resolveAvatarUrl(avatarUrl);
        setResolvedUrl(resolved);
      } catch (error) {
        console.error('Failed to resolve avatar URL:', error);
        setResolvedUrl(null);
      } finally {
        setIsLoading(false);
      }
    };

    resolveUrl();
  }, [avatarUrl]);

  return [resolvedUrl, isLoading];
};

/**
 * Generate a consistent avatar URL from email using DiceBear API
 * @param {string} email - User's email
 * @param {number} size - Avatar size (default: 40)
 * @returns {string|null} - Generated avatar URL or null
 */
export const getAvatarFromEmail = (email, size = 40) => {
  if (!email) return null;
  
  // Use DiceBear API for consistent avatars based on email
  const seed = encodeURIComponent(email.toLowerCase().trim());
  return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&size=${size}&backgroundColor=random`;
};

/**
 * Get user initials for fallback avatar display
 * @param {Object} user - User object
 * @returns {string} - User's initials (max 2 characters)
 */
export const getUserInitials = (user) => {
  if (!user) return 'U';
  
  const firstName = user.firstName || user.first_name || user.fname;
  const lastName = user.lastName || user.last_name || user.lname;
  
  // If we have both first and last name
  if (firstName && lastName) {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  }
  
  // If we have only first name
  if (firstName) {
    return firstName.substring(0, 2).toUpperCase();
  }
  
  // Try full name
  const fullName = user.fullName || user.full_name || user.name;
  if (fullName) {
    const nameParts = fullName.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0].charAt(0)}${nameParts[1].charAt(0)}`.toUpperCase();
    }
    return fullName.substring(0, 2).toUpperCase();
  }
  
  // Try display name
  const displayName = user.displayName || user.display_name;
  if (displayName) {
    const nameParts = displayName.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0].charAt(0)}${nameParts[1].charAt(0)}`.toUpperCase();
    }
    return displayName.substring(0, 2).toUpperCase();
  }
  
  // Try username
  if (user.username || user.userName) {
    const username = user.username || user.userName;
    return username.substring(0, 2).toUpperCase();
  }
  
  // Try email
  if (user.email) {
    const emailName = user.email.split('@')[0];
    return emailName.substring(0, 2).toUpperCase();
  }
  
  return 'U';
};
