import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Platform,
} from 'react-native';

// Conditionally import LiveKit components only on mobile platforms
let VideoView;
if (Platform.OS === 'ios' || Platform.OS === 'android') {
  try {
    const LiveKit = require('@livekit/react-native');
    VideoView = LiveKit.VideoView;
  } catch (error) {
    console.log('LiveKit not available, using fallback');
    VideoView = null;
  }
} else {
  VideoView = null;
}
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../context/AppContext';
import { getThemeColors } from '../utils/theme';

const { width, height } = Dimensions.get('window');

// Fallback VideoView component for web compatibility and mock mode
const FallbackVideoView = ({ style, track, objectFit, participant, ...props }) => {
  const isEnabled = track && (track.mock || track.enabled);

  return (
    <View style={[style, {
      backgroundColor: isEnabled ? '#2a2a2a' : '#1a1a1a',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
    }]}>
      {isEnabled ? (
        <>
          <Ionicons name="videocam" size={48} color="#4CAF50" />
          <Text style={{ color: '#4CAF50', marginTop: 8, fontSize: 12, fontWeight: '600' }}>
            Camera Active
          </Text>
          {track?.mock && (
            <Text style={{ color: '#FFA500', marginTop: 4, fontSize: 10 }}>
              Mock Mode
            </Text>
          )}
        </>
      ) : (
        <>
          <Ionicons name="videocam-off" size={48} color="#666" />
          <Text style={{ color: '#666', marginTop: 8, fontSize: 12 }}>
            Camera Off
          </Text>
        </>
      )}
    </View>
  );
};

const VideoCallView = ({ 
  participants = [], 
  localVideoTrack, 
  onToggleCamera, 
  onToggleMicrophone, 
  onSwitchCamera,
  onEndCall,
  isCameraEnabled = false,
  isMicrophoneEnabled = false,
  userRole = 'member'
}) => {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [selectedParticipant, setSelectedParticipant] = useState(null);

  // Get the main video to display (selected participant or first remote participant)
  const getMainVideo = () => {
    if (selectedParticipant) {
      return selectedParticipant;
    }
    
    // Show first remote participant, or local if no remote participants
    const remoteParticipants = participants.filter(p => !p.isLocal);
    return remoteParticipants.length > 0 ? remoteParticipants[0] : participants.find(p => p.isLocal);
  };

  const mainParticipant = getMainVideo();

  // Role-based permissions
  const canControlMeeting = userRole === 'chairperson';
  const canTakeNotes = userRole === 'secretary' || userRole === 'chairperson';
  const canRecord = userRole === 'chairperson';

  const renderParticipantThumbnail = (participant, index) => {
    // Get video track - handle both real LiveKit and mock implementations
    let videoTrack = null;
    if (participant.participant?.getTrackByKind) {
      videoTrack = participant.participant.getTrackByKind('video');
    } else if (participant.participant?.videoTracks) {
      // Alternative way to get video track for real LiveKit
      const videoPublication = Array.from(participant.participant.videoTracks.values())[0];
      videoTrack = videoPublication?.track;
    }

    const isSelected = selectedParticipant?.identity === participant.identity;
    
    return (
      <TouchableOpacity
        key={participant.identity}
        style={[
          styles.thumbnailContainer,
          { borderColor: isSelected ? colors.primary : colors.border },
          isSelected && styles.selectedThumbnail
        ]}
        onPress={() => setSelectedParticipant(participant)}
      >
        {participant.isCameraEnabled ? (
          // Show video if camera is enabled (real or mock)
          VideoView && videoTrack && !videoTrack.mock ? (
            <VideoView
              style={styles.thumbnail}
              track={videoTrack}
              objectFit="cover"
            />
          ) : (
            <FallbackVideoView
              style={styles.thumbnail}
              track={videoTrack}
              participant={participant}
              objectFit="cover"
            />
          )
        ) : (
          <View style={[styles.avatarPlaceholder, { backgroundColor: colors.surface }]}>
            <Ionicons 
              name="person" 
              size={24} 
              color={colors.textSecondary} 
            />
          </View>
        )}
        
        <View style={[styles.participantInfo, { backgroundColor: colors.overlay }]}>
          <Text style={[styles.participantName, { color: colors.text }]} numberOfLines={1}>
            {participant.name}
            {participant.isLocal && ' (You)'}
          </Text>
          
          <View style={styles.participantControls}>
            <Ionicons
              name={participant.isMicrophoneEnabled ? "mic" : "mic-off"}
              size={12}
              color={participant.isMicrophoneEnabled ? colors.success : colors.error}
            />
            <Ionicons
              name={participant.isCameraEnabled ? "videocam" : "videocam-off"}
              size={12}
              color={participant.isCameraEnabled ? colors.success : colors.error}
              style={{ marginLeft: 4 }}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMainVideo = () => {
    if (!mainParticipant) {
      return (
        <View style={[styles.mainVideoContainer, { backgroundColor: colors.surface }]}>
          <Ionicons name="videocam-off" size={64} color={colors.textSecondary} />
          <Text style={[styles.noVideoText, { color: colors.textSecondary }]}>
            No video available
          </Text>
        </View>
      );
    }

    // Get video track - handle both real LiveKit and mock implementations
    let videoTrack = null;
    if (mainParticipant.participant?.getTrackByKind) {
      videoTrack = mainParticipant.participant.getTrackByKind('video');
    } else if (mainParticipant.participant?.videoTracks) {
      // Alternative way to get video track for real LiveKit
      const videoPublication = Array.from(mainParticipant.participant.videoTracks.values())[0];
      videoTrack = videoPublication?.track;
    }

    return (
      <View style={styles.mainVideoContainer}>
        {mainParticipant.isCameraEnabled ? (
          // Show video if camera is enabled (real or mock)
          VideoView && videoTrack && !videoTrack.mock ? (
            <VideoView
              style={styles.mainVideo}
              track={videoTrack}
              objectFit="cover"
            />
          ) : (
            <FallbackVideoView
              style={styles.mainVideo}
              track={videoTrack}
              participant={mainParticipant}
              objectFit="cover"
            />
          )
        ) : (
          <View style={[styles.mainVideoPlaceholder, { backgroundColor: colors.surface }]}>
            <Ionicons name="person" size={80} color={colors.textSecondary} />
            <Text style={[styles.participantNameLarge, { color: colors.text }]}>
              {mainParticipant.name}
              {mainParticipant.isLocal && ' (You)'}
            </Text>
          </View>
        )}
        
        {/* Main participant info overlay */}
        <View style={[styles.mainParticipantInfo, { backgroundColor: colors.overlay }]}>
          <Text style={[styles.mainParticipantName, { color: colors.text }]}>
            {mainParticipant.name}
            {mainParticipant.isLocal && ' (You)'}
          </Text>
          <View style={styles.mainParticipantControls}>
            <Ionicons
              name={mainParticipant.isMicrophoneEnabled ? "mic" : "mic-off"}
              size={16}
              color={mainParticipant.isMicrophoneEnabled ? colors.success : colors.error}
            />
            <Ionicons
              name={mainParticipant.isCameraEnabled ? "videocam" : "videocam-off"}
              size={16}
              color={mainParticipant.isCameraEnabled ? colors.success : colors.error}
              style={{ marginLeft: 8 }}
            />
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Main video area */}
      <View style={styles.mainVideoArea}>
        {renderMainVideo()}
      </View>

      {/* Participants thumbnails */}
      {participants.length > 1 && (
        <View style={[styles.thumbnailsContainer, { backgroundColor: colors.surface }]}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.thumbnailsScroll}
          >
            {participants.map((participant, index) => 
              renderParticipantThumbnail(participant, index)
            )}
          </ScrollView>
        </View>
      )}

      {/* Control buttons */}
      <View style={[styles.controlsContainer, { backgroundColor: colors.surface }]}>
        <View style={styles.controlsRow}>
          {/* Camera toggle */}
          <TouchableOpacity
            style={[
              styles.controlButton,
              { backgroundColor: isCameraEnabled ? colors.success : colors.error }
            ]}
            onPress={onToggleCamera}
          >
            <Ionicons
              name={isCameraEnabled ? "videocam" : "videocam-off"}
              size={24}
              color="white"
            />
          </TouchableOpacity>

          {/* Microphone toggle */}
          <TouchableOpacity
            style={[
              styles.controlButton,
              { backgroundColor: isMicrophoneEnabled ? colors.success : colors.error }
            ]}
            onPress={onToggleMicrophone}
          >
            <Ionicons
              name={isMicrophoneEnabled ? "mic" : "mic-off"}
              size={24}
              color="white"
            />
          </TouchableOpacity>

          {/* Switch camera */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.primary }]}
            onPress={onSwitchCamera}
          >
            <Ionicons name="camera-reverse" size={24} color="white" />
          </TouchableOpacity>

          {/* Role-based controls */}
          {canTakeNotes && (
            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.warning }]}
              onPress={() => {/* Open notes */}}
            >
              <Ionicons name="document-text" size={24} color="white" />
            </TouchableOpacity>
          )}

          {canRecord && (
            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.info }]}
              onPress={() => {/* Toggle recording */}}
            >
              <Ionicons name="radio-button-on" size={24} color="white" />
            </TouchableOpacity>
          )}

          {/* End call */}
          <TouchableOpacity
            style={[styles.controlButton, styles.endCallButton, { backgroundColor: colors.error }]}
            onPress={onEndCall}
          >
            <Ionicons name="call" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainVideoArea: {
    flex: 1,
    position: 'relative',
  },
  mainVideoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainVideo: {
    width: '100%',
    height: '100%',
  },
  mainVideoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  participantNameLarge: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  mainParticipantInfo: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainParticipantName: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  mainParticipantControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thumbnailsContainer: {
    height: 120,
    paddingVertical: 8,
  },
  thumbnailsScroll: {
    paddingHorizontal: 16,
  },
  thumbnailContainer: {
    width: 80,
    height: 100,
    marginRight: 12,
    borderRadius: 8,
    borderWidth: 2,
    overflow: 'hidden',
    position: 'relative',
  },
  selectedThumbnail: {
    borderWidth: 3,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  avatarPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  participantName: {
    fontSize: 10,
    fontWeight: '500',
    marginBottom: 2,
  },
  participantControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlsContainer: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  endCallButton: {
    marginLeft: 16,
  },
  noVideoText: {
    fontSize: 16,
    marginTop: 8,
  },
});

export default VideoCallView;
