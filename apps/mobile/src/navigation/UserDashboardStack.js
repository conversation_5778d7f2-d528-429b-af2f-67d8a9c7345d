import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { View, Text, StyleSheet } from 'react-native';

// User Dashboard Screens
import EnhancedUserDashboard from '../screens/user/EnhancedUserDashboard';
import MarketplaceScreen from '../screens/marketplace/MarketplaceScreen';
import LearningHubScreen from '../screens/learning/LearningHubScreen';
import CourseDetailScreen from '../screens/learning/CourseDetailScreen';
import QuizTakingScreen from '../screens/learning/QuizTakingScreen';
import VideoPlayerScreen from '../screens/learning/VideoPlayerScreen';
import ArticleReaderScreen from '../screens/learning/ArticleReaderScreen';
import CourseNavigationScreen from '../screens/learning/CourseNavigationScreen';
import ChamaMeetingsScreen from '../screens/chama/ChamaMeetingsScreen';
import PhysicalMeetingScreen from '../screens/chama/PhysicalMeetingScreen';
import JitsiMeetScreen from '../screens/chama/JitsiMeetScreen';

// Other User Screens
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import SecuritySettingsScreen from '../screens/settings/SecuritySettingsScreen';
import NotificationToneScreen from '../screens/settings/NotificationToneScreen';
import ContactSupportScreen from '../screens/support/ContactSupportScreen';
import HelpCenterScreen from '../screens/settings/HelpCenterScreen';
import ChangePasswordScreen from '../screens/security/ChangePasswordScreen';
import LoginHistoryScreen from '../screens/security/LoginHistoryScreen';
import AdminSupportScreen from '../screens/admin/AdminSupportScreen';
import AdminSupportChatScreen from '../screens/admin/AdminSupportChatScreen';
import UpdateSupportRequestScreen from '../screens/admin/UpdateSupportRequestScreen';
import PaymentMethodsScreen from '../screens/settings/PaymentMethodsScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

// Additional Marketplace Screens
import OrdersScreen from '../screens/marketplace/OrdersScreen';
import SellerSettingsScreen from '../screens/marketplace/SellerSettingsScreen';

// Reminder Screen
import ReminderScreen from '../screens/reminders/ReminderScreen';

// Chama Screens
import InvitationsScreen from '../screens/chama/InvitationsScreen';
import WalletScreen from '../screens/wallet/WalletScreen';
import ChamaListScreen from '../screens/chama/ChamaListScreen';
import MyChamasScreen from '../screens/chama/MyChamasScreen';
import CreateChamaScreen from '../screens/chama/CreateChamaScreen';
import ChamaDetailsScreen from '../screens/chama/ChamaDetailsScreen';
import ChamaMembersScreen from '../screens/chama/ChamaMembersScreen';
import ChamaTransactionsScreen from '../screens/chama/ChamaTransactionsScreen';
import PollsVotingScreen from '../screens/chama/PollsVotingScreen';
import LoanApplication from '../screens/chama/LoanApplication';
import AIAssistantScreen from '../screens/ai/AIAssistantScreen';
import ChatScreen from '../screens/chat/ChatScreen';
import ChatRoomScreen from '../screens/chat/ChatRoomScreen';
import CreatePrivateChatScreen from '../screens/chat/CreatePrivateChatScreen';
import CreateGroupChatScreen from '../screens/chat/CreateGroupChatScreen';
import UserSearchScreen from '../screens/chat/UserSearchScreen';

// Wallet Screens
import DepositScreen from '../screens/wallet/DepositScreen';
import WithdrawScreen from '../screens/wallet/WithdrawScreen';
import TransferScreen from '../screens/wallet/TransferScreen';
import TransactionHistoryScreen from '../screens/wallet/TransactionHistoryScreen';
import RequestMoneyScreen from '../screens/wallet/RequestMoneyScreen';

// Service Screens
import BuyAirtimeScreen from '../screens/services/BuyAirtimeScreen';
import PayBillsScreen from '../screens/services/PayBillsScreen';

// Marketplace Screens
import AddProductScreen from '../screens/marketplace/AddProductScreen';
import ProductDetailsScreen from '../screens/marketplace/ProductDetailsScreen';
import CartScreen from '../screens/marketplace/CartScreen';
import CheckoutScreen from '../screens/marketplace/CheckoutScreen';
import WishlistScreen from '../screens/marketplace/WishlistScreen';
import ManageOrdersScreen from '../screens/marketplace/ManageOrdersScreen';
import SellerDashboardScreen from '../screens/marketplace/SellerDashboardScreen';
import BuyerDashboardScreen from '../screens/marketplace/BuyerDashboardScreen';
import SellerAnalyticsScreen from '../screens/marketplace/SellerAnalyticsScreen';
import OrderTrackingScreen from '../screens/marketplace/OrderTrackingScreen';
import DeliveryManagementScreen from '../screens/marketplace/DeliveryManagementScreen';
import DeliveryContactsScreen from '../screens/marketplace/DeliveryContactsScreen';
import DeliveryPersonDashboard from '../screens/marketplace/DeliveryPersonDashboard';
import UnifiedDeliveryTrackingScreen from '../screens/marketplace/UnifiedDeliveryTrackingScreen';
import MarketplaceRegistrationModal from '../screens/marketplace/MarketplaceRegistrationModal';
import WriteReviewScreen from '../screens/marketplace/WriteReviewScreen';
import ProductReviewsScreen from '../screens/marketplace/ProductReviewsScreen';
import MyReviewsScreen from '../screens/marketplace/MyReviewsScreen';

import { useApp } from '../context/AppContext';
import { getThemeColors } from '../utils/theme';

// Import the extracted UserTabBar and HOC
import UserTabBar from '../components/common/UserTabBar';
import withUserFooter from '../components/common/withUserFooter';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// UserTabBar is now imported from components/common/UserTabBar.js

// User Tab Navigator
function UserTabNavigator() {  
  
  return (
    <Tab.Navigator
      tabBar={(props) => <UserTabBar {...props} />}
      screenOptions={({ route, navigation }) => ({
        headerShown: true,
        header: ({ options }) => {
          // Import SmartHeader here to avoid circular imports
          const SmartHeader = require('../components/common/SmartHeader').default;

          // Determine if back button should be shown based on navigation state
          const canGoBack = navigation.canGoBack();
          const isTabScreen = ['Home', 'Marketplace', 'Learn', 'Meetings', 'Chat'].includes(route.name);

          // Get navigation state for more intelligent back button logic
          const state = navigation.getState();
          const routeHistory = state?.routes || [];
          const hasNavigationHistory = routeHistory.length > 1;

          // Show back button for non-tab screens OR when we have clear navigation history
          // This ensures users can always navigate back from screens they navigated to
          const shouldShowBackButton = !isTabScreen || (canGoBack && hasNavigationHistory);

          // Enhanced header with profile pic, home icon, notification bell, and smart navigation
          return (
            <SmartHeader
              title={options.title || route.name}
              showBackButton={shouldShowBackButton}
              showHomeButton={true}
              showProfilePic={true}
              showNotificationBell={true}
            />
          );
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={EnhancedUserDashboard}
        options={{
          title: 'Home',
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen 
        name="Marketplace" 
        component={MarketplaceScreen}
        options={{
          title: 'Marketplace',
          tabBarLabel: 'Marketplace',
        }}
      />
      <Tab.Screen
        name="Learn"
        component={LearningHubScreen}
        options={{
          title: 'Learning Hub',
          tabBarLabel: 'Learn',
        }}
      />
      <Tab.Screen
        name="Meetings"
        component={ChamaMeetingsScreen}
        initialParams={{ fromUserDashboard: true }}
        options={{
          title: 'Meetings',
          tabBarLabel: 'Meetings',
        }}
      />
      <Tab.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          title: 'Messages',
          tabBarLabel: 'Chat',
        }}
      />
      <Tab.Screen
        name="ChatRoom"
        component={ChatRoomScreen}
        options={{
          title: 'Chat Room',
          tabBarLabel: 'Room',
        }}
        initialParams={{ roomId: 'default', roomName: 'General Chat', roomType: 'group' }}
      />

      {/* Hidden Tab Screens - These have footer but don't show in tab bar */}
      <Tab.Screen
        name="Wallet"
        component={WalletScreen}
        options={{
          title: 'My Wallet',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="AIAssistant"
        component={AIAssistantScreen}
        options={{
          title: 'AI Assistant',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      <Tab.Screen
        name="MyChamas"
        component={MyChamasScreen}
        options={{
          title: 'My Chamas',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ChamaList"
        component={ChamaListScreen}
        options={{
          title: 'Browse Chamas',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="CreateChama"
        component={CreateChamaScreen}
        options={{
          title: 'Create New Chama',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ChamaDetails"
        component={ChamaDetailsScreen}
        options={{
          title: 'Chama Details',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ChamaMembersScreen"
        component={ChamaMembersScreen}
        options={{
          title: 'Chama Members',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ChamaTransactionsScreen"
        component={ChamaTransactionsScreen}
        options={{
          title: 'Chama Transactions',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="PollsVotingScreen"
        component={PollsVotingScreen}
        options={{
          title: 'Polls & Voting',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="LoanApplication"
        component={LoanApplication}
        options={{
          title: 'Loan Application',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Wallet Screens */}
      <Tab.Screen
        name="Deposit"
        component={DepositScreen}
        options={{
          title: 'Deposit Money',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Withdraw"
        component={WithdrawScreen}
        options={{
          title: 'Withdraw Money',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Transfer"
        component={TransferScreen}
        options={{
          title: 'Transfer Money',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="TransactionHistory"
        component={TransactionHistoryScreen}
        options={{
          title: 'Transaction History',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="RequestMoney"
        component={RequestMoneyScreen}
        options={{
          title: 'Request Money',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Service Screens */}
      <Tab.Screen
        name="BuyAirtime"
        component={BuyAirtimeScreen}
        options={{
          title: 'Buy Airtime',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="PayBills"
        component={PayBillsScreen}
        options={{
          title: 'Pay Bills',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Key Marketplace Screens */}
      <Tab.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          title: 'Add Product',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ProductDetails"
        component={ProductDetailsScreen}
        options={{
          title: 'Product Details',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Cart"
        component={CartScreen}
        options={{
          title: 'Shopping Cart',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ManageOrders"
        component={ManageOrdersScreen}
        options={{
          title: 'Manage Orders',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Profile & Settings Screens */}
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'My Profile',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notifications',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Additional Settings Screens */}
      <Tab.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{
          title: 'Security Settings',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="NotificationTone"
        component={NotificationToneScreen}
        options={{
          title: 'Notification Tones',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ContactSupport"
        component={ContactSupportScreen}
        options={{
          title: 'Contact Support',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="HelpCenter"
        component={HelpCenterScreen}
        options={{
          title: 'Help Center',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="PaymentMethods"
        component={PaymentMethodsScreen}
        options={{
          title: 'Payment Methods',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ChangePassword"
        component={ChangePasswordScreen}
        options={{
          title: 'Change Password',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="LoginHistory"
        component={LoginHistoryScreen}
        options={{
          title: 'Login History',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="AdminSupport"
        component={AdminSupportScreen}
        options={{
          title: 'Support Management',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="AdminSupportChat"
        component={AdminSupportChatScreen}
        options={{
          title: 'Support Chat',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="UpdateSupportRequest"
        component={UpdateSupportRequestScreen}
        options={{
          title: 'Update Request',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Learning Screens */}
      <Tab.Screen
        name="CourseDetail"
        component={CourseDetailScreen}
        options={{
          title: 'Course Details',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Marketplace Screens */}
      <Tab.Screen
        name="SellerAnalytics"
        component={SellerAnalyticsScreen}
        options={{
          title: 'Sales Analytics',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="DeliveryManagement"
        component={DeliveryManagementScreen}
        options={{
          title: 'Delivery Management',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="DeliveryContacts"
        component={DeliveryContactsScreen}
        options={{
          title: 'Delivery Contacts',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="DeliveryPersonDashboard"
        component={DeliveryPersonDashboard}
        options={{
          title: 'My Deliveries',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="UnifiedDeliveryTracking"
        component={UnifiedDeliveryTrackingScreen}
        options={{
          title: 'Delivery Tracking',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="MyPurchases"
        component={OrdersScreen}
        options={{
          title: 'My Purchases',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="SellerSettings"
        component={SellerSettingsScreen}
        options={{
          title: 'Seller Settings',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
     <Tab.Screen
        name="Checkout"
        component={CheckoutScreen}
        options={{
          title: 'Checkout',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Wishlist"
        component={WishlistScreen}
        options={{
          title: 'My Whishlist',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="WriteReview"
        component={WriteReviewScreen}
        options={{
          title: 'Write Review',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ProductReviews"
        component={ProductReviewsScreen}
        options={{
          title: 'Product Reviews',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="MyReviews"
        component={MyReviewsScreen}
        options={{
          title: 'My Reviews',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="star" size={size} color={color} />
          ),
        }}
      />
     <Tab.Screen
        name="SellerDashboard"
        component={SellerDashboardScreen}
        options={{
          title: 'Seller Dashboard',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="BuyerDashboard"
        component={BuyerDashboardScreen}
        options={{
          title: 'My Purchases',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
     <Tab.Screen
        name="OrderTracking"
        component={OrderTrackingScreen}
        options={{
          title: 'Tracking Order',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      {/* Chama Screens */}
      <Tab.Screen
        name="Invitations"
        component={InvitationsScreen}
        options={{
          title: 'Invitations',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Reminder Screen */}
      <Tab.Screen
        name="Reminders"
        component={ReminderScreen}
        options={{
          title: 'Reminders',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />

      {/* Learning Content Screens - Hidden from tab bar */}
      <Tab.Screen
        name="QuizTaking"
        component={QuizTakingScreen}
        options={{
          title: 'Take Quiz',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="VideoPlayer"
        component={VideoPlayerScreen}
        options={{
          title: 'Video Lesson',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="ArticleReader"
        component={ArticleReaderScreen}
        options={{
          title: 'Article',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="CourseNavigation"
        component={CourseNavigationScreen}
        options={{
          title: 'Course Navigation',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="PhysicalMeeting"
        component={PhysicalMeetingScreen}
        options={{
          title: 'Physical Meeting',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="OnlineMeeting"
        component={JitsiMeetScreen}
        options={{
          title: 'Online Meeting',
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
    </Tab.Navigator>
  );
}

// Main User Dashboard Stack
export default function UserDashboardStack() {
  console.log('👤 UserDashboardStack rendered');

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false, // Temporarily disable custom headers to fix "header" text node error
      }}
    >
      <Stack.Screen
        name="UserTabs"
        component={UserTabNavigator}
      />
      
      {/* Chat Sub-screens */}
      <Stack.Screen
        name="ChatRoom"
        component={withUserFooter(ChatRoomScreen)}
        options={{ title: 'Chat Room' }}
      />
      <Stack.Screen
        name="CreatePrivateChat"
        component={withUserFooter(CreatePrivateChatScreen)}
        options={{ title: 'New Private Chat' }}
      />
      <Stack.Screen
        name="CreateGroupChat"
        component={withUserFooter(CreateGroupChatScreen)}
        options={{ title: 'New Group Chat' }}
      />
      <Stack.Screen
        name="UserSearch"
        component={withUserFooter(UserSearchScreen)}
        options={{ title: 'Search Users' }}
      />

      {/* Additional Marketplace Screens not in Tab Navigator */}

    </Stack.Navigator>
  );
}


