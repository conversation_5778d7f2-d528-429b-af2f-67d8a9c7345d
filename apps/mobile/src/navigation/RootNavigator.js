import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';

// Navigation Stacks
import AuthStack from './AuthStack';
import UserDashboardStack from './UserDashboardStack';
import AdminDashboardStack from './AdminDashboardStack';
import ChamaDashboardStack from './ChamaDashboardStack';

import { useApp } from '../context/AppContext';
import { getThemeColors } from '../utils/theme';

const Stack = createStackNavigator();

// Loading Screen Component
function LoadingScreen() {
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  return (
    <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={[styles.loadingText, { color: colors.text }]}>
        Loading VaultKe...
      </Text>
    </View>
  );
}

export default function RootNavigator() {
  const { 
    isLoading, 
    isAuthenticated, 
    currentDashboard, 
    user,
    switchToUserDashboard 
  } = useApp();

  // Debug logging
  useEffect(() => {
    console.log('🔄 RootNavigator state:', {
      isLoading,
      isAuthenticated,
      currentDashboard,
      userRole: user?.role,
      userId: user?.id
    });
  }, [isLoading, isAuthenticated, currentDashboard, user]);

  // Auto-switch to user dashboard if no specific dashboard is set
  useEffect(() => {
    if (isAuthenticated && !isLoading && !currentDashboard) {
      console.log('🔄 No dashboard set, switching to user dashboard');
      switchToUserDashboard();
    }
  }, [isAuthenticated, isLoading, currentDashboard, switchToUserDashboard]);

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen />;
  }

  // Show auth stack if not authenticated
  if (!isAuthenticated) {
    console.log('🔐 Rendering AuthStack - user not authenticated');
    return (
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Auth" component={AuthStack} />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }

  // Show appropriate dashboard based on currentDashboard state
  // console.log(`🎯 Rendering ${currentDashboard}DashboardStack`);
  
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {currentDashboard === 'user' && (
          <Stack.Screen 
            name="UserDashboard" 
            component={UserDashboardStack}
            options={{ title: 'User Dashboard' }}
          />
        )}
        {currentDashboard === 'admin' && (
          <Stack.Screen
            name="AdminDashboard"
            component={AdminDashboardStack}
            options={{ title: 'Admin Dashboard' }}
          />
        )}
        {currentDashboard === 'chama' && (
          <Stack.Screen 
            name="ChamaDashboard" 
            component={ChamaDashboardStack}
            options={{ title: 'Chama Dashboard' }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
});
