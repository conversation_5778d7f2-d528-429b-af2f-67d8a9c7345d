import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import { getUserFirstName } from '../../utils/userUtils';
import WalletCard from '../../components/wallet/WalletCard';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const { width } = Dimensions.get('window');

const EnhancedUserDashboard = ({ navigation }) => {
  const {
    user,
    wallets,
    chamas,
    transactions,
    notifications,
    isOnline,
    isSyncing,
    theme,
    refreshData,
    getCachedData,
    prefetchForPage,
    switchToAdminDashboard,
  } = useApp();

  const colors = getThemeColors(theme);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [userStats, setUserStats] = useState({
    totalChamas: 0,
    totalMeetings: 0,
    totalContributions: 0,
    walletBalance: 0,
  });

  useEffect(() => {
    // Set the selected wallet to the first personal wallet
    const personalWallet = wallets.find(w => w.type === 'personal');
    if (personalWallet) {
      setSelectedWallet(personalWallet);
    }
  }, [wallets]);

  useEffect(() => {
    // Load user statistics on component mount
    loadUserStatistics();
  }, []);

  const loadUserStatistics = async () => {
    try {
      console.log('🔍 Loading user statistics...');
      const response = await ApiService.getUserStatistics();
      console.log('📊 User statistics response:', response);

      if (response.success) {
        const { wallet_stats, chama_stats, contribution_stats, meeting_stats } = response.data;

        setUserStats({
          totalChamas: chama_stats?.active_chamas || 0,
          totalMeetings: meeting_stats?.active_meetings || 0, // Only upcoming + ongoing meetings
          totalContributions: contribution_stats?.total_contributions || 0,
          walletBalance: wallet_stats?.personal_balance || 0,
        });

        console.log('✅ User statistics loaded:', {
          totalChamas: chama_stats?.active_chamas || 0,
          totalMeetings: meeting_stats?.active_meetings || 0, // Only upcoming + ongoing meetings
          totalContributions: contribution_stats?.total_contributions || 0,
          walletBalance: wallet_stats?.personal_balance || 0,
        });
      }
    } catch (error) {
      console.error('❌ Error loading user statistics:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);

    // Use lightning-fast refresh with instant cached data first
    console.log('⚡ Lightning refresh started...');
    await refreshData(true); // Force refresh

    // Reload user statistics
    await loadUserStatistics();

    setRefreshing(false);
    console.log('✅ Lightning refresh completed');
  };

  // Prefetch data for likely next pages when component mounts
  useEffect(() => {
    console.log('🚀 Light prefetching for essential data...');

    // Only prefetch essential data to prevent storage issues
    prefetchForPage('wallet', 'low');
    prefetchForPage('chama-dashboard', 'low');
    // Removed aggressive chat preloading to prevent storage quota issues
  }, [prefetchForPage]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const quickActions = [
    // {
    //   id: 1,
    //   title: 'Learning Hub',
    //   icon: 'school',
    //   color: colors.primary,
    //   onPress: () => navigation.navigate('Learn'),
    // },
    {
      id: 2,
      title: 'AI Assistant',
      icon: 'chatbubble-ellipses',
      color: colors.secondary,
      onPress: () => navigation.navigate('AIAssistant'),
    },
    // {
    //   id: 3,
    //   title: 'Marketplace',
    //   icon: 'storefront',
    //   color: colors.accent,
    //   onPress: () => navigation.navigate('Marketplace'),
    // },
    {
      id: 4,
      title: 'Wallet',
      icon: 'wallet',
      color: colors.primary,
      onPress: () => navigation.navigate('Wallet'),
    },
    {
      id: 5,
      title: 'Groups',
      icon: 'people',
      color: colors.secondary,
      onPress: () => navigation.navigate('MyChamas'),
    },
    {
      id: 6,
      title: 'Reminders',
      icon: 'notifications',
      color: colors.accent,
      onPress: () => navigation.navigate('Reminders'),
    },
    {
      id: 7,
      title: 'Meetings',
      icon: 'calendar',
      color: colors.primary,
      onPress: () => navigation.navigate('Meetings'),
    },
    // {
    //   id: 8,
    //   title: 'Chat',
    //   icon: 'chatbubble',
    //   color: colors.secondary,
    //   onPress: () => navigation.navigate('Chat'),
    // },
    {
      id: 9,
      title: 'Transuctions',
      icon: 'receipt',
      color: colors.accent,
      onPress: () => navigation.navigate('TransactionHistory'),
    },
  ];

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.background }]}>
      <View style={styles.headerGradient}>
        <View style={styles.headerContent}>
          <View style={styles.userInfo}>
            <View style={styles.greetingContainer}>
              <Text style={[styles.greeting, { color: colors.textSecondary }]}>
                {getGreeting()}
              </Text>
              <Text style={[styles.userName, { color: colors.text }]}>
                Welcome back, {getUserFirstName(user)}
              </Text>
              <Text style={[styles.userSubtitle, { color: colors.textSecondary }]}>
                Here's what's happening with your finances today
              </Text>
            </View>
            {!isOnline && (
              <View style={styles.offlineIndicator}>
                <View style={[styles.offlineIcon, { backgroundColor: colors.warning + '20' }]}>
                  <Ionicons name="wifi-outline" size={14} color={colors.warning} />
                </View>
                <Text style={[styles.offlineText, { color: colors.warning }]}>
                  Offline mode
                </Text>
              </View>
            )}
          </View>

          <View style={styles.headerActions}>
            {/* Header actions can be added here if needed */}
          </View>
        </View>
      </View>
    </View>
  );

   const renderQuickStats = () => {
       return (
      <Card style={styles.statsCard}>
        <View style={styles.statsGrid}>
          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.primary + '15' }]}>
                <Ionicons name="wallet" size={20} color={colors.primary} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Wallet Balance
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              Ksh {userStats.walletBalance.toLocaleString()}
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.secondary + '15' }]}>
                <Ionicons name="people" size={20} color={colors.secondary} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Total Chamas
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {userStats.totalChamas}
            </Text>
          </View>
          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.warning + '15' }]}>
                <Ionicons name="calendar" size={20} color={colors.warning} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Active Meetings
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {userStats.totalMeetings}
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.success + '15' }]}>
                <Ionicons name="trending-up" size={20} color={colors.success} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Contributions
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {userStats.totalContributions}
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderQuickActions = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Quick Actions
      </Text>
      <View style={styles.quickActionsGrid}>
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={styles.quickActionItem}
            onPress={action.onPress}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: action.color }]}>
              <Ionicons name={action.icon} size={24} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, { color: colors.text }]}>
              {action.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || isSyncing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}

        {/* Wallet Card */}
        {selectedWallet && (
          <WalletCard
            wallet={selectedWallet}
            onDeposit={(wallet) => handleWalletAction('deposit', wallet)}
            onWithdraw={(wallet) => handleWalletAction('withdraw', wallet)}
            onTransfer={(wallet) => handleWalletAction('transfer', wallet)}
            onViewTransactions={(wallet) => handleWalletAction('transactions', wallet)}
            style={styles.walletCard}
          />
        )}
        {renderQuickStats()}
        {renderQuickActions()}
        {/* {renderWalletsSummary()} */}
        {/* {renderChamasSummary()} */}
        {/* {renderRecentTransactions()} */}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    paddingHorizontal: 0,
  },
  headerGradient: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    backgroundColor: 'rgba(0, 212, 170, 0.05)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 212, 170, 0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  userInfo: {
    flex: 1,
  },
  greetingContainer: {
    marginBottom: spacing.sm,
  },
  greeting: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs / 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  userName: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
    lineHeight: typography.fontSize['3xl'] * 1.2,
  },
  userSubtitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.fontSize.base * 1.4,
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.lg,
    backgroundColor: 'rgba(210, 153, 34, 0.1)',
    alignSelf: 'flex-start',
  },
  offlineIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.xs,
  },
  offlineText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: spacing.md,
  },

  headerButton: {
    marginLeft: spacing.md,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    borderRadius: borderRadius.full,
    minWidth: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  notificationCount: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  walletCard: {
    marginHorizontal: spacing.md,
  },
  section: {
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statsCard: {
    margin: spacing.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statTile: {
    width: '48%',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    marginBottom: spacing.md,
    
  },
  statTileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statTileIcon: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  statTileLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
  },
  statTileValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'left',
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  seeAllText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xs, // Add small horizontal padding
  },
  quickActionItem: {
    width: (width - spacing.md * 4 - spacing.xs * 2) / 3, // 3 items per row with proper spacing
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingHorizontal: spacing.xs,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  quickActionText: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
    fontWeight: typography.fontWeight.medium,
  },
  walletItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  walletInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  walletIcon: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  walletDetails: {
    flex: 1,
  },
  walletName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  walletBalance: {
    fontSize: typography.fontSize.sm,
  },
  chamaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  chamaInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  chamaIcon: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  chamaDetails: {
    flex: 1,
  },
  chamaName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  chamaMembers: {
    fontSize: typography.fontSize.sm,
  },
  switchButton: {
    marginTop: spacing.md,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionType: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  transactionTime: {
    fontSize: typography.fontSize.sm,
  },
  transactionAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },
  exploreButton: {
    marginTop: spacing.md,
  },
});

export default EnhancedUserDashboard;
