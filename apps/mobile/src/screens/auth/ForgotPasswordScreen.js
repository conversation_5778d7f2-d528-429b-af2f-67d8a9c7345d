import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import { useApp } from '../../context/AppContext';
import apiService from '../../services/api';

export default function ForgotPasswordScreen({ navigation }) {
  const [identifier, setIdentifier] = useState(''); // email or phone
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState('request'); // 'request' or 'sent'
  const [errors, setErrors] = useState({});

  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const handleResetRequest = async () => {
    if (!identifier.trim()) {
      setErrors({ identifier: 'Please enter your email or phone number' });
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\+?[\d\s-()]+$/;

    if (!emailRegex.test(identifier.trim()) && !phoneRegex.test(identifier.trim())) {
      setErrors({ identifier: 'Please enter a valid email address or phone number' });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const response = await apiService.makeRequest('/auth/forgot-password', {
        method: 'POST',
        body: { identifier: identifier.trim() },
      });

      if (response.success) {
        // Show success message briefly then redirect to reset password screen
        setStep('sent');

        // Auto-redirect to reset password screen after 2 seconds
        setTimeout(() => {
          navigation.navigate('ResetPassword');
        }, 2000);
      } else {
        // Check if it's a user not found error
        if (response.error && response.error.toLowerCase().includes('user not found')) {
          setErrors({ identifier: 'No account found with this email or phone number' });
        } else {
          setErrors({ general: response.error || 'Failed to send reset instructions' });
        }
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setErrors({ general: 'Failed to send reset instructions. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const renderRequestStep = () => (
    <>
      {/* Logo Section */}
      <View style={styles.logoContainer}>
        <View style={[styles.logoCircle, { backgroundColor: colors.surface, borderColor: colors.primary }]}>
          <Ionicons name="wallet" size={48} color={colors.primary} />
        </View>
        <Text style={[styles.logoText, { color: colors.text }]}>VaultKe</Text>
      </View>

      <View style={styles.headerContainer}>
        <Text style={[styles.titleText, { color: colors.text }]}>Forgot Password?</Text>
        <Text style={[styles.subtitleText, { color: colors.textSecondary }]}>
          Enter your email to reset your password
        </Text>
      </View>

      <View style={styles.formContainer}>
        {/* General Error */}
        {errors.general && (
          <View style={[styles.errorContainer, { backgroundColor: colors.error + '15', borderColor: colors.error }]}>
            <Ionicons name="alert-circle" size={20} color={colors.error} />
            <Text style={[styles.errorText, { color: colors.error }]}>{errors.general}</Text>
          </View>
        )}

        <View style={[
          styles.inputContainer,
          { backgroundColor: colors.surface, borderColor: colors.border }
        ]}>
          <Ionicons
            name="person-outline"
            size={20}
            color={colors.textSecondary}
            style={styles.inputIcon}
          />
          <TextInput
            style={[styles.input, { color: colors.text }]}
            placeholder="Email or phone"
            placeholderTextColor={colors.textSecondary}
            value={identifier}
            onChangeText={(text) => {
              setIdentifier(text);
              if (errors.identifier) setErrors({});
            }}
            autoCapitalize="none"
            keyboardType="email-address"
          />
        </View>
        {errors.identifier && (
          <Text style={[styles.fieldError, { color: colors.error }]}>{errors.identifier}</Text>
        )}

        <TouchableOpacity
          style={[
            styles.resetButton,
            { backgroundColor: colors.primary },
            isLoading && styles.resetButtonDisabled
          ]}
          onPress={handleResetRequest}
          disabled={isLoading}
        >
          <Text style={[styles.resetButtonText, { color: colors.white }]}>
            {isLoading ? 'Sending...' : 'Send Reset Instructions'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={20} color={colors.primary} />
          <Text style={[styles.backButtonText, { color: colors.primary }]}>Back to Login</Text>
        </TouchableOpacity>
      </View>
    </>
  );

  const renderSentStep = () => (
    <>
      {/* Logo Section */}
      <View style={styles.logoContainer}>
        <View style={[styles.logoCircle, { backgroundColor: colors.surface, borderColor: colors.primary }]}>
          <Ionicons name="wallet" size={48} color={colors.primary} />
        </View>
        <Text style={[styles.logoText, { color: colors.text }]}>VaultKe</Text>
        <Text style={[styles.taglineText, { color: colors.textSecondary }]}>
          Your trusted chama finance companion
        </Text>
      </View>

      <View style={styles.headerContainer}>
        <View style={[styles.iconContainer, { backgroundColor: colors.surface, borderColor: colors.success }]}>
          <Ionicons name="mail" size={28} color={colors.success} />
        </View>
        <Text style={[styles.titleText, { color: colors.text }]}>Check Your Inbox</Text>
        <Text style={[styles.subtitleText, { color: colors.textSecondary }]}>
          We've sent password reset instructions to your email or phone number. You'll be redirected to enter the code in 2 seconds.
        </Text>
      </View>

      <View style={styles.formContainer}>
        <View style={[styles.instructionsContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.instructionsText, { color: colors.text }]}>
            • Check your email inbox and spam folder{'\n'}
            • Check your SMS messages{'\n'}
            • Follow the link or code provided{'\n'}
            • Create a new password
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.resetButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate('ResetPassword', { email: identifier })}
        >
          <Text style={[styles.resetButtonText, { color: colors.white }]}>
            Enter Reset Code
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.secondaryButton, { borderColor: colors.primary }]}
          onPress={() => setStep('request')}
        >
          <Text style={[styles.secondaryButtonText, { color: colors.primary }]}>
            Resend Instructions
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate('Login')}
        >
          <Ionicons name="arrow-back" size={20} color={colors.primary} />
          <Text style={[styles.backButtonText, { color: colors.primary }]}>Back to Login</Text>
        </TouchableOpacity>
      </View>
    </>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={[styles.scrollContainer, { flexGrow: 1 }]}
          showsVerticalScrollIndicator={false}
        >
          {step === 'request' ? renderRequestStep() : renderSentStep()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    borderWidth: 2,
  },
  logoText: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  taglineText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    borderWidth: 1.5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  titleText: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    lineHeight: 24,
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.lg,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  fieldError: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.md,
    marginLeft: spacing.sm,
  },
  inputIcon: {
    marginRight: spacing.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 14,
  },
  resetButton: {
    borderRadius: borderRadius.md,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  resetButtonDisabled: {
    opacity: 0.6,
  },
  resetButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  secondaryButton: {
    borderRadius: borderRadius.md,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },
  backButtonText: {
    fontSize: typography.fontSize.base,
    marginLeft: spacing.sm,
  },
  instructionsContainer: {
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    borderWidth: 1,
  },
  instructionsText: {
    fontSize: typography.fontSize.base,
    lineHeight: 24,
  },
});
