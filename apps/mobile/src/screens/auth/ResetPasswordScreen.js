import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import { useApp } from '../../context/AppContext';
import apiService from '../../services/api';

export default function ResetPasswordScreen({ navigation, route }) {
  const [resetCode, setResetCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const { theme } = useApp();
  const colors = getThemeColors(theme);

  // Get email from route params if passed from ForgotPasswordScreen
  const userEmail = route?.params?.email || '';

  const validateForm = () => {
    const newErrors = {};

    if (!resetCode.trim()) {
      newErrors.resetCode = 'Reset code is required';
    } else if (resetCode.trim().length !== 6) {
      newErrors.resetCode = 'Reset code must be 6 digits';
    }

    if (!newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters';
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleResetPassword = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const response = await apiService.makeRequest('/auth/reset-password', {
        method: 'POST',
        body: {
          token: resetCode.trim(),
          newPassword: newPassword,
        },
      });

      if (response.success) {
        // Show success message
        Alert.alert(
          '✅ Password Reset Successful!',
          response.message || 'Your password has been reset successfully. You can now login with your new password.',
          [
            {
              text: 'Login Now',
              onPress: () => navigation.navigate('Login'),
            },
          ]
        );

        // Auto-redirect to login after 3 seconds if user doesn't click
        setTimeout(() => {
          navigation.navigate('Login');
        }, 3000);
      } else {
        setErrors({ general: response.error || 'Failed to reset password' });
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setErrors({ general: 'Failed to reset password. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={[styles.scrollContainer, { flexGrow: 1 }]}
          showsVerticalScrollIndicator={false}
        >
          {/* Logo Section */}
          <View style={styles.logoContainer}>
            <View style={[styles.logoCircle, { backgroundColor: colors.surface, borderColor: colors.primary }]}>
              <Ionicons name="wallet" size={48} color={colors.primary} />
            </View>
            <Text style={[styles.logoText, { color: colors.text }]}>VaultKe</Text>
          </View>

          <View style={styles.headerContainer}>
            <Text style={[styles.titleText, { color: colors.text }]}>Reset Password</Text>
            <Text style={[styles.subtitleText, { color: colors.textSecondary }]}>
              Enter the code sent to your email and create a new password
            </Text>
          </View>

          <View style={styles.formContainer}>
            {/* General Error */}
            {errors.general && (
              <View style={[styles.errorContainer, { backgroundColor: colors.error + '15', borderColor: colors.error }]}>
                <Ionicons name="alert-circle" size={20} color={colors.error} />
                <Text style={[styles.errorText, { color: colors.error }]}>{errors.general}</Text>
              </View>
            )}

            {/* Reset Code Input */}
            <View style={[
              styles.inputContainer,
              { backgroundColor: colors.surface, borderColor: errors.resetCode ? colors.error : colors.border }
            ]}>
              <Ionicons
                name="keypad-outline"
                size={20}
                color={errors.resetCode ? colors.error : colors.textSecondary}
                style={styles.inputIcon}
              />
              <TextInput
                style={[styles.input, { color: colors.text }]}
                placeholder="Reset code"
                placeholderTextColor={colors.textSecondary}
                value={resetCode}
                onChangeText={setResetCode}
                keyboardType="numeric"
                maxLength={6}
                autoCapitalize="none"
              />
            </View>
            {errors.resetCode && (
              <Text style={[styles.fieldError, { color: colors.error }]}>{errors.resetCode}</Text>
            )}

            {/* New Password Input */}
            <View style={[
              styles.inputContainer,
              { backgroundColor: colors.surface, borderColor: errors.newPassword ? colors.error : colors.border }
            ]}>
              <Ionicons
                name="lock-closed-outline"
                size={20}
                color={errors.newPassword ? colors.error : colors.textSecondary}
                style={styles.inputIcon}
              />
              <TextInput
                style={[styles.input, { color: colors.text }]}
                placeholder="New password"
                placeholderTextColor={colors.textSecondary}
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={styles.eyeIcon}
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
            {errors.newPassword && (
              <Text style={[styles.fieldError, { color: colors.error }]}>{errors.newPassword}</Text>
            )}

            {/* Confirm Password Input */}
            <View style={[
              styles.inputContainer,
              { backgroundColor: colors.surface, borderColor: errors.confirmPassword ? colors.error : colors.border }
            ]}>
              <Ionicons
                name="lock-closed-outline"
                size={20}
                color={errors.confirmPassword ? colors.error : colors.textSecondary}
                style={styles.inputIcon}
              />
              <TextInput
                style={[styles.input, { color: colors.text }]}
                placeholder="Confirm password"
                placeholderTextColor={colors.textSecondary}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                style={styles.eyeIcon}
              >
                <Ionicons
                  name={showConfirmPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
            {errors.confirmPassword && (
              <Text style={[styles.fieldError, { color: colors.error }]}>{errors.confirmPassword}</Text>
            )}

            <TouchableOpacity
              style={[
                styles.resetButton,
                { backgroundColor: colors.primary },
                isLoading && styles.resetButtonDisabled
              ]}
              onPress={handleResetPassword}
              disabled={isLoading}
            >
              <Text style={[styles.resetButtonText, { color: colors.white }]}>
                {isLoading ? 'Resetting Password...' : 'Reset Password'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={20} color={colors.primary} />
              <Text style={[styles.backButtonText, { color: colors.primary }]}>Back</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    borderWidth: 2,
  },
  logoText: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  taglineText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    borderWidth: 1.5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  titleText: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.sm,
  },
  emailText: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.lg,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  inputIcon: {
    marginRight: spacing.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 14,
  },
  eyeIcon: {
    padding: spacing.xs,
  },
  fieldError: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.md,
    marginLeft: spacing.sm,
  },
  resetButton: {
    borderRadius: borderRadius.md,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },
  resetButtonDisabled: {
    opacity: 0.6,
  },
  resetButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },
  backButtonText: {
    fontSize: typography.fontSize.base,
    marginLeft: spacing.sm,
  },
});
