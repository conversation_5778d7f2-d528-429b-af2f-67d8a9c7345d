import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import apiService from '../../services/api';
import MessageBanner from '../../components/MessageBanner';
import FormField from '../../components/FormField';
import LoadingButton from '../../components/LoadingButton';

export default function RegisterScreen({ navigation }) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  // Enhanced error handling state
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState({ visible: false, text: '', type: 'error' });

  const { register, isAuthenticated, theme } = useApp();
  const colors = getThemeColors(theme);

  // Redirect if already authenticated
  React.useEffect(() => {
    if (isAuthenticated) {
      navigation.replace('MainTabs');
    }
  }, [isAuthenticated, navigation]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Enhanced message display function
  const showMessage = (text, type = 'error', duration = 4000) => {
    setMessage({ visible: true, text, type });
    if (duration > 0) {
      setTimeout(() => {
        setMessage(prev => ({ ...prev, visible: false }));
      }, duration);
    }
  };

  const validateForm = () => {
    const { firstName, lastName, email, phone, password, confirmPassword } = formData;
    const newErrors = {};

    // First Name validation
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    } else if (!/^[a-zA-Z\s]+$/.test(firstName.trim())) {
      newErrors.firstName = 'First name can only contain letters and spaces';
    }

    // Last Name validation
    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    } else if (!/^[a-zA-Z\s]+$/.test(lastName.trim())) {
      newErrors.lastName = 'Last name can only contain letters and spaces';
    }

    // Email validation
    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email.trim())) {
        newErrors.email = 'Please enter a valid email address';
      }
    }

    // Phone validation (Kenyan format)
    if (!phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else {
      const phoneRegex = /^(\+254|0)[17]\d{8}$/;
      if (!phoneRegex.test(phone.trim())) {
        newErrors.phone = 'Please enter a valid Kenyan phone number (e.g., 0712345678)';
      }
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, number, and special character';
    }

    // Confirm Password validation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Terms validation
    if (!acceptedTerms) {
      newErrors.terms = 'Please accept the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    // Clear previous errors and messages
    setErrors({});
    setMessage({ visible: false, text: '', type: 'error' });

    if (!validateForm()) {
      showMessage('Please fix the errors below', 'error');
      return;
    }

    setIsLoading(true);

    try {
      const { confirmPassword, ...userData } = formData;

      // Format data for backend (lowercase field names as per backend expectation)
      const backendUserData = {
        firstName: userData.firstName.trim(),
        lastName: userData.lastName.trim(),
        email: userData.email.trim().toLowerCase(),
        phone: userData.phone.trim(),
        password: userData.password,
      };

      console.log('🔐 Attempting registration');

      // Use AppContext register method which handles sync
      const result = await register(backendUserData);

      if (result.success) {
        console.log('✅ Registration successful, navigating to main app');
        showMessage('Welcome to VaultKe! Account created successfully!', 'success', 3000);
        // Navigation will be handled by useEffect when isAuthenticated changes
      } else {
        throw new Error(result.error || 'Registration failed');
      }

    } catch (error) {
      console.error('Registration error:', error);

      // Enhanced error handling with specific messages
      let errorMessage = 'Unable to create account. Please try again.';

      if (error.message.includes('email') && error.message.includes('exists')) {
        errorMessage = 'An account with this email already exists. Please use a different email or try logging in.';
        setErrors({ email: 'This email is already registered' });
      } else if (error.message.includes('phone') && error.message.includes('exists')) {
        errorMessage = 'An account with this phone number already exists. Please use a different number or try logging in.';
        setErrors({ phone: 'This phone number is already registered' });
      } else if (error.message.includes('validation')) {
        errorMessage = 'Please check your information and try again.';
      } else if (error.message.includes('Network') || error.message.includes('connection')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (error.message.includes('Rate limit') || error.message.includes('Too many')) {
        errorMessage = 'Too many registration attempts. Please wait a moment and try again.';
      }

      showMessage(errorMessage, 'error', 0);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Message Banner */}
        <MessageBanner
          visible={message.visible}
          message={message.text}
          type={message.type}
          onDismiss={() => setMessage(prev => ({ ...prev, visible: false }))}
        />

        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Logo Section */}
          <View style={styles.logoContainer}>
            <View style={[styles.logoCircle, { backgroundColor: colors.surface, borderColor: colors.primary }]}>
              <Ionicons name="wallet" size={48} color={colors.primary} />
            </View>
            <Text style={[styles.logoText, { color: colors.text }]}>VaultKe</Text>
          </View>

          <View style={styles.headerContainer}>
            <Text style={[styles.title, { color: colors.text }]}>Create Account</Text>
            <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
              Join thousands of users managing their finances
            </Text>
          </View>

        <View style={styles.formContainer}>
          {/* First Name */}
          <FormField
            label="First Name"
            value={formData.firstName}
            onChangeText={(value) => {
              handleInputChange('firstName', value);
              if (errors.firstName) {
                setErrors(prev => ({ ...prev, firstName: null }));
              }
            }}
            placeholder="First name"
            icon="person-outline"
            autoCapitalize="words"
            error={errors.firstName}
          />

          {/* Last Name */}
          <FormField
            label="Last Name"
            value={formData.lastName}
            onChangeText={(value) => {
              handleInputChange('lastName', value);
              if (errors.lastName) {
                setErrors(prev => ({ ...prev, lastName: null }));
              }
            }}
            placeholder="Last name"
            icon="person-outline"
            autoCapitalize="words"
            error={errors.lastName}
          />

          {/* Email */}
          <FormField
            label="Email Address"
            value={formData.email}
            onChangeText={(value) => {
              handleInputChange('email', value);
              if (errors.email) {
                setErrors(prev => ({ ...prev, email: null }));
              }
            }}
            placeholder="Email address"
            icon="mail-outline"
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
          />

          {/* Phone */}
          <FormField
            label="Phone Number"
            value={formData.phone}
            onChangeText={(value) => {
              handleInputChange('phone', value);
              if (errors.phone) {
                setErrors(prev => ({ ...prev, phone: null }));
              }
            }}
            placeholder="Phone number"
            icon="call-outline"
            keyboardType="phone-pad"
            error={errors.phone}
          />

          {/* Password */}
          <FormField
            label="Password"
            value={formData.password}
            onChangeText={(value) => {
              handleInputChange('password', value);
              if (errors.password) {
                setErrors(prev => ({ ...prev, password: null }));
              }
            }}
            placeholder="Password"
            icon="lock-closed-outline"
            secureTextEntry={true}
            showPassword={showPassword}
            onTogglePassword={() => setShowPassword(!showPassword)}
            error={errors.password}
          />

          {/* Confirm Password */}
          <FormField
            label="Confirm Password"
            value={formData.confirmPassword}
            onChangeText={(value) => {
              handleInputChange('confirmPassword', value);
              if (errors.confirmPassword) {
                setErrors(prev => ({ ...prev, confirmPassword: null }));
              }
            }}
            placeholder="Confirm password"
            icon="lock-closed-outline"
            secureTextEntry={true}
            showPassword={showConfirmPassword}
            onTogglePassword={() => setShowConfirmPassword(!showConfirmPassword)}
            error={errors.confirmPassword}
          />

          {/* Terms and Conditions */}
          <TouchableOpacity
            style={styles.termsContainer}
            onPress={() => {
              setAcceptedTerms(!acceptedTerms);
              if (errors.terms) {
                setErrors(prev => ({ ...prev, terms: null }));
              }
            }}
          >
            <View style={[
              styles.checkbox,
              { borderColor: colors.border },
              acceptedTerms && { backgroundColor: colors.primary, borderColor: colors.primary },
              errors.terms && { borderColor: colors.error }
            ]}>
              {acceptedTerms && (
                <Ionicons name="checkmark" size={16} color={colors.white} />
              )}
            </View>
            <Text style={[styles.termsText, { color: colors.textSecondary }]}>
              I agree to the{' '}
              <Text style={[styles.termsLink, { color: colors.primary }]}>Terms and Conditions</Text>
              {' '}and{' '}
              <Text style={[styles.termsLink, { color: colors.primary }]}>Privacy Policy</Text>
            </Text>
          </TouchableOpacity>

          {/* Terms Error */}
          {errors.terms && (
            <View style={styles.termsErrorContainer}>
              <Ionicons
                name="alert-circle"
                size={16}
                color={colors.error}
                style={styles.errorIcon}
              />
              <Text style={styles.termsErrorText}>{errors.terms}</Text>
            </View>
          )}

          {/* Register Button */}
          <LoadingButton
            title="Create Account"
            onPress={handleRegister}
            loading={isLoading}
            loadingText="Creating Account..."
            style={[styles.registerButton, { backgroundColor: colors.primary }]}
            disabled={!formData.firstName.trim() || !formData.lastName.trim() ||
                     !formData.email.trim() || !formData.phone.trim() ||
                     !formData.password || !formData.confirmPassword || !acceptedTerms}
          />

          {/* Divider */}
          <View style={styles.dividerContainer}>
            <View style={[styles.dividerLine, { backgroundColor: colors.border }]} />
            <Text style={[styles.dividerText, { color: colors.textSecondary }]}>OR</Text>
            <View style={[styles.dividerLine, { backgroundColor: colors.border }]} />
          </View>

          {/* Sign In Link */}
          <View style={styles.signInContainer}>
            <Text style={[styles.signInText, { color: colors.textSecondary }]}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={[styles.signInLink, { color: colors.primary }]}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    borderWidth: 2,
  },
  logoText: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  taglineText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    marginTop: spacing.lg,
  },
  titleText: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  subtitleText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  inputIcon: {
    marginRight: spacing.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: typography.fontSize.base,
  },
  passwordInput: {
    paddingRight: spacing.xl,
  },
  eyeIcon: {
    position: 'absolute',
    right: spacing.md,
    padding: spacing.xs,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    marginRight: spacing.sm,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    // Dynamic colors will be applied inline
  },
  checkboxError: {
    // Dynamic colors will be applied inline
  },
  termsText: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
  },
  termsLink: {
    textDecorationLine: 'underline',
  },
  registerButton: {
    borderRadius: borderRadius.md,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  registerButtonDisabled: {
    opacity: 0.6,
  },
  registerButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginHorizontal: spacing.md,
  },
  signInContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signInText: {
    fontSize: typography.fontSize.sm,
  },
  signInLink: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
  },
});
