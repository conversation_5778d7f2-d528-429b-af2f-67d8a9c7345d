import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useApp } from '../../context/AppContext';
import { useLightningData, useOptimisticUpdate } from '../../hooks/useLightningData';
import useSmartNavigation from '../../hooks/useSmartNavigation';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';
import webSocketService from '../../services/websocket';
import encryptedChat from '../../services/encryptedChat';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ChatRoomScreen = ({ route, navigation }) => {
  const { roomId, roomName, roomType } = route.params || {};
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  const { goBack } = useSmartNavigation();

  // Safety check for required parameters
  useEffect(() => {
    if (!roomId) {
      console.error('ChatRoomScreen: roomId is required');
      goBack();
    }
  }, [roomId, navigation]);

  // Early return if no roomId
  if (!roomId) {
    return null;
  }

  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [selectedImages, setSelectedImages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [decryptedContents, setDecryptedContents] = useState(new Map()); // Cache for decrypted message contents
  const flatListRef = useRef(null);
  const pollingIntervalRef = useRef(null);
  const lastMessageIdRef = useRef(null);

  // Removed fallback messages - only show real chat data from database

  // Helper function to decrypt message content for display
  const decryptMessageForDisplay = async (message) => {
    try {
      console.log('🔍 Decrypting message:', message.id, 'Type:', message.type);

      // Check if message is encrypted - handle multiple formats
      let encryptedData = null;

      // Format 1: message.type === 'military_encrypted_text' with content
      if (message.type === 'military_encrypted_text' && message.content) {
        try {
          encryptedData = typeof message.content === 'string' ? JSON.parse(message.content) : message.content;
        } catch (parseError) {
          console.log('⚠️ Could not parse encrypted content, treating as plain text');
          return message.content;
        }
      }

      // Format 2: Direct encrypted message object (like your example)
      else if (message.ciphertext && message.iv && message.metadata) {
        encryptedData = message;
      }

      // Format 3: Message content is the encrypted object
      else if (message.content && typeof message.content === 'object' && message.content.ciphertext) {
        encryptedData = message.content;
      }

      // Format 4: Message content is stringified encrypted object
      else if (message.content && typeof message.content === 'string') {
        try {
          const parsed = JSON.parse(message.content);
          if (parsed.ciphertext && parsed.iv) {
            encryptedData = parsed;
          } else {
            console.log('📝 Parsed content does not contain encryption keys, treating as plain text');
            return message.content;
          }
        } catch (parseError) {
          // Not JSON, treat as plain text
          console.log('📝 Content is not JSON, treating as plain text');
          return message.content;
        }
      }

      // Format 5: Check if message has displayContent already
      else if (message.displayContent) {
        console.log('✅ Message already has displayContent, using it');
        return message.displayContent;
      }

      // Now try to decrypt if we found encrypted data
      if (encryptedData && encryptedData.ciphertext) {
        try {
          console.log('🔓 Found encrypted data, attempting decryption...');
          console.log('🔍 Security level:', encryptedData.securityLevel || encryptedData.metadata?.securityLevel);

          // Handle basic fallback encryption
          if (encryptedData.securityLevel === 'BASIC_FALLBACK' && encryptedData.fallback) {
            try {
              const decrypted = atob(encryptedData.ciphertext);
              console.log('✅ Successfully decrypted basic fallback message');
              return decrypted;
            } catch (basicError) {
              console.log('⚠️ Basic fallback decryption failed:', basicError.message);
            }
          }

          // Handle military-grade encryption
          try {
            console.log('🔓 Attempting military-grade decryption...');

            // Initialize encryptedChat if needed
            if (!encryptedChat.isInitialized) {
              await encryptedChat.initialize(user?.id);
            }

            // Use the encryptedChat service to decrypt
            const decryptedMessage = await encryptedChat.receiveMessage(encryptedData);

            if (decryptedMessage && (decryptedMessage.content || decryptedMessage.text)) {
              const decryptedText = decryptedMessage.content || decryptedMessage.text;
              console.log('✅ Successfully decrypted military-grade message');
              return decryptedText;
            } else {
              console.log('⚠️ Decryption returned empty content, trying fallback...');

              // Try to use original content as fallback
              if (message.content && typeof message.content === 'string' && message.content.trim() !== '') {
                console.log('📝 Using original content as fallback');
                return message.content;
              }

              return '🔒 [Decryption failed - empty content]';
            }
          } catch (decryptError) {
            console.log('⚠️ Military decryption failed:', decryptError.message);

            // Try to extract any fallback content
            if (encryptedData.fallback) {
              try {
                return atob(encryptedData.fallback);
              } catch (fallbackError) {
                console.log('⚠️ Fallback extraction failed:', fallbackError.message);
              }
            }

            return `🔒 [Encrypted Message - Decryption Failed]`;
          }
        } catch (error) {
          console.log('⚠️ General decryption error:', error.message);
          return `🔒 [Encrypted Message - Error]`;
        }
      }

      // Return original content if not encrypted or if it's plain text
      return message.content || message.text || '';
    } catch (error) {
      console.log('⚠️ Error in decryptMessageForDisplay:', error.message);
      return message.content || message.text || '';
    }
  };

  // Function to decrypt messages and cache the results
  const decryptMessages = async (messageList) => {
    const newDecryptedContents = new Map(decryptedContents);

    // Process messages in parallel for better performance
    const decryptionPromises = messageList.map(async (message) => {
      if (message && message.id && !newDecryptedContents.has(message.id)) {
        try {
          const decryptedContent = await decryptMessageForDisplay(message);
          return { id: message.id, content: decryptedContent };
        } catch (error) {
          console.log('⚠️ Failed to decrypt message:', message.id, error.message);
          return { id: message.id, content: message.content || message.text || '🔒 [Decryption Error]' };
        }
      }
      return null;
    });

    const results = await Promise.all(decryptionPromises);

    results.forEach(result => {
      if (result) {
        newDecryptedContents.set(result.id, result.content);
      }
    });

    setDecryptedContents(newDecryptedContents);
  };

  useEffect(() => {
    console.log('🔒 ChatRoomScreen: User accessing room with authenticated session');
    console.log('🔒 Privacy: Room access validated by backend membership check');
    loadMessages();

    // Connect to WebSocket and join room
    const connectWebSocket = async () => {
      if (!webSocketService.getConnectionStatus().isConnected) {
        await webSocketService.connect();
      }
      webSocketService.joinRoom(roomId);
    };

    connectWebSocket();

    // Listen for new messages via WebSocket
    const handleNewMessage = async (wsMessage) => {
      console.log('🔴 Received new message via WebSocket:', wsMessage);
      if (wsMessage.roomId === roomId && wsMessage.data) {
        let messageData = wsMessage.data;

        // Decrypt message if it's encrypted
        if (messageData.metadata?.encrypted) {
          try {
            console.log('🔓 Decrypting incoming message...');
            messageData = await encryptedChat.receiveMessage(messageData);
          } catch (error) {
            console.error('❌ Failed to decrypt message:', error);
            // Show encrypted message indicator
            messageData.content = '🔒 Encrypted message (decryption failed)';
            messageData.encrypted = true;
          }
        }

        // Check if message already exists to avoid duplicates
        const messageExists = messages.some(msg => msg.id === messageData.id);

        if (!messageExists) {
          console.log('📨 Processing new WebSocket message:', wsMessage.data.id);

          // Use pre-decrypted content from WebSocket service if available
          const messageToAdd = { ...wsMessage.data };
          if (wsMessage.data.displayContent) {
            console.log('✅ Using pre-decrypted WebSocket message content');
            // Message was already decrypted by WebSocket service
          } else {
            console.log('🔓 Decrypting WebSocket message in ChatRoom...');
            // Decrypt the new message for display if not already done
            try {
              await decryptMessages([messageToAdd]);
              console.log('✅ WebSocket message decrypted in ChatRoom');
            } catch (decryptError) {
              console.warn('⚠️ WebSocket message decryption failed:', decryptError);

              // Try to use original content as fallback
              if (messageToAdd.content && typeof messageToAdd.content === 'string') {
                console.log('📝 Using original content as fallback for WebSocket message');
                messageToAdd.displayContent = messageToAdd.content;
              } else {
                messageToAdd.displayContent = '[Decryption failed]';
              }
            }
          }

          // Remove any optimistic message for the same content
          const filteredMessages = messages.filter(msg => {
            if (msg.isOptimistic &&
                msg.content === messageToAdd.content &&
                msg.senderId === messageToAdd.senderId) {
              return false;
            }
            return true;
          });

          // Add the real message
          const newMessages = [...filteredMessages, messageToAdd];

          // Sort messages by creation time to maintain order
          const sortedMessages = newMessages.sort((a, b) =>
            new Date(a.createdAt || a.created_at) - new Date(b.createdAt || b.created_at)
          );

          // Update messages state
          setMessages(sortedMessages);
        }

        // Scroll to bottom after a short delay to ensure rendering is complete
        setTimeout(scrollToBottom, 100);
      }
    };

    webSocketService.onMessage('new_message', handleNewMessage);

    // Start polling as fallback for real-time updates
    const startPolling = () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }

      pollingIntervalRef.current = setInterval(async () => {
        try {
          const response = await ApiService.getChatMessages(roomId, 10, 0);
          if (response.success && response.data && response.data.length > 0) {
            const latestMessage = response.data[response.data.length - 1];

            // Only update if we have new messages
            if (lastMessageIdRef.current !== latestMessage.id) {
              setMessages(prevMessages => {
                const newMessages = response.data.filter(newMsg =>
                  newMsg && newMsg.id && !prevMessages.some(existingMsg => existingMsg && existingMsg.id === newMsg.id)
                );

                if (newMessages.length > 0) {
                  console.log('📡 Polling found new messages:', newMessages.length);

                  // Decrypt new messages
                  decryptMessages(newMessages).catch(console.warn);

                  lastMessageIdRef.current = latestMessage.id;
                  const combined = [...prevMessages, ...newMessages];
                  return combined.sort((a, b) =>
                    new Date(a.createdAt || a.created_at) - new Date(b.createdAt || b.created_at)
                  );
                }
                return prevMessages;
              });
            }
          }
        } catch (error) {
          console.error('Polling error:', error);
          // No fallback - just log the error
        }
      }, 3000); // Poll every 3 seconds
    };

    startPolling();

    // Cleanup on unmount
    return () => {
      if (webSocketService && typeof webSocketService.leaveRoom === 'function') {
        webSocketService.leaveRoom(roomId);
      }
      if (webSocketService && typeof webSocketService.offMessage === 'function') {
        webSocketService.offMessage('new_message');
      }
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [roomId]);

  const loadMessages = async () => {
    try {
      setLoading(true);

      // First try to get preloaded decrypted messages (instant)
      try {
        const lightningDataService = (await import('../../services/lightningDataService')).default;
        const preloadedResponse = await lightningDataService.getPreloadedMessages(roomId);

        if (preloadedResponse.success && preloadedResponse.data.length > 0) {
          console.log(`⚡ Using ${preloadedResponse.data.length} preloaded DECRYPTED messages for instant display`);

          // Check if messages are already decrypted
          const alreadyDecrypted = preloadedResponse.data.every(msg => msg.displayContent || msg.isDecrypted);

          if (alreadyDecrypted) {
            console.log('✅ Messages are already decrypted - instant display!');
            setMessages(preloadedResponse.data);
          } else {
            console.log('🔓 Decrypting preloaded messages...');
            setMessages(preloadedResponse.data); // Show immediately, decrypt in background
            await decryptMessages(preloadedResponse.data);
          }

          // Set the last message ID for polling reference
          if (preloadedResponse.data.length > 0) {
            const sortedMessages = preloadedResponse.data.sort((a, b) =>
              new Date(a.createdAt || a.created_at) - new Date(b.createdAt || b.created_at)
            );
            lastMessageIdRef.current = sortedMessages[sortedMessages.length - 1].id;
          }

          setLoading(false);
          console.log('⚡ Preloaded messages displayed instantly');

          // Fetch any newer messages in background
          setTimeout(async () => {
            try {
              const freshResponse = await ApiService.getChatMessages(roomId, 50, 0);
              if (freshResponse.success && freshResponse.data && freshResponse.data.length > preloadedResponse.data.length) {
                console.log('🔄 Found newer messages, updating...');
                setMessages(freshResponse.data);
                await decryptMessages(freshResponse.data);
              }
            } catch (error) {
              console.warn('Background fresh message fetch failed:', error);
            }
          }, 1000);
          return;
        } else {
          console.log('ℹ️ No preloaded messages found, fetching from API...');
        }
      } catch (preloadError) {
        console.warn('⚠️ Failed to get preloaded messages:', preloadError);
      }

      // Fallback to API call
      const response = await ApiService.getChatMessages(roomId, 50, 0);
      if (response.success) {
        const messages = response.data || [];
        console.log(`📥 Loaded ${messages.length} messages for room ${roomId}`);

        // Log first message structure for debugging
        if (messages.length > 0) {
          console.log('🔍 First message structure:', {
            id: messages[0].id,
            type: messages[0].type,
            hasContent: !!messages[0].content,
            hasCiphertext: !!(messages[0].content && JSON.stringify(messages[0].content).includes('ciphertext')),
            contentPreview: typeof messages[0].content === 'string' ? messages[0].content.substring(0, 100) : 'object'
          });
        }

        setMessages(messages);

        // Decrypt messages for display
        console.log('🔓 Starting message decryption...');
        await decryptMessages(messages);
        console.log('✅ Message decryption completed');

        // Set the last message ID for polling reference
        if (messages.length > 0) {
          const sortedMessages = messages.sort((a, b) =>
            new Date(a.createdAt || a.created_at) - new Date(b.createdAt || b.created_at)
          );
          lastMessageIdRef.current = sortedMessages[sortedMessages.length - 1].id;
        }
      } else {
        // API call succeeded but no messages - this is normal for new rooms
        console.log('📭 No messages found for room:', roomId);
      }
    } catch (error) {
      console.error('Failed to load messages:', error);
      // No fallback - just show empty state
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() && selectedImages.length === 0) return;

    const messageContent = messageText.trim();
    const hasImages = selectedImages.length > 0;

    try {
      setSending(true);

      // Initialize E2EE if not already done
      let currentUserId = await AsyncStorage.getItem('currentUserId');

      // Fallback to user context if AsyncStorage doesn't have it
      if (!currentUserId && user?.id) {
        currentUserId = user.id;
        // Store it for future use
        await AsyncStorage.setItem('currentUserId', currentUserId);
      }

      if (!currentUserId) {
        throw new Error('User ID not available - please log in again');
      }

      if (!encryptedChat.isInitialized) {
        console.log('🔐 Initializing E2EE for chat...');
        console.log('🔐 User ID:', currentUserId);
        await encryptedChat.initialize(currentUserId);
        console.log('✅ E2EE initialization completed');
      }

      // Clear input fields after E2EE init
      setMessageText('');
      setSelectedImages([]);

      // If there are images, send them one by one with the text on the first one
      if (hasImages) {
        for (let i = 0; i < selectedImages.length; i++) {
          const image = selectedImages[i];
          const tempMessageId = 'temp_' + Date.now() + '_' + i;

          // Only include text content with the first image
          const currentContent = i === 0 ? messageContent : '';

          // Create optimistic message for immediate UI feedback
          const optimisticMessage = {
            id: tempMessageId,
            roomId: roomId,
            senderId: user?.id,
            content: currentContent,
            type: 'image',
            metadata: { imageUri: image.uri },
            fileUrl: image.uri, // Show the local URI immediately
            createdAt: new Date().toISOString(),
            isRead: false,
            isOptimistic: true,
          };

          // Add optimistic message to UI immediately
          setMessages(prevMessages => [...prevMessages, optimisticMessage]);

          // Scroll to bottom to show new message
          setTimeout(scrollToBottom, 100);

          try {
            // Send encrypted image using E2EE service
            // Determine recipient ID based on room type
            let recipientId = 'group';
            if (roomType === 'private') {
              recipientId = roomId;
            }

            const response = await encryptedChat.sendImage(
              roomId,
              recipientId,
              image.uri,
              {
                fileName: `image_${Date.now()}.jpg`,
                textContent: currentContent, // Include text with first image
              }
            );

            if (response.success) {
              // Replace optimistic message with real message from server
              setMessages(prevMessages => {
                const updatedMessages = prevMessages.filter(msg => msg.id !== tempMessageId);

                // Check if the real message already exists (from WebSocket)
                const realMessageExists = response.data && updatedMessages.some(msg => msg.id === response.data.id);

                if (!realMessageExists && response.data) {
                  return [...updatedMessages, response.data];
                }

                return updatedMessages;
              });
            } else {
              // Remove optimistic message on failure
              setMessages(prevMessages => prevMessages.filter(msg => msg.id !== tempMessageId));
            }
          } catch (error) {
            console.error('Send image error:', error);
            // Remove optimistic message on error
            setMessages(prevMessages => prevMessages.filter(msg => msg.id !== tempMessageId));
          }
        }
      } else if (messageContent) {
        // Send text-only message
        const tempMessageId = 'temp_' + Date.now();

        const optimisticMessage = {
          id: tempMessageId,
          roomId: roomId,
          senderId: user?.id,
          content: messageContent,
          type: 'text',
          metadata: {},
          createdAt: new Date().toISOString(),
          isRead: false,
          isOptimistic: true,
        };

        setMessages(prevMessages => [...prevMessages, optimisticMessage]);
        setTimeout(scrollToBottom, 100);

        // Send encrypted text message using E2EE service
        // For private chats, we need to determine the other participant
        // For group chats, we use 'group' as recipient ID
        let recipientId = 'group';
        if (roomType === 'private') {
          // For private chats, we'll use the room ID as recipient for now
          // In a full implementation, this would be the other participant's ID
          recipientId = roomId;
        }

        const response = await encryptedChat.sendMessage(
          roomId,
          recipientId,
          messageContent,
          {
            timestamp: Date.now(),
          }
        );

        if (response.success) {
          setMessages(prevMessages => {
            const updatedMessages = prevMessages.filter(msg => msg.id !== tempMessageId);
            const realMessageExists = response.data && updatedMessages.some(msg => msg.id === response.data.id);

            if (!realMessageExists && response.data) {
              return [...updatedMessages, response.data];
            }

            return updatedMessages;
          });
        } else {
          setMessages(prevMessages => prevMessages.filter(msg => msg.id !== tempMessageId));
          Alert.alert('Error', 'Failed to send message');
        }
      }

      // Clear input after sending
      setMessageText('');
      setSelectedImages([]);

    } catch (error) {
      console.error('Send message error:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to send images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsMultipleSelection: true,
        selectionLimit: 5, // Allow up to 5 images
        quality: 0.8,
      });

      if (!result.canceled && result.assets) {
        // Add new images to existing selection
        setSelectedImages(prevImages => [...prevImages, ...result.assets]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined,
      });
    }
  };

  const renderMessage = ({ item, index }) => {
    // Handle both API response formats (senderId vs sender_id)
    const senderId = item.senderId || item.sender_id;
    const isMyMessage = senderId === user?.id;
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;

    // Handle both API response formats (createdAt vs created_at)
    const createdAt = item.createdAt || item.created_at;
    const showDate = !previousMessage ||
      new Date(createdAt).toDateString() !== new Date(previousMessage.createdAt || previousMessage.created_at).toDateString();

    const showAvatar = !isMyMessage && (!nextMessage || (nextMessage.senderId || nextMessage.sender_id) !== senderId);
    const showSenderName = !isMyMessage && roomType === 'group' &&
      (!previousMessage || (previousMessage.senderId || previousMessage.sender_id) !== senderId);

    // Get decrypted content for display
    const displayContent = decryptedContents.get(item.id) || item.content || item.text || '';

    // Calculate responsive message dimensions
    const messageLength = displayContent ? displayContent.length : 0;
    const isShortMessage = messageLength < 20;
    const isMediumMessage = messageLength >= 20 && messageLength < 100;
    const isLongMessage = messageLength >= 100 && messageLength < 300;
    const isVeryLongMessage = messageLength >= 300;

    // Dynamic width based on message length
    let maxWidth = '75%';
    if (isShortMessage) maxWidth = '50%';
    else if (isMediumMessage) maxWidth = '70%';
    else if (isLongMessage) maxWidth = '85%';
    else if (isVeryLongMessage) maxWidth = '95%';

    // Dynamic font size and line height for readability
    const fontSize = isVeryLongMessage ? typography.fontSize.sm : typography.fontSize.base;
    const lineHeight = isVeryLongMessage ? 18 : 20;

    return (
      <View>
        {showDate && (
          <View style={styles.dateContainer}>
            <Text style={[styles.dateText, { color: colors.textTertiary }]}>
              {formatDate(createdAt)}
            </Text>
          </View>
        )}

        <View style={[
          styles.messageContainer,
          isMyMessage ? styles.myMessageContainer : styles.otherMessageContainer,
        ]}>
          {!isMyMessage && (
            <View style={styles.avatarContainer}>
              {showAvatar ? (
                <View style={[styles.avatar, { backgroundColor: colors.primary }]}>
                  <Text style={[styles.avatarText, { color: colors.white }]}>
                    {item.sender?.first_name?.[0] || 'U'}
                  </Text>
                </View>
              ) : (
                <View style={styles.avatarSpacer} />
              )}
            </View>
          )}

          <View style={[
            styles.messageBubble,
            {
              backgroundColor: isMyMessage ? colors.primary : colors.surface,
              borderColor: colors.border,
              maxWidth: maxWidth,
              minWidth: isShortMessage ? 'auto' : '30%',
            },
            isMyMessage ? styles.myMessageBubble : styles.otherMessageBubble,
          ]}>
            {showSenderName && (
              <Text style={[styles.senderName, { color: colors.textSecondary }]}>
                {item.sender?.first_name} {item.sender?.last_name}
              </Text>
            )}

            {item.type === 'image' && (item.fileUrl || item.file_url || item.metadata?.imageUri) && (
              <Image
                source={{
                  uri: item.fileUrl || item.file_url || item.metadata?.imageUri
                }}
                style={[
                  styles.messageImage,
                  {
                    width: isVeryLongMessage ? 250 : 200,
                    height: isVeryLongMessage ? 188 : 150,
                  }
                ]}
                resizeMode="cover"

              />
            )}

            {displayContent && (
              <Text
                style={[
                  styles.messageText,
                  {
                    color: isMyMessage ? colors.white : colors.text,
                    fontSize: fontSize,
                    lineHeight: lineHeight,
                  }
                ]}
                selectable={true}
              >
                {displayContent}
              </Text>
            )}

            <View style={[
              styles.messageFooter,
              isLongMessage || isVeryLongMessage ? styles.longMessageFooter : null
            ]}>
              <View style={styles.messageTimeContainer}>
                <Text style={[
                  styles.messageTime,
                  { color: isMyMessage ? colors.white + '80' : colors.textTertiary }
                ]}>
                  {formatTime(createdAt)}
                </Text>

                {messageLength > 50 && (
                  <Text style={[
                    styles.characterCount,
                    { color: isMyMessage ? colors.white + '60' : colors.textTertiary }
                  ]}>
                    {messageLength} chars
                  </Text>
                )}
              </View>

              {isMyMessage && (
                <View style={styles.messageStatus}>
                  {item.isOptimistic ? (
                    <Ionicons
                      name="time-outline"
                      size={14}
                      color={colors.white + '60'}
                      style={styles.readIndicator}
                    />
                  ) : (
                    <Ionicons
                      name={(item.isRead || item.is_read) ? 'checkmark-done' : 'checkmark'}
                      size={14}
                      color={colors.white + '80'}
                      style={styles.readIndicator}
                    />
                  )}
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  const removeImage = (index) => {
    setSelectedImages(prevImages => prevImages.filter((_, i) => i !== index));
  };

  const renderInputArea = () => (
    <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
      {selectedImages.length > 0 && (
        <ScrollView
          horizontal
          style={styles.selectedImagesContainer}
          showsHorizontalScrollIndicator={false}
        >
          {selectedImages.map((image, index) => (
            <View key={index} style={styles.selectedImageWrapper}>
              <Image source={{ uri: image.uri }} style={styles.selectedImage} />
              <TouchableOpacity
                style={[styles.removeImageButton, { backgroundColor: colors.error }]}
                onPress={() => removeImage(index)}
              >
                <Ionicons name="close" size={16} color={colors.white} />
              </TouchableOpacity>
              {selectedImages.length > 1 && (
                <View style={[styles.imageCounter, { backgroundColor: colors.primary }]}>
                  <Text style={[styles.imageCounterText, { color: colors.white }]}>
                    {index + 1}
                  </Text>
                </View>
              )}
            </View>
          ))}
        </ScrollView>
      )}

      <View style={styles.inputRow}>
        <TouchableOpacity
          style={styles.attachButton}
          onPress={pickImage}
        >
          <Ionicons name="attach" size={24} color={colors.primary} />
        </TouchableOpacity>

        <TextInput
          style={[
            styles.textInput,
            {
              backgroundColor: colors.backgroundSecondary,
              color: colors.text,
              borderColor: colors.border,
            }
          ]}
          placeholder="Type a message..."
          placeholderTextColor={colors.textTertiary}
          value={messageText}
          onChangeText={setMessageText}
          multiline
          maxLength={1000}
        />

        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: (messageText.trim() || selectedImages.length > 0) ? colors.primary : colors.backgroundSecondary,
            }
          ]}
          onPress={sendMessage}
          disabled={(!messageText.trim() && selectedImages.length === 0) || sending}
        >
          <Ionicons
            name="send"
            size={20}
            color={(messageText.trim() || selectedImages.length > 0) ? colors.white : colors.textTertiary}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="chatbubble-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No messages yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Start the conversation by sending a message
      </Text>
    </View>
  );

  const startVoiceCall = () => {
    Alert.alert('Voice Call', 'Voice calling feature coming soon!');
  };

  const startVideoCall = () => {
    Alert.alert('Video Call', 'Video calling feature coming soon!');
  };

  const renderHeader = () => (
    <View style={styles.header}>
        <View style={styles.headerInfo}>
        <Text style={[styles.headerTitle, { color: colors.text }]} numberOfLines={1}>
          {roomName || 'Chat'}
        </Text>
        <View style={styles.headerSubtitleContainer}>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {roomType === 'private' ? 'Private Chat' : 'Group Chat'}
          </Text>
          <View style={styles.e2eeIndicator}>
            <Ionicons name="shield-checkmark" size={12} color={colors.success} />
            <Text style={[styles.e2eeText, { color: colors.success }]}>E2EE</Text>
          </View>
        </View>
      </View>

      <View style={styles.headerActions}>
        <TouchableOpacity
          style={styles.callButton}
          onPress={startVoiceCall}
        >
          <Ionicons name="call" size={20} color={colors.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.callButton}
          onPress={startVideoCall}
        >
          <Ionicons name="videocam" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Fixed Header */}
      <View style={[styles.headerContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        {renderHeader()}
      </View>

      {/* Scrollable Messages Area */}
      <View style={styles.messagesContainer}>
        <FlatList
          ref={flatListRef}
          data={messages.filter(msg => msg && msg.id)}
          renderItem={renderMessage}
          keyExtractor={(item, index) => item?.id || `message_${index}`}
          contentContainerStyle={styles.messagesList}
          ListEmptyComponent={!loading && renderEmptyState()}
          onContentSizeChange={scrollToBottom}
          showsVerticalScrollIndicator={false}
          inverted={false}
          removeClippedSubviews={false}
          initialNumToRender={20}
          maxToRenderPerBatch={10}
          windowSize={10}
          getItemLayout={null}
        />

        {isTyping && (
          <View style={[styles.typingIndicator, { backgroundColor: colors.surface }]}>
            <Text style={[styles.typingText, { color: colors.textSecondary }]}>
              Someone is typing...
            </Text>
          </View>
        )}
      </View>

      {/* Fixed Input Area with Keyboard Handling */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        style={styles.keyboardAvoidingView}
      >
        {renderInputArea()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    borderBottomWidth: 1,
    ...shadows.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    paddingTop: spacing.md, // Reduced top padding
  },
  messagesContainer: {
    flex: 1,
    marginTop: 60, // Reduced height for compact header
    marginBottom: 100, // Space for input area
  },
  keyboardAvoidingView: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  backButton: {
    padding: spacing.sm,
    marginRight: spacing.sm,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  headerSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    marginRight: spacing.sm,
  },
  e2eeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.sm,
  },
  e2eeText: {
    fontSize: 10,
    fontWeight: typography.fontWeight.semibold,
    marginLeft: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callButton: {
    padding: spacing.sm,
    marginLeft: spacing.sm,
  },
  messagesList: {
    padding: spacing.md,
    paddingBottom: spacing.xl, // Extra bottom padding so last message isn't hidden behind input
    flexGrow: 1,
  },
  dateContainer: {
    alignItems: 'center',
    marginVertical: spacing.md,
  },
  dateText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  messageContainer: {
    flexDirection: 'row',
    marginVertical: spacing.xs,
  },
  myMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    marginRight: spacing.sm,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
  },
  avatarSpacer: {
    width: 32,
  },
  messageBubble: {
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    marginBottom: spacing.xs,
    flexShrink: 1,
  },
  myMessageBubble: {
    borderBottomRightRadius: borderRadius.sm,
  },
  otherMessageBubble: {
    borderBottomLeftRadius: borderRadius.sm,
  },
  senderName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  messageImage: {
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
  },
  messageText: {
    fontSize: typography.fontSize.base,
    lineHeight: 20,
    marginBottom: spacing.sm,
    flexWrap: 'wrap',
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginTop: spacing.xs,
  },
  longMessageFooter: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  messageTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  messageTime: {
    fontSize: typography.fontSize.xs,
  },
  characterCount: {
    fontSize: typography.fontSize.xs,
    fontStyle: 'italic',
    opacity: 0.7,
  },
  messageStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  readIndicator: {
    marginLeft: spacing.xs,
  },
  inputContainer: {
    borderTopWidth: 1,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    paddingBottom: spacing.md, // Extra bottom padding for safe area
  },
  selectedImagesContainer: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm,
    marginBottom: spacing.sm,
  },
  selectedImageWrapper: {
    position: 'relative',
    marginRight: spacing.sm,
  },
  selectedImage: {
    width: 80,
    height: 60,
    borderRadius: borderRadius.md,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageCounter: {
    position: 'absolute',
    bottom: -8,
    left: -8,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageCounterText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  attachButton: {
    padding: spacing.sm,
    marginRight: spacing.sm,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.base,
    maxHeight: 100,
    marginRight: spacing.sm,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingIndicator: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  typingText: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
});

export default ChatRoomScreen;
