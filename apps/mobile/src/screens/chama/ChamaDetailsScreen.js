import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const ChamaDetailsScreen = ({ route, navigation }) => {
  const { chamaId } = route.params;
  const { theme, user, setSelectedChama, switchToChamaDashboard } = useApp();
  const colors = getThemeColors(theme);
  
  const [chama, setChama] = useState(null);
  const [members, setMembers] = useState([]);
  const [meetings, setMeetings] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [loans, setLoans] = useState([]);
  const [polls, setPolls] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAvatarMember, setSelectedAvatarMember] = useState(null);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [userMembership, setUserMembership] = useState(null);
  const [cachedAvatarData, setCachedAvatarData] = useState(null);

  // Load cached avatar data on component mount
  useEffect(() => {
    const loadCachedAvatarData = async () => {
      try {
        const cachedData = await AsyncStorage.getItem('cached_avatar_data');
        if (cachedData) {
          setCachedAvatarData(cachedData);
        }
      } catch (error) {
        console.error('Failed to load cached avatar data:', error);
      }
    };

    loadCachedAvatarData();
  }, []);

  // Reset state and reload data when chamaId changes
  useEffect(() => {
    if (chamaId) {
      console.log('🔄 ChamaId changed, resetting state and loading new data:', chamaId);

      // Reset all state to prevent showing previous chama data
      setChama(null);
      setMembers([]);
      setMeetings([]);
      setTransactions([]);
      setLoans([]);
      setPolls([]);
      setStatistics(null);
      setUserMembership(null);
      setLoading(true);

      // Load new chama data
      loadChamaDetails();
    }
  }, [chamaId]);

  // Also reload when screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      if (chamaId) {
        console.log('🔄 Screen focused, reloading chama data:', chamaId);
        loadChamaDetails();
      }
    });

    return unsubscribe;
  }, [navigation, chamaId]);

  const loadChamaDetails = async (targetChamaId = chamaId) => {
    try {
      console.log('🔍 Loading chama details for chamaId:', targetChamaId);

      // Ensure we have a valid chamaId
      if (!targetChamaId) {
        console.error('❌ No chamaId provided');
        return;
      }

      // Load basic chama data and members first (critical for page display)
      const [chamaResponse, membersResponse] = await Promise.all([
        ApiService.getChamaById(targetChamaId),
        ApiService.getChamaMembers(targetChamaId)
      ]);

      console.log('📊 Chama response:', chamaResponse);
      console.log('👥 Members response:', membersResponse);

      if (chamaResponse.success) {
        setChama(chamaResponse.data);
        setSelectedChama(chamaResponse.data);
        console.log('✅ Chama data loaded:', chamaResponse.data);
      }

      if (membersResponse.success) {
        setMembers(membersResponse.data || []);
        const membership = membersResponse.data?.find(m => m.user_id === user?.id);
        setUserMembership(membership);
        console.log('✅ Members loaded:', membersResponse.data?.length || 0);
      }

      // Load additional data in background (non-blocking)
      Promise.all([
        // Load user transactions
        ApiService.getChamaTransactions(targetChamaId).then(response => {
          if (response.success) {
            const allTransactions = response.data || [];
            const userTransactions = allTransactions.filter(transaction => {
              return transaction.user_id === user?.id ||
                     transaction.initiated_by === user?.id ||
                     transaction.member_id === user?.id ||
                     transaction.sender_id === user?.id ||
                     transaction.recipient_id === user?.id ||
                     (transaction.user && transaction.user.id === user?.id) ||
                     (transaction.member && transaction.member.user_id === user?.id);
            });
            setTransactions(userTransactions);
            console.log('✅ User transactions loaded:', userTransactions.length);
          }
        }).catch(error => {
          console.log('⚠️ Transactions not available:', error.message);
          setTransactions([]);
        }),

        // Load active polls
        ApiService.getActiveVotes(targetChamaId).then(response => {
          if (response.success) {
            const pollsData = response.data || [];
            setPolls(pollsData);
            console.log('✅ Active polls loaded:', pollsData.length);

            if (pollsData.length > 0) {
              console.log('📋 First active poll:', {
                id: pollsData[0].id,
                title: pollsData[0].title,
                status: pollsData[0].status,
                userVoted: pollsData[0].userVoted
              });
            }
          }
        }).catch(error => {
          console.log('⚠️ Active polls not available:', error.message);
          setPolls([]);
        }),

        // Load meetings (like ChamaMeetingsScreen does)
        ApiService.getMeetings(targetChamaId).then(response => {
          if (response.success) {
            const meetingsData = response.data || [];
            setMeetings(meetingsData);
            console.log('✅ Meetings loaded:', meetingsData.length);

            if (meetingsData.length > 0) {
              console.log('📋 First meeting:', {
                id: meetingsData[0].id,
                title: meetingsData[0].title,
                scheduledAt: meetingsData[0].scheduledAt,
                status: meetingsData[0].status
              });
            }
          }
        }).catch(error => {
          console.log('⚠️ Meetings not available:', error.message);
          setMeetings([]);
        }),

        // Load statistics
        ApiService.getChamaStatistics(targetChamaId).then(response => {
          if (response.success) {
            setStatistics(response.data);
            console.log('✅ Statistics loaded');
          }
        }).catch(error => {
          console.log('⚠️ Statistics not available:', error.message);
          setStatistics(null);
        })
      ]);

      // Set empty array for loans (API not implemented yet)
      setLoans([]);

    } catch (error) {
      console.error('❌ Failed to load chama details:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    console.log('🔄 Manual refresh triggered for chamaId:', chamaId);
    setRefreshing(true);
    await loadChamaDetails(chamaId);
    setRefreshing(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleJoinChama = async () => {
    try {
      const response = await ApiService.joinChama(chamaId);
      if (response.success) {
        Alert.alert('Success', 'You have successfully joined the chama!');
        await loadChamaDetails();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to join chama');
    }
  };

  const handleLeaveChama = () => {
    Alert.alert(
      'Leave Chama',
      'Are you sure you want to leave this chama?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Leave', style: 'destructive', onPress: confirmLeaveChama },
      ]
    );
  };

  const confirmLeaveChama = async () => {
    try {
      const response = await ApiService.leaveChama(chamaId);
      if (response.success) {
        Alert.alert('Success', 'You have left the chama');
        navigation.goBack();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to leave chama');
    }
  };

  const renderChamaHeader = () => {
    // Debug logging to check category value
    console.log('🔍 Chama category debug:', {
      chamaName: chama?.name,
      category: chama?.category,
      type: chama?.type,
      fullChamaData: chama
    });

    // Determine if it's a chama or contribution group
    const isContributionGroup = chama?.category === 'contribution';
    console.log('📊 Category determination:', {
      category: chama?.category,
      isContributionGroup,
      comparison: `"${chama?.category}" === "contribution"`
    });

    const typeConfig = isContributionGroup ? {
      color: colors.success,
      icon: 'heart',
      label: 'Fund Group', // Shorter for mobile
      fullLabel: 'Contribution Group',
      bgColor: colors.success + '15'
    } : {
      color: colors.primary,
      icon: 'people',
      label: 'Chama',
      fullLabel: 'Chama',
      bgColor: colors.primary + '15'
    };

    return (
      <Card style={[styles.section, { borderLeftWidth: 4, borderLeftColor: typeConfig.color }]}>
        {/* Category Badge */}
        <View style={[styles.categoryBadge, { backgroundColor: typeConfig.color }]}>
          <Ionicons name={typeConfig.icon} size={12} color={colors.white} />
          <Text style={[styles.categoryBadgeText, { color: colors.white }]}>
            {typeConfig.label.toUpperCase()}
          </Text>
        </View>

        <View style={styles.chamaHeader}>
          <View style={[styles.chamaAvatar, { backgroundColor: typeConfig.color }]}>
            <Ionicons name={typeConfig.icon} size={32} color={colors.white} />
          </View>

          <View style={styles.chamaInfo}>
            <Text style={[styles.chamaName, { color: colors.text }]}>
              {chama?.name}
            </Text>
            <Text style={[styles.chamaType, { color: colors.textSecondary }]}>
              {chama?.type} • {chama?.county}, {chama?.town}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: colors.success + '20' }]}>
              <Text style={[styles.statusText, { color: colors.success }]}>
                {chama?.status?.toUpperCase()}
              </Text>
            </View>
          </View>
        </View>

        {/* Improved Description Section */}
        {chama?.description && (
          <View style={styles.descriptionContainer}>
            <Text style={[styles.descriptionLabel, { color: colors.text }]}>
              About this {typeConfig.fullLabel}
            </Text>
            <Text
              style={[styles.chamaDescription, { color: colors.textSecondary }]}
              numberOfLines={0}
              allowFontScaling={true}
            >
              {chama.description}
            </Text>
          </View>
        )}
      </Card>
    );
  };

  const renderStats = () => {
    // Extract data using the same structure as ChamaDashboard
    const financialStats = statistics?.financial_stats || {};
    const memberStats = statistics?.member_stats || {};
    const activityStats = statistics?.activity_stats || {};
    const chamaInfo = statistics?.chama_info || {};

    console.log('📊 Rendering stats with structured data:', {
      financialStats,
      memberStats,
      activityStats,
      chamaInfo,
      rawStatistics: statistics
    });

    // Calculate values using the same logic as ChamaDashboard
    const walletBalance = chamaInfo.wallet_balance || chamaInfo.total_funds || chama?.total_funds || 0;
    const totalMembers = memberStats.active_members || memberStats.total_members || chamaInfo.current_members || members.length || 0;
    const maxMembers = chama?.max_members || chamaInfo.max_members || 50;
    const totalMeetings = activityStats.total_meetings || 0;
    const contributionAmount = chama?.contribution_amount || 0;
    const contributionFrequency = chama?.contribution_frequency || 'Monthly';
    const totalContributions = financialStats.total_contributions || 0;
    const activePolls = polls.length || 0; // Polls from API call

    return (
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Chama Statistics
        </Text>

        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Ionicons name="people" size={24} color={colors.primary} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {totalMembers}/{maxMembers}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Members
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="wallet" size={24} color={colors.secondary} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {formatCurrency(walletBalance)}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Balance
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="trending-up" size={24} color={colors.success} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {formatCurrency(contributionAmount)}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {contributionFrequency} Contribution
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="cash" size={24} color={colors.warning} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {totalContributions}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Accounts
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="calendar-outline" size={24} color={colors.info} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {totalMeetings}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Meetings
            </Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="checkmark-circle" size={24} color={colors.accent} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {activePolls}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Active Polls
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  // Helper function to handle avatar tap
  const handleAvatarPress = (member) => {
    setSelectedAvatarMember(member);
    setShowAvatarModal(true);
  };

  const closeAvatarModal = () => {
    setShowAvatarModal(false);
    setSelectedAvatarMember(null);
  };

  // Helper function to generate a consistent avatar URL from email
  const getAvatarFromEmail = (email, size = 40) => {
    if (!email) return null;
    // Use DiceBear API for consistent avatars based on email
    const seed = encodeURIComponent(email.toLowerCase().trim());
    return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&size=${size}&backgroundColor=random`;
  };

  // Helper function to render member avatar with real profile photo
  const renderMemberAvatar = (member) => {
    if (!member) {
      return (
        <View style={[styles.memberAvatar, { backgroundColor: colors.primary }]}>
          <Text style={[styles.memberInitials, { color: colors.white }]}>?</Text>
        </View>
      );
    }

    // Access data from nested user object
    const user = member?.user || {};
    const firstName = user?.first_name || member?.first_name || '';
    const lastName = user?.last_name || member?.last_name || '';
    const email = user?.email || member?.email;

    // Try multiple avatar sources
    const avatarUrl = user?.avatar_url || user?.avatar || user?.profile_image || member?.avatar || member?.avatarUrl;

    // Generate initials for fallback
    const initials = (firstName?.[0] || '') + (lastName?.[0] || '');

    // Try to use provided avatar URL first
    if (avatarUrl && avatarUrl.trim()) {
      let fullAvatarUrl;
      if (avatarUrl.startsWith('http') || avatarUrl.startsWith('data:')) {
        fullAvatarUrl = avatarUrl;
      } else {
        fullAvatarUrl = `${ApiService.baseURL}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`;
      }

      return (
        <TouchableOpacity onPress={() => handleAvatarPress(member)}>
          <Image
            source={{ uri: fullAvatarUrl }}
            style={styles.memberAvatar}
            onError={(error) => {
              console.log('Member avatar load error for:', fullAvatarUrl, error);
              // Will fallback to initials or generated avatar
            }}
          />
        </TouchableOpacity>
      );
    }

    // Try generated avatar as fallback if email is available
    if (email && email.trim()) {
      const generatedAvatarUrl = getAvatarFromEmail(email, 40);
      return (
        <TouchableOpacity onPress={() => handleAvatarPress(member)}>
          <Image
            source={{ uri: generatedAvatarUrl }}
            style={styles.memberAvatar}
            onError={(error) => {
              console.log('Generated avatar load error for:', email, error);
              // Will fallback to initials
            }}
          />
        </TouchableOpacity>
      );
    }

    // Final fallback to initials
    return (
      <TouchableOpacity onPress={() => handleAvatarPress(member)}>
        <View style={[styles.memberAvatar, { backgroundColor: colors.primary }]}>
          <Text style={[styles.memberInitials, { color: colors.white }]}>
            {initials || '?'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMembers = () => (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Members ({members.length})
        </Text>
        {members.length > 5 && (
          <TouchableOpacity onPress={() => navigation.navigate('ChamaMembersScreen', { chamaId })}>
            <Text style={[styles.viewMoreText, { color: colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {members.length === 0 ? (
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No members found
        </Text>
      ) : (
        <View style={styles.membersList}>
          {members.slice(0, 5).map((member, index) => {
            // Get member name safely - check multiple possible data structures
            const firstName = member.first_name || member.user?.first_name || '';
            const lastName = member.last_name || member.user?.last_name || '';
            const fullName = `${firstName} ${lastName}`.trim() || 'Unknown Member';

            // Parse join date safely
            let joinDate = 'Unknown Date';
            try {
              if (member.joined_at) {
                const date = new Date(member.joined_at);
                if (!isNaN(date.getTime())) {
                  joinDate = date.toLocaleDateString();
                }
              }
            } catch (error) {
              console.log('Error parsing join date:', error);
            }

            // Get status safely
            const memberStatus = member.status || member.user?.status || 'active';
            const isActive = memberStatus === 'active';

            return (
              <View key={member.user_id || member.id || index} style={[styles.memberItem, { borderBottomColor: colors.border }]}>
                {renderMemberAvatar(member)}
                <View style={styles.memberInfo}>
                  <Text style={[styles.memberName, { color: colors.text }]}>
                    {fullName}
                  </Text>
                  <Text style={[styles.memberRole, { color: colors.textSecondary }]}>
                    {member.role || 'Member'} • Joined {joinDate}
                  </Text>
                </View>
                <View style={[styles.memberStatus, { backgroundColor: isActive ? colors.success + '20' : colors.warning + '20' }]}>
                  <Text style={[styles.memberStatusText, { color: isActive ? colors.success : colors.warning }]}>
                    {memberStatus}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>
      )}
    </Card>
  );

  const renderMeetings = () => (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Recent Meetings ({meetings.length})
        </Text>
        {meetings.length > 5 && (
          <TouchableOpacity onPress={() => navigation.navigate('ChamaMeetingsScreen', { chamaId })}>
            <Text style={[styles.viewMoreText, { color: colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {meetings.length === 0 ? (
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No meetings scheduled
        </Text>
      ) : (
        <View style={styles.meetingsList}>
          {meetings.slice(0, 5).map((meeting, index) => {
            // Parse meeting date safely (using ChamaMeetingsScreen structure)
            let meetingDate = 'Unknown Date';
            let meetingTime = '';

            try {
              const dateStr = meeting.scheduledAt || meeting.scheduled_date || meeting.date;
              if (dateStr) {
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                  meetingDate = date.toLocaleDateString();
                  meetingTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                }
              }
            } catch (error) {
              console.log('Error parsing meeting date:', error);
            }

            // Determine meeting status and icon
            const meetingStatus = meeting.status || 'scheduled';
            const isCompleted = meetingStatus === 'completed' || meetingStatus === 'ended';
            const isPast = new Date(meeting.scheduledAt || meeting.scheduled_date || meeting.date) < new Date();

            return (
              <View key={meeting.id || index} style={[styles.meetingItem, { borderBottomColor: colors.border }]}>
                <View style={[styles.meetingIcon, { backgroundColor: colors.warning + '20' }]}>
                  <Ionicons
                    name={isCompleted || isPast ? "checkmark-circle" : "calendar"}
                    size={20}
                    color={isCompleted || isPast ? colors.success : colors.warning}
                  />
                </View>
                <View style={styles.meetingInfo}>
                  <Text style={[styles.meetingTitle, { color: colors.text }]}>
                    {meeting.title || 'Chama Meeting'}
                  </Text>
                  <Text style={[styles.meetingDate, { color: colors.textSecondary }]}>
                    {meetingDate} at {meetingTime}
                  </Text>
                  <Text style={[styles.meetingType, { color: colors.textSecondary }]}>
                    {meeting.meetingType || meeting.type || 'General'} • {meeting.location || 'Online'}
                  </Text>
                  {meeting.description && (
                    <Text style={[styles.meetingDescription, { color: colors.textSecondary }]} numberOfLines={1}>
                      {meeting.description}
                    </Text>
                  )}
                </View>
                <View style={[styles.meetingStatus, {
                  backgroundColor: isCompleted ? colors.success + '20' : isPast ? colors.textSecondary + '20' : colors.info + '20'
                }]}>
                  <Text style={[styles.meetingStatusText, {
                    color: isCompleted ? colors.success : isPast ? colors.textSecondary : colors.info
                  }]}>
                    {isCompleted ? 'Completed' : isPast ? 'Past' : 'Scheduled'}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>
      )}
    </Card>
  );

  const renderTransactions = () => (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          My Transactions ({transactions.length})
        </Text>
        {transactions.length > 5 && (
          <TouchableOpacity onPress={() => navigation.navigate('ChamaTransactionsScreen', { chamaId })}>
            <Text style={[styles.viewMoreText, { color: colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {transactions.length === 0 ? (
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No transactions found for your account
        </Text>
      ) : (
        <View style={styles.transactionsList}>
          {transactions.slice(0, 5).map((transaction, index) => {
            console.log(`🔍 Processing user transaction ${index}:`, JSON.stringify(transaction, null, 2));

            // Parse contribution date - comprehensive date extraction
            let contributionDate = 'Unknown Date';

            // Log all available date fields for debugging
            console.log(`🗓️ Available date fields in transaction:`, {
              contribution_date: transaction.contribution_date,
              created_at: transaction.created_at,
              transaction_date: transaction.transaction_date,
              date: transaction.date,
              timestamp: transaction.timestamp,
              updated_at: transaction.updated_at,
              processed_at: transaction.processed_at,
              completed_at: transaction.completed_at,
              payment_date: transaction.payment_date,
              recorded_at: transaction.recorded_at
            });

            const possibleDateFields = [
              'contribution_date', 'payment_date', 'transaction_date', 'created_at',
              'recorded_at', 'processed_at', 'completed_at', 'date', 'timestamp', 'updated_at'
            ];

            for (const field of possibleDateFields) {
              if (transaction[field]) {
                console.log(`🔍 Trying to parse date from ${field}:`, transaction[field]);
                try {
                  // Handle different date formats
                  let dateValue = transaction[field];

                  // If it's already a Date object
                  if (dateValue instanceof Date) {
                    contributionDate = dateValue.toLocaleDateString();
                    console.log(`✅ Date object parsed from ${field}:`, contributionDate);
                    break;
                  }

                  // If it's a string or number, try to parse it
                  const date = new Date(dateValue);
                  if (!isNaN(date.getTime()) && date.getFullYear() > 1900) {
                    contributionDate = date.toLocaleDateString();
                    console.log(`✅ Date string/number parsed from ${field}:`, contributionDate, 'from value:', dateValue);
                    break;
                  } else {
                    console.log(`❌ Invalid date from ${field}:`, dateValue, 'parsed as:', date);
                  }
                } catch (error) {
                  console.log(`❌ Error parsing date from ${field}:`, error, 'value was:', transaction[field]);
                }
              } else {
                console.log(`⚠️ Field ${field} is empty or undefined`);
              }
            }

            // If still no date found, try to extract from any string field that might contain a date
            if (contributionDate === 'Unknown Date') {
              console.log('🔍 No standard date field found, checking all fields for date-like values...');
              Object.keys(transaction).forEach(key => {
                if (typeof transaction[key] === 'string' && transaction[key].match(/\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}/)) {
                  try {
                    const date = new Date(transaction[key]);
                    if (!isNaN(date.getTime()) && date.getFullYear() > 1900) {
                      contributionDate = date.toLocaleDateString();
                      console.log(`✅ Date found in field ${key}:`, contributionDate, 'from value:', transaction[key]);
                      return;
                    }
                  } catch (error) {
                    // Continue searching
                  }
                }
              });
            }

            // Check if this is an anonymous contribution
            const isAnonymousContribution = transaction.metadata?.isAnonymous || transaction.metadata?.displayName === 'Anonymous';

            // Get contributor name - handle anonymous contributions
            let contributorName = 'You';

            if (isAnonymousContribution) {
              contributorName = 'Anonymous';
            } else {
              // If we want to show the actual name instead of "You"
              if (user?.first_name || user?.last_name) {
                contributorName = `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'You';
              }

              // Alternative: get name from transaction data
              const transactionContributorName =
                transaction.contributor_name ||
                transaction.member_name ||
                transaction.user_name ||
                (transaction.user ? `${transaction.user.first_name || ''} ${transaction.user.last_name || ''}`.trim() : '') ||
                (transaction.member ? `${transaction.member.first_name || ''} ${transaction.member.last_name || ''}`.trim() : '');

              if (transactionContributorName && transactionContributorName !== 'undefined undefined') {
                contributorName = transactionContributorName;
              }
            }

            // Determine transaction type and description
            const transactionType = transaction.type || 'transaction';
            const isContribution = transactionType === 'contribution' || transactionType === 'deposit' || transaction.description?.toLowerCase().includes('contribution');
            const transactionDescription = transaction.description ||
                                         (isContribution ? 'Chama Contribution' : 'Transaction') ||
                                         transactionType;

            console.log(`📊 User transaction ${index} processed:`, {
              date: contributionDate,
              contributor: contributorName,
              amount: transaction.amount,
              type: transactionType,
              isContribution,
              description: transactionDescription
            });

            return (
              <View key={transaction.id || index} style={[styles.transactionItem, { borderBottomColor: colors.border }]}>
                <View style={[styles.transactionIcon, { backgroundColor: isContribution ? colors.success + '20' : colors.primary + '20' }]}>
                  <Ionicons
                    name={isContribution ? 'arrow-up' : 'arrow-down'}
                    size={20}
                    color={isContribution ? colors.success : colors.primary}
                  />
                </View>
                <View style={styles.transactionInfo}>
                  <Text style={[styles.transactionTitle, { color: colors.text }]}>
                    {transactionDescription}
                  </Text>
                  <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>
                    {contributionDate} • {contributorName}
                  </Text>
                </View>
                <Text style={[styles.transactionAmount, { color: isContribution ? colors.success : colors.primary }]}>
                  {isContribution ? '+' : '-'}{formatCurrency(transaction.amount || 0)}
                </Text>
              </View>
            );
          })}
        </View>
      )}
    </Card>
  );

  const renderActivePolls = () => {
    console.log('🗳️ Rendering Active Polls section with data:', {
      pollsLength: polls.length,
      pollsData: polls,
      chamaId: chamaId
    });

    return (
      <Card style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Active Polls ({polls.length})
          </Text>
          {polls.length > 3 && (
            <TouchableOpacity onPress={() => navigation.navigate('PollsVotingScreen', { chamaId })}>
              <Text style={[styles.viewMoreText, { color: colors.primary }]}>
                View All
              </Text>
            </TouchableOpacity>
          )}
        </View>

      {polls.length === 0 ? (
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No active polls at the moment
        </Text>
      ) : (
        <View style={styles.pollsList}>
          {polls.slice(0, 3).map((poll, index) => {
            // Parse poll dates
            let pollEndDate = 'No deadline';
            let pollStartDate = 'Unknown';

            try {
              if (poll.endDate || poll.end_date || poll.deadline) {
                const endDate = new Date(poll.endDate || poll.end_date || poll.deadline);
                if (!isNaN(endDate.getTime())) {
                  pollEndDate = endDate.toLocaleDateString();
                }
              }

              if (poll.createdAt || poll.created_at || poll.start_date) {
                const startDate = new Date(poll.createdAt || poll.created_at || poll.start_date);
                if (!isNaN(startDate.getTime())) {
                  pollStartDate = startDate.toLocaleDateString();
                }
              }
            } catch (error) {
              console.log('Error parsing poll dates:', error);
            }

            // Check if user has voted (using PollsVotingScreen structure)
            const userHasVoted = poll.userVoted || poll.user_has_voted || false;

            // Calculate vote counts (using PollsVotingScreen structure)
            const totalVotes = poll.totalVotes || poll.total_votes || 0;
            const pollStatus = poll.status || 'active';

            // Determine if poll is still active
            const isActive = pollStatus === 'active' &&
                           (!poll.endDate && !poll.end_date ||
                            new Date(poll.endDate || poll.end_date) > new Date());

            return (
              <View key={poll.id || index} style={[styles.pollItem, { borderBottomColor: colors.border }]}>
                <View style={[styles.pollIcon, { backgroundColor: colors.info + '20' }]}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.info} />
                </View>

                <View style={styles.pollInfo}>
                  <Text style={[styles.pollTitle, { color: colors.text }]}>
                    {poll.title || poll.question || 'Poll'}
                  </Text>

                  <Text style={[styles.pollDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                    {poll.description || poll.question || 'No description available'}
                  </Text>

                  <View style={styles.pollMeta}>
                    <Text style={[styles.pollDate, { color: colors.textSecondary }]}>
                      Created: {pollStartDate}
                    </Text>
                    {poll.end_date && (
                      <Text style={[styles.pollDeadline, { color: isActive ? colors.warning : colors.textSecondary }]}>
                        Ends: {pollEndDate}
                      </Text>
                    )}
                  </View>

                  <View style={styles.pollStats}>
                    <Text style={[styles.pollVotes, { color: colors.primary }]}>
                      {totalVotes} vote{totalVotes !== 1 ? 's' : ''}
                    </Text>

                    {userHasVoted && (
                      <View style={[styles.votedBadge, { backgroundColor: colors.success + '20' }]}>
                        <Ionicons name="checkmark" size={12} color={colors.success} />
                        <Text style={[styles.votedText, { color: colors.success }]}>
                          Voted
                        </Text>
                      </View>
                    )}
                  </View>
                </View>

                <View style={styles.pollActions}>
                  <View style={[styles.pollStatus, {
                    backgroundColor: isActive ? colors.success + '20' : colors.textSecondary + '20'
                  }]}>
                    <Text style={[styles.pollStatusText, {
                      color: isActive ? colors.success : colors.textSecondary
                    }]}>
                      {isActive ? 'Active' : 'Closed'}
                    </Text>
                  </View>

                  {isActive && (
                    <TouchableOpacity
                      style={[styles.voteButton, {
                        backgroundColor: userHasVoted ? colors.textSecondary + '20' : colors.primary + '20',
                        borderColor: userHasVoted ? colors.textSecondary : colors.primary
                      }]}
                      onPress={() => navigation.navigate('PollsVotingScreen', {
                        chamaId,
                        pollId: poll.id,
                        focusPoll: true
                      })}
                      disabled={userHasVoted}
                    >
                      <Text style={[styles.voteButtonText, {
                        color: userHasVoted ? colors.textSecondary : colors.primary
                      }]}>
                        {userHasVoted ? 'Voted' : 'Vote'}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            );
          })}
        </View>
      )}
    </Card>
    );
  };

  const renderChamaRules = () => {
    // Parse rules - they might be a JSON string, object, or plain string
    let rules = [];
    if (chama?.rules) {
      try {
        if (typeof chama.rules === 'string') {
          // Try to parse as JSON first
          try {
            const parsed = JSON.parse(chama.rules);
            if (Array.isArray(parsed)) {
              rules = parsed;
            } else if (typeof parsed === 'object') {
              rules = Object.entries(parsed).map(([key, value]) => ({
                title: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
                description: typeof value === 'string' ? value : JSON.stringify(value)
              }));
            } else {
              // If parsed result is not array/object, treat as single rule
              rules = [{ title: 'Chama Rule', description: String(parsed) }];
            }
          } catch (jsonError) {
            // If JSON parsing fails, treat as plain text rule
            rules = [{ title: 'Chama Rule', description: chama.rules }];
          }
        } else if (Array.isArray(chama.rules)) {
          rules = chama.rules;
        } else if (typeof chama.rules === 'object') {
          // Convert object to array format
          rules = Object.entries(chama.rules).map(([key, value]) => ({
            title: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
            description: typeof value === 'string' ? value : JSON.stringify(value)
          }));
        }
      } catch (error) {
        console.log('Error parsing chama rules:', error);
        // Fallback: treat as plain text
        if (typeof chama.rules === 'string') {
          rules = [{ title: 'Chama Rule', description: chama.rules }];
        }
      }
    }

    return (
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Chama Rules & Regulations
        </Text>

        <View style={styles.rulesList}>
          {/* Show custom rules first if they exist */}
          {rules.length > 0 && (
            <>
              <Text style={[styles.rulesSubtitle, { color: colors.text }]}>
                Chama-Specific Rules:
              </Text>
              {rules.map((rule, index) => (
                <View key={`custom-${index}`} style={[styles.ruleItem, { borderLeftColor: colors.primary }]}>
                  <View style={styles.ruleHeader}>
                    <Text style={[styles.ruleNumber, { color: colors.primary }]}>
                      {index + 1}
                    </Text>
                    <Text style={[styles.ruleTitle, { color: colors.text }]}>
                      {rule.title || `Rule ${index + 1}`}
                    </Text>
                  </View>
                  <Text style={[styles.ruleDescription, { color: colors.textSecondary }]}>
                    {rule.description || rule}
                  </Text>
                  {rule.penalty && (
                    <Text style={[styles.rulePenalty, { color: colors.warning }]}>
                      Penalty: {rule.penalty}
                    </Text>
                  )}
                </View>
              ))}
            </>
          )}

          {/* Always show standard guidelines */}
          <Text style={[styles.rulesSubtitle, { color: colors.text, marginTop: rules.length > 0 ? spacing.lg : 0 }]}>
            Standard Chama Guidelines:
          </Text>

          <View style={[styles.ruleItem, { borderLeftColor: colors.secondary }]}>
            <View style={styles.ruleHeader}>
              <Text style={[styles.ruleNumber, { color: colors.secondary }]}>
                {rules.length + 1}
              </Text>
              <Text style={[styles.ruleTitle, { color: colors.text }]}>
                Regular Contributions
              </Text>
            </View>
            <Text style={[styles.ruleDescription, { color: colors.textSecondary }]}>
              Members must make their contributions on time as per the agreed schedule
            </Text>
          </View>

          <View style={[styles.ruleItem, { borderLeftColor: colors.secondary }]}>
            <View style={styles.ruleHeader}>
              <Text style={[styles.ruleNumber, { color: colors.secondary }]}>
                {rules.length + 2}
              </Text>
              <Text style={[styles.ruleTitle, { color: colors.text }]}>
                Meeting Attendance
              </Text>
            </View>
            <Text style={[styles.ruleDescription, { color: colors.textSecondary }]}>
              Members are expected to attend scheduled meetings or provide advance notice
            </Text>
          </View>

          <View style={[styles.ruleItem, { borderLeftColor: colors.secondary }]}>
            <View style={styles.ruleHeader}>
              <Text style={[styles.ruleNumber, { color: colors.secondary }]}>
                {rules.length + 3}
              </Text>
              <Text style={[styles.ruleTitle, { color: colors.text }]}>
                Respectful Communication
              </Text>
            </View>
            <Text style={[styles.ruleDescription, { color: colors.textSecondary }]}>
              All members should maintain respectful and professional communication
            </Text>
          </View>

          <View style={[styles.ruleItem, { borderLeftColor: colors.secondary }]}>
            <View style={styles.ruleHeader}>
              <Text style={[styles.ruleNumber, { color: colors.secondary }]}>
                {rules.length + 4}
              </Text>
              <Text style={[styles.ruleTitle, { color: colors.text }]}>
                Financial Transparency
              </Text>
            </View>
            <Text style={[styles.ruleDescription, { color: colors.textSecondary }]}>
              All financial transactions and decisions must be transparent and documented
            </Text>
          </View>

          <View style={[styles.ruleItem, { borderLeftColor: colors.secondary }]}>
            <View style={styles.ruleHeader}>
              <Text style={[styles.ruleNumber, { color: colors.secondary }]}>
                {rules.length + 5}
              </Text>
              <Text style={[styles.ruleTitle, { color: colors.text }]}>
                Confidentiality
              </Text>
            </View>
            <Text style={[styles.ruleDescription, { color: colors.textSecondary }]}>
              Members must maintain confidentiality of chama matters and member information
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  // const renderGroupChat = () => (
    // <Card style={styles.section}>
    //   <Text style={[styles.sectionTitle, { color: colors.text }]}>
    //     Group Communication
    //   </Text>

    //   <TouchableOpacity
    //     style={[styles.chatButton, { backgroundColor: colors.success + '20', borderColor: colors.success }]}
    //     onPress={async () => {
    //       if (!chama?.chat_room_id) {
    //         try {
    //           const response = await ApiService.createChatRoom({
    //             type: 'chama',
    //             chamaId: chamaId,
    //             name: `${chama?.name || 'Chama'} Group Chat`
    //           });

    //           if (response.success) {
    //             navigation.navigate('ChatRoom', {
    //               roomId: response.data.id,
    //               roomName: response.data.name || `${chama?.name || 'Chama'} Group Chat`,
    //               roomType: 'group'
    //             });
    //           } else {
    //             Alert.alert('Error', 'Failed to access group chat');
    //           }
    //         } catch (error) {
    //           console.error('Failed to create chama chat room:', error);
    //           Alert.alert('Error', 'Failed to access group chat');
    //         }
    //       } else {
    //         navigation.navigate('ChatRoom', {
    //           roomId: chama.chat_room_id,
    //           roomName: chama.name,
    //           roomType: 'group'
    //         });
    //       }
    //     }}
    //   >
    //     <Ionicons name="chatbubbles" size={24} color={colors.success} />
    //     <Text style={[styles.chatButtonText, { color: colors.success }]}>
    //       Open Group Chat
    //     </Text>
    //   </TouchableOpacity>
    // </Card>
  // );

  const renderMembershipActions = () => {
    if (!userMembership) {
      return (
        <Card style={styles.section}>
          <Button
            title="Join This Chama"
            onPress={handleJoinChama}
            disabled={chama?.current_members >= chama?.max_members}
            icon={<Ionicons name="person-add" size={20} color={colors.white} />}
          />
        </Card>
      );
    }

    return (
      <Card style={styles.section}>
        <View style={styles.membershipInfo}>
          <Text style={[styles.membershipTitle, { color: colors.text }]}>
            Your Membership
          </Text>
          <Text style={[styles.membershipRole, { color: colors.primary }]}>
            {userMembership.role?.toUpperCase()}
          </Text>
          <Text style={[styles.membershipDate, { color: colors.textSecondary }]}>
            Joined {new Date(userMembership.joined_at).toLocaleDateString()}
          </Text>
        </View>
        
        <View style={styles.membershipActions}>
          <Button
            title="Switch to Chama Dashboard"
            onPress={() => {
              console.log('🏘️ Switching to chama dashboard for:', chama?.name);
              switchToChamaDashboard(chama);
            }}
            style={styles.membershipButton}
          />
          
          <Button
            title="Leave Chama"
            variant="outline"
            onPress={handleLeaveChama}
            style={[styles.membershipButton, { borderColor: colors.error }]}
            textStyle={{ color: colors.error }}
          />
        </View>
      </Card>
    );
  };

  // Render enlarged avatar modal
  const renderAvatarModal = () => {
    if (!selectedAvatarMember) return null;

    const user = selectedAvatarMember?.user || {};
    const firstName = user?.first_name || selectedAvatarMember?.first_name || '';
    const lastName = user?.last_name || selectedAvatarMember?.last_name || '';
    const fullName = `${firstName} ${lastName}`.trim() || 'Unknown Member';
    const email = user?.email || selectedAvatarMember?.email;
    const avatarUrl = user?.avatar_url || user?.avatar || user?.profile_image || selectedAvatarMember?.avatar || selectedAvatarMember?.avatarUrl;
    const initials = (firstName?.[0] || '') + (lastName?.[0] || '');

    const screenWidth = Dimensions.get('window').width;
    const avatarSize = screenWidth * 0.75; // Increased from 60% to 75% of screen width

    let avatarSource = null;
    let isInitials = false;

    // Determine avatar source
    if (avatarUrl && avatarUrl.trim()) {
      let fullAvatarUrl;

      // Handle cached base64 image placeholder
      if (avatarUrl === 'avatar://cached-base64-image') {
        // For modal display, use the cached avatar state if available
        if (cachedAvatarData) {
          avatarSource = { uri: cachedAvatarData };
        } else {
          isInitials = true;
        }
      } else if (avatarUrl.startsWith('http') || avatarUrl.startsWith('data:')) {
        fullAvatarUrl = avatarUrl;
        avatarSource = { uri: fullAvatarUrl };
      } else {
        fullAvatarUrl = `${ApiService.baseURL}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`;
        avatarSource = { uri: fullAvatarUrl };
      }
    } else if (email && email.trim()) {
      const generatedAvatarUrl = getAvatarFromEmail(email, Math.round(avatarSize));
      avatarSource = { uri: generatedAvatarUrl };
    } else {
      isInitials = true;
    }

    return (
      <Modal
        visible={showAvatarModal}
        transparent={true}
        animationType="fade"
        onRequestClose={closeAvatarModal}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeAvatarModal}
        >
          <View style={styles.modalContent}>
            <Card style={styles.avatarCard}>
              {/* Close button positioned absolutely */}
              <TouchableOpacity onPress={closeAvatarModal} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.white} />
              </TouchableOpacity>

              {/* Frameless Avatar - touches top, left, and right edges */}
              <View style={styles.framelessAvatarContainer}>
                {isInitials ? (
                  <View style={[
                    styles.framelessAvatar,
                    {
                      backgroundColor: colors.primary,
                      width: '100%',
                      height: avatarSize * 1.2,
                    }
                  ]}>
                    <Text style={[
                      styles.enlargedAvatarText,
                      {
                        color: colors.white,
                        fontSize: avatarSize * 0.25,
                      }
                    ]}>
                      {initials || '?'}
                    </Text>
                  </View>
                ) : (
                  <Image
                    source={avatarSource}
                    style={[
                      styles.framelessAvatar,
                      {
                        width: '100%',
                        height: avatarSize * 1.2,
                      }
                    ]}
                    onError={(error) => {
                      console.log('Enlarged avatar load error:', error);
                    }}
                  />
                )}
              </View>

              {/* Name and Email in lower section */}
              <View style={styles.avatarInfoSection}>
                <Text style={[styles.avatarModalName, { color: colors.text }]}>
                  {fullName}
                </Text>
                {email && (
                  <Text style={[styles.avatarModalEmail, { color: colors.textSecondary }]}>
                    {email}
                  </Text>
                )}
              </View>
            </Card>
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading chama details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        key={chamaId} // Force re-render when chamaId changes
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderChamaHeader()}
        {renderStats()}
        {renderMembers()}
        {renderMeetings()}
        {renderTransactions()}
        {renderActivePolls()}
        {renderChamaRules()}
        {/* {renderGroupChat()} */}
        {renderMembershipActions()}
      </ScrollView>

      {/* Avatar Modal */}
      {renderAvatarModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  chamaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  chamaAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  chamaInfo: {
    flex: 1,
  },
  chamaName: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  chamaType: {
    fontSize: typography.fontSize.base,
    marginBottom: spacing.sm,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  chamaDescription: {
    fontSize: typography.fontSize.base,
    lineHeight: 24, // Fixed line height for better mobile readability
    marginBottom: spacing.md,
    textAlign: 'left',
    letterSpacing: 0.2,
    paddingHorizontal: spacing.xs, // Add slight horizontal padding
  },
  rulesSection: {
    marginTop: spacing.md,
  },
  rulesTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
  },
  rulesText: {
    fontSize: typography.fontSize.sm,
    lineHeight: typography.lineHeight.relaxed,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  statValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginTop: spacing.sm,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  actionCard: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  membershipInfo: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  membershipTitle: {
    fontSize: typography.fontSize.base,
    marginBottom: spacing.xs,
  },
  membershipRole: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  membershipDate: {
    fontSize: typography.fontSize.sm,
  },
  membershipActions: {
    gap: spacing.md,
  },
  membershipButton: {
    marginBottom: spacing.sm,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: typography.fontSize.base,
  },
  // New styles for comprehensive view
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  viewMoreText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  // Members styles
  membersList: {
    gap: spacing.sm,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
    overflow: 'hidden', // Ensures image stays within circular bounds
    backgroundColor: '#f0f0f0', // Light background for loading state
  },
  memberInitials: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  memberRole: {
    fontSize: typography.fontSize.sm,
  },
  memberStatus: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  memberStatusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textTransform: 'capitalize',
  },
  // Meetings styles
  meetingsList: {
    gap: spacing.sm,
  },
  meetingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  meetingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  meetingInfo: {
    flex: 1,
  },
  meetingTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  meetingDate: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  meetingType: {
    fontSize: typography.fontSize.xs,
  },
  meetingStatus: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  meetingStatusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textTransform: 'capitalize',
  },
  // Transactions styles
  transactionsList: {
    gap: spacing.sm,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  transactionDate: {
    fontSize: typography.fontSize.sm,
  },
  transactionAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
  },
  // Chama rules styles
  rulesList: {
    gap: spacing.md,
  },
  ruleItem: {
    paddingLeft: spacing.md,
    paddingRight: spacing.sm,
    paddingVertical: spacing.sm,
    borderLeftWidth: 3,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 8,
  },
  ruleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  ruleNumber: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginRight: spacing.sm,
    minWidth: 24,
  },
  ruleTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    flex: 1,
  },
  ruleDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
    marginLeft: 32,
  },
  rulePenalty: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginTop: spacing.xs,
    marginLeft: 32,
    fontStyle: 'italic',
  },
  defaultRules: {
    marginTop: spacing.sm,
  },
  defaultRulesTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.md,
  },
  rulesSubtitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
    marginTop: spacing.sm,
  },
  meetingDescription: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
  },
  // Polls styles
  pollsList: {
    gap: spacing.md,
  },
  pollItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    gap: spacing.sm,
  },
  pollIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.xs,
  },
  pollInfo: {
    flex: 1,
    gap: spacing.xs,
  },
  pollTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    lineHeight: 20,
  },
  pollDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: 18,
  },
  pollMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  pollDate: {
    fontSize: typography.fontSize.xs,
  },
  pollDeadline: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  pollStats: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: spacing.xs,
  },
  pollVotes: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  votedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  votedText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  pollActions: {
    alignItems: 'flex-end',
    gap: spacing.sm,
    marginTop: spacing.xs,
  },
  pollStatus: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  pollStatusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textTransform: 'capitalize',
  },
  voteButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    borderWidth: 1,
  },
  voteButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  // Chat button styles
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    gap: spacing.sm,
  },
  chatButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  // Avatar Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    justifyContent: 'center',
    alignItems: 'center',
    backdropFilter: 'blur(10px)', // iOS blur effect
  },
  modalContent: {
    width: '92%', // Increased from 85% to 92%
    maxWidth: 420, // Increased from 380 to 420
    marginHorizontal: spacing.sm, // Reduced margin
  },
  avatarCard: {
    padding: 0, // Remove all padding for frameless design
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
    overflow: 'hidden', // Ensure image touches edges
  },
  avatarModalName: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  closeButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: spacing.sm,
  },
  framelessAvatarContainer: {
    width: '100%',
    alignItems: 'center',
  },
  framelessAvatar: {
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: 20, // Only top corners rounded to match card
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0, // No bottom rounding for frameless effect
    borderBottomRightRadius: 0,
  },
  avatarInfoSection: {
    padding: spacing.md,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },

  enlargedAvatarText: {
    fontWeight: typography.fontWeight.bold,
  },
  avatarModalEmail: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  // Category Badge Styles
  categoryBadge: {
    alignSelf: 'flex-start', // Remove absolute positioning
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
    maxWidth: 80, // Limit width to prevent overlap
  },
  categoryBadgeText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
    marginLeft: spacing.xs,
    textTransform: 'uppercase',
  },
  // Description Container Styles
  descriptionContainer: {
    marginTop: spacing.lg,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  descriptionLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});

export default ChamaDetailsScreen;
