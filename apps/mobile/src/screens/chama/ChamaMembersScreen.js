import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  Modal,
  ScrollView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';

const ChamaMembersScreen = ({ route, navigation, onRouteChange }) => {
  const { chamaId } = route.params;
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMember, setSelectedMember] = useState(null);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showMemberDetails, setShowMemberDetails] = useState(false);
  const [userRole, setUserRole] = useState('member');
  const [lastUpdated, setLastUpdated] = useState(null);
  const [memberStats, setMemberStats] = useState({});
  const [userProfiles, setUserProfiles] = useState({}); // Cache for user profile data

  // Invitations tracking state
  const [activeTab, setActiveTab] = useState('members'); // 'members' or 'invitations'
  const [sentInvitations, setSentInvitations] = useState([]);
  const [invitationsLoading, setInvitationsLoading] = useState(false);

  // Real-time update interval
  const refreshIntervalRef = useRef(null);

  const roles = [
    { id: 'chairperson', name: 'Chairperson', icon: 'star', description: 'Full administrative access'},
    { id: 'treasurer', name: 'Treasurer', icon: 'wallet', description: 'Manages finances' },
    { id: 'secretary', name: 'Secretary', icon: 'document-text', description: 'Keeps records' },
    { id: 'assistant', name: 'Assistant', icon: 'person-add', description: 'Helps with operations' },
    { id: 'member', name: 'Member', icon: 'person', description: 'Regular member' },
  ];

  useEffect(() => {
    loadMembers();
    if (canManageMembers()) {
      loadSentInvitations();
    }

    // Set up real-time updates every 30 seconds
    refreshIntervalRef.current = setInterval(() => {
      loadMembers(true); // Silent refresh
      if (canManageMembers()) {
        loadSentInvitations(true);
      }
    }, 30000);

    // Cleanup interval on unmount
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  // Load invitations when switching to invitations tab
  useEffect(() => {
    if (activeTab === 'invitations' && canManageMembers()) {
      loadSentInvitations();
    }
  }, [activeTab]);

  const loadMembers = async (silent = false) => {
    try {
      if (!silent) setLoading(true);

      const response = await ApiService.getChamaMembers(chamaId);
      if (response.success) {
        const membersData = response.data || [];
        setMembers(membersData);
        setLastUpdated(new Date().toISOString());

        // Debug: Log member data structure
        console.log('🔍 Members data structure:', {
          totalMembers: membersData.length,
          firstMember: membersData[0],
          sampleMemberKeys: membersData[0] ? Object.keys(membersData[0]) : [],
          userKeys: membersData[0]?.user ? Object.keys(membersData[0].user) : []
        });

        // Extract member stats from response meta
        if (response.meta) {
          setMemberStats(response.meta);
        }

        // Find current user's role
        const currentUser = membersData.find(m => m.user_id === user?.id);
        const detectedRole = currentUser?.role || 'member';
        setUserRole(detectedRole);

        console.log('🔍 User role detection:', {
          currentUserId: user?.id,
          foundUser: !!currentUser,
          detectedRole: detectedRole,
          canManage: ['chairperson', 'secretary', 'treasurer'].includes(detectedRole),
          allMemberRoles: membersData.map(m => ({ id: m.user_id, role: m.role }))
        });

        console.log(`✅ Loaded ${membersData.length} members for chama ${chamaId}`);
      }
    } catch (error) {
      console.error('Failed to load members:', error);
      if (!silent) {
        Alert.alert('Error', 'Failed to load chama members. Please try again.');
      }
    } finally {
      if (!silent) setLoading(false);
    }
  };

  const loadSentInvitations = async (silent = false) => {
    if (!canManageMembers()) return;

    try {
      if (!silent) setInvitationsLoading(true);

      const response = await ApiService.getChamaSentInvitations(chamaId);
      if (response.success) {
        setSentInvitations(response.data || []);
        console.log(`Loaded ${response.data?.length || 0} sent invitations for chama ${chamaId}`);
      }
    } catch (error) {
      console.error('Failed to load sent invitations:', error);
      if (!silent) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to load invitations',
        });
      }
    } finally {
      if (!silent) setInvitationsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadMembers(),
      activeTab === 'invitations' ? loadSentInvitations() : Promise.resolve()
    ]);
    setRefreshing(false);
  };

  const canManageMembers = () => {
    return ['chairperson', 'secretary', 'treasurer'].includes(userRole);
  };

  const canManageRoles = () => {
    return userRole === 'chairperson';
  };

  const handleChangeRole = async (memberId, newRole) => {
    try {
      const response = await ApiService.updateMemberRole(chamaId, memberId, newRole);
      if (response.success) {
        Alert.alert('Success', 'Member role updated successfully');
        await loadMembers();
        setShowRoleModal(false);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update member role');
    }
  };

  const handleRemoveMember = (member) => {
    Alert.alert(
      'Remove Member',
      `Are you sure you want to remove ${member.user?.first_name} ${member.user?.last_name} from the chama?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => confirmRemoveMember(member.id) },
      ]
    );
  };

  const confirmRemoveMember = async (memberId) => {
    try {
      const response = await ApiService.removeChamaMember(chamaId, memberId);
      if (response.success) {
        Alert.alert('Success', 'Member removed successfully');
        await loadMembers();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to remove member');
    }
  };

  // Invitation management functions
  const handleCancelInvitation = async (invitationId) => {
    Alert.alert(
      'Cancel Invitation',
      'Are you sure you want to cancel this invitation?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await ApiService.cancelInvitation(invitationId);
              if (response.success) {
                Toast.show({
                  type: 'success',
                  text1: 'Invitation Cancelled',
                  text2: 'The invitation has been cancelled successfully',
                });
                await loadSentInvitations();
              } else {
                throw new Error(response.error || 'Failed to cancel invitation');
              }
            } catch (error) {
              Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to cancel invitation',
              });
            }
          }
        },
      ]
    );
  };

  const handleResendInvitation = async (invitationId) => {
    try {
      const response = await ApiService.resendInvitation(invitationId);
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Invitation Resent',
          text2: 'The invitation has been sent again',
        });
        await loadSentInvitations();
      } else {
        throw new Error(response.error || 'Failed to resend invitation');
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to resend invitation',
      });
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getFilteredMembers = () => {
    if (!searchQuery) return members;

    return members.filter(member =>
      `${member.user?.first_name} ${member.user?.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.role.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getRoleIcon = (role) => {
    const roleData = roles.find(r => r.id === role);
    return roleData?.icon || 'person';
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'chairperson': return colors.warning;
      case 'treasurer': return colors.warning;
      case 'secretary': return colors.warning;
      case 'assistant': return colors.secondary;
      default: return colors.textSecondary;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getInvitationStatus = (invitation) => {
    const now = new Date();
    const expiresAt = new Date(invitation.expires_at);

    if (invitation.status === 'accepted') return { status: 'accepted', color: colors.success };
    if (invitation.status === 'rejected') return { status: 'rejected', color: colors.error };
    if (invitation.status === 'cancelled') return { status: 'cancelled', color: colors.textSecondary };
    if (expiresAt < now) return { status: 'expired', color: colors.warning };
    return { status: 'pending', color: colors.primary };
  };

  const renderInvitationCard = ({ item }) => {
    const statusInfo = getInvitationStatus(item);
    const isExpired = statusInfo.status === 'expired';
    const isPending = statusInfo.status === 'pending';

    return (
      <Card style={[styles.invitationCard, { opacity: isExpired ? 0.7 : 1 }]}>
        <View style={styles.invitationHeader}>
          <View style={styles.invitationInfo}>
            <Text style={[styles.inviteeEmail, { color: colors.text }]}>
              {item.email}
            </Text>
            {item.phone_number && (
              <Text style={[styles.inviteePhone, { color: colors.textSecondary }]}>
                {item.phone_number}
              </Text>
            )}
            {item.role && (
              <View style={styles.invitationRole}>
                <Ionicons name="shield-checkmark" size={14} color={colors.primary} />
                <Text style={[styles.roleText, { color: colors.primary }]}>
                  {item.role_name || item.role}
                </Text>
              </View>
            )}
          </View>

          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.status.toUpperCase()}
            </Text>
          </View>
        </View>

        <View style={styles.invitationDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={16} color={colors.textSecondary} />
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Sent:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {formatDate(item.created_at)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Ionicons name="time" size={16} color={colors.textSecondary} />
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Expires:
            </Text>
            <Text style={[styles.detailValue, { color: isExpired ? colors.error : colors.text }]}>
              {formatDate(item.expires_at)}
            </Text>
          </View>

          {item.responded_at && (
            <View style={styles.detailRow}>
              <Ionicons name="checkmark-circle" size={16} color={statusInfo.color} />
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Responded:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {formatDate(item.responded_at)}
              </Text>
            </View>
          )}
        </View>

        {item.message && (
          <View style={styles.messageContainer}>
            <Text style={[styles.messageLabel, { color: colors.textSecondary }]}>
              Message:
            </Text>
            <Text style={[styles.messageText, { color: colors.text }]} numberOfLines={2}>
              {item.message}
            </Text>
          </View>
        )}

        {isPending && (
          <View style={styles.invitationActions}>
            <Button
              title="Resend"
              variant="outline"
              size="small"
              onPress={() => handleResendInvitation(item.id)}
              style={styles.actionButton}
              icon={<Ionicons name="refresh" size={16} color={colors.primary} />}
            />
            <Button
              title="Cancel"
              variant="outline"
              size="small"
              onPress={() => handleCancelInvitation(item.id)}
              style={[styles.actionButton, { borderColor: colors.error }]}
              textStyle={{ color: colors.error }}
              icon={<Ionicons name="close" size={16} color={colors.error} />}
            />
          </View>
        )}
      </Card>
    );
  };

  // Helper function to generate a consistent avatar URL from email
  const getAvatarFromEmail = (email, size = 50) => {
    if (!email) return null;

    // Use a simple avatar service that doesn't require MD5 hashing
    // DiceBear API provides consistent avatars based on seed (email)
    const seed = encodeURIComponent(email.toLowerCase().trim());
    return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&size=${size}&backgroundColor=random`;
  };

  // Helper function to render member avatar with real profile photo
  const renderMemberAvatar = (item) => {
    // Debug member data structure
    console.log('🔍 Member item data:', item);

    // Access data from nested user object (correct structure)
    const user = item?.user || {};
    const firstName = user?.first_name || item?.firstName || item?.first_name;
    const lastName = user?.last_name || item?.lastName || item?.last_name;
    const email = user?.email || item?.email;
    const memberId = item?.user_id || item?.userId;

    // Try multiple avatar sources from user object
    const avatarUrl = user?.avatar_url || user?.avatar || user?.profile_image || item?.avatar || item?.avatarUrl;

    // console.log('🖼️ Avatar data:', { firstName, lastName, memberId, email, avatarUrl });

    // Try to use provided avatar URL first
    if (avatarUrl) {
      let fullAvatarUrl;
      if (avatarUrl.startsWith('http') || avatarUrl.startsWith('data:')) {
        fullAvatarUrl = avatarUrl;
      } else {
        fullAvatarUrl = `${ApiService.baseURL}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`;
      }

      return (
        <Image
          source={{ uri: fullAvatarUrl }}
          style={styles.memberAvatar}
          onError={(error) => {
            console.log('Member avatar load error:', error);
            // Fallback to initials if image fails to load
          }}
        />
      );
    }

    // Try generated avatar as fallback if email is available
    if (email) {
      const generatedAvatarUrl = getAvatarFromEmail(email, 50);
      return (
        <Image
          source={{ uri: generatedAvatarUrl }}
          style={styles.memberAvatar}
          onError={(error) => {
            console.log('Generated avatar load error:', error);
            // Will fallback to initials in the parent component
          }}
        />
      );
    }

    // Final fallback to initials
    return (
      <View style={[styles.memberAvatar, { backgroundColor: colors.primary }]}>
        <Text style={[styles.memberInitials, { color: colors.white }]}>
          {firstName?.[0]?.toUpperCase() || 'U'}{lastName?.[0]?.toUpperCase() || ''}
        </Text>
      </View>
    );
  };

  // Helper function to get member name from various data structures
  const getMemberName = (item) => {
    // Access from nested user object (correct structure)
    const user = item?.user || {};
    const firstName = user?.first_name || item?.firstName || item?.first_name || '';
    const lastName = user?.last_name || item?.lastName || item?.last_name || '';
    const fullName = item?.fullName || `${firstName} ${lastName}`.trim();

    // Fallback to email or ID if no name
    if (!fullName) {
      return user?.email || item?.email || `Member ${(item?.user_id || item?.id || '').slice(-4)}`;
    }

    return fullName;
  };

  // Helper function to get member join date
  const getMemberJoinDate = (item) => {
    const joinDate = item?.joined_at || item?.joinedAt || item?.created_at || item?.createdAt;
    if (joinDate) {
      return new Date(joinDate).toLocaleDateString();
    }
    return 'Recently joined';
  };

  const renderMemberCard = ({ item }) => (
    <Card style={styles.memberCard}>
      <View style={styles.memberHeader}>
        <View style={styles.memberAvatarContainer}>
          {renderMemberAvatar(item)}
          {item.user?.is_online && (
            <View style={[styles.onlineIndicator, { backgroundColor: colors.success }]} />
          )}
        </View>

        <View style={styles.memberInfo}>
          <View style={styles.memberNameRow}>
            <Text style={[styles.memberName, { color: colors.text }]}>
              {getMemberName(item)}
            </Text>
            {item.phone_verified && (
              <Ionicons name="checkmark-circle" size={16} color={colors.success} />
            )}
          </View>

          <View style={styles.memberRole}>
            <Ionicons
              name={getRoleIcon(item.role)}
              size={16}
              color={getRoleColor(item.role)}
            />
            <Text style={[styles.roleText, { color: getRoleColor(item.role) }]}>
              {item.role.charAt(0).toUpperCase() + item.role.slice(1)}
            </Text>
            <View style={[styles.statusBadge, {
              backgroundColor: item.status === 'active' ? colors.success + '20' : colors.warning + '20'
            }]}>
              <Text style={[styles.statusText, {
                color: item.status === 'active' ? colors.success : colors.warning
              }]}>
                {item.status}
              </Text>
            </View>
          </View>

          <View style={styles.memberDetails}>
            <Text style={[styles.memberJoined, { color: colors.textTertiary }]}>
              {item.business_type || 'Member'} • {item.location || 'Location not set'}
            </Text>
            <Text style={[styles.memberJoined, { color: colors.textTertiary }]}>
              Joined {getMemberJoinDate(item)}
            </Text>
          </View>
        </View>

        {canManageMembers() && item.user_id !== user?.id && (
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => {
              setSelectedMember(item);
              setShowRoleModal(true);
            }}
          >
            <Ionicons name="ellipsis-vertical" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Enhanced Stats Section */}
      <View style={styles.memberStats}>
        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Total Contributions
          </Text>
          <Text style={[styles.statValue, { color: colors.success }]}>
            {formatCurrency(item.total_contributions || 0)}
          </Text>
        </View>

        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Attendance Rate
          </Text>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {item.attendance_rate?.toFixed(1) || 0}%
          </Text>
        </View>

        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Reputation
          </Text>
          <View style={styles.reputationContainer}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={[styles.statValue, { color: colors.text, marginLeft: 4 }]}>
              {item.reputation_score?.toFixed(1) || 0}
            </Text>
          </View>
        </View>
      </View>

      {/* Financial Summary */}
      <View style={styles.financialSummary}>
        <View style={styles.financialItem}>
          <Text style={[styles.financialLabel, { color: colors.textSecondary }]}>
            Savings Balance
          </Text>
          <Text style={[styles.financialValue, { color: colors.primary }]}>
            {formatCurrency(item.savings_balance || 0)}
          </Text>
        </View>

        {item.loan_balance > 0 && (
          <View style={styles.financialItem}>
            <Text style={[styles.financialLabel, { color: colors.textSecondary }]}>
              Loan Balance
            </Text>
            <Text style={[styles.financialValue, { color: colors.error }]}>
              {formatCurrency(item.loan_balance || 0)}
            </Text>
          </View>
        )}

        <View style={styles.financialItem}>
          <Text style={[styles.financialLabel, { color: colors.textSecondary }]}>
            Last Contribution
          </Text>
          <Text style={[styles.financialValue, { color: colors.text }]}>
            {item.last_contribution_date
              ? `${formatCurrency(item.last_contribution_amount || 0)} on ${new Date(item.last_contribution_date).toLocaleDateString()}`
              : 'None'
            }
          </Text>
        </View>
      </View>

      {/* Activity Summary */}
      {item.activity_summary && (
        <View style={styles.activitySummary}>
          <Text style={[styles.activityTitle, { color: colors.text }]}>
            Recent Activity
          </Text>
          <View style={styles.activityGrid}>
            <View style={styles.activityItem}>
              <Text style={[styles.activityValue, { color: colors.primary }]}>
                {item.activity_summary.meetings_attended}/{item.activity_summary.total_meetings}
              </Text>
              <Text style={[styles.activityLabel, { color: colors.textSecondary }]}>
                Meetings
              </Text>
            </View>
            <View style={styles.activityItem}>
              <Text style={[styles.activityValue, { color: colors.success }]}>
                {item.activity_summary.contributions_made}
              </Text>
              <Text style={[styles.activityLabel, { color: colors.textSecondary }]}>
                Contributions
              </Text>
            </View>
            <View style={styles.activityItem}>
              <Text style={[styles.activityValue, { color: colors.warning }]}>
                {item.activity_summary.loans_taken}
              </Text>
              <Text style={[styles.activityLabel, { color: colors.textSecondary }]}>
                Loans
              </Text>
            </View>
          </View>
        </View>
      )}

      <View style={styles.memberActions}>
        <Button
          title={item.user_id === user?.id ? "View My Profile" : "View Details"}
          variant={item.user_id === user?.id ? "primary" : "outline"}
          size="small"
          onPress={() => {
            navigation.navigate('ViewMember', {
              memberId: item.user_id,
              chamaId: chamaId,
              userRole: userRole,
            });
          }}
          style={styles.memberActionButton}
          icon={<Ionicons
            name={item.user_id === user?.id ? "person-circle" : "person"}
            size={16}
            color={item.user_id === user?.id ? colors.white : colors.primary}
          />}
        />

        {item.user_id !== user?.id && (
          <Button
            title="Chat"
            size="small"
            onPress={async () => {
              try {
                // Debug: Check the member data structure
                console.log('🔍 Member data for chat:', {
                  user_id: item.user_id,
                  id: item.id,
                  user: item.user,
                  fullItem: item
                });

                // Validate user ID
                const recipientId = item.user_id;
                if (!recipientId) {
                  console.error('❌ No user ID found for member:', item);
                  Alert.alert('Error', 'Cannot start chat: User ID not found');
                  return;
                }

                // Create or get existing private chat room
                console.log('🔄 Creating private chat with user ID:', recipientId);
                const response = await ApiService.createPrivateChat(recipientId);

                if (response.success) {
                  const roomName = item.user?.first_name && item.user?.last_name
                    ? `${item.user.first_name} ${item.user.last_name}`
                    : 'Chat';

                  navigation.navigate('ChatRoom', {
                    roomId: response.data.id,
                    roomName: roomName,
                    roomType: 'private'
                  });
                } else {
                  console.error('❌ Failed to create chat room:', response);
                  Alert.alert('Error', response.error || 'Failed to create chat room');
                }
              } catch (error) {
                console.error('❌ Failed to create private chat:', error);
                Alert.alert('Error', 'Failed to start chat: ' + error.message);
              }
            }}
            style={styles.memberActionButton}
            icon={<Ionicons name="chatbubble" size={16} color={colors.white} />}
          />
        )}
      </View>
    </Card>
  );

  const renderRoleModal = () => (
    <Modal
      visible={showRoleModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowRoleModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Manage Member
            </Text>
            <TouchableOpacity onPress={() => setShowRoleModal(false)}>
              <Ionicons name="close" size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {selectedMember && (
            <View style={styles.modalBody}>
              <Text style={[styles.memberNameModal, { color: colors.text }]}>
                {selectedMember.user?.first_name} {selectedMember.user?.last_name}
              </Text>

              {canManageRoles() && (
                <View style={styles.roleSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    Change Role
                  </Text>
                  {roles.map((role) => (
                    <TouchableOpacity
                      key={role.id}
                      style={[
                        styles.roleOption,
                        {
                          backgroundColor: selectedMember.role === role.id ? colors.primary + '20' : 'transparent',
                          borderColor: colors.border,
                        }
                      ]}
                      onPress={() => handleChangeRole(selectedMember.id, role.id)}
                    >
                      <Ionicons
                        name={role.icon}
                        size={20}
                        color={selectedMember.role === role.id ? colors.primary : colors.textSecondary}
                      />
                      <View style={styles.roleInfo}>
                        <Text style={[
                          styles.roleName,
                          { color: selectedMember.role === role.id ? colors.primary : colors.text }
                        ]}>
                          {role.name}
                        </Text>
                        <Text style={[styles.roleDescription, { color: colors.textSecondary }]}>
                          {role.description}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              <View style={styles.actionSection}>
                <Button
                  title="Remove from Chama"
                  variant="outline"
                  onPress={() => {
                    setShowRoleModal(false);
                    handleRemoveMember(selectedMember);
                  }}
                  style={[styles.removeButton, { borderColor: colors.error }]}
                  textStyle={{ color: colors.error }}
                />
              </View>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name={activeTab === 'members' ? "people-outline" : "mail-outline"}
        size={64}
        color={colors.textTertiary}
      />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {activeTab === 'members' ? 'No members found' : 'No invitations sent'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {activeTab === 'members'
          ? (searchQuery ? 'Try adjusting your search' : 'Invite people to join your chama')
          : 'Send invitations to grow your chama membership'
        }
      </Text>
    </View>
  );

  const renderTabNavigation = () => (
    <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'members' && { backgroundColor: colors.primary + '15', borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('members')}
      >
        <Ionicons
          name="people"
          size={20}
          color={activeTab === 'members' ? colors.primary : colors.textSecondary}
        />
        <Text style={[
          styles.tabText,
          { color: activeTab === 'members' ? colors.primary : colors.textSecondary }
        ]}>
          Members ({filteredMembers.length})
        </Text>
      </TouchableOpacity>

      {canManageMembers() && (
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'invitations' && { backgroundColor: colors.primary + '15', borderBottomColor: colors.primary }
          ]}
          onPress={() => setActiveTab('invitations')}
        >
          <Ionicons
            name="mail"
            size={20}
            color={activeTab === 'invitations' ? colors.primary : colors.textSecondary}
          />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'invitations' ? colors.primary : colors.textSecondary }
          ]}>
            Invitations ({sentInvitations.length})
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const filteredMembers = getFilteredMembers();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Input
          placeholder={activeTab === 'members' ? "Search members..." : "Search invitations..."}
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search"
          style={styles.searchInput}
        />

        {/* Real-time Stats Header */}
        <View style={styles.statsHeader}>
          <View style={styles.memberCount}>
            <Text style={[styles.countText, { color: colors.textSecondary }]}>
              {activeTab === 'members'
                ? `${filteredMembers.length} member${filteredMembers.length !== 1 ? 's' : ''}`
                : `${sentInvitations.length} invitation${sentInvitations.length !== 1 ? 's' : ''}`
              }
            </Text>
            {activeTab === 'members' && memberStats.total_members && (
              <Text style={[styles.statsText, { color: colors.textTertiary }]}>
                {memberStats.active_members} active • {memberStats.pending_members} pending
              </Text>
            )}
            {activeTab === 'invitations' && sentInvitations.length > 0 && (
              <Text style={[styles.statsText, { color: colors.textTertiary }]}>
                {sentInvitations.filter(inv => inv.status === 'pending').length} pending • {' '}
                {sentInvitations.filter(inv => inv.status === 'accepted').length} accepted • {' '}
                {sentInvitations.filter(inv => new Date(inv.expires_at) < new Date()).length} expired
              </Text>
            )}
          </View>

          {lastUpdated && (
            <View style={styles.lastUpdated}>
              <Ionicons name="time" size={12} color={colors.textTertiary} />
              <Text style={[styles.lastUpdatedText, { color: colors.textTertiary }]}>
                Updated {new Date(lastUpdated).toLocaleTimeString()}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Tab Navigation */}
      {renderTabNavigation()}

      {/* Content based on active tab */}
      {activeTab === 'members' ? (
        <FlatList
          data={filteredMembers}
          renderItem={renderMemberCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.membersList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          ListEmptyComponent={!loading && renderEmptyState()}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <FlatList
          data={sentInvitations.filter(inv =>
            !searchQuery ||
            inv.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
            inv.status.toLowerCase().includes(searchQuery.toLowerCase())
          )}
          renderItem={renderInvitationCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.membersList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          ListEmptyComponent={!invitationsLoading && renderEmptyState()}
          showsVerticalScrollIndicator={false}
        />
      )}

      {canManageMembers() && (
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: colors.primary }]}
          onPress={() => {
            if (onRouteChange) {
              // Use route change to stay within ChamaLayoutProvider
              onRouteChange('invite-members', 'InviteMembers', {
                userRole: userRole,
              });
            } else {
              // Fallback to direct navigation if not in ChamaLayoutProvider
              navigation.navigate('InviteMembers', {
                chamaId: chamaId,
                chamaName: route.params?.chamaName || 'Chama',
                userRole: userRole,
              });
            }
          }}
        >
          <Ionicons name="person-add" size={24} color={colors.white} />
        </TouchableOpacity>
      )}

      {renderRoleModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    ...shadows.sm,
  },
  searchInput: {
    marginBottom: spacing.sm,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  memberCount: {
    alignItems: 'flex-start',
  },
  countText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  statsText: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
  },
  lastUpdated: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  lastUpdatedText: {
    fontSize: typography.fontSize.xs,
  },
  membersList: {
    padding: spacing.md,
  },
  memberCard: {
    marginBottom: spacing.md,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  memberAvatarContainer: {
    position: 'relative',
    marginRight: spacing.md,
  },
  memberAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden', // Ensures image stays within circular bounds
    backgroundColor: '#f0f0f0', // Light background for loading state
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: 'white',
  },
  memberInitials: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  memberInfo: {
    flex: 1,
  },
  memberNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: spacing.xs,
  },
  memberName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  memberDetails: {
    gap: spacing.xs,
  },
  memberRole: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  roleText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    marginLeft: spacing.sm,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textTransform: 'capitalize',
  },
  memberJoined: {
    fontSize: typography.fontSize.sm,
  },
  reputationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  financialSummary: {
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    gap: spacing.sm,
  },
  financialItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  financialLabel: {
    fontSize: typography.fontSize.sm,
    flex: 1,
  },
  financialValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'right',
  },
  activitySummary: {
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  activityTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  activityGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  activityItem: {
    alignItems: 'center',
  },
  activityValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  activityLabel: {
    fontSize: typography.fontSize.xs,
    textAlign: 'center',
  },
  menuButton: {
    padding: spacing.sm,
  },
  memberStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  statValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  memberActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  memberActionButton: {
    flex: 1,
  },
  fullWidthButton: {
    flex: 0,
    width: '100%',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: spacing.lg,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  modalBody: {
    gap: spacing.lg,
  },
  memberNameModal: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
  roleSection: {
    gap: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
  },
  roleInfo: {
    marginLeft: spacing.md,
    flex: 1,
  },
  roleName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  roleDescription: {
    fontSize: typography.fontSize.sm,
  },
  actionSection: {
    gap: spacing.md,
  },
  removeButton: {
    marginTop: spacing.md,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
  },
  // Tab navigation styles
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  // Invitation card styles
  invitationCard: {
    marginBottom: spacing.md,
  },
  invitationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  invitationInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  inviteeEmail: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  inviteePhone: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  invitationRole: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  invitationDetails: {
    gap: spacing.sm,
    marginBottom: spacing.md,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  detailLabel: {
    fontSize: typography.fontSize.sm,
    minWidth: 80,
  },
  detailValue: {
    fontSize: typography.fontSize.sm,
    flex: 1,
  },
  messageContainer: {
    marginBottom: spacing.md,
    padding: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: borderRadius.md,
  },
  messageLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  messageText: {
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
  },
  invitationActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
});

export default ChamaMembersScreen;
