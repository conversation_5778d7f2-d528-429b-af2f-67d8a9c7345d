import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { spacing, typography, borderRadius } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import ApiService from '../../services/api';

// 🏦 MODERN SHARES & DIVIDENDS MANAGEMENT SCREEN
// Comprehensive share trading and dividend management platform
// Built with excellent mobile UX and integration with Account Management

const SharesDividendsScreen = ({ route, navigation }) => {
  const { chamaId } = route.params;
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  // 📱 CORE STATE
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('portfolio'); // 'portfolio', 'market', 'dividends', 'history'

  // 📊 DATA STATE
  const [userShares, setUserShares] = useState(null); // User's personal share portfolio
  const [availableShares, setAvailableShares] = useState([]); // Shares available for purchase
  const [dividends, setDividends] = useState([]);
  const [shareHistory, setShareHistory] = useState([]);
  const [sharesSummary, setSharesSummary] = useState(null);
  const [marketData, setMarketData] = useState(null);

  // 🔐 USER & PERMISSIONS
  const [userRole, setUserRole] = useState('member');

  // 🎭 MODAL STATE
  const [showBuyModal, setShowBuyModal] = useState(false);
  const [showSellModal, setShowSellModal] = useState(false);
  const [selectedShare, setSelectedShare] = useState(null);

  // 🎨 ANIMATION REFS
  const slideAnim = useRef(new Animated.Value(0)).current;

  // 💰 BUY SHARES FORM
  const [buyForm, setBuyForm] = useState({
    shareType: 'ordinary',
    quantity: '',
    pricePerShare: '',
    totalAmount: '',
    paymentMethod: 'mobile_money',
    notes: '',
  });

  // 💸 SELL SHARES FORM
  const [sellForm, setSellForm] = useState({
    shareType: 'ordinary',
    quantity: '',
    pricePerShare: '',
    totalAmount: '',
    reason: '',
    notes: '',
  });

  // Removed unused dividend form state

  useEffect(() => {
    loadData();
  }, [chamaId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadUserShares(),
        loadAvailableShares(),
        loadDividends(),
        loadShareHistory(),
        loadSharesSummary(),
        loadMarketData(),
        loadUserRole(),
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load shares and dividends data');
    } finally {
      setLoading(false);
    }
  };

  // 👤 LOAD USER'S PERSONAL SHARE PORTFOLIO
  const loadUserShares = async () => {
    try {
      const response = await ApiService.getUserShares(chamaId, user.id);
      if (response.success) {
        setUserShares(response.data || {
          totalShares: 0,
          totalValue: 0,
          shareTypes: [],
          averagePrice: 0,
          totalDividendsReceived: 0,
          portfolioPerformance: 0,
        });
      } else {
        console.error('Failed to load user shares:', response.error);
        setUserShares(null);
      }
    } catch (error) {
      console.error('Error loading user shares:', error);
      setUserShares(null);
    }
  };

  // 🏪 LOAD AVAILABLE SHARES FOR PURCHASE
  const loadAvailableShares = async () => {
    try {
      const response = await ApiService.getAvailableShares(chamaId);
      if (response.success) {
        setAvailableShares(response.data || []);
      } else {
        console.error('Failed to load available shares:', response.error);
        setAvailableShares([]);
      }
    } catch (error) {
      console.error('Error loading available shares:', error);
      setAvailableShares([]);
    }
  };

  // 📈 LOAD SHARE TRANSACTION HISTORY
  const loadShareHistory = async () => {
    try {
      const response = await ApiService.getShareHistory(chamaId, user.id);
      if (response.success) {
        setShareHistory(response.data || []);
      } else {
        console.error('Failed to load share history:', response.error);
        setShareHistory([]);
      }
    } catch (error) {
      console.error('Error loading share history:', error);
      setShareHistory([]);
    }
  };

  // 📊 LOAD MARKET DATA
  const loadMarketData = async () => {
    try {
      const response = await ApiService.getShareMarketData(chamaId);
      if (response.success) {
        setMarketData(response.data || {
          currentPrice: 0,
          priceChange: 0,
          priceChangePercent: 0,
          volume: 0,
          marketCap: 0,
          lastUpdated: new Date(),
        });
      } else {
        console.error('Failed to load market data:', response.error);
        setMarketData(null);
      }
    } catch (error) {
      console.error('Error loading market data:', error);
      setMarketData(null);
    }
  };

  const loadDividends = async () => {
    try {
      const response = await ApiService.getChamaDividendDeclarations(chamaId);
      if (response.success) {
        setDividends(response.data || []);
      } else {
        console.error('Failed to load dividends:', response.error);
        // Set empty array for graceful handling
        setDividends([]);
      }
    } catch (error) {
      console.error('Error loading dividends:', error);
      // Set empty array for graceful handling
      setDividends([]);
    }
  };

  const loadSharesSummary = async () => {
    try {
      const response = await ApiService.getChamaSharesSummary(chamaId);
      if (response.success) {
        setSharesSummary({
          totalShares: response.totalShares || 0,
          totalValue: response.totalValue || 0,
          totalMembers: response.totalMembers || 0,
          averageSharesPerMember: response.totalMembers > 0 ? (response.totalShares / response.totalMembers) : 0,
        });
      } else {
        console.error('Failed to load shares summary:', response.error);
      }
    } catch (error) {
      console.error('Error loading shares summary:', error);
    }
  };

  const loadUserRole = async () => {
    try {
      const response = await ApiService.getMemberRole(chamaId, user.id);
      if (response.success) {
        setUserRole(response.data?.role || 'member');
      } else {
        setUserRole('member'); // Default to member if role fetch fails
      }
    } catch (error) {
      console.error('Error loading user role:', error);
      setUserRole('member'); // Default to member on error
    }
  };

  // 💰 BUY SHARES FUNCTIONALITY
  const handleBuyShares = async () => {
    if (!buyForm.quantity || !buyForm.pricePerShare) {
      Alert.alert('Validation Error', 'Please fill in all required fields.');
      return;
    }

    const quantity = parseInt(buyForm.quantity);
    const pricePerShare = parseFloat(buyForm.pricePerShare);
    const totalAmount = quantity * pricePerShare;

    Alert.alert(
      'Confirm Purchase',
      `You are about to buy ${quantity} shares at KES ${formatCurrency(pricePerShare)} each.\n\nTotal Amount: KES ${formatCurrency(totalAmount)}\n\nProceed with purchase?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Buy Shares',
          onPress: async () => {
            try {
              const purchaseData = {
                shareType: buyForm.shareType,
                quantity,
                pricePerShare,
                totalAmount,
                paymentMethod: buyForm.paymentMethod,
                notes: buyForm.notes,
                purchaseDate: new Date(),
              };

              const response = await ApiService.buyShares(chamaId, user.id, purchaseData);

              if (response.success) {
                Alert.alert('Success', 'Shares purchased successfully! Your portfolio has been updated.');
                setShowBuyModal(false);
                resetBuyForm();
                await loadData(); // Refresh all data
              } else {
                Alert.alert('Error', response.error || 'Failed to purchase shares');
              }
            } catch (error) {
              console.error('Error buying shares:', error);
              Alert.alert('Error', 'Failed to process share purchase');
            }
          }
        }
      ]
    );
  };

  // 💸 SELL SHARES FUNCTIONALITY
  const handleSellShares = async () => {
    if (!sellForm.quantity || !sellForm.pricePerShare) {
      Alert.alert('Validation Error', 'Please fill in all required fields.');
      return;
    }

    const quantity = parseInt(sellForm.quantity);
    const pricePerShare = parseFloat(sellForm.pricePerShare);
    const totalAmount = quantity * pricePerShare;

    // Check if user has enough shares
    if (userShares && quantity > userShares.totalShares) {
      Alert.alert('Insufficient Shares', `You only have ${userShares.totalShares} shares available for sale.`);
      return;
    }

    Alert.alert(
      'Confirm Sale',
      `You are about to sell ${quantity} shares at KES ${formatCurrency(pricePerShare)} each.\n\nTotal Amount: KES ${formatCurrency(totalAmount)}\n\nProceed with sale?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sell Shares',
          onPress: async () => {
            try {
              const saleData = {
                shareType: sellForm.shareType,
                quantity,
                pricePerShare,
                totalAmount,
                reason: sellForm.reason,
                notes: sellForm.notes,
                saleDate: new Date(),
              };

              const response = await ApiService.sellShares(chamaId, user.id, saleData);

              if (response.success) {
                Alert.alert('Success', 'Shares sold successfully! Your portfolio has been updated.');
                setShowSellModal(false);
                resetSellForm();
                await loadData(); // Refresh all data
              } else {
                Alert.alert('Error', response.error || 'Failed to sell shares');
              }
            } catch (error) {
              console.error('Error selling shares:', error);
              Alert.alert('Error', 'Failed to process share sale');
            }
          }
        }
      ]
    );
  };

  // 🔄 FORM RESET FUNCTIONS
  const resetBuyForm = () => {
    setBuyForm({
      shareType: 'ordinary',
      quantity: '',
      pricePerShare: '',
      totalAmount: '',
      paymentMethod: 'mobile_money',
      notes: '',
    });
  };

  const resetSellForm = () => {
    setSellForm({
      shareType: 'ordinary',
      quantity: '',
      pricePerShare: '',
      totalAmount: '',
      reason: '',
      notes: '',
    });
  };

  // 🧮 CALCULATION HELPERS
  const calculateBuyTotal = () => {
    const quantity = parseInt(buyForm.quantity) || 0;
    const price = parseFloat(buyForm.pricePerShare) || 0;
    return quantity * price;
  };

  const calculateSellTotal = () => {
    const quantity = parseInt(sellForm.quantity) || 0;
    const price = parseFloat(sellForm.pricePerShare) || 0;
    return quantity * price;
  };

  // 🔐 PERMISSION CHECKS
  const canDeclareDividends = () => {
    return ['chairperson', 'treasurer', 'secretary'].includes(userRole.toLowerCase());
  };

  // 🎨 ANIMATION HELPERS
  const animateTabChange = (toValue) => {
    Animated.timing(slideAnim, {
      toValue,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // 🎨 UI RENDERING COMPONENTS
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      {/* Top Row - Back Button, Title, Portfolio Value */}
      <View style={styles.headerTopRow}>
        <View style={styles.headerActions}>
          {userShares && (
            <View style={[styles.portfolioValue, { backgroundColor: colors.primary + '20' }]}>
              <Text style={[styles.portfolioValueLabel, { color: colors.primary }]}>
                Portfolio
              </Text>
              <Text style={[styles.portfolioValueAmount, { color: colors.primary }]}>
                KES {formatCurrency(userShares.totalValue)}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Bottom Row - Subtitle and Tab Navigation */}
      <View style={styles.headerBottomRow}>
        <View style={styles.headerTabs}>
          {[
            { key: 'portfolio', label: 'Portfolio', icon: 'pie-chart' },
            { key: 'market', label: 'Market', icon: 'trending-up' },
            { key: 'dividends', label: 'Dividends', icon: 'cash' },
            { key: 'history', label: 'History', icon: 'time' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.headerTab,
                activeTab === tab.key && {
                  backgroundColor: colors.primary + '15',
                  borderBottomWidth: 2,
                  borderBottomColor: colors.primary
                }
              ]}
              onPress={() => {
                setActiveTab(tab.key);
                Animated.timing(slideAnim, {
                  toValue: 0,
                  duration: 300,
                  useNativeDriver: true,
                }).start();
              }}
            >
              <Ionicons
                name={tab.icon}
                size={14}
                color={activeTab === tab.key ? colors.primary : colors.textSecondary}
              />
              <Text
                style={[
                  styles.headerTabText,
                  { color: activeTab === tab.key ? colors.primary : colors.textSecondary }
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  // Tab navigation now integrated into header - removed standalone function

  const renderPortfolioView = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Portfolio Summary Card */}
      <Card style={[styles.portfolioCard, { backgroundColor: colors.surface }]}>
        <View style={styles.portfolioHeader}>
          <View style={styles.portfolioTitleRow}>
            <Ionicons name="pie-chart" size={24} color={colors.primary} />
            <Text style={[styles.portfolioTitle, { color: colors.text }]}>
              My Portfolio
            </Text>
            <View style={[styles.performanceBadge, {
              backgroundColor: userShares?.portfolioPerformance >= 0 ? colors.success + '20' : colors.error + '20'
            }]}>
              <Ionicons
                name={userShares?.portfolioPerformance >= 0 ? 'trending-up' : 'trending-down'}
                size={14}
                color={userShares?.portfolioPerformance >= 0 ? colors.success : colors.error}
              />
              <Text style={[styles.performanceText, {
                color: userShares?.portfolioPerformance >= 0 ? colors.success : colors.error
              }]}>
                {userShares?.portfolioPerformance >= 0 ? '+' : ''}{userShares?.portfolioPerformance?.toFixed(2)}%
              </Text>
            </View>
          </View>
        </View>

        {userShares ? (
          <View style={styles.portfolioStats}>
            <View style={styles.statRow}>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Total Shares
                </Text>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {userShares.totalShares.toLocaleString()}
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Total Value
                </Text>
                <Text style={[styles.statValue, { color: colors.primary }]}>
                  KES {formatCurrency(userShares.totalValue)}
                </Text>
              </View>
            </View>

            <View style={styles.statRow}>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Average Price
                </Text>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  KES {formatCurrency(userShares.averagePrice)}
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Dividends Received
                </Text>
                <Text style={[styles.statValue, { color: colors.success }]}>
                  KES {formatCurrency(userShares.totalDividendsReceived)}
                </Text>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.emptyPortfolio}>
            <Ionicons name="pie-chart-outline" size={48} color={colors.textTertiary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No Shares Yet
            </Text>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              Start building your portfolio by purchasing shares
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.portfolioActions}>
          <Button
            title="Buy Shares"
            onPress={() => setShowBuyModal(true)}
            style={[styles.actionButton, { backgroundColor: colors.success }]}
            icon={<Ionicons name="add" size={20} color={colors.white} />}
          />
          {userShares && userShares.totalShares > 0 && (
            <Button
              title="Sell Shares"
              onPress={() => setShowSellModal(true)}
              style={[styles.actionButton, { backgroundColor: colors.error }]}
              icon={<Ionicons name="remove" size={20} color={colors.white} />}
            />
          )}
        </View>
      </Card>

      {/* Share Types Breakdown */}
      {userShares && userShares.shareTypes && userShares.shareTypes.length > 0 && (
        <Card style={[styles.shareTypesCard, { backgroundColor: colors.surface }]}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Share Types Breakdown
            </Text>
          </View>

          {userShares.shareTypes.map((shareType, index) => (
            <View key={index} style={styles.shareTypeItem}>
              <View style={styles.shareTypeInfo}>
                <Text style={[styles.shareTypeName, { color: colors.text }]}>
                  {shareType.type} Shares
                </Text>
                <Text style={[styles.shareTypeCount, { color: colors.textSecondary }]}>
                  {shareType.quantity} shares
                </Text>
              </View>
              <View style={styles.shareTypeValue}>
                <Text style={[styles.shareTypeAmount, { color: colors.primary }]}>
                  KES {formatCurrency(shareType.value)}
                </Text>
                <Text style={[styles.shareTypePrice, { color: colors.textSecondary }]}>
                  @ KES {formatCurrency(shareType.averagePrice)}
                </Text>
              </View>
            </View>
          ))}
        </Card>
      )}
    </ScrollView>
  );

  const renderMarketView = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Market Overview Card */}
      {marketData && (
        <Card style={[styles.marketCard, { backgroundColor: colors.surface }]}>
          <View style={styles.marketHeader}>
            <View style={styles.marketTitleRow}>
              <Ionicons name="trending-up" size={24} color={colors.info} />
              <Text style={[styles.marketTitle, { color: colors.text }]}>
                Market Overview
              </Text>
              <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
                Updated {formatDate(marketData.lastUpdated)}
              </Text>
            </View>
          </View>

          <View style={styles.marketStats}>
            <View style={styles.priceSection}>
              <Text style={[styles.currentPrice, { color: colors.text }]}>
                KES {formatCurrency(marketData.currentPrice)}
              </Text>
              <View style={[styles.priceChange, {
                backgroundColor: marketData.priceChange >= 0 ? colors.success + '20' : colors.error + '20'
              }]}>
                <Ionicons
                  name={marketData.priceChange >= 0 ? 'arrow-up' : 'arrow-down'}
                  size={14}
                  color={marketData.priceChange >= 0 ? colors.success : colors.error}
                />
                <Text style={[styles.priceChangeText, {
                  color: marketData.priceChange >= 0 ? colors.success : colors.error
                }]}>
                  {marketData.priceChange >= 0 ? '+' : ''}{formatCurrency(marketData.priceChange)} ({marketData.priceChangePercent?.toFixed(2)}%)
                </Text>
              </View>
            </View>

            <View style={styles.marketMetrics}>
              <View style={styles.metricItem}>
                <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
                  Volume
                </Text>
                <Text style={[styles.metricValue, { color: colors.text }]}>
                  {marketData.volume?.toLocaleString()}
                </Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
                  Market Cap
                </Text>
                <Text style={[styles.metricValue, { color: colors.text }]}>
                  KES {formatCurrency(marketData.marketCap)}
                </Text>
              </View>
            </View>
          </View>
        </Card>
      )}

      {/* Available Shares for Purchase */}
      <Card style={[styles.availableSharesCard, { backgroundColor: colors.surface }]}>
        <View style={styles.cardHeader}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Available Shares
          </Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={loadAvailableShares}
          >
            <Ionicons name="refresh" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {availableShares.length > 0 ? (
          <FlatList
            data={availableShares}
            keyExtractor={(item) => item.id}
            renderItem={renderAvailableShareItem}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        ) : (
          <View style={styles.emptyShares}>
            <Ionicons name="storefront-outline" size={48} color={colors.textTertiary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No Shares Available
            </Text>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              Check back later for new share offerings
            </Text>
          </View>
        )}
      </Card>
    </ScrollView>
  );

  const renderAvailableShareItem = ({ item }) => (
    <View style={styles.shareItem}>
      <View style={styles.shareItemHeader}>
        <View style={styles.shareItemInfo}>
          <Text style={[styles.shareItemTitle, { color: colors.text }]}>
            {item.shareType} Shares
          </Text>
          <Text style={[styles.shareItemDescription, { color: colors.textSecondary }]}>
            {item.description}
          </Text>
        </View>
        <View style={styles.shareItemPrice}>
          <Text style={[styles.sharePrice, { color: colors.primary }]}>
            KES {formatCurrency(item.pricePerShare)}
          </Text>
          <Text style={[styles.sharePriceLabel, { color: colors.textSecondary }]}>
            per share
          </Text>
        </View>
      </View>

      <View style={styles.shareItemDetails}>
        <View style={styles.shareDetail}>
          <Text style={[styles.shareDetailLabel, { color: colors.textSecondary }]}>
            Available
          </Text>
          <Text style={[styles.shareDetailValue, { color: colors.text }]}>
            {item.availableQuantity?.toLocaleString()} shares
          </Text>
        </View>
        <View style={styles.shareDetail}>
          <Text style={[styles.shareDetailLabel, { color: colors.textSecondary }]}>
            Min Purchase
          </Text>
          <Text style={[styles.shareDetailValue, { color: colors.text }]}>
            {item.minimumPurchase} shares
          </Text>
        </View>
      </View>

      <Button
        title="Buy Shares"
        onPress={() => {
          setSelectedShare(item);
          setBuyForm(prev => ({
            ...prev,
            shareType: item.shareType,
            pricePerShare: item.pricePerShare.toString(),
          }));
          setShowBuyModal(true);
        }}
        style={[styles.buyButton, { backgroundColor: colors.success }]}
        size="small"
      />
    </View>
  );

  const renderDividendsView = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Dividend Summary Card */}
      <Card style={[styles.dividendSummaryCard, { backgroundColor: colors.surface }]}>
        <View style={styles.cardHeader}>
          <View style={styles.cardTitleRow}>
            <Ionicons name="cash" size={24} color={colors.success} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Dividend Summary
            </Text>
            {canDeclareDividends() && (
              <TouchableOpacity
                style={[styles.declareButton, { backgroundColor: colors.primary + '20' }]}
                onPress={() => Alert.alert('Feature Coming Soon', 'Dividend declaration will be available in the next update.')}
              >
                <Ionicons name="add" size={16} color={colors.primary} />
                <Text style={[styles.declareButtonText, { color: colors.primary }]}>
                  Declare
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {userShares && (
          <View style={styles.dividendStats}>
            <View style={styles.statRow}>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Total Received
                </Text>
                <Text style={[styles.statValue, { color: colors.success }]}>
                  KES {formatCurrency(userShares.totalDividendsReceived)}
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Expected Next
                </Text>
                <Text style={[styles.statValue, { color: colors.info }]}>
                  KES {formatCurrency(userShares.totalShares * (sharesSummary?.lastDividendPerShare || 0))}
                </Text>
              </View>
            </View>
          </View>
        )}
      </Card>

      {/* Dividend History */}
      <Card style={[styles.dividendHistoryCard, { backgroundColor: colors.surface }]}>
        <View style={styles.cardHeader}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Dividend History
          </Text>
        </View>

        {dividends.length > 0 ? (
          <FlatList
            data={dividends}
            keyExtractor={(item) => item.id}
            renderItem={renderDividendItem}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        ) : (
          <View style={styles.emptyDividends}>
            <Ionicons name="cash-outline" size={48} color={colors.textTertiary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No Dividends Yet
            </Text>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              Dividend payments will appear here when declared
            </Text>
          </View>
        )}
      </Card>
    </ScrollView>
  );

  const renderDividendItem = ({ item }) => (
    <View style={styles.dividendItem}>
      <View style={styles.dividendItemHeader}>
        <View style={styles.dividendItemInfo}>
          <Text style={[styles.dividendItemTitle, { color: colors.text }]}>
            {item.description || 'Dividend Payment'}
          </Text>
          <Text style={[styles.dividendItemDate, { color: colors.textSecondary }]}>
            Declared: {formatDate(item.declarationDate)}
          </Text>
        </View>
        <View style={styles.dividendItemAmount}>
          <Text style={[styles.dividendAmount, { color: colors.success }]}>
            KES {formatCurrency(item.amountReceived || item.dividendPerShare * (userShares?.totalShares || 0))}
          </Text>
          <Text style={[styles.dividendPerShare, { color: colors.textSecondary }]}>
            KES {formatCurrency(item.dividendPerShare)} per share
          </Text>
        </View>
      </View>

      <View style={styles.dividendItemDetails}>
        <View style={styles.dividendDetail}>
          <Text style={[styles.dividendDetailLabel, { color: colors.textSecondary }]}>
            Payment Date
          </Text>
          <Text style={[styles.dividendDetailValue, { color: colors.text }]}>
            {formatDate(item.paymentDate)}
          </Text>
        </View>
        <View style={styles.dividendDetail}>
          <Text style={[styles.dividendDetailLabel, { color: colors.textSecondary }]}>
            Status
          </Text>
          <View style={[styles.statusBadge, {
            backgroundColor: getStatusColor(item.status) + '20'
          }]}>
            <Text style={[styles.statusText, {
              color: getStatusColor(item.status)
            }]}>
              {item.status?.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid': return colors.success;
      case 'pending': return colors.warning;
      case 'processing': return colors.info;
      case 'cancelled': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const renderHistoryView = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Card style={[styles.historyCard, { backgroundColor: colors.surface }]}>
        <View style={styles.cardHeader}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Transaction History
          </Text>
        </View>

        {shareHistory.length > 0 ? (
          <FlatList
            data={shareHistory}
            keyExtractor={(item) => item.id}
            renderItem={renderHistoryItem}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        ) : (
          <View style={styles.emptyHistory}>
            <Ionicons name="time-outline" size={48} color={colors.textTertiary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No Transaction History
            </Text>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              Your share transactions will appear here
            </Text>
          </View>
        )}
      </Card>
    </ScrollView>
  );

  const renderHistoryItem = ({ item }) => (
    <View style={styles.historyItem}>
      <View style={styles.historyItemHeader}>
        <View style={[styles.transactionTypeIcon, {
          backgroundColor: item.type === 'buy' ? colors.success + '20' : colors.error + '20'
        }]}>
          <Ionicons
            name={item.type === 'buy' ? 'add' : 'remove'}
            size={16}
            color={item.type === 'buy' ? colors.success : colors.error}
          />
        </View>
        <View style={styles.historyItemInfo}>
          <Text style={[styles.historyItemTitle, { color: colors.text }]}>
            {item.type === 'buy' ? 'Purchased' : 'Sold'} {item.quantity} shares
          </Text>
          <Text style={[styles.historyItemDate, { color: colors.textSecondary }]}>
            {formatDate(item.transactionDate)}
          </Text>
        </View>
        <View style={styles.historyItemAmount}>
          <Text style={[styles.historyAmount, {
            color: item.type === 'buy' ? colors.error : colors.success
          }]}>
            {item.type === 'buy' ? '-' : '+'}KES {formatCurrency(item.totalAmount)}
          </Text>
          <Text style={[styles.historyPricePerShare, { color: colors.textSecondary }]}>
            @ KES {formatCurrency(item.pricePerShare)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderBuyModal = () => (
    <Modal
      visible={showBuyModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={[styles.modalHeader, { backgroundColor: colors.surface }]}>
          <TouchableOpacity
            onPress={() => {
              setShowBuyModal(false);
              resetBuyForm();
              setSelectedShare(null);
            }}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Buy Shares
          </Text>
          <View style={styles.modalCloseButton} />
        </View>

        <ScrollView style={styles.modalContent} contentContainerStyle={styles.modalContentContainer}>
          {selectedShare && (
            <Card style={[styles.selectedShareCard, { backgroundColor: colors.surface }]}>
              <Text style={[styles.selectedShareTitle, { color: colors.text }]}>
                {selectedShare.shareType} Shares
              </Text>
              <Text style={[styles.selectedSharePrice, { color: colors.primary }]}>
                KES {formatCurrency(selectedShare.pricePerShare)} per share
              </Text>
              <Text style={[styles.selectedShareAvailable, { color: colors.textSecondary }]}>
                {selectedShare.availableQuantity?.toLocaleString()} shares available
              </Text>
            </Card>
          )}

          <Card style={[styles.buyFormCard, { backgroundColor: colors.surface }]}>
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Quantity *
              </Text>
              <TextInput
                style={[styles.formInput, {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }]}
                value={buyForm.quantity}
                onChangeText={(text) => {
                  setBuyForm(prev => ({ ...prev, quantity: text }));
                }}
                placeholder="Enter number of shares"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
              {selectedShare && buyForm.quantity && (
                <Text style={[styles.formHint, { color: colors.textSecondary }]}>
                  Min: {selectedShare.minimumPurchase} shares
                </Text>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Price per Share *
              </Text>
              <TextInput
                style={[styles.formInput, {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }]}
                value={buyForm.pricePerShare}
                onChangeText={(text) => setBuyForm(prev => ({ ...prev, pricePerShare: text }))}
                placeholder="Enter price per share"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
                editable={!selectedShare} // Disable if share is pre-selected
              />
            </View>

            {buyForm.quantity && buyForm.pricePerShare && (
              <View style={[styles.totalAmountCard, { backgroundColor: colors.primary + '10' }]}>
                <Text style={[styles.totalAmountLabel, { color: colors.primary }]}>
                  Total Amount
                </Text>
                <Text style={[styles.totalAmountValue, { color: colors.primary }]}>
                  KES {formatCurrency(calculateBuyTotal())}
                </Text>
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Payment Method
              </Text>
              <View style={styles.paymentMethods}>
                {[
                  { key: 'mobile_money', label: 'Mobile Money', icon: 'phone-portrait' },
                  { key: 'bank_transfer', label: 'Bank Transfer', icon: 'card' },
                  { key: 'cash', label: 'Cash', icon: 'cash' },
                ].map((method) => (
                  <TouchableOpacity
                    key={method.key}
                    style={[
                      styles.paymentMethod,
                      { borderColor: colors.border },
                      buyForm.paymentMethod === method.key && {
                        backgroundColor: colors.primary + '20',
                        borderColor: colors.primary
                      }
                    ]}
                    onPress={() => setBuyForm(prev => ({ ...prev, paymentMethod: method.key }))}
                  >
                    <Ionicons
                      name={method.icon}
                      size={20}
                      color={buyForm.paymentMethod === method.key ? colors.primary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.paymentMethodText,
                      { color: buyForm.paymentMethod === method.key ? colors.primary : colors.textSecondary }
                    ]}>
                      {method.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Notes (Optional)
              </Text>
              <TextInput
                style={[styles.formTextArea, {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }]}
                value={buyForm.notes}
                onChangeText={(text) => setBuyForm(prev => ({ ...prev, notes: text }))}
                placeholder="Add any notes about this purchase"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </Card>

          <View style={styles.modalActions}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => {
                setShowBuyModal(false);
                resetBuyForm();
                setSelectedShare(null);
              }}
              style={styles.cancelButton}
            />
            <Button
              title={`Buy for KES ${formatCurrency(calculateBuyTotal())}`}
              onPress={handleBuyShares}
              style={[styles.confirmButton, { backgroundColor: colors.success }]}
              disabled={!buyForm.quantity || !buyForm.pricePerShare}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const renderSellModal = () => (
    <Modal
      visible={showSellModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={[styles.modalHeader, { backgroundColor: colors.surface }]}>
          <TouchableOpacity
            onPress={() => {
              setShowSellModal(false);
              resetSellForm();
            }}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Sell Shares
          </Text>
          <View style={styles.modalCloseButton} />
        </View>

        <ScrollView style={styles.modalContent} contentContainerStyle={styles.modalContentContainer}>
          {userShares && (
            <Card style={[styles.portfolioSummaryCard, { backgroundColor: colors.surface }]}>
              <Text style={[styles.portfolioSummaryTitle, { color: colors.text }]}>
                Your Portfolio
              </Text>
              <Text style={[styles.portfolioSummaryShares, { color: colors.primary }]}>
                {userShares.totalShares} shares available
              </Text>
              <Text style={[styles.portfolioSummaryValue, { color: colors.textSecondary }]}>
                Average price: KES {formatCurrency(userShares.averagePrice)}
              </Text>
            </Card>
          )}

          <Card style={[styles.sellFormCard, { backgroundColor: colors.surface }]}>
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Quantity to Sell *
              </Text>
              <TextInput
                style={[styles.formInput, {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }]}
                value={sellForm.quantity}
                onChangeText={(text) => setSellForm(prev => ({ ...prev, quantity: text }))}
                placeholder="Enter number of shares to sell"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
              {userShares && (
                <Text style={[styles.formHint, { color: colors.textSecondary }]}>
                  Max: {userShares.totalShares} shares available
                </Text>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Selling Price per Share *
              </Text>
              <TextInput
                style={[styles.formInput, {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }]}
                value={sellForm.pricePerShare}
                onChangeText={(text) => setSellForm(prev => ({ ...prev, pricePerShare: text }))}
                placeholder="Enter selling price per share"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
              {marketData && (
                <Text style={[styles.formHint, { color: colors.textSecondary }]}>
                  Current market price: KES {formatCurrency(marketData.currentPrice)}
                </Text>
              )}
            </View>

            {sellForm.quantity && sellForm.pricePerShare && (
              <View style={[styles.totalAmountCard, { backgroundColor: colors.success + '10' }]}>
                <Text style={[styles.totalAmountLabel, { color: colors.success }]}>
                  Total Amount (You'll Receive)
                </Text>
                <Text style={[styles.totalAmountValue, { color: colors.success }]}>
                  KES {formatCurrency(calculateSellTotal())}
                </Text>
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Reason for Selling
              </Text>
              <View style={styles.sellReasons}>
                {[
                  { key: 'profit_taking', label: 'Profit Taking' },
                  { key: 'emergency_funds', label: 'Emergency Funds' },
                  { key: 'portfolio_rebalancing', label: 'Portfolio Rebalancing' },
                  { key: 'other', label: 'Other' },
                ].map((reason) => (
                  <TouchableOpacity
                    key={reason.key}
                    style={[
                      styles.sellReason,
                      { borderColor: colors.border },
                      sellForm.reason === reason.key && {
                        backgroundColor: colors.primary + '20',
                        borderColor: colors.primary
                      }
                    ]}
                    onPress={() => setSellForm(prev => ({ ...prev, reason: reason.key }))}
                  >
                    <Text style={[
                      styles.sellReasonText,
                      { color: sellForm.reason === reason.key ? colors.primary : colors.textSecondary }
                    ]}>
                      {reason.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Additional Notes (Optional)
              </Text>
              <TextInput
                style={[styles.formTextArea, {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }]}
                value={sellForm.notes}
                onChangeText={(text) => setSellForm(prev => ({ ...prev, notes: text }))}
                placeholder="Add any notes about this sale"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </Card>

          <View style={styles.modalActions}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => {
                setShowSellModal(false);
                resetSellForm();
              }}
              style={styles.cancelButton}
            />
            <Button
              title={`Sell for KES ${formatCurrency(calculateSellTotal())}`}
              onPress={handleSellShares}
              style={[styles.confirmButton, { backgroundColor: colors.error }]}
              disabled={!sellForm.quantity || !sellForm.pricePerShare}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  // Removed old handleDeclareDividend - using modern version above

  // Removed all old unused functions

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* 🎨 MODERN HEADER WITH INTEGRATED TABS */}
      {renderHeader()}

      {/* 📋 MAIN CONTENT */}
      <View style={styles.contentContainer}>
        {activeTab === 'portfolio' && renderPortfolioView()}
        {activeTab === 'market' && renderMarketView()}
        {activeTab === 'dividends' && renderDividendsView()}
        {activeTab === 'history' && renderHistoryView()}
      </View>

      {/* 💰 BUY SHARES MODAL */}
      {renderBuyModal()}

      {/* 💸 SELL SHARES MODAL */}
      {renderSellModal()}
    </SafeAreaView>
  );
};

// 🎨 MODERN SHARES & DIVIDENDS STYLES
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // 🎨 HEADER STYLES
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    gap: spacing.md,
  },
  headerTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerBottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flex: 1,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
  },
  headerActions: {
    alignItems: 'flex-end',
  },
  portfolioValue: {
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
  },
  portfolioValueLabel: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  portfolioValueAmount: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
  },

  // 📱 HEADER TABS STYLES
  headerTabs: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    gap: spacing.xs,
    marginLeft: spacing.md,
  },
  headerTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
    minWidth: 0, // Allow shrinking
  },
  headerTabText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
    flexShrink: 1,
  },

  // 📱 TAB NAVIGATION STYLES (Legacy - can be removed)
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: spacing.sm,
    marginVertical: spacing.md,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    gap: spacing.xs,
  },
  tabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },

  // 📋 CONTENT STYLES
  contentContainer: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    paddingHorizontal: spacing.sm,
  },

  // 💰 PORTFOLIO STYLES
  portfolioCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  portfolioHeader: {
    marginBottom: spacing.lg,
  },
  portfolioTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
    gap: spacing.sm,
  },
  portfolioTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    flex: 1,
  },
  performanceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  performanceText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  portfolioStats: {
    gap: spacing.md,
  },
  statRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  statValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  emptyPortfolio: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.md,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
    lineHeight: 20,
  },
  portfolioActions: {
    flexDirection: 'row',
    gap: spacing.md,
    marginTop: spacing.lg,
  },
  actionButton: {
    flex: 1,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
  },

  // 📊 SHARE TYPES STYLES
  shareTypesCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  cardHeader: {
    marginBottom: spacing.lg,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  cardTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  shareTypeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  shareTypeInfo: {
    flex: 1,
  },
  shareTypeName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  shareTypeCount: {
    fontSize: typography.fontSize.sm,
  },
  shareTypeValue: {
    alignItems: 'flex-end',
  },
  shareTypeAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  shareTypePrice: {
    fontSize: typography.fontSize.sm,
  },

  // 🏪 MARKET STYLES
  marketCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  marketHeader: {
    marginBottom: spacing.lg,
  },
  marketTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  marketTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    flex: 1,
  },
  lastUpdated: {
    fontSize: typography.fontSize.xs,
  },
  marketStats: {
    gap: spacing.lg,
  },
  priceSection: {
    alignItems: 'center',
    gap: spacing.sm,
  },
  currentPrice: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
  },
  priceChange: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  priceChangeText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  marketMetrics: {
    flexDirection: 'row',
    gap: spacing.lg,
  },
  metricItem: {
    flex: 1,
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  metricValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },

  // 📋 AVAILABLE SHARES STYLES
  availableSharesCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  refreshButton: {
    padding: spacing.sm,
  },
  emptyShares: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.md,
  },
  shareItem: {
    padding: spacing.md,
    gap: spacing.md,
  },
  shareItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  shareItemInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  shareItemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  shareItemDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: 18,
  },
  shareItemPrice: {
    alignItems: 'flex-end',
  },
  sharePrice: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  sharePriceLabel: {
    fontSize: typography.fontSize.xs,
  },
  shareItemDetails: {
    flexDirection: 'row',
    gap: spacing.lg,
  },
  shareDetail: {
    flex: 1,
  },
  shareDetailLabel: {
    fontSize: typography.fontSize.xs,
    marginBottom: spacing.xs,
  },
  shareDetailValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  buyButton: {
    marginTop: spacing.sm,
  },
  separator: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginVertical: spacing.sm,
  },

  // 💸 DIVIDEND STYLES
  dividendSummaryCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  declareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  declareButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  dividendStats: {
    gap: spacing.md,
  },
  dividendHistoryCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  emptyDividends: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.md,
  },
  dividendItem: {
    padding: spacing.md,
    gap: spacing.md,
  },
  dividendItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  dividendItemInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  dividendItemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  dividendItemDate: {
    fontSize: typography.fontSize.sm,
  },
  dividendItemAmount: {
    alignItems: 'flex-end',
  },
  dividendAmount: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  dividendPerShare: {
    fontSize: typography.fontSize.sm,
  },
  dividendItemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dividendDetail: {
    flex: 1,
  },
  dividendDetailLabel: {
    fontSize: typography.fontSize.xs,
    marginBottom: spacing.xs,
  },
  dividendDetailValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },

  // 📈 HISTORY STYLES
  emptyHistory: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.md,
  },
  historyItem: {
    padding: spacing.md,
  },
  historyItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  transactionTypeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  historyItemInfo: {
    flex: 1,
  },
  historyItemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  historyItemDate: {
    fontSize: typography.fontSize.sm,
  },
  historyItemAmount: {
    alignItems: 'flex-end',
  },
  historyAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  historyPricePerShare: {
    fontSize: typography.fontSize.sm,
  },


  // 🎭 MODAL STYLES
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  modalContent: {
    flex: 1,
  },
  modalContentContainer: {
    padding: spacing.lg,
    gap: spacing.lg,
  },

  // 📝 FORM STYLES
  selectedShareCard: {
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  selectedShareTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  selectedSharePrice: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  selectedShareAvailable: {
    fontSize: typography.fontSize.sm,
  },
  buyFormCard: {
    padding: spacing.lg,
    borderRadius: borderRadius.md,
  },
  sellFormCard: {
    padding: spacing.lg,
    borderRadius: borderRadius.md,
  },
  portfolioSummaryCard: {
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  portfolioSummaryTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  portfolioSummaryShares: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  portfolioSummaryValue: {
    fontSize: typography.fontSize.sm,
  },
  formGroup: {
    marginBottom: spacing.lg,
  },
  formLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.fontSize.base,
  },
  formTextArea: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.fontSize.base,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  formHint: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
  },
  totalAmountCard: {
    padding: spacing.md,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    marginVertical: spacing.md,
  },
  totalAmountLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  totalAmountValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
  },
  paymentMethods: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  paymentMethod: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  paymentMethodText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  sellReasons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  sellReason: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderWidth: 1,
    borderRadius: borderRadius.md,
  },
  sellReasonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  modalActions: {
    flexDirection: 'row',
    gap: spacing.md,
    paddingTop: spacing.lg,
  },
  cancelButton: {
    flex: 1,
  },
  confirmButton: {
    flex: 1,
  },

  // Missing share type styles
  shareTypeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  shareTypeInfo: {
    flex: 1,
  },
  shareTypeName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  shareTypeCount: {
    fontSize: typography.fontSize.sm,
  },
  shareTypeValue: {
    alignItems: 'flex-end',
  },
  shareTypeAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  shareTypePrice: {
    fontSize: typography.fontSize.sm,
  },


});

export default SharesDividendsScreen;
