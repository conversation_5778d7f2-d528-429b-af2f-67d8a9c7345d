import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { useLightningData, useOptimisticUpdate } from '../../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const { width } = Dimensions.get('window');

const ChamaDashboard = ({ navigation, onRouteChange, route }) => {
  const { theme, user, selectedChama, setSelectedChama, chamas } = useApp();
  const colors = getThemeColors(theme);

  const [userChamas, setUserChamas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [chamaFeatures, setChamaFeatures] = useState({
    allowMerryGoRound: true,
    allowWelfare: true,
  });
  const [chamaStats, setChamaStats] = useState(null);
  const [statsLoading, setStatsLoading] = useState(false);
  const [realTimeData, setRealTimeData] = useState({
    walletBalance: 0,
    totalContributions: 0,
    totalMembers: 0,
    totalMeetings: 0,
    lastUpdated: null,
  });

  // Ref to track current chama ID to prevent race conditions
  const currentChamaIdRef = useRef(null);

  // Handle chama selection from navigation params (when coming from chama list)
  useEffect(() => {
    if (route?.params?.chamaId && route?.params?.chama) {
      const chamaFromParams = route.params.chama;
      console.log('🎯 ChamaDashboard: Setting chama from navigation params:', chamaFromParams);
      setSelectedChama(chamaFromParams);
    } else if (route?.params?.chamaId && !selectedChama) {
      // If we have chamaId but no chama object, try to find it in userChamas
      const foundChama = userChamas.find(c => c.id === route.params.chamaId);
      if (foundChama) {
        console.log('🎯 ChamaDashboard: Found chama in userChamas:', foundChama);
        setSelectedChama(foundChama);
      }
    }
  }, [route?.params, userChamas]);

  useEffect(() => {
    loadUserChamas();
  }, []);

  useEffect(() => {
    if (selectedChama) {
      const chamaId = selectedChama?.id || selectedChama?.chamaId || selectedChama;
      currentChamaIdRef.current = chamaId;
      // console.log('🎯 Selected chama changed to:', chamaId);
      loadChamaFeatures();
      loadChamaStatistics();
    } else {
      currentChamaIdRef.current = null;
    }
  }, [selectedChama]);

  // Auto-refresh statistics every 30 seconds when screen is active
  useEffect(() => {
    if (!selectedChama) return;

    // Get the current chama ID to prevent stale closures
    const currentChamaId = selectedChama?.id || selectedChama?.chamaId || selectedChama;

    const interval = setInterval(() => {
      console.log('🔄 Auto-refreshing chama statistics for:', currentChamaId);
      loadChamaStatistics(currentChamaId);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [selectedChama?.id || selectedChama?.chamaId || selectedChama]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      if (selectedChama) {
        const currentChamaId = selectedChama?.id || selectedChama?.chamaId || selectedChama;
        console.log('🎯 Screen focused, refreshing chama statistics for:', currentChamaId);
        loadChamaStatistics(currentChamaId);
      }
    }, [selectedChama?.id || selectedChama?.chamaId || selectedChama])
  );

  const loadUserChamas = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getUserChamas(20, 0);
      if (response.success) {
        const userChamasData = response.data || [];
        setUserChamas(userChamasData);

        // If no chama is currently selected and we have chamas, select the first one
        if (userChamasData.length > 0 && !selectedChama) {
          console.log('🎯 ChamaDashboard: Auto-selecting first chama:', userChamasData[0]);
          setSelectedChama(userChamasData[0]);
        }

        // If we have a selected chama, make sure it's still in the list (in case it was deleted)
        if (selectedChama && !userChamasData.find(c => c.id === selectedChama.id)) {
          console.log('🎯 ChamaDashboard: Selected chama no longer exists, clearing selection');
          setSelectedChama(null);
        }
      }
    } catch (error) {
      console.error('Failed to load user chamas:', error);
      // Set empty array on error to show empty state
      setUserChamas([]);
    } finally {
      setLoading(false);
    }
  };

  const loadChamaFeatures = async () => {
    try {
      // console.log('🔍 loadChamaFeatures called with selectedChama:', selectedChama);

      if (!selectedChama) {
        console.warn('No chama selected for loading features');
        return;
      }

      // Extract chamaId properly
      let chamaId;
      if (typeof selectedChama === 'string') {
        chamaId = selectedChama;
      } else if (selectedChama && selectedChama.id) {
        chamaId = selectedChama.id;
      } else {
        console.error('Invalid selectedChama structure:', selectedChama);
        return;
      }

      // Ensure chamaId is a string
      chamaId = String(chamaId);
      // console.log('🔍 Loading chama features for ID:', chamaId, 'Type:', typeof chamaId);

      const response = await ApiService.makeRequest(`/chamas/${chamaId}`);
      if (response.success && response.data) {
        const chama = response.data;
        setChamaFeatures({
          allowMerryGoRound: chama.permissions?.allowMerryGoRound ?? true,
          allowWelfare: chama.permissions?.allowWelfare ?? true,
        });
      }
    } catch (error) {
      console.error('Failed to load chama features:', error);
      // Keep default features on error
    }
  };

  const loadChamaStatistics = async (targetChamaId = null) => {
    // Use provided chamaId or extract from selectedChama
    const currentChama = selectedChama;
    if (!currentChama && !targetChamaId) {
      console.warn('No chama selected for loading statistics');
      return;
    }

    try {
      setStatsLoading(true);

      // Extract chamaId properly
      let chamaId = targetChamaId;
      if (!chamaId) {
        if (typeof currentChama === 'string') {
          chamaId = currentChama;
        } else if (currentChama.id) {
          chamaId = currentChama.id;
        } else if (currentChama.chamaId) {
          chamaId = currentChama.chamaId;
        } else {
          throw new Error('Invalid chama ID format');
        }
      }

      // console.log('🔄 Loading chama statistics for:', chamaId);

      // Get comprehensive chama statistics
      const [statsResponse, chamaResponse] = await Promise.all([
        ApiService.getChamaStatistics(chamaId),
        ApiService.getChamaById(chamaId)
      ]);

      // Check if the chama is still the same (prevent race conditions)
      if (currentChamaIdRef.current !== chamaId) {
        console.log('🚫 Chama changed during loading, discarding stale data for:', chamaId);
        return;
      }

      // console.log('📊 Statistics response:', statsResponse);
      // console.log('🏠 Chama response:', chamaResponse);

      if (statsResponse.success && statsResponse.data) {
        setChamaStats(statsResponse.data);

        // Update real-time data from statistics
        const stats = statsResponse.data;
        const financialStats = stats.financial_stats || {};
        const memberStats = stats.member_stats || {};
        const activityStats = stats.activity_stats || {};
        const chamaInfo = stats.chama_info || {};
        const userStats = stats.user_stats || {};

        // console.log('🔍 Activity stats received:', activityStats);
        // console.log('🔍 Total meetings from API:', activityStats?.total_meetings);

        setRealTimeData({
          walletBalance: chamaInfo.wallet_balance || chamaInfo.total_funds || 0,
          totalContributions: financialStats.total_contributions || 0,
          contributionCount: financialStats.total_transactions || 0, // Number of contribution transactions
          totalMembers: memberStats.active_members || memberStats.total_members || chamaInfo.current_members || 0,
          totalMeetings: activityStats.total_meetings || 0, // All meetings (completed + upcoming + ongoing)
          upcomingMeetings: activityStats.upcoming_meetings || 0,
          ongoingMeetings: activityStats.ongoing_meetings || 0,
          completedMeetings: activityStats.completed_meetings || 0,
          totalTransactions: financialStats.total_transactions || 0,
          averageContribution: financialStats.average_contribution || 0,
          userRole: userStats.role || 'member',
          userTransactionCount: userStats.total_transactions || 0,
          userContributionCount: userStats.contribution_count || 0,
          lastUpdated: new Date().toISOString(),
        });

        // console.log('🔍 Set totalMeetings to:', activityStats.total_meetings || 0);

        // console.log('✅ Real-time data updated:', {
        //   walletBalance: chamaInfo.total_funds || 0,
        //   totalContributions: financialStats.total_contributions || 0,
        //   totalMembers: memberStats.total_members || 0,
        //   totalMeetings: activityStats.total_meetings || 0,
        // });
      }

      // Update selected chama with fresh data if available
      if (chamaResponse.success && chamaResponse.data) {
        setSelectedChama(chamaResponse.data);
      }

    } catch (error) {
      console.error('❌ Failed to load chama statistics:', error);
      // Don't show error to user, just log it
    } finally {
      setStatsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserChamas();
    if (selectedChama) {
      await Promise.all([
        loadChamaFeatures(),
        loadChamaStatistics()
      ]);
    }
    setRefreshing(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getUserRole = (chama) => {
    // Use real-time data if available
    if (realTimeData.userRole && realTimeData.userRole !== 'not_member') {
      return realTimeData.userRole.charAt(0).toUpperCase() + realTimeData.userRole.slice(1);
    }

    if (!chama || !user) return 'Member';

    // Fallback to simplified role detection
    if (chama.created_by === user.id) {
      return 'Chairman';
    }

    return 'Member';
  };

  const renderChamaSelector = () => {
    if (userChamas.length === 0) return null;

    return (
      <Card style={styles.selectorCard}>
        <Text style={[styles.selectorTitle, { color: colors.text }]}>
          Select Chama
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {userChamas.map((chama) => (
            <TouchableOpacity
              key={chama.id}
              style={[
                styles.chamaChip,
                {
                  backgroundColor: selectedChama?.id === chama.id ? colors.primary : colors.surface,
                  borderColor: selectedChama?.id === chama.id ? colors.primary : colors.border,
                }
              ]}
              onPress={() => {
                console.log('🎯 ChamaDashboard: User selected chama:', chama);
                setSelectedChama(chama);
              }}
            >
              <Text
                style={[
                  styles.chamaChipText,
                  {
                    color: selectedChama?.id === chama.id ? colors.white : colors.text,
                  }
                ]}
              >
                {chama.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Card>
    );
  };

  const renderQuickStats = () => {
    if (!selectedChama) return null;

    return (
      <Card style={styles.statsCard}>
        <View style={styles.statsHeader}>
          <View style={styles.statsHeaderLeft}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {selectedChama.name} Overview
            </Text>
            {realTimeData.lastUpdated && (
              <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
                Updated: {new Date(realTimeData.lastUpdated).toLocaleTimeString()}
              </Text>
            )}
          </View>
          {/* <View style={styles.statsHeaderRight}>
            {statsLoading ? (
              <View style={styles.loadingIndicator}>
                <Ionicons name="refresh" size={16} color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Updating...
                </Text>
              </View>
            ) : (
              <TouchableOpacity
                style={[styles.refreshButton, { borderColor: colors.border }]}
                onPress={loadChamaStatistics}
                disabled={statsLoading}
              >
                <Ionicons name="refresh" size={18} color={colors.primary} />
              </TouchableOpacity>
            )}
          </View> */}
        </View>

        <View style={styles.statsGrid}>
          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.primary + '15' }]}>
                <Ionicons name="wallet" size={20} color={colors.primary} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                {selectedChama?.category === 'contribution' ? 'Group Wallet' : 'Chama Wallet'}
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {formatCurrency(realTimeData.walletBalance)}
            </Text>
            <Text style={[styles.statTileSubtext, { color: colors.textSecondary }]}>
              Available Balance
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.secondary + '15' }]}>
                <Ionicons name="people" size={20} color={colors.secondary} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Members
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {realTimeData.totalMembers}/{selectedChama.max_members || 50}
            </Text>
            <Text style={[styles.statTileSubtext, { color: colors.textSecondary }]}>
              Active Members
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.warning + '15' }]}>
                <Ionicons name="calendar" size={20} color={colors.warning} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Meetings
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {realTimeData.totalMeetings || 0}
            </Text>
            <Text style={[styles.statTileSubtext, { color: colors.textSecondary }]}>
              Total Meetings
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.success + '15' }]}>
                <Ionicons name="trending-up" size={20} color={colors.success} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Contributions
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {realTimeData.contributionCount || 0}
            </Text>
            <Text style={[styles.statTileSubtext, { color: colors.textSecondary }]}>
              Total Accounts
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.info + '15' }]}>
                <Ionicons name="shield-checkmark" size={20} color={colors.info} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Your Role
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {getUserRole(selectedChama)}
            </Text>
            <Text style={[styles.statTileSubtext, { color: colors.textSecondary }]}>
              Member Status
            </Text>
          </View>

          <View style={[styles.statTile, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.statTileHeader}>
              <View style={[styles.statTileIcon, { backgroundColor: colors.primary + '15' }]}>
                <Ionicons name="swap-horizontal" size={20} color={colors.primary} />
              </View>
              <Text style={[styles.statTileLabel, { color: colors.textSecondary }]}>
                Your Transactions
              </Text>
            </View>
            <Text style={[styles.statTileValue, { color: colors.text }]}>
              {realTimeData.userTransactionCount || 0}
            </Text>
            <Text style={[styles.statTileSubtext, { color: colors.textSecondary }]}>
              Your Activity
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderQuickActions = () => {
    // Determine if current chama is a contribution group
    const isContributionGroup = selectedChama?.category === 'contribution';

    const actions = [
      {
        id: 'members',
        title: 'Members',
        icon: 'people',
        color: colors.secondary,
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('members', 'ChamaMembersScreen');
            } else {
              navigation.navigate('ChamaMembersScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'contributions',
        title: isContributionGroup ? 'Contribute' : 'Contributions',
        icon: 'wallet',
        color: colors.primary,
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('contributions', 'ContributeScreen');
            } else {
              navigation.navigate('ContributeScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'transactions',
        title: 'Transactions',
        icon: 'receipt',
        color: colors.success,
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('transactions', 'ChamaTransactionsScreen');
            } else {
              navigation.navigate('ChamaTransactionsScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'loans',
        title: 'Loans',
        icon: 'card',
        color: '#6366F1', // Indigo color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('loans', 'ChamaLoansScreen');
            } else {
              navigation.navigate('ChamaLoansScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'meetings',
        title: 'Meetings',
        icon: 'calendar',
        color: '#8B5CF6', // Purple color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('meetings', 'ChamaMeetingsScreen');
            } else {
              navigation.navigate('ChamaMeetingsScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'merry-go-round',
        title: 'Merry-Go-Round',
        icon: 'refresh-circle',
        color: '#F59E0B', // Amber color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('merry-go-round', 'MerryGoRoundScreen');
            } else {
              navigation.navigate('MerryGoRoundScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'welfare',
        title: 'Welfare',
        icon: 'heart',
        color: '#EC4899', // Pink color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('welfare', 'WelfareScreen');
            } else {
              navigation.navigate('WelfareScreen', {
                chamaId: selectedChama.id,
                chama: selectedChama
              });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'shares-dividends',
        title: 'Shares & Dividends',
        icon: 'pie-chart',
        color: '#10B981', // Emerald color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('shares-dividends', 'SharesDividendsScreen');
            } else {
              navigation.navigate('SharesDividendsScreen', { chamaId: selectedChama.id });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'polls-voting',
        title: 'Polls & Voting',
        icon: 'checkmark-circle',
        color: '#8B5CF6', // Violet color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('polls-voting', 'PollsVotingScreen');
            } else {
              navigation.navigate('PollsVotingScreen', { chamaId: selectedChama.id });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'account-management',
        title: 'Account Management',
        icon: 'business',
        color: '#F59E0B', // Amber color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('account-management', 'AccountManagementScreen');
            } else {
              navigation.navigate('AccountManagementScreen', { chamaId: selectedChama.id });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'chat',
        title: 'Group Chat',
        icon: 'chatbubbles',
        color: '#3B82F6', // Blue color
        onPress: async () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('chat', 'ChatRoom');
            } else {
              try {
                // Create or get existing chama chat room
                const response = await ApiService.createChatRoom({
                  type: 'chama',
                  chamaId: selectedChama.id,
                  name: `${selectedChama.name} Group Chat`
                });

                if (response.success) {
                  const room = response.data;
                  navigation.navigate('ChatRoom', {
                    roomId: room.id,
                    roomName: room.name || `${selectedChama.name} Group Chat`,
                    roomType: 'group',
                    chamaId: selectedChama.id
                  });
                } else {
                  Alert.alert('Error', 'Failed to access group chat');
                }
              } catch (error) {
                console.error('Failed to create/get chama chat room:', error);
                Alert.alert('Error', 'Failed to access group chat');
              }
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
      {
        id: 'settings',
        title: 'Settings',
        icon: 'settings',
        color: '#6B7280', // Gray color
        onPress: () => {
          if (selectedChama) {
            if (onRouteChange) {
              onRouteChange('settings', 'ChamaSettings');
            } else {
              navigation.navigate('ChamaSettings', { chamaId: selectedChama.id });
            }
          } else {
            Alert.alert('No Chama Selected', 'Please select a chama first');
          }
        },
      },
    ];

    // Filter actions based on feature toggles and chama type
    const filteredActions = actions.filter(action => {
      // For contribution groups, only show specific actions
      if (isContributionGroup) {
        const allowedForContributionGroups = [
          'contributions', // Contribute
          'transactions',  // Transactions
          'members',       // Members
          'meetings'       // Meetings
        ];
        return allowedForContributionGroups.includes(action.id);
      }

      // For regular chamas, apply feature toggles
      switch (action.id) {
        case 'merry-go-round':
          return chamaFeatures.allowMerryGoRound;
        case 'welfare':
          return chamaFeatures.allowWelfare;
        default:
          return true; // Show all other actions
      }
    });

    return (
      <Card style={styles.actionsCard}>
        <Text style={[styles.cardTitle, { color: colors.text }]}>
          Quick Actions
        </Text>

        <View style={[
          styles.actionsGrid,
          isContributionGroup && styles.actionsGridContribution
        ]}>
          {filteredActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={[
                styles.actionItem,
                isContributionGroup && styles.actionItemContribution
              ]}
              onPress={action.onPress}
              disabled={!selectedChama}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIcon, { backgroundColor: action.color }]}>
                <Ionicons name={action.icon} size={24} color={colors.white} />
              </View>
              <Text style={[styles.actionTitle, { color: colors.text }]} numberOfLines={2}>
                {action.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Chamas Yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Join or create a chama to start managing group finances
      </Text>

      <View style={styles.emptyActions}>
        <Button
          title="Create Chama"
          onPress={() => navigation.navigate('CreateChama')}
          style={styles.emptyButton}
          icon={<Ionicons name="add" size={20} color={colors.white} />}
        />

        <Button
          title="Browse Chamas"
          variant="outline"
          onPress={() => navigation.navigate('ChamaList')}
          style={styles.emptyButton}
          icon={<Ionicons name="search" size={20} color={colors.primary} />}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >


        {userChamas.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {renderChamaSelector()}
            {renderQuickStats()}
            {renderQuickActions()}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    ...shadows.sm,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  homeButton: {
    padding: spacing.sm,
    marginRight: spacing.md,
    borderRadius: borderRadius.md,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.base,
  },
  listButton: {
    padding: spacing.sm,
  },
  selectorCard: {
    margin: spacing.md,
  },
  selectorTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  chamaChip: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    borderWidth: 1,
  },
  chamaChipText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  statsCard: {
    margin: spacing.md,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  statsHeaderLeft: {
    flex: 1,
  },
  statsHeaderRight: {
    alignItems: 'flex-end',
  },
  refreshButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  loadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  loadingText: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
  },
  lastUpdated: {
    fontSize: typography.fontSize.xs,
    fontStyle: 'italic',
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statTile: {
    width: '48%',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  statTileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statTileIcon: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  statTileLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
  },
  statTileValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'left',
  },
  statTileSubtext: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
    textAlign: 'left',
  },
  actionsCard: {
    margin: spacing.md,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
  },
  actionItem: {
    width: '22%', // 4 items per row (22% x 4 = 88% + gaps = 100%)
    alignItems: 'center',
    padding: spacing.sm,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.md,
    backgroundColor: 'transparent',
  },
  // Contribution group specific styles (2x2 grid for 4 actions)
  actionsGridContribution: {
    justifyContent: 'space-around', // Better spacing for 2x2 grid
    paddingHorizontal: spacing.lg, // More padding for better centering
    alignItems: 'flex-start', // Align items to top
  },
  actionItemContribution: {
    width: '45%', // 2 items per row (45% x 2 = 90% + gaps = 100%)
    padding: spacing.md, // More padding for larger touch targets
    marginBottom: spacing.lg, // More space between rows
    minHeight: 80, // Consistent height for all action items
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xs,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionTitle: {
    fontSize: 10,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
    lineHeight: 12,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  emptyActions: {
    width: '100%',
    gap: spacing.md,
  },
  emptyButton: {
    width: '100%',
  },
});

export default ChamaDashboard;
