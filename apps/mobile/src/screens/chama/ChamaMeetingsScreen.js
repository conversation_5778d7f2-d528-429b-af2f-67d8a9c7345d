import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { CommonActions } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import { toEAT, formatDate, nowEAT } from '../../utils/dateUtils';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const ChamaMeetingsScreen = ({ route, navigation, onRouteChange }) => {
  const { chamaId, chamaName, newMeeting, refresh, fromUserDashboard } = route.params || {};
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  // Determine if we're showing all user meetings or chama-specific meetings
  const isUserMeetingsView = fromUserDashboard || !chamaId;

  const [meetings, setMeetings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('upcoming');
  // Removed joinableMeetings state - banner was removed
  const [persistentNotifications, setPersistentNotifications] = useState([]);
  const [dismissedNotifications, setDismissedNotifications] = useState(new Set());

  const tabs = [
    { id: 'upcoming', name: 'Upcoming', icon: 'calendar' },
    { id: 'ongoing', name: 'Ongoing', icon: 'radio-button-on' },
    { id: 'past', name: 'Past', icon: 'time' },
    { id: 'all', name: 'All', icon: 'list' },
  ];

  useEffect(() => {
    loadMeetings();
    loadDismissedNotifications();
    loadPersistedMeetingState();
  }, [chamaId, selectedTab]);

  // Load persisted meeting state from AsyncStorage
  const loadPersistedMeetingState = async () => {
    try {
      const persistedState = await AsyncStorage.getItem(`meetings_${chamaId}_${selectedTab}`);
      if (persistedState) {
        const { meetings: persistedMeetings, timestamp } = JSON.parse(persistedState);
        const now = Date.now();

        // Use persisted data if it's less than 5 minutes old
        if (now - timestamp < 5 * 60 * 1000) {
          console.log('📱 Using persisted meeting data');
          setMeetings(persistedMeetings);
          updateMeetingNotifications(persistedMeetings);
        }
      }
    } catch (error) {
      console.error('Failed to load persisted meeting state:', error);
    }
  };

  // Persist meeting state to AsyncStorage
  const persistMeetingState = async (meetingsData) => {
    try {
      const stateToSave = {
        meetings: meetingsData,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(`meetings_${chamaId}_${selectedTab}`, JSON.stringify(stateToSave));
      console.log('💾 Meeting state persisted');
    } catch (error) {
      console.error('Failed to persist meeting state:', error);
    }
  };

  // Auto-refresh every minute to update meeting join buttons
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('🔄 Auto-refreshing meetings for join button updates...');
      loadMeetings();
    }, 60000); // Refresh every minute

    return () => clearInterval(interval);
  }, [chamaId, selectedTab]);

  // Handle new meeting data from navigation
  useEffect(() => {
    if (newMeeting && refresh) {
      console.log('🎉 New meeting received:', newMeeting);

      // Add the new meeting to the current list immediately
      setMeetings(prevMeetings => {
        // Check if the new meeting should be visible in the current tab
        const meetingDate = new Date(newMeeting.scheduledAt || newMeeting.date);
        const now = new Date();
        const meetingDurationMinutes = newMeeting.duration || 60;
        const meetingEndTime = new Date(meetingDate.getTime() + (meetingDurationMinutes * 60 * 1000));
        const isUpcoming = meetingEndTime > now; // Meeting is upcoming if it hasn't ended yet

        let shouldAddToCurrentTab = true;
        if (selectedTab === 'upcoming' && !isUpcoming) {
          shouldAddToCurrentTab = false;
        } else if (selectedTab === 'past' && isUpcoming) {
          shouldAddToCurrentTab = false;
        }

        if (shouldAddToCurrentTab) {
          const updatedMeetings = [newMeeting, ...prevMeetings];
          console.log('📝 Updated meetings list:', updatedMeetings.length, 'meetings');
          return updatedMeetings;
        } else {
          console.log('📝 New meeting not added to current tab:', selectedTab);
          return prevMeetings;
        }
      });

      // Show success toast with appropriate message
      const meetingDate = new Date(newMeeting.scheduledAt || newMeeting.date);
      const now = new Date();
      const meetingDurationMinutes = newMeeting.duration || 60;
      const meetingEndTime = new Date(meetingDate.getTime() + (meetingDurationMinutes * 60 * 1000));
      const isUpcoming = meetingEndTime > now; // Meeting is upcoming if it hasn't ended yet

      let toastMessage = `${newMeeting.title} has been scheduled successfully`;
      if (selectedTab === 'upcoming' && !isUpcoming) {
        toastMessage += '. Switch to "Past" tab to see it.';
      } else if (selectedTab === 'past' && isUpcoming) {
        toastMessage += '. Switch to "Upcoming" tab to see it.';
      }

      Toast.show({
        type: 'success',
        text1: 'Meeting Scheduled! 📅',
        text2: toastMessage,
        position: 'top',
        visibilityTime: 4000,
      });

      // Clear the navigation params to prevent re-adding
      navigation.setParams({ newMeeting: null, refresh: false });

      // Optionally refresh the full list from server after a short delay
      setTimeout(() => {
        loadMeetings();
      }, 1000);
    }
  }, [newMeeting, refresh, navigation]);

  const loadMeetings = async () => {
    try {
      setLoading(true);

      // Try to load from API first
      try {
        console.log('📅 Loading meetings...', { isUserMeetingsView, chamaId, selectedTab });

        // Use different API calls based on context
        const response = isUserMeetingsView
          ? await ApiService.getUserMeetings(50, 0)  // Get all user meetings across chamas
          : await ApiService.getMeetings(chamaId);   // Get meetings for specific chama

        console.log('📅 Meetings API response:', response);

        if (response.success) {
          let filteredMeetings = response.data || [];
          console.log('📅 Raw meetings data:', filteredMeetings.length, 'meetings');
          const now = nowEAT(); // Use EAT for current time

          if (selectedTab === 'upcoming') {
            filteredMeetings = filteredMeetings.filter(meeting => {
              const meetingDate = meeting.scheduledAt || meeting.date;
              const meetingDateEAT = toEAT(meetingDate);
              const meetingDurationMinutes = meeting.duration || 60;
              const meetingEndTime = new Date(meetingDateEAT.getTime() + (meetingDurationMinutes * 60 * 1000));

              // Meeting is upcoming if it hasn't started yet
              return meetingDateEAT > now;
            });
          } else if (selectedTab === 'ongoing') {
            filteredMeetings = filteredMeetings.filter(meeting => {
              const meetingDate = meeting.scheduledAt || meeting.date;
              const meetingDateEAT = toEAT(meetingDate);
              const meetingDurationMinutes = meeting.duration || 60;
              const meetingEndTime = new Date(meetingDateEAT.getTime() + (meetingDurationMinutes * 60 * 1000));

              // Meeting is ongoing if it has started but not ended
              return meetingDateEAT <= now && meetingEndTime > now;
            });
          } else if (selectedTab === 'past') {
            filteredMeetings = filteredMeetings.filter(meeting => {
              const meetingDate = meeting.scheduledAt || meeting.date;
              const meetingDateEAT = toEAT(meetingDate);
              const meetingDurationMinutes = meeting.duration || 60;
              const meetingEndTime = new Date(meetingDateEAT.getTime() + (meetingDurationMinutes * 60 * 1000));

              // Meeting is past only if it has completely ended
              return meetingEndTime <= now;
            });
          }

          console.log('📅 Setting meetings:', filteredMeetings.length, 'meetings');
          setMeetings(filteredMeetings);
          persistMeetingState(filteredMeetings);

          // Update meeting notifications
          updateMeetingNotifications(response.data || []);
          return;
        }
      } catch (apiError) {
        console.log('API call failed, using mock data:', apiError);
      }

      // Fallback to mock data
      const mockMeetings = [
        {
          id: '1',
          title: 'Monthly Contribution Meeting',
          description: 'Review monthly contributions and discuss new investment opportunities',
          date: '2025-12-15T14:00:00Z', // Future date for upcoming
          scheduledAt: '2025-12-15T14:00:00Z',
          location: 'Community Center',
          type: 'physical',
          meetingType: 'physical',
          status: 'scheduled',
          attendees: 15,
          agenda: [
            'Review previous meeting minutes',
            'Monthly contribution collection',
            'Investment proposal discussion',
            'New member applications',
          ],
        },
        {
          id: '2',
          title: 'Virtual Strategy Meeting',
          description: 'Discuss investment strategies and portfolio review',
          date: '2025-12-20T16:00:00Z', // Future date for upcoming
          scheduledAt: '2025-12-20T16:00:00Z',
          location: 'Online Meeting Room',
          type: 'virtual',
          meetingType: 'virtual',
          status: 'scheduled',
          attendees: 12,
          agenda: [
            'Portfolio performance review',
            'New investment opportunities',
            'Risk assessment discussion',
          ],
        },
        {
          id: '3',
          title: 'Hybrid Annual Meeting',
          description: 'Annual general meeting with both physical and virtual attendance',
          date: '2025-12-25T10:00:00Z', // Future date for upcoming
          scheduledAt: '2025-12-25T10:00:00Z',
          location: 'Main Hall + Online',
          type: 'hybrid',
          meetingType: 'hybrid',
          status: 'scheduled',
          attendees: 25,
          agenda: [
            'Annual financial report',
            'Election of new officials',
            'Strategic planning for next year',
          ],
        },
        {
          id: '4',
          title: 'Past Emergency Meeting',
          description: 'Discuss urgent loan application',
          date: '2024-02-10T16:00:00Z', // Past date
          scheduledAt: '2024-02-10T16:00:00Z',
          location: 'Virtual Meeting',
          type: 'virtual',
          meetingType: 'virtual',
          status: 'completed',
          attendees: 12,
          agenda: [
            'Urgent loan application review',
            'Emergency fund allocation',
          ],
        },
      ];

      let filteredMeetings = mockMeetings;
      const now = new Date();

      if (selectedTab === 'upcoming') {
        filteredMeetings = mockMeetings.filter(meeting => {
          const meetingDate = new Date(meeting.date);
          const meetingDurationMinutes = meeting.duration || 60;
          const meetingEndTime = new Date(meetingDate.getTime() + (meetingDurationMinutes * 60 * 1000));

          // Meeting is upcoming if it hasn't started yet
          return meetingDate > now;
        });
      } else if (selectedTab === 'ongoing') {
        filteredMeetings = mockMeetings.filter(meeting => {
          const meetingDate = new Date(meeting.date);
          const meetingDurationMinutes = meeting.duration || 60;
          const meetingEndTime = new Date(meetingDate.getTime() + (meetingDurationMinutes * 60 * 1000));

          // Meeting is ongoing if it has started but not ended
          return meetingDate <= now && meetingEndTime > now;
        });
      } else if (selectedTab === 'past') {
        filteredMeetings = mockMeetings.filter(meeting => {
          const meetingDate = new Date(meeting.date);
          const meetingDurationMinutes = meeting.duration || 60;
          const meetingEndTime = new Date(meetingDate.getTime() + (meetingDurationMinutes * 60 * 1000));

          // Meeting is past only if it has completely ended
          return meetingEndTime <= now;
        });
      }

      setMeetings(filteredMeetings);
      persistMeetingState(filteredMeetings);
    } catch (error) {
      console.error('Failed to load meetings:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMeetings();
    setRefreshing(false);
  };

  // Load dismissed notifications from storage
  const loadDismissedNotifications = async () => {
    try {
      const dismissed = await AsyncStorage.getItem(`dismissed_notifications_${chamaId}`);
      if (dismissed) {
        setDismissedNotifications(new Set(JSON.parse(dismissed)));
      }
    } catch (error) {
      console.error('Failed to load dismissed notifications:', error);
    }
  };

  // Save dismissed notifications to storage
  const saveDismissedNotifications = async (dismissedSet) => {
    try {
      await AsyncStorage.setItem(
        `dismissed_notifications_${chamaId}`,
        JSON.stringify(Array.from(dismissedSet))
      );
    } catch (error) {
      console.error('Failed to save dismissed notifications:', error);
    }
  };

  // Create persistent notification for meeting starting soon
  const createPersistentNotification = (meeting) => {
    const notificationId = `meeting_${meeting.id}_starting_soon`;

    // Don't create if already dismissed
    if (dismissedNotifications.has(notificationId)) {
      return;
    }

    const meetingDate = toEAT(meeting.scheduledAt || meeting.date);
    const currentTime = nowEAT();
    const timeDiffMinutes = Math.floor((meetingDate - currentTime) / (1000 * 60));

    // Only show for meetings starting within 10 minutes
    // if (timeDiffMinutes <= 10 && timeDiffMinutes > -30) {
    //   const notification = {
    //     id: notificationId,
    //     meetingId: meeting.id,
    //     type: 'meeting_starting_soon',
    //     title: timeDiffMinutes > 0 ? '⏰ MEETING STARTING SOON!' : '🔴 MEETING IS LIVE!',
    //     subtitle: meeting.title,
    //     timeText: timeDiffMinutes > 0
    //       ? `Starting in ${timeDiffMinutes} minute${timeDiffMinutes !== 1 ? 's' : ''}`
    //       : timeDiffMinutes === 0
    //         ? 'Starting now!'
    //         : `Started ${Math.abs(timeDiffMinutes)} minute${Math.abs(timeDiffMinutes) !== 1 ? 's' : ''} ago`,
    //     meeting: meeting,
    //     createdAt: currentTime.getTime(),
    //     priority: 'high'
    //   };

    //   setPersistentNotifications(prev => {
    //     // Remove existing notification for this meeting
    //     const filtered = prev.filter(n => n.meetingId !== meeting.id);
    //     return [...filtered, notification];
    //   });
    // }
  };

  // Dismiss a persistent notification
  const dismissNotification = async (notificationId) => {
    const newDismissed = new Set(dismissedNotifications);
    newDismissed.add(notificationId);
    setDismissedNotifications(newDismissed);
    await saveDismissedNotifications(newDismissed);

    // Remove from current notifications
    setPersistentNotifications(prev =>
      prev.filter(n => n.id !== notificationId)
    );
  };

  // Update persistent notifications based on current meetings
  const updatePersistentNotifications = (allMeetings) => {
    const currentTime = nowEAT();

    // Clean up expired notifications (older than 30 minutes after meeting start)
    setPersistentNotifications(prev => {
      return prev.filter(notification => {
        const meetingDate = toEAT(notification.meeting.scheduledAt || notification.meeting.date);
        const timeSinceStart = (currentTime - meetingDate) / (1000 * 60);
        return timeSinceStart < 30; // Keep for 30 minutes after start
      });
    });

    // Create notifications for meetings starting soon
    allMeetings.forEach(meeting => {
      if (meeting.status === 'scheduled') {
        createPersistentNotification(meeting);
      }
    });
  };

  // Update persistent notifications for active meetings
  const updateMeetingNotifications = (allMeetings) => {
    console.log('📋 Processing meetings for notifications');

    // Update persistent notifications for active meetings only
    updatePersistentNotifications(allMeetings);
  };

  const handleJoinMeeting = (meeting) => {
    // Debug: Log the entire meeting object to see what fields are available
    console.log('🔍 Full meeting object:', meeting);
    console.log('🔍 Available fields:', Object.keys(meeting));
    console.log('🔍 Navigation object:', navigation);
    console.log('🔍 Available navigation methods:', Object.keys(navigation));

    const meetingType = meeting.meetingType || meeting.type || meeting.meeting_type || 'virtual'; // Default to virtual
    const meetingDate = toEAT(meeting.scheduledAt || meeting.date);
    const currentTime = nowEAT();
    const meetingDurationMinutes = meeting.duration || 60;
    const meetingEndTime = new Date(meetingDate.getTime() + (meetingDurationMinutes * 60 * 1000));

    const isMeetingActive = currentTime >= meetingDate && currentTime <= meetingEndTime;
    const isMeetingEnded = currentTime > meetingEndTime;
    const isMeetingStartingSoon = (meetingDate - currentTime) <= (10 * 60 * 1000) && currentTime < meetingDate;

    console.log('🎯 Joining meeting:', {
      id: meeting.id,
      title: meeting.title,
      originalType: meeting.type,
      originalMeetingType: meeting.meetingType,
      originalMeeting_type: meeting.meeting_type,
      resolvedType: meetingType,
      isActive: isMeetingActive,
      isEnded: isMeetingEnded,
      isStartingSoon: isMeetingStartingSoon,
      location: meeting.location,
      meetingDate: meetingDate.toISOString(),
      currentTime: currentTime.toISOString()
    });

    // Handle ended meetings
    if (isMeetingEnded) {
      Alert.alert(
        '📝 Meeting Ended',
        `The meeting "${meeting.title}" has ended.\n\nWould you like to view the meeting summary or notes?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'View Summary', onPress: () => {
            navigation.navigate('MeetingSummary', {
              chamaId: chamaId || meeting.chamaId,
              chamaName: chamaName || meeting.chamaName || 'Chama'
            });
          }}
        ]
      );
      return;
    }

    if (meetingType === 'virtual') {
      // Navigate directly to online meeting screen
      console.log('🎥 Navigating to virtual meeting...');

      // Show status-appropriate message
      if (isMeetingStartingSoon) {
        Alert.alert(
          '⏰ Meeting Starting Soon',
          `"${meeting.title}" will start in a few minutes.\n\nYou can join the virtual meeting room now.`,
          [
            { text: 'Wait', style: 'cancel' },
            { text: 'Join Now', onPress: () => {
              console.log('🎥 Navigating to OnlineMeeting with params:', {
                meetingId: meeting.id,
                meetingTitle: meeting.title,
                userRole: getUserRole(),
                meetingData: meeting,
              });

              // Use chama layout navigation for meetings
              console.log('🔄 Using chama layout navigation for online meeting');
              try {
                if (onRouteChange) {
                  console.log('🔄 Using onRouteChange for OnlineMeeting');
                  onRouteChange('online-meeting', 'OnlineMeeting', {
                    meetingId: meeting.id,
                    meetingTitle: meeting.title,
                    userRole: getUserRole(),
                    meetingData: meeting,
                  });
                } else {
                  // Fallback to direct navigation if onRouteChange not available
                  console.log('🔄 Fallback to direct navigation');
                  try {
                    // Check if we're in UserDashboard context
                    if (fromUserDashboard) {
                      console.log('🔄 From UserDashboard - using UserTabs navigation');
                      navigation.navigate('OnlineMeeting', {
                        meetingId: meeting.id,
                        meetingTitle: meeting.title,
                        userRole: getUserRole(),
                        meetingData: meeting,
                      });
                    } else {
                      // We're in chama context
                      navigation.navigate('OnlineMeeting', {
                        meetingId: meeting.id,
                        meetingTitle: meeting.title,
                        userRole: getUserRole(),
                        meetingData: meeting,
                      });
                    }
                  } catch (navError) {
                    console.error('❌ Direct navigation failed:', navError);
                    console.log('🌐 Attempting alternative navigation...');

                    // Try to navigate to chama dashboard first, then to meeting
                    try {
                      if (meeting.chamaId) {
                        console.log('🔄 Navigating via chama dashboard');
                        // Navigate to chama dashboard first
                        navigation.navigate('ChamaDashboard', {
                          screen: 'ChamaTabs',
                          params: {
                            screen: 'OnlineMeeting',
                            params: {
                              meetingId: meeting.id,
                              meetingTitle: meeting.title,
                              userRole: getUserRole(),
                              meetingData: meeting,
                            }
                          }
                        });
                      } else {
                        throw new Error('No chama ID available for navigation');
                      }
                    } catch (altNavError) {
                      console.error('❌ Alternative navigation failed:', altNavError);
                      Alert.alert(
                        'Navigation Error',
                        'Unable to join meeting through app navigation. This may be due to chama membership restrictions.',
                        [
                          { text: 'OK', style: 'default' }
                        ]
                      );
                    }
                  }
                }
              } catch (error) {
                console.error('❌ Navigation error:', error);
                Alert.alert('Navigation Error', `Failed to join meeting: ${error.message}. Please try again.`);
              }
            }}
          ]
        );
      } else {
        console.log('🎥 Direct navigation to OnlineMeeting with params:', {
          meetingId: meeting.id,
          meetingTitle: meeting.title,
          userRole: getUserRole(),
          meetingData: meeting,
        });

        // Use chama layout navigation for meetings
        console.log('🔄 Using chama layout navigation for virtual meeting');
        try {
          if (onRouteChange) {
            console.log('🔄 Using onRouteChange for OnlineMeeting');
            onRouteChange('online-meeting', 'OnlineMeeting', {
              meetingId: meeting.id,
              meetingTitle: meeting.title,
              userRole: getUserRole(),
              meetingData: meeting,
            });
          } else {
            // Fallback to direct navigation if onRouteChange not available
            console.log('🔄 Fallback to direct navigation');
            try {
              // Check if we're in UserDashboard context
              if (fromUserDashboard) {
                console.log('🔄 From UserDashboard - using UserTabs navigation');
                navigation.navigate('OnlineMeeting', {
                  meetingId: meeting.id,
                  meetingTitle: meeting.title,
                  userRole: getUserRole(),
                  meetingData: meeting,
                });
              } else {
                // We're in chama context
                navigation.navigate('OnlineMeeting', {
                  meetingId: meeting.id,
                  meetingTitle: meeting.title,
                  userRole: getUserRole(),
                  meetingData: meeting,
                });
              }
            } catch (navError) {
              console.error('❌ Direct navigation failed:', navError);
              console.log('🔄 Attempting alternative navigation...');

              // Try to navigate to chama dashboard first, then to meeting
              try {
                if (meeting.chamaId) {
                  console.log('🔄 Navigating via chama dashboard');
                  // Navigate to chama dashboard first
                  navigation.navigate('ChamaDashboard', {
                    screen: 'ChamaTabs',
                    params: {
                      screen: 'OnlineMeeting',
                      params: {
                        meetingId: meeting.id,
                        meetingTitle: meeting.title,
                        userRole: getUserRole(),
                        meetingData: meeting,
                      }
                    }
                  });
                } else {
                  throw new Error('No chama ID available for navigation');
                }
              } catch (altNavError) {
                console.error('❌ Alternative navigation failed:', altNavError);
                Alert.alert(
                  'Navigation Error',
                  'Unable to join meeting through app navigation. This may be due to chama membership restrictions.',
                  [
                    { text: 'OK', style: 'default' }
                  ]
                );
              }
            }
          }
        } catch (error) {
          console.error('❌ Navigation error:', error);
          Alert.alert('Navigation Error', 'Failed to join meeting. Please try again.');
        }
      }
    } else if (meetingType === 'physical') {
      // For active physical meetings, navigate directly without showing alert
      if (isMeetingActive || isMeetingStartingSoon) {
        console.log('📍 Active physical meeting detected, navigating directly...');

        try {
          console.log('🔄 Navigating to PhysicalMeeting with params:', {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            userRole: getUserRole(),
            meetingData: meeting,
          });

          // Use chama layout navigation for meetings
          let navigationSuccess = false;

          try {
            if (onRouteChange) {
              console.log('🔄 Using onRouteChange for PhysicalMeeting');
              onRouteChange('physical-meeting', 'PhysicalMeeting', {
                meetingId: meeting.id,
                meetingTitle: meeting.title,
                userRole: getUserRole(),
                meetingData: meeting,
                chamaId: chamaId,
              });
              navigationSuccess = true;
              console.log('✅ Chama layout navigation successful');
            } else {
              // Fallback to direct navigation if onRouteChange not available
              console.log('🔄 Fallback to direct navigation');
              navigation.navigate('PhysicalMeeting', {
                meetingId: meeting.id,
                meetingTitle: meeting.title,
                userRole: getUserRole(),
                meetingData: meeting,
                chamaId: chamaId,
              });
              navigationSuccess = true;
              console.log('✅ Direct navigation successful');
            }
          } catch (error) {
            console.error('❌ Navigation failed:', error);
          }

          if (navigationSuccess) {
            return; // Exit early to avoid showing the alert
          } else {
            console.error('❌ All navigation methods failed');
            Alert.alert('Navigation Error', 'Failed to join meeting. Please try again.');
            return;
          }
        } catch (error) {
          console.error('❌ Navigation error:', error);
          Alert.alert('Navigation Error', 'Failed to join meeting. Please try again.');
          return;
        }
      }

      // For non-active physical meetings, show the alert with options
      console.log('📍 Showing physical meeting details...');
      const statusMessage = isMeetingActive
        ? 'The meeting is currently in progress!'
        : isMeetingStartingSoon
          ? 'The meeting will start soon. Please head to the location.'
          : 'Please arrive at the location on time.';

      Alert.alert(
        '📍 Physical Meeting',
        `Meeting: ${meeting.title}\n\nLocation: ${meeting.location}\n\nTime: ${formatMeetingTime(meeting.scheduledAt || meeting.date)} EAT\n\n${statusMessage}`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Get Directions',
            onPress: () => {
              // TODO: Integrate with maps app
              Alert.alert('Directions', 'Map integration will be implemented here.');
            }
          },
          {
            text: 'Add to Calendar',
            onPress: () => handleAddToCalendar(meeting)
          },
          {
            text: 'Join Meeting',
            style: 'default',
            onPress: () => {
              console.log('📍 Navigating to PhysicalMeeting from alert...');

              try {
                if (onRouteChange) {
                  console.log('🔄 Using onRouteChange for PhysicalMeeting from alert');
                  onRouteChange('physical-meeting', 'PhysicalMeeting', {
                    meetingId: meeting.id,
                    meetingTitle: meeting.title,
                    userRole: getUserRole(),
                    meetingData: meeting,
                    chamaId: chamaId,
                  });
                } else {
                  // Fallback to direct navigation
                  const rootNavigation = navigation.getParent?.() || navigation;
                  rootNavigation.navigate('PhysicalMeeting', {
                    meetingId: meeting.id,
                    meetingTitle: meeting.title,
                    userRole: getUserRole(),
                    meetingData: meeting,
                    chamaId: chamaId,
                  });
                }

                console.log('✅ Successfully navigated to PhysicalMeeting from alert');
              } catch (error) {
                console.error('❌ Navigation error from alert:', error);
                Alert.alert('Navigation Error', 'Failed to join meeting. Please try again.');
              }
            }
          },
        ]
      );
    } else if (meetingType === 'hybrid') {
      // Show options for hybrid meeting
      console.log('🔄 Showing hybrid meeting options...');
      const statusText = isMeetingActive
        ? 'The meeting is currently in progress.'
        : isMeetingStartingSoon
          ? 'The meeting will start soon.'
          : 'Choose how you would like to attend.';

      Alert.alert(
        '🔄 Hybrid Meeting Options',
        `"${meeting.title}"\n\n${statusText}\n\nHow would you like to attend?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: '🎥 Join Online',
            onPress: () => {
              console.log('🎥 User chose online for hybrid meeting');
              console.log('🎥 Navigating to OnlineMeeting with params:', {
                meetingId: meeting.id,
                meetingTitle: meeting.title,
                userRole: getUserRole(),
                meetingData: meeting,
              });

              try {
                if (onRouteChange) {
                  console.log('🔄 Using onRouteChange for OnlineMeeting from hybrid');
                  onRouteChange('online-meeting', 'OnlineMeeting', {
                    meetingId: meeting.id,
                    meetingTitle: meeting.title,
                    userRole: getUserRole(),
                    meetingData: meeting,
                  });
                } else {
                  // Fallback to direct navigation
                  navigation.navigate('OnlineMeeting', {
                    meetingId: meeting.id,
                    meetingTitle: meeting.title,
                    userRole: getUserRole(),
                    meetingData: meeting,
                  });
                }
              } catch (error) {
                console.error('❌ Navigation error:', error);
                Alert.alert('Navigation Error', 'Failed to join meeting. Please try again.');
              }
            }
          },
          {
            text: '📍 Attend Physically',
            onPress: () => {
              console.log('📍 User chose physical for hybrid meeting');
              Alert.alert(
                '📍 Physical Attendance',
                `Location: ${meeting.location}\n\nTime: ${formatMeetingTime(meeting.scheduledAt || meeting.date)} EAT\n\n${statusMessage}`,
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Add to Calendar', onPress: () => handleAddToCalendar(meeting) },
                  {
                    text: 'Join Meeting',
                    style: 'default',
                    onPress: () => {
                      console.log('📍 Navigating to PhysicalMeeting from hybrid choice');

                      // Navigate to PhysicalMeeting screen for hybrid physical attendance
                      console.log('🔄 Navigating to PhysicalMeeting for hybrid meeting...');
                      try {
                        if (onRouteChange) {
                          console.log('🔄 Using onRouteChange for PhysicalMeeting from hybrid');
                          onRouteChange('physical-meeting', 'PhysicalMeeting', {
                            meetingId: meeting.id,
                            meetingTitle: meeting.title,
                            userRole: getUserRole(),
                            meetingData: meeting,
                            chamaId: chamaId,
                          });
                        } else {
                          // Fallback to direct navigation
                          const rootNavigation = navigation.getParent?.() || navigation;
                          rootNavigation.navigate('PhysicalMeeting', {
                            meetingId: meeting.id,
                            meetingTitle: meeting.title,
                            userRole: getUserRole(),
                            meetingData: meeting,
                            chamaId: chamaId,
                          });
                        }

                        console.log('✅ Successfully navigated to PhysicalMeeting for hybrid');
                      } catch (error) {
                        console.error('❌ Navigation error:', error);
                        Alert.alert('Navigation Error', 'Failed to join meeting. Please try again.');
                      }
                    }
                  }
                ]
              );
            }
          },
        ]
      );
    } else {
      // Fallback for unknown meeting types - provide helpful information
      console.log('❓ Unknown meeting type, showing comprehensive info...');
      Alert.alert(
        'Meeting Information',
        `Meeting: ${meeting.title}\nType: ${meetingType}\nLocation: ${meeting.location}\nTime: ${formatMeetingTime(meeting.scheduledAt || meeting.date)} EAT\n\nPlease contact the meeting organizer for specific join instructions.`,
        [
          { text: 'OK' },
          { text: 'Add to Calendar', onPress: () => handleAddToCalendar(meeting) }
        ]
      );
    }
  };

  // Handle adding meeting to calendar
  const handleAddToCalendar = (meeting) => {
    const meetingDate = new Date(meeting.scheduledAt || meeting.date);
    const endDate = new Date(meetingDate.getTime() + (meeting.duration || 60) * 60000);

    Alert.alert(
      'Add to Calendar',
      `Add "${meeting.title}" to your calendar?\n\nDate: ${meetingDate.toLocaleDateString()}\nTime: ${meetingDate.toLocaleTimeString()}\nLocation: ${meeting.location}`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Add to Calendar',
          onPress: () => {
            // Here you would integrate with the device's calendar
            // For now, show a success message
            Alert.alert(
              'Calendar Integration',
              'Calendar integration will be implemented here. The meeting details have been noted.',
              [{ text: 'OK' }]
            );
          }
        },
      ]
    );
  };

  // Helper function to get user role in the chama
  const getUserRole = () => {
    // TODO: Replace this with actual role checking logic
    // For now, return 'chairperson' for testing preview functionality
    // MODERATORS: chairperson, secretary, treasurer (can approve participants)
    return 'chairperson'; // Change this to test different roles: 'chairperson', 'secretary', 'treasurer', 'member'
  };

  // Check if user can preview meetings (chairperson, secretary, or treasurer)
  const canPreviewMeeting = () => {
    const role = getUserRole();
    return role === 'chairperson' || role === 'secretary' || role === 'treasurer';
  };

  // Handle meeting preview for chairpersons and secretaries
  const handlePreviewMeeting = async (meeting) => {
    try {
      const userRole = getUserRole();

      // Call the preview API endpoint
      const response = await ApiService.makeRequest(`/meetings/${meeting.id}/preview?role=${userRole}`, {
        method: 'GET',
      });

      if (response.success) {
        const previewData = response.data;
        const meetingType = previewData.meetingType || meeting.meetingType || meeting.type;

        console.log('🔍 Preview data received:', {
          meetingType,
          hasAccessToken: !!previewData.accessToken,
          hasLocation: !!previewData.location,
          previewData: previewData,
        });

        // Handle different meeting types
        if (meetingType === 'physical') {
          // For physical meetings, navigate directly to PhysicalMeetingScreen
          console.log('🔍 Physical meeting detected, navigating directly to PhysicalMeetingScreen...');

          try {
            console.log('🔄 Attempting navigation to PhysicalMeeting preview...');
            console.log('🔄 Navigation object methods:', Object.keys(navigation));

            // Try different navigation approaches
            let navigationSuccess = false;

            // Navigate to PhysicalMeeting preview
            try {
              // Get the root navigation to access main stack screens
              const rootNavigation = navigation.getParent?.() || navigation;

              console.log('🔄 Root navigation found for preview:', !!rootNavigation);

              // Use chama layout navigation for preview
              if (onRouteChange) {
                console.log('🔄 Using onRouteChange for PhysicalMeeting preview');
                onRouteChange('physical-meeting', 'PhysicalMeeting', {
                  meetingId: meeting.id,
                  meetingTitle: meeting.title,
                  userRole: userRole,
                  isPreview: true,
                  previewData: previewData,
                  meetingData: meeting,
                  chamaId: chamaId,
                });
              } else {
                // Fallback to direct navigation
                rootNavigation.navigate('PhysicalMeeting', {
                  meetingId: meeting.id,
                  meetingTitle: meeting.title,
                  userRole: userRole,
                  isPreview: true,
                  previewData: previewData,
                  meetingData: meeting,
                  chamaId: chamaId,
                });
              }

              console.log('✅ Successfully navigated to PhysicalMeeting preview');
            } catch (error) {
              console.error('❌ Preview navigation error:', error);
              Alert.alert('Navigation Error', 'Failed to open meeting preview. Please try again.');
            }
          } catch (error) {
            console.error('❌ Navigation error:', error);
            Alert.alert('Navigation Error', 'Failed to open meeting details. Please try again.');
          }
        } else {
          // For virtual/hybrid meetings
          if (previewData.fallbackMode) {
            // Handle fallback mode for meetings without LiveKit rooms
            Alert.alert(
              '🎥 Virtual Meeting Preview',
              `Meeting: ${meeting.title}\n\nTime: ${formatMeetingTime(meeting.scheduledAt || meeting.date)} EAT\n\n${previewData.previewMessage || 'This is a virtual meeting.'}\n\nMeeting URL: ${previewData.meetingUrl || 'Will be provided when meeting starts'}`,
              [
                { text: 'Close', style: 'cancel' },
                {
                  text: 'Open Meeting Details',
                  onPress: () => {
                    try {
                      if (onRouteChange) {
                        console.log('🔄 Using onRouteChange for OnlineMeeting fallback preview');
                        onRouteChange('online-meeting', 'OnlineMeeting', {
                          meetingId: meeting.id,
                          meetingTitle: meeting.title,
                          userRole: userRole,
                          isPreview: true,
                          previewData: previewData,
                          meetingData: meeting,
                          fallbackMode: true,
                        });
                      } else {
                        // Fallback to direct navigation
                        navigation.navigate('OnlineMeeting', {
                          meetingId: meeting.id,
                          meetingTitle: meeting.title,
                          userRole: userRole,
                          isPreview: true,
                          previewData: previewData,
                          meetingData: meeting,
                          fallbackMode: true,
                        });
                      }
                    } catch (error) {
                      console.error('❌ Navigation error:', error);
                      Alert.alert('Navigation Error', 'Failed to open meeting details. Please try again.');
                    }
                  }
                }
              ]
            );
          } else {
            // Normal LiveKit-enabled virtual meeting
            console.log('🔍 Navigating to OnlineMeeting preview with params:', {
              meetingId: meeting.id,
              meetingTitle: meeting.title,
              userRole: userRole,
              isPreview: true,
              previewData: previewData,
            });

            try {
              if (onRouteChange) {
                console.log('🔄 Using onRouteChange for OnlineMeeting preview');
                onRouteChange('online-meeting', 'OnlineMeeting', {
                  meetingId: meeting.id,
                  meetingTitle: meeting.title,
                  userRole: userRole,
                  isPreview: true,
                  previewData: previewData,
                });
              } else {
                // Fallback to direct navigation
                navigation.navigate('OnlineMeeting', {
                  meetingId: meeting.id,
                  meetingTitle: meeting.title,
                  userRole: userRole,
                  isPreview: true,
                  previewData: previewData,
                });
              }
            } catch (error) {
              console.error('❌ Navigation error:', error);
              Alert.alert('Navigation Error', 'Failed to join meeting preview. Please try again.');
            }
          }
        }
      } else {
        console.error('❌ Preview Error:', response.error);
        Alert.alert('Preview Error', response.error || 'Failed to generate meeting preview');
      }
    } catch (error) {
      console.error('Failed to preview meeting:', error);
      Alert.alert('Error', 'Failed to preview meeting. Please try again.');
    }
  };

  const handleScheduleMeeting = () => {
    if (onRouteChange) {
      onRouteChange('create-meeting', 'CreateMeeting');
    } else {
      navigation.navigate('CreateMeeting', { chamaId });
    }
  };

  const formatMeetingDate = (dateString) => {
    const eatDate = toEAT(dateString);
    return formatDate(eatDate, 'long');
  };

  const formatMeetingTime = (dateString) => {
    const eatDate = toEAT(dateString);
    return formatDate(eatDate, 'time');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return colors.primary;
      case 'in_progress':
        return colors.warning;
      case 'completed':
        return colors.success;
      case 'cancelled':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  // Joinable meetings banner removed - was yellowish and close to header



  const renderMeeting = ({ item }) => {
    const meetingDate = item.scheduledAt || item.date; // Support both API and mock data
    const meetingDateEAT = toEAT(meetingDate);
    const currentTime = nowEAT();
    const isUpcoming = meetingDateEAT > currentTime;

    // Calculate time difference in minutes
    const timeDiffMinutes = Math.floor((meetingDateEAT - currentTime) / (1000 * 60));
    const meetingDurationMinutes = item.duration || 60;
    const meetingEndTime = new Date(meetingDateEAT.getTime() + (meetingDurationMinutes * 60 * 1000));

    // SIMPLIFIED: Clear meeting states based on actual time
    const isMeetingActive = currentTime >= meetingDateEAT && currentTime <= meetingEndTime;
    const isMeetingEnded = currentTime > meetingEndTime;
    const isMeetingStartingSoon = timeDiffMinutes <= 10 && timeDiffMinutes > 0;
    const hasMeetingStarted = currentTime >= meetingDateEAT;

    // Only allow joining if meeting hasn't ended
    const canJoinMeeting = !isMeetingEnded;

    // Calculate remaining time for active meetings
    const remainingMinutes = isMeetingActive ? Math.floor((meetingEndTime - currentTime) / (1000 * 60)) : 0;

    return (
      <Card style={styles.meetingCard}>
        <View style={styles.meetingHeader}>
          <View style={styles.meetingInfo}>
            <Text style={[styles.meetingTitle, { color: colors.text }]}>
              {item.title}
            </Text>
            {isUserMeetingsView && item.chamaName && (
              <Text style={[styles.chamaName, { color: colors.primary }]}>
                📍 {item.chamaName}
              </Text>
            )}
            <Text style={[styles.meetingDescription, { color: colors.textSecondary }]}>
              {item.description}
            </Text>
          </View>

          <View style={styles.statusContainer}>
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(item.status) + '20' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: getStatusColor(item.status) }
              ]}>
                {item.status.toUpperCase()}
              </Text>
            </View>

            {/* Meeting timing status */}
            {item.status === 'scheduled' && (
              <View style={[
                styles.timingBadge,
                {
                  backgroundColor: isMeetingActive
                    ? colors.success + '20'
                    : canJoinMeeting
                      ? colors.warning + '20'
                      : 'transparent'
                }
              ]}>
                {isMeetingActive && (
                  <Text style={[styles.timingText, { color: colors.success }]}>
                    LIVE
                  </Text>
                )}
                {!isMeetingActive && canJoinMeeting && timeDiffMinutes > 0 && (
                  <Text style={[styles.timingText, { color: colors.warning }]}>
                    {timeDiffMinutes}m
                  </Text>
                )}
                {!isMeetingActive && canJoinMeeting && timeDiffMinutes <= 0 && (
                  <Text style={[styles.timingText, { color: colors.warning }]}>
                    STARTING
                  </Text>
                )}
              </View>
            )}
          </View>
        </View>

        <View style={styles.meetingDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={16} color={colors.primary} />
            <Text style={[styles.detailText, { color: colors.text }]}>
              {formatMeetingDate(meetingDate)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Ionicons name="time" size={16} color={colors.secondary} />
            <Text style={[styles.detailText, { color: colors.text, fontWeight: isUserMeetingsView ? 'bold' : 'normal' }]}>
              {formatMeetingTime(meetingDate)} EAT
            </Text>
            {/* Show time status for user meetings view */}
            {isUserMeetingsView && item.status === 'scheduled' && (
              <Text style={[
                styles.timeStatus,
                {
                  color: isMeetingActive
                    ? colors.success
                    : canJoinMeeting
                      ? colors.warning
                      : colors.textSecondary,
                  fontWeight: 'bold',
                  marginLeft: 8
                }
              ]}>
                {isMeetingActive
                  ? '🔴 LIVE NOW'
                  : canJoinMeeting
                    ? `⏰ ${timeDiffMinutes > 0 ? `${timeDiffMinutes}m to go` : 'Starting soon'}`
                    : timeDiffMinutes < 0
                      ? '✅ Ended'
                      : `📅 ${Math.abs(timeDiffMinutes)}m away`
                }
              </Text>
            )}
          </View>

          <View style={styles.detailRow}>
            <Ionicons
              name={item.type === 'virtual' ? 'videocam' : 'location'}
              size={16}
              color={colors.warning}
            />
            <Text style={[styles.detailText, { color: colors.text }]}>
              {item.location}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Ionicons name="people" size={16} color={colors.success} />
            <Text style={[styles.detailText, { color: colors.text }]}>
              {item.attendees} attendees
            </Text>
          </View>
        </View>

        {item.agenda && item.agenda.length > 0 && (
          <View style={styles.agendaSection}>
            <Text style={[styles.agendaTitle, { color: colors.text }]}>
              Agenda
            </Text>
            {item.agenda.map((agendaItem, index) => (
              <Text key={index} style={[styles.agendaItem, { color: colors.textSecondary }]}>
                {index + 1}. {agendaItem}
              </Text>
            ))}
          </View>
        )}

        {/* ACTION BUTTONS */}
        <View style={styles.cardJoinButtonSection}>
          {isMeetingEnded ? (
            // Show View Summary button for past meetings
            <Button
              title="View Summary"
              onPress={() => navigation.navigate('MeetingSummary', {
                chamaId: chamaId || item.chamaId,
                chamaName: chamaName || item.chamaName || 'Chama'
              })}
              style={[styles.cardJoinButton, styles.summaryButton]}
              textStyle={[styles.cardJoinButtonText, { color: colors.white }]}
              icon={<Ionicons name="document-text" size={18} color={colors.white} />}
            />
          ) : (
            // Show Join button for upcoming/active meetings
            <Button
              title={
                isMeetingActive
                  ? `🔴 JOIN ${item.type?.toUpperCase() || 'MEETING'} NOW - LIVE`
                  : hasMeetingStarted
                    ? `🟡 JOIN ${item.type?.toUpperCase() || 'MEETING'} - IN PROGRESS`
                    : `🟢 JOIN ${item.type?.toUpperCase() || 'MEETING'}`
              }
              onPress={() => handleJoinMeeting(item)}
              disabled={false}
              style={[
                styles.cardJoinButton,
                isMeetingActive
                  ? styles.liveMeetingButton
                  : hasMeetingStarted
                    ? styles.inProgressMeetingButton
                    : isMeetingStartingSoon
                      ? styles.readyMeetingButton
                      : styles.scheduledMeetingButton
              ]}
              textStyle={[styles.cardJoinButtonText, { color: colors.white }]}
              icon={
                <Ionicons
                  name={
                    item.type === 'virtual' || item.type === 'hybrid'
                      ? isMeetingActive ? 'videocam' : 'videocam-outline'
                      : isMeetingActive ? 'location' : 'location-outline'
                  }
                  size={18}
                  color={colors.white}
                />
              }
            />
          )}
        </View>

        {/* Additional action buttons for scheduled meetings */}
        {item.status === 'scheduled' && !isMeetingActive && (
          <View style={styles.actionButtons}>
            <Button
              title="Add to Calendar"
              onPress={() => handleAddToCalendar(item)}
              style={styles.actionButton}
              variant="outline"
              icon={
                <Ionicons
                  name="calendar"
                  size={16}
                  color={colors.primary}
                />
              }
            />

            {/* Preview button for chairpersons and secretaries on virtual/hybrid meetings - Hidden when accessed from user dashboard */}
            {!isUserMeetingsView && (() => {
              const canPreview = canPreviewMeeting();
              const isVirtualOrHybrid = item.type === 'virtual' || item.meetingType === 'virtual' || item.type === 'hybrid' || item.meetingType === 'hybrid';
              // For testing: show preview for all meetings when user can preview
              const shouldShowPreview = canPreview; // Change back to: canPreview && isVirtualOrHybrid for production

              return shouldShowPreview ? (
                <Button
                  title="Preview Room"
                  onPress={() => handlePreviewMeeting(item)}
                  style={[styles.actionButton, styles.previewButton]}
                  variant="outline"
                  icon={
                    <Ionicons
                      name="eye"
                      size={16}
                      color={colors.secondary}
                    />
                  }
                />
              ) : null;
            })()}
          </View>
        )}
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="calendar-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Meetings Found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {isUserMeetingsView
          ? selectedTab === 'upcoming'
            ? 'No upcoming meetings from your chamas'
            : selectedTab === 'past'
            ? 'No past meetings from your chamas'
            : 'No meetings found from your chamas'
          : selectedTab === 'upcoming'
          ? 'No upcoming meetings scheduled'
          : selectedTab === 'past'
          ? 'No past meetings found'
          : 'No meetings have been scheduled yet'
        }
      </Text>

      {!isUserMeetingsView && (
        <Button
          title="Schedule Meeting"
          onPress={handleScheduleMeeting}
          style={styles.scheduleButton}
          icon={<Ionicons name="add" size={20} color={colors.white} />}
        />
      )}
    </View>
  );

  // Render persistent notifications that survive screen reloads
  const renderPersistentNotifications = () => {
    if (persistentNotifications.length === 0) return null;

    return (
      <View style={styles.persistentNotificationsContainer}>
        {persistentNotifications.map((notification) => (
          <View
            key={notification.id}
            style={[
              styles.persistentNotification,
              {
                backgroundColor: notification.type === 'meeting_starting_soon'
                  ? notification.timeText.includes('Starting in')
                    ? '#FFA500' // Orange for starting soon
                    : '#FF4444' // Red for live/started
                  : colors.primary
              }
            ]}
          >
            <View style={styles.persistentNotificationContent}>
              <View style={styles.persistentNotificationIcon}>
                <Ionicons
                  name={
                    notification.timeText.includes('Starting in')
                      ? 'time'
                      : notification.timeText.includes('now')
                        ? 'radio-button-on'
                        : 'play-circle'
                  }
                  size={24}
                  color="white"
                />
              </View>

              <View style={styles.persistentNotificationText}>
                <Text style={styles.persistentNotificationTitle}>
                  {notification.title}
                </Text>
                <Text style={styles.persistentNotificationSubtitle}>
                  {notification.subtitle}
                </Text>
                <Text style={styles.persistentNotificationTime}>
                  {notification.timeText}
                </Text>
              </View>

              <TouchableOpacity
                style={styles.persistentNotificationJoinButton}
                onPress={() => handleJoinMeeting(notification.meeting)}
              >
                <Ionicons name="arrow-forward" size={20} color="white" />
                <Text style={styles.persistentNotificationJoinText}>JOIN</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.persistentNotificationDismissButton}
                onPress={() => dismissNotification(notification.id)}
              >
                <Ionicons name="close" size={18} color="rgba(255, 255, 255, 0.8)" />
              </TouchableOpacity>
            </View>

            {/* Pulsing indicator for live meetings */}
            {!notification.timeText.includes('Starting in') && (
              <View style={[styles.pulsingDot, { backgroundColor: 'rgba(255, 255, 255, 0.8)' }]} />
            )}
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        {/* Tab Icons in Header */}
        <View style={styles.headerTabsContainer}>
          {tabs.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.headerTab,
                {
                  backgroundColor: selectedTab === item.id ? colors.primary : colors.backgroundSecondary,
                  borderColor: colors.border,
                }
              ]}
              onPress={() => setSelectedTab(item.id)}
            >
              <Ionicons
                name={item.icon}
                size={20}
                color={selectedTab === item.id ? colors.white : colors.textSecondary}
              />
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* PERSISTENT NOTIFICATIONS - SURVIVE SCREEN RELOADS */}
      {renderPersistentNotifications()}

      <FlatList
        data={meetings}
        renderItem={renderMeeting}
        keyExtractor={(item) => item.id}
        style={{ flex: 1 }}
        contentContainerStyle={[
          styles.meetingsList,
          meetings.length === 0 && { flexGrow: 1 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={!loading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
      />

      {!isUserMeetingsView && (
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: colors.primary }]}
          onPress={handleScheduleMeeting}
        >
          <Ionicons name="add" size={24} color={colors.white} />
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...shadows.sm,
  },
  headerTabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  headerTab: {
    width: 44,
    height: 44,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },

  meetingsList: {
    padding: spacing.md,
  },
  meetingCard: {
    marginBottom: spacing.md,
  },
  meetingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  meetingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  meetingTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  chamaName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
    fontStyle: 'italic',
  },
  meetingDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: typography.lineHeight.relaxed,
  },
  statusContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  timingBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    alignSelf: 'flex-start',
  },
  timingText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
    textTransform: 'uppercase',
  },
  activeMeetingButton: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  joinButtonContainer: {
    marginTop: spacing.md,
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    borderColor: '#FFA500',
    backgroundColor: '#FFF8DC',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  liveMeetingContainer: {
    borderColor: '#FF4444',
    backgroundColor: '#FFE6E6',
    shadowColor: '#FF4444',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  pulseIndicator: {
    position: 'absolute',
    top: -20,
    right: -20,
    width: 40,
    height: 40,
    borderRadius: 20,
    opacity: 0.3,
  },
  prominentJoinButton: {
    width: '100%',
    paddingVertical: spacing.lg,
    borderRadius: borderRadius.lg,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  liveMeetingButton: {
    backgroundColor: '#FF4444',
  },
  countdownMeetingButton: {
    backgroundColor: '#FFA500',
  },
  readyMeetingButton: {
    backgroundColor: '#28A745',
  },
  inProgressMeetingButton: {
    backgroundColor: '#FFA500',
  },
  scheduledMeetingButton: {
    backgroundColor: '#6C757D',
  },
  endedMeetingButton: {
    backgroundColor: '#495057',
  },
  summaryButton: {
    backgroundColor: '#6C63FF',
  },
  disabledMeetingButton: {
    backgroundColor: '#ADB5BD',
    opacity: 0.6,
  },
  prominentJoinButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  joinButtonSubtext: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Prominent banner styles
  joinableBanner: {
    margin: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  bannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
  },
  bannerIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  bannerText: {
    flex: 1,
    marginRight: spacing.md,
  },
  bannerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: 'white',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  bannerSubtitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: 'white',
    marginTop: spacing.xs,
  },
  bannerTime: {
    fontSize: typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  bannerJoinButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  bannerJoinButtonText: {
    color: 'white',
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    textTransform: 'uppercase',
  },
  pulsingDot: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    width: 12,
    height: 12,
    borderRadius: 6,
    opacity: 0.8,
  },
  meetingDetails: {
    marginBottom: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
  },
  timeStatus: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
    marginLeft: spacing.xs,
  },
  agendaSection: {
    marginBottom: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  agendaTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
  },
  agendaItem: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
    lineHeight: typography.lineHeight.relaxed,
  },
  cardJoinButtonSection: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  cardJoinButton: {
    width: '100%',
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  cardJoinButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  actionButtons: {
    marginTop: spacing.md,
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  previewButton: {
    flex: 0.8,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  scheduleButton: {
    marginTop: spacing.md,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
  },
  // Persistent notification styles
  persistentNotificationsContainer: {
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
  },
  persistentNotification: {
    borderRadius: borderRadius.lg,
    marginBottom: spacing.sm,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  persistentNotificationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
  },
  persistentNotificationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  persistentNotificationText: {
    flex: 1,
    marginRight: spacing.md,
  },
  persistentNotificationTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: 'white',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  persistentNotificationSubtitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: 'white',
    marginTop: spacing.xs,
  },
  persistentNotificationTime: {
    fontSize: typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  persistentNotificationJoinButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    marginRight: spacing.sm,
  },
  persistentNotificationJoinText: {
    color: 'white',
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    textTransform: 'uppercase',
  },
  persistentNotificationDismissButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.full,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
});

export default ChamaMeetingsScreen;
