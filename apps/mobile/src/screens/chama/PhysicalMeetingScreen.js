import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import api from '../../services/api';
import Toast from 'react-native-toast-message';

const PhysicalMeetingScreen = ({ route, navigation }) => {
  const {
    meetingId,
    meetingTitle,
    userRole = 'member',
    isPreview = false,
    previewData = null,
    meetingData: initialMeetingData = null,
    chamaId
  } = route.params;
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  console.log('🏢 PhysicalMeetingScreen loaded with params:', {
    meetingId,
    meetingTitle,
    userRole,
    isPreview,
    hasPreviewData: !!previewData,
    hasInitialMeetingData: !!initialMeetingData
  });
  
  const [loading, setLoading] = useState(false);
  const [meetingData, setMeetingData] = useState(initialMeetingData);
  const [attendanceList, setAttendanceList] = useState([]);
  const [chamaMembers, setChamaMembers] = useState([]);
  const [memberAttendance, setMemberAttendance] = useState({}); // Track attendance status for each member
  const [notes, setNotes] = useState('');
  const [uploadedDocuments, setUploadedDocuments] = useState([]);
  const [isSaving, setIsSaving] = useState(false);
  const [hasLoadedData, setHasLoadedData] = useState(false);

  useEffect(() => {
    if (!hasLoadedData) {
      console.log('🏢 Loading PhysicalMeetingScreen data for the first time...');
      setHasLoadedData(true);
      loadMeetingData();
      loadChamaMembers();
      loadAttendanceList();
      loadMeetingDocuments();
    }
  }, [hasLoadedData]);

  const loadMeetingData = async () => {
    try {
      setLoading(true);
      const response = await api.makeRequest(`/meetings/${meetingId}`);
      if (response.success && response.data) {
        setMeetingData(response.data);
      } else {
        // Use initial meeting data if API call fails
        console.log('📊 Using initial meeting data from navigation params');
      }
    } catch (error) {
      console.error('Failed to load meeting data:', error);
      // Don't show error toast for 404s - just use initial data
      if (error.message && error.message.includes('404')) {
        console.log('📊 Meeting endpoint not found (404), using initial data');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to load meeting details',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const loadChamaMembers = async () => {
    try {
      const currentChamaId = chamaId || meetingData?.chamaId;
      if (!currentChamaId) {
        console.log('👥 No chamaId available, cannot load chama members');
        return;
      }

      const response = await api.makeRequest(`/chamas/${currentChamaId}/members`);
      if (response.success && response.data) {
        const members = response.data || [];
        console.log('👥 Loaded chama members:', members.length);
        setChamaMembers(members);

        // Initialize attendance tracking for all members
        const initialAttendance = {};
        members.forEach(member => {
          const userId = member.user_id || member.id;
          initialAttendance[userId] = false; // Default to not present
        });
        setMemberAttendance(initialAttendance);
      } else {
        console.log('👥 No chama members data received');
        setChamaMembers([]);
        setMemberAttendance({});
      }
    } catch (error) {
      console.error('Failed to load chama members:', error);
      setChamaMembers([]);
      setMemberAttendance({});
    }
  };

  const loadAttendanceList = async () => {
    try {
      const response = await api.makeRequest(`/meetings/${meetingId}/attendance`);
      if (response.success && response.data) {
        setAttendanceList(response.data);
        console.log('📋 Loaded attendance list:', response.data.length, 'attendees');

        // Update memberAttendance state based on existing attendance records
        const attendanceMap = {};
        response.data.forEach(attendance => {
          if (attendance.userId && attendance.isPresent) {
            attendanceMap[attendance.userId] = true;
          }
        });
        setMemberAttendance(prev => ({ ...prev, ...attendanceMap }));
      } else {
        console.log('📝 No attendance data available yet');
        setAttendanceList([]);
      }
    } catch (error) {
      console.error('Failed to load attendance list:', error);
      // Don't show error toast for expected cases
      if (!error.message?.includes('404')) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to load attendance list',
        });
      }
      setAttendanceList([]);
    }
  };





  const loadMeetingDocuments = async () => {
    try {
      const response = await api.makeRequest(`/meetings/${meetingId}/documents`);
      if (response.success && response.data) {
        const documents = response.data || [];
        console.log('📄 Loaded meeting documents:', documents.length);
        setUploadedDocuments(documents);
      } else {
        console.log('📄 No meeting documents found');
        setUploadedDocuments([]);
      }
    } catch (error) {
      console.error('Failed to load meeting documents:', error);
      setUploadedDocuments([]);
    }
  };



  // Secretary function to toggle member attendance
  const toggleMemberAttendance = async (userId) => {
    if (userRole !== 'secretary') {
      Toast.show({
        type: 'error',
        text1: 'Access Denied',
        text2: 'Only the secretary can mark attendance',
      });
      return;
    }

    try {
      const currentStatus = memberAttendance[userId] || false;
      const newStatus = !currentStatus;

      // Update local state immediately for UI responsiveness
      setMemberAttendance(prev => ({
        ...prev,
        [userId]: newStatus
      }));

      // Save to backend immediately
      const response = await api.makeRequest(`/meetings/${meetingId}/attendance`, {
        method: 'POST',
        body: {
          userId: userId,
          attendanceType: 'physical',
          isPresent: newStatus,
        },
      });

      if (response.success) {
        console.log(`✅ Attendance updated for user ${userId}: ${newStatus ? 'Present' : 'Absent'}`);

        // Show subtle feedback
        Toast.show({
          type: 'success',
          text1: newStatus ? 'Marked Present' : 'Marked Absent',
          text2: `Attendance updated for member`,
          visibilityTime: 2000,
        });

        // Refresh attendance list to sync with backend
        await loadAttendanceList();
      } else {
        // Revert local state if backend save failed
        setMemberAttendance(prev => ({
          ...prev,
          [userId]: currentStatus
        }));

        Toast.show({
          type: 'error',
          text1: 'Save Failed',
          text2: 'Failed to save attendance. Please try again.',
        });
      }
    } catch (error) {
      console.error('Failed to update attendance:', error);

      // Revert local state on error
      const currentStatus = memberAttendance[userId] || false;
      setMemberAttendance(prev => ({
        ...prev,
        [userId]: currentStatus
      }));

      Toast.show({
        type: 'error',
        text1: 'Update Failed',
        text2: 'Failed to update attendance. Please check your connection.',
      });
    }
  };

  // Save meeting data including attendance
  const saveMeetingData = async () => {
    try {
      setIsSaving(true);
      let savedItems = [];

      // Since attendance is now saved immediately when marked,
      // we just need to ensure all current attendance is synced
      console.log('💾 Syncing final attendance data...');

      // Get all present members
      const presentMembers = Object.entries(memberAttendance)
        .filter(([, isPresent]) => isPresent)
        .map(([userId]) => userId);

      // Sync attendance for all members (present and absent)
      const attendancePromises = chamaMembers.map(member => {
        const userId = member.user_id || member.id;
        const isPresent = memberAttendance[userId] || false;

        return api.makeRequest(`/meetings/${meetingId}/attendance`, {
          method: 'POST',
          body: {
            userId: userId,
            attendanceType: 'physical',
            isPresent: isPresent,
          },
        });
      });

      await Promise.all(attendancePromises);
      savedItems.push('attendance');

      // Save meeting notes if any
      if (notes.trim() && canTakeNotes) {
        await api.makeRequest(`/meetings/${meetingId}/minutes`, {
          method: 'POST',
          body: {
            content: notes.trim(),
            status: 'draft',
            meetingId: meetingId,
            authorRole: userRole,
          },
        });
        savedItems.push('notes');
      }

      // Update meeting status to indicate it has been conducted
      await api.makeRequest(`/meetings/${meetingId}`, {
        method: 'PATCH',
        body: {
          status: 'completed',
          conductedAt: new Date().toISOString(),
          attendeeCount: presentMembers.length,
        },
      });
      savedItems.push('meeting status');

      Toast.show({
        type: 'success',
        text1: 'Meeting Data Saved',
        text2: `Successfully saved: ${savedItems.join(', ')}`,
      });

      // Refresh all data
      await Promise.all([
        loadAttendanceList(),
        loadMeetingDocuments(),
      ]);

    } catch (error) {
      console.error('Failed to save meeting data:', error);
      Toast.show({
        type: 'error',
        text1: 'Save Failed',
        text2: 'Failed to save meeting data. Please try again.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Exit meeting function
  const exitMeeting = () => {
    Alert.alert(
      'Exit Meeting',
      'Are you sure you want to exit the meeting?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Exit',
          style: 'default',
          onPress: () => {
            navigation.goBack();
          },
        },
      ]
    );
  };

  // Save and exit function
  const saveAndExit = async () => {
    Alert.alert(
      'Save and Exit',
      'Do you want to save the meeting data before exiting?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Exit Without Saving',
          style: 'destructive',
          onPress: () => navigation.goBack(),
        },
        {
          text: 'Save and Exit',
          style: 'default',
          onPress: async () => {
            await saveMeetingData();
            navigation.goBack();
          },
        },
      ]
    );
  };



  const uploadDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const document = result.assets[0];

        // Create FormData for file upload
        const formData = new FormData();

        console.log('📄 Document details:', {
          uri: document.uri,
          type: document.mimeType,
          name: document.name,
          size: document.size
        });

        // Convert data URI to File object for web compatibility
        let fileToUpload;
        if (document.uri.startsWith('data:')) {
          // Convert data URI to Blob/File for web
          const response = await fetch(document.uri);
          const blob = await response.blob();
          fileToUpload = new File([blob], document.name, { type: document.mimeType });
        } else {
          // For React Native, use the original format
          fileToUpload = {
            uri: document.uri,
            type: document.mimeType,
            name: document.name,
          };
        }

        formData.append('file', fileToUpload);
        formData.append('meetingId', meetingId);
        formData.append('documentType', 'meeting_document');
        formData.append('description', `Document uploaded during physical meeting: ${meetingTitle}`);

        console.log('📄 FormData created, about to upload...');

        // Upload to backend
        const response = await api.makeRequest(`/meetings/${meetingId}/documents`, {
          method: 'POST',
          body: formData,
          // Don't set Content-Type header - let browser set it automatically for FormData
        });

        if (response.success) {
          const newDoc = {
            id: response.data.id || Date.now().toString(),
            name: document.name,
            size: document.size,
            type: document.mimeType,
            uri: document.uri,
            uploadedAt: new Date().toISOString(),
            url: response.data.url,
          };

          setUploadedDocuments(prev => [...prev, newDoc]);

          Toast.show({
            type: 'success',
            text1: 'Document Uploaded',
            text2: `${document.name} has been uploaded to the meeting`,
          });
        } else {
          throw new Error(response.error || 'Upload failed');
        }
      }
    } catch (error) {
      console.error('Failed to upload document:', error);
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: error.message || 'Failed to upload document to server',
      });
    }
  };

  const removeDocument = (docId) => {
    Alert.alert(
      'Remove Document',
      'Are you sure you want to remove this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // Remove from backend first
              const response = await api.makeRequest(`/meetings/${meetingId}/documents/${docId}`, {
                method: 'DELETE',
              });

              if (response.success) {
                // Remove from local state only if backend deletion succeeded
                setUploadedDocuments(prev => prev.filter(doc => doc.id !== docId));

                Toast.show({
                  type: 'success',
                  text1: 'Document Removed',
                  text2: 'Document has been deleted from the meeting',
                });
              } else {
                throw new Error(response.error || 'Failed to delete document');
              }
            } catch (error) {
              console.error('Failed to remove document:', error);
              Toast.show({
                type: 'error',
                text1: 'Delete Failed',
                text2: error.message || 'Failed to remove document from server',
              });
            }
          },
        },
      ]
    );
  };

  const saveNotes = async () => {
    if (!notes.trim()) {
      Toast.show({
        type: 'warning',
        text1: 'No Notes',
        text2: 'Please enter some notes before saving',
      });
      return;
    }

    try {
      // Save notes to backend
      const response = await api.makeRequest(`/meetings/${meetingId}/minutes`, {
        method: 'POST',
        body: {
          content: notes.trim(),
          status: 'draft',
          meetingId: meetingId,
          authorRole: userRole,
        },
      });

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Notes Saved',
          text2: 'Meeting notes have been saved to the database',
        });
      } else {
        throw new Error(response.error || 'Failed to save notes');
      }
    } catch (error) {
      console.error('Failed to save notes:', error);
      Toast.show({
        type: 'error',
        text1: 'Save Failed',
        text2: error.message || 'Failed to save notes to server',
      });
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const canTakeNotes = userRole === 'secretary' || userRole === 'chairperson';

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading meeting details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Meeting Header */}
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          {isPreview && (
            <View style={[styles.previewBadge, { backgroundColor: colors.warning + '20' }]}>
              <Ionicons name="eye" size={16} color={colors.warning} />
              <Text style={[styles.previewText, { color: colors.warning }]}>
                PREVIEW MODE
              </Text>
            </View>
          )}
          <Text style={[styles.meetingTitle, { color: colors.text }]}>
            {meetingTitle}
          </Text>
          <Text style={[styles.meetingType, { color: colors.textSecondary }]}>
            Physical Meeting
          </Text>
          {meetingData?.location && (
            <View style={styles.locationContainer}>
              <Ionicons name="location" size={16} color={colors.primary} />
              <Text style={[styles.locationText, { color: colors.textSecondary }]}>
                {meetingData.location}
              </Text>
            </View>
          )}
        </View>

        {/* Attendance Section */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <View style={styles.attendanceHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Meeting Attendance
            </Text>
            {userRole === 'secretary' && (
              <Text style={[styles.attendanceSubtitle, { color: colors.textSecondary }]}>
                Mark members as present by tapping their names
              </Text>
            )}
          </View>

          {/* Member List for Attendance */}
          {chamaMembers.length > 0 ? (
            <View style={styles.membersList}>
              {chamaMembers.map((member) => {
                const userData = member.user || member;
                const userId = member.user_id || member.id;
                const firstName = userData.first_name || member.first_name || '';
                const lastName = userData.last_name || member.last_name || '';
                const fullName = `${firstName} ${lastName}`.trim();
                const displayName = fullName || userData.name || member.name || userData.email?.split('@')[0] || `Member ${userId?.slice(-4)}`;
                const isPresent = memberAttendance[userId] || false;
                const memberRole = member.role || 'Member';

                return (
                  <TouchableOpacity
                    key={userId}
                    style={[
                      styles.memberItem,
                      {
                        backgroundColor: isPresent ? colors.success + '10' : colors.background,
                        borderColor: isPresent ? colors.success : colors.border,
                      }
                    ]}
                    onPress={() => toggleMemberAttendance(userId)}
                    disabled={userRole !== 'secretary'}
                  >
                    <View style={styles.memberInfo}>
                      <View style={[styles.memberAvatar, { backgroundColor: colors.primary + '20' }]}>
                        <Text style={[styles.memberAvatarText, { color: colors.primary }]}>
                          {firstName?.[0]?.toUpperCase() || 'M'}{lastName?.[0]?.toUpperCase() || ''}
                        </Text>
                      </View>
                      <View style={styles.memberDetails}>
                        <Text style={[styles.memberName, { color: colors.text }]}>
                          {displayName}
                        </Text>
                        <Text style={[styles.memberRole, { color: colors.textSecondary }]}>
                          {memberRole}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.attendanceStatus}>
                      {isPresent ? (
                        <View style={styles.presentIndicator}>
                          <Ionicons name="checkmark-circle" size={24} color={colors.success} />
                          <Text style={[styles.presentText, { color: colors.success }]}>Present</Text>
                        </View>
                      ) : (
                        <View style={styles.absentIndicator}>
                          <Ionicons name="ellipse-outline" size={24} color={colors.textSecondary} />
                          <Text style={[styles.absentText, { color: colors.textSecondary }]}>Absent</Text>
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="people-outline" size={48} color={colors.textSecondary} />
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                Loading chama members...
              </Text>
            </View>
          )}

          {/* Attendance Summary */}
          {chamaMembers.length > 0 && (
            <View style={[styles.attendanceSummary, { backgroundColor: colors.background }]}>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Members:</Text>
                <Text style={[styles.summaryValue, { color: colors.text }]}>{chamaMembers.length}</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Present:</Text>
                <Text style={[styles.summaryValue, { color: colors.success }]}>
                  {Object.values(memberAttendance).filter(Boolean).length}
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Absent:</Text>
                <Text style={[styles.summaryValue, { color: colors.error }]}>
                  {chamaMembers.length - Object.values(memberAttendance).filter(Boolean).length}
                </Text>
              </View>
            </View>
          )}
        </View>



        {/* Notes Section (for Secretary/Chairperson) */}
        {canTakeNotes && (
          <View style={[styles.section, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Meeting Notes
            </Text>
            
            <TextInput
              style={[styles.notesInput, { 
                backgroundColor: colors.background,
                color: colors.text,
                borderColor: colors.border 
              }]}
              placeholder="Enter meeting notes, decisions, and action items..."
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={8}
              value={notes}
              onChangeText={setNotes}
              textAlignVertical="top"
            />
            
            <TouchableOpacity
              style={[styles.saveNotesButton, { backgroundColor: colors.primary }]}
              onPress={saveNotes}
            >
              <Ionicons name="save" size={20} color="white" />
              <Text style={styles.saveNotesText}>Save Notes</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Documents Section */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Documents
          </Text>
          
          <TouchableOpacity
            style={[styles.uploadButton, { borderColor: colors.primary }]}
            onPress={uploadDocument}
          >
            <Ionicons name="cloud-upload" size={24} color={colors.primary} />
            <Text style={[styles.uploadButtonText, { color: colors.primary }]}>
              Upload Document
            </Text>
          </TouchableOpacity>

          {uploadedDocuments.length > 0 && (
            <View style={styles.documentsList}>
              {uploadedDocuments.map((doc) => (
                <View key={doc.id} style={[styles.documentItem, { borderBottomColor: colors.border }]}>
                  <View style={styles.documentInfo}>
                    <Ionicons name="document" size={20} color={colors.primary} />
                    <View style={styles.documentDetails}>
                      <Text style={[styles.documentName, { color: colors.text }]} numberOfLines={1}>
                        {doc.name}
                      </Text>
                      <Text style={[styles.documentSize, { color: colors.textSecondary }]}>
                        {formatFileSize(doc.size)}
                      </Text>
                    </View>
                  </View>
                  <TouchableOpacity
                    style={styles.removeDocButton}
                    onPress={() => removeDocument(doc.id)}
                  >
                    <Ionicons name="trash" size={18} color={colors.error} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: colors.surface }]}>
        {userRole === 'secretary' && (
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={saveMeetingData}
            disabled={isSaving}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Ionicons name="save" size={20} color="white" />
            )}
            <Text style={styles.saveButtonText}>
              {isSaving ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.exitButton,
            {
              backgroundColor: userRole === 'secretary' || userRole === 'chairperson'
                ? colors.warning
                : colors.textSecondary,
              flex: userRole === 'secretary' ? 1 : 2
            }
          ]}
          onPress={userRole === 'secretary' || userRole === 'chairperson' ? saveAndExit : exitMeeting}
        >
          <Ionicons name="exit" size={20} color="white" />
          <Text style={styles.exitButtonText}>
            {userRole === 'secretary' || userRole === 'chairperson' ? 'Save & Exit' : 'Exit Meeting'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  header: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  previewBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 12,
  },
  previewText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
  meetingTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  meetingType: {
    fontSize: 16,
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    marginLeft: 8,
  },
  section: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  attendanceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
  },
  attendanceButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  attendanceMarked: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
  },
  attendanceMarkedText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  attendanceList: {
    marginTop: 20,
  },
  attendanceListTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  attendanceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  attendeeName: {
    flex: 1,
    fontSize: 14,
    marginLeft: 8,
  },
  attendanceTime: {
    fontSize: 12,
  },
  notesInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    minHeight: 120,
    marginBottom: 16,
  },
  saveNotesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  saveNotesText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  documentsList: {
    marginTop: 16,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  documentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  documentDetails: {
    marginLeft: 12,
    flex: 1,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '500',
  },
  documentSize: {
    fontSize: 12,
    marginTop: 2,
  },
  removeDocButton: {
    padding: 8,
  },
  // New attendance styles
  attendanceHeader: {
    marginBottom: 16,
  },
  attendanceSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  membersList: {
    gap: 8,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  memberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberAvatarText: {
    fontSize: 14,
    fontWeight: '600',
  },
  memberDetails: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '500',
  },
  memberRole: {
    fontSize: 12,
    marginTop: 2,
  },
  attendanceStatus: {
    alignItems: 'center',
  },
  presentIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  presentText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  absentIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  absentText: {
    fontSize: 12,
    marginLeft: 4,
  },
  attendanceSummary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  // Action buttons styles
  actionButtons: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  saveButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  exitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  exitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PhysicalMeetingScreen;
