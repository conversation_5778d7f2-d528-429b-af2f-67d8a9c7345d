import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';

const MyChamasScreen = ({ navigation, route }) => {
  const { theme, user, switchToChamaDashboard } = useApp();
  const colors = getThemeColors(theme);

  const [chamas, setChamas] = useState([]);
  const [filteredChamas, setFilteredChamas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [selectedCategory, setSelectedCategory] = useState('all'); // 'all', 'chama', 'contribution'

  // Dynamic filters based on selected category
  const chamaFilters = [
    { id: 'all', name: 'All', icon: 'grid' },
    { id: 'savings', name: 'Savings', icon: 'wallet' },
    { id: 'investment', name: 'Investment', icon: 'trending-up' },
    { id: 'welfare', name: 'Welfare', icon: 'heart' },
    { id: 'business', name: 'Business', icon: 'briefcase' },
  ];

  const contributionFilters = [
    { id: 'all', name: 'All', icon: 'grid' },
    { id: 'emergency', name: 'Emergency', icon: 'warning' },
    { id: 'medical', name: 'Medical', icon: 'medical' },
    { id: 'education', name: 'Education', icon: 'school' },
    { id: 'community', name: 'Community', icon: 'people' },
    { id: 'personal', name: 'Personal', icon: 'person' },
  ];

  // Get current filters based on selected category
  const getCurrentFilters = () => {
    if (selectedCategory === 'contribution') {
      return contributionFilters;
    } else if (selectedCategory === 'chama') {
      return chamaFilters;
    } else {
      // For 'all' category, show combined unique filters
      return [
        { id: 'all', name: 'All', icon: 'grid' },
        { id: 'savings', name: 'Savings', icon: 'wallet' },
        { id: 'investment', name: 'Investment', icon: 'trending-up' },
        { id: 'welfare', name: 'Welfare', icon: 'heart' },
        { id: 'business', name: 'Business', icon: 'briefcase' },
        { id: 'emergency', name: 'Emergency', icon: 'warning' },
        { id: 'medical', name: 'Medical', icon: 'medical' },
        { id: 'education', name: 'Education', icon: 'school' },
        { id: 'community', name: 'Community', icon: 'people' },
        { id: 'personal', name: 'Personal', icon: 'person' },
      ];
    }
  };

  const sortOptions = [
    { id: 'recent', name: 'Most Recent', icon: 'time' },
    { id: 'name', name: 'Name A-Z', icon: 'text' },
    { id: 'members', name: 'Most Members', icon: 'people' },
    { id: 'funds', name: 'Highest Funds', icon: 'cash' },
  ];

  useEffect(() => {
    loadUserChamas();
  }, []);

  useEffect(() => {
    applyFiltersAndSort();
  }, [chamas, searchQuery, selectedFilter, sortBy, selectedCategory]);

  // Reset selectedFilter when category changes
  useEffect(() => {
    setSelectedFilter('all');
  }, [selectedCategory]);

  // Refresh chamas when screen is focused (e.g., after creating a new chama)
  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.refresh) {
        loadUserChamas();
        // Clear the refresh param to avoid unnecessary refreshes
        navigation.setParams({ refresh: undefined });
      }
    }, [route.params?.refresh])
  );

  const loadUserChamas = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getUserChamas(50, 0);

      if (response.success) {
        console.log('📋 Loaded chamas:', response.data?.length || 0);
        console.log('🔍 Sample chama data:', response.data?.[0]);
        setChamas(response.data || []);
      } else {
        throw new Error(response.error || 'Failed to load chamas');
      }
    } catch (error) {
      console.error('Failed to load user chamas:', error);
      Alert.alert('Error', 'Failed to load your chamas. Please try again.');
      setChamas([]);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserChamas();
    setRefreshing(false);
  };

  const applyFiltersAndSort = () => {
    let filtered = [...chamas];

    // Apply category filter first (chama vs contribution)
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(chama => {
        const isContribution = chama.category === 'contribution';
        return selectedCategory === 'contribution' ? isContribution : !isContribution;
      });
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(chama =>
        chama.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chama.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chama.county.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chama.town.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply type filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(chama => chama.type === selectedFilter);
    }

    // Apply sorting
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'members':
        filtered.sort((a, b) => (b.currentMembers || 0) - (a.currentMembers || 0));
        break;
      case 'funds':
        filtered.sort((a, b) => (b.totalFunds || 0) - (a.totalFunds || 0));
        break;
      case 'recent':
      default:
        filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
    }

    setFilteredChamas(filtered);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount || 0);
  };

  const getUserRole = (chama) => {
    // For now, return 'member' as default
    // In the future, this should be fetched from the chama membership data
    if (chama.createdBy === user?.id) {
      return 'chairperson';
    }
    return 'member';
  };

  const renderHeader = () => {
    // Calculate counts for each category
    console.log('🔍 All chamas data:', chamas.map(c => ({ name: c.name, category: c.category })));
    const chamaCount = chamas.filter(c => c.category !== 'contribution').length;
    const contributionCount = chamas.filter(c => c.category === 'contribution').length;
    console.log('📊 Counts - Chamas:', chamaCount, 'Contributions:', contributionCount);

    return (
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        {/* Search Input */}
        <Input
          placeholder="Search your groups..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search"
          style={styles.searchInput}
        />

        {/* Category Tabs - Compact Three Columns */}
        <View style={[styles.categoryTabs, { marginTop: spacing.lg }]}>
          <TouchableOpacity
            style={[
              styles.categoryTab,
              {
                backgroundColor: selectedCategory === 'all' ? colors.primary : 'transparent',
                borderColor: selectedCategory === 'all' ? colors.primary : colors.border,
                borderWidth: selectedCategory === 'all' ? 2 : 1,
              }
            ]}
            onPress={() => setSelectedCategory('all')}
          >
            <Ionicons
              name="apps"
              size={16}
              color={selectedCategory === 'all' ? colors.white : colors.textSecondary}
            />
            <Text style={[
              styles.categoryTabText,
              { color: selectedCategory === 'all' ? colors.white : colors.text }
            ]}>
              All ({chamas.length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.categoryTab,
              {
                backgroundColor: selectedCategory === 'chama' ? colors.primary : 'transparent',
                borderColor: selectedCategory === 'chama' ? colors.primary : colors.border,
                borderWidth: selectedCategory === 'chama' ? 2 : 1,
              }
            ]}
            onPress={() => setSelectedCategory('chama')}
          >
            <Ionicons
              name="people"
              size={16}
              color={selectedCategory === 'chama' ? colors.white : colors.primary}
            />
            <Text style={[
              styles.categoryTabText,
              { color: selectedCategory === 'chama' ? colors.white : colors.text }
            ]}>
              Chamas ({chamaCount})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.categoryTab,
              {
                backgroundColor: selectedCategory === 'contribution' ? colors.success : 'transparent',
                borderColor: selectedCategory === 'contribution' ? colors.success : colors.border,
                borderWidth: selectedCategory === 'contribution' ? 2 : 1,
              }
            ]}
            onPress={() => setSelectedCategory('contribution')}
          >
            <Ionicons
              name="heart"
              size={16}
              color={selectedCategory === 'contribution' ? colors.white : colors.success}
            />
            <Text style={[
              styles.categoryTabText,
              { color: selectedCategory === 'contribution' ? colors.white : colors.text }
            ]}>
              Contributions ({contributionCount})
            </Text>
          </TouchableOpacity>
        </View>

        {/* Filter Type Indicator */}
        {selectedCategory !== 'all' && (
          <View style={styles.filterIndicator}>
            <Text style={[styles.filterIndicatorText, { color: colors.textSecondary }]}>
              {selectedCategory === 'chama' ? 'Chama Types:' : 'Contribution Types:'}
            </Text>
          </View>
        )}

        {/* Dynamic Filters Row - Changes based on selected category */}
        <View style={styles.filtersRow}>
          <FlatList
            horizontal
            data={getCurrentFilters()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  {
                    backgroundColor: selectedFilter === item.id ? colors.primary : colors.backgroundSecondary,
                    borderColor: colors.border,
                  }
                ]}
                onPress={() => setSelectedFilter(item.id)}
              >
                <Ionicons
                  name={item.icon}
                  size={12}
                  color={selectedFilter === item.id ? colors.white : colors.textSecondary}
                />
                <Text style={[
                  styles.filterText,
                  { color: selectedFilter === item.id ? colors.white : colors.textSecondary }
                ]}>
                  {item.name}
                </Text>
              </TouchableOpacity>
            )}
            keyExtractor={(item) => item.id}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filtersContainer}
          />
        </View>
      </View>
    );
  };

  const renderChamaCard = ({ item }) => {
    const userRole = getUserRole(item);

    // Determine if it's a chama or contribution group
    const isContributionGroup = item.category === 'contribution';

    // Different styling for each type
    const typeConfig = isContributionGroup ? {
      color: colors.success,
      icon: 'heart',
      label: 'Fund Group', // Even shorter for mobile
      fullLabel: 'Contribution Group',
      bgColor: colors.success + '15',
      borderColor: colors.success + '30'
    } : {
      color: colors.primary,
      icon: 'people',
      label: 'Chama',
      fullLabel: 'Chama',
      bgColor: colors.primary + '15',
      borderColor: colors.primary + '30'
    };

    return (
      <Card style={[
        styles.chamaCard,
        {
          borderLeftWidth: 4,
          borderLeftColor: typeConfig.color,
          backgroundColor: typeConfig.bgColor
        }
      ]}>
        {/* Category Badge */}
        <View style={[styles.categoryBadge, { backgroundColor: typeConfig.color }]}>
          <Ionicons name={typeConfig.icon} size={10} color={colors.white} />
          <Text style={[styles.categoryBadgeText, { color: colors.white }]}>
            {typeConfig.label}
          </Text>
        </View>

        <View style={styles.chamaHeader}>
          <View style={[styles.chamaAvatar, { backgroundColor: typeConfig.color }]}>
            <Ionicons name={typeConfig.icon} size={24} color={colors.white} />
          </View>

          <View style={styles.chamaInfo}>
            <Text style={[styles.chamaName, { color: colors.text }]} numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={[styles.chamaType, { color: colors.textSecondary }]}>
              {item.type} • {item.county}, {item.town}
            </Text>
          </View>

          <View style={[
            styles.roleBadge,
            { backgroundColor: userRole === 'chairperson' ? colors.warning + '20' : colors.info + '20' }
          ]}>
            <Text style={[
              styles.roleText,
              { color: userRole === 'chairperson' ? colors.warning : colors.info }
            ]}>
              {userRole.toUpperCase()}
            </Text>
          </View>
        </View>
        <View style={styles.chamaStats}>
          <View style={styles.statItem}>
            <Ionicons name="people" size={16} color={colors.primary} />
            <Text style={[styles.statText, { color: colors.text }]}>
              {item.currentMembers}/{item.maxMembers} members
            </Text>
          </View>

          <View style={styles.statItem}>
            <Ionicons name="wallet" size={16} color={colors.secondary} />
            <Text style={[styles.statText, { color: colors.text }]}>
              {formatCurrency(item.contributionAmount)} {item.contributionFrequency}
            </Text>
          </View>

          <View style={styles.statItem}>
            <Ionicons name="trending-up" size={16} color={colors.success} />
            <Text style={[styles.statText, { color: colors.text }]}>
              {formatCurrency(item.totalFunds)} total
            </Text>
          </View>
        </View>

        <View style={styles.chamaActions}>
          <Button
            title="View Details"
            variant="outline"
            size="small"
            onPress={() => navigation.navigate('ChamaDetails', { chamaId: item.id })}
            style={styles.actionButton}
          />

          <Button
            title="Open Dashboard"
            size="small"
            onPress={() => {
              console.log('🏘️ Opening chama dashboard for:', item.name);
              switchToChamaDashboard(item);
            }}
            style={styles.actionButton}
          />
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No chamas found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {searchQuery || selectedFilter !== 'all'
          ? 'Try adjusting your search or filters'
          : "You haven't joined any chamas yet"
        }
      </Text>

      <Button
        title={searchQuery || selectedFilter !== 'all' ? "Clear Filters" : "Explore Chamas"}
        onPress={() => {
          if (searchQuery || selectedFilter !== 'all') {
            setSearchQuery('');
            setSelectedFilter('all');
          } else {
            navigation.navigate('ChamaList');
          }
        }}
        style={styles.emptyButton}
        icon={<Ionicons name={searchQuery || selectedFilter !== 'all' ? "refresh" : "search"} size={20} color={colors.white} />}
      />
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Static Header */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        {renderHeader()}
      </View>

      {/* Scrollable Content */}
      <FlatList
        data={filteredChamas}
        renderItem={renderChamaCard}
        keyExtractor={(item) => item.id}
        style={[styles.scrollableContainer, { flex: 1 }]}
        contentContainerStyle={[
          styles.chamasList,
          filteredChamas.length === 0 && { flexGrow: 1 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={!loading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
      />

      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('CreateChama')}
      >
        <Ionicons name="add" size={24} color={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  scrollableContainer: {
    flex: 1,
  },

  searchInput: {
    marginBottom: spacing.xs,
    height: 36, // Compact search box
  },
  filtersRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filtersContainer: {
    paddingRight: spacing.sm,
    flex: 1,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    marginRight: spacing.xs,
    borderWidth: 1,
  },
  filterText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    borderWidth: 1,
  },
  sortText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  chamasList: {
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
    paddingBottom: spacing.xxxl, // Extra space for FAB
  },
  chamaCard: {
    marginBottom: spacing.sm,
  },
  chamaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  chamaAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  chamaInfo: {
    flex: 1,
  },
  chamaName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  chamaType: {
    fontSize: typography.fontSize.sm,
  },
  roleBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  roleText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  descriptionContainer: {
    marginTop: spacing.lg,
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm,
  },
  chamaDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: typography.lineHeight.normal,
    opacity: 0.9,
    textAlign: 'left',
  },
  noDescription: {
    fontStyle: 'italic',
    opacity: 0.6,
  },
  chamaStats: {
    marginBottom: spacing.md,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
  },
  chamaActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  emptyButton: {
    marginTop: spacing.md,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
  },
  // Category Tabs Styles
  categoryTabs: {
    flexDirection: 'row',
    marginBottom: spacing.lg, // Increased bottom margin
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm, // Added vertical padding
  },
  categoryTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md, // Increased vertical padding
    paddingHorizontal: spacing.sm, // Increased horizontal padding
    marginHorizontal: spacing.xs,
    borderRadius: borderRadius.lg, // Increased border radius
    borderWidth: 1,
    minHeight: 48, // Increased minimum height for better touch targets
  },
  categoryTabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm, // Increased margin from icon
    textAlign: 'center',
    flexShrink: 1, // Allow text to shrink if needed
    numberOfLines: 1,
  },
  // Filter Indicator Styles
  filterIndicator: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm, // Increased vertical padding
    marginBottom: spacing.xs, // Added bottom margin
    alignItems: 'center',
  },
  filterIndicatorText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  // Category Badge Styles
  categoryBadge: {
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.sm,
    minWidth: 50, // Smaller minimum width
    maxWidth: 70, // Much smaller max width to prevent overlap
  },
  categoryBadgeText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
    marginLeft: spacing.xs,
    textTransform: 'uppercase',
    letterSpacing: 0.3,
  },
});

export default MyChamasScreen;
