import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { useChamaContext } from '../../context/ChamaContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import ApiService from '../../services/api';
import ReceiptService from '../../services/receiptService';

const ChamaTransactionsScreen = ({ route, navigation }) => {
  const { theme, user } = useApp();
  const {
    currentChamaId,
    selectedChama,
    canViewGroupRecords
  } = useChamaContext();
  const colors = getThemeColors(theme);

  // State variables
  const [transactions, setTransactions] = useState([]);
  const [allRecords, setAllRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [viewMode, setViewMode] = useState('personal'); // 'personal' or 'group'
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);
  const [chamaMembers, setChamaMembers] = useState([]);
  const [showMemberSelector, setShowMemberSelector] = useState(false);

  const filters = [
    { id: 'all', name: 'All Records', icon: 'list' },
    { id: 'contribution', name: 'Contributions', icon: 'add-circle' },
    { id: 'welfare', name: 'Welfare', icon: 'heart' },
    { id: 'merry-go-round', name: 'Merry-Go-Round', icon: 'refresh-circle' },
    { id: 'loan', name: 'Loans', icon: 'card' },
    { id: 'withdrawal', name: 'Withdrawals', icon: 'remove-circle' },
    { id: 'penalty', name: 'Penalties', icon: 'warning' },
  ];

  // Apply role-based filtering to determine what user can see
  const applyRoleBasedFiltering = (allData) => {
    const isLeader = canViewGroupRecords();
    const shouldShowGroupData = isLeader && viewMode === 'group';

    if (shouldShowGroupData) {
      // Leadership in group view: show ALL records
      console.log('👑 Leadership group view: showing all', allData.length, 'records');
      return allData;
    } else {
      // Personal view OR regular member: show personal + group welfare/merry-go-round
      const filteredData = allData.filter(item => {
        // User's own transactions/contributions - check all possible field names
        const isUserRecord = item.initiatedBy === user.id ||
                             item.initiated_by === user.id ||
                             item.user_id === user.id ||
                             item.userId === user.id ||
                             item.contributed_by === user.id ||
                             item.member_id === user.id ||
                             item.memberId === user.id;

        // Group welfare and merry-go-round are visible to all members
        const isGroupVisible = ['welfare', 'merry-go-round'].includes(item.type?.toLowerCase());

        return isUserRecord || isGroupVisible;
      });

      console.log('👤 Personal/member view: showing', filteredData.length, 'of', allData.length, 'records');
      return filteredData;
    }
  };

  useEffect(() => {
    if (currentChamaId) {
      loadInitialData();
    }
  }, [currentChamaId]);

  useEffect(() => {
    if (currentChamaId) {
      loadTransactions();
    }
  }, [currentChamaId, selectedFilter, viewMode]);

  // Separate effect for view mode changes to re-filter existing data
  useEffect(() => {
    if (allRecords.length > 0) {
      console.log('🔄 View mode changed to:', viewMode, '- Re-filtering data');
      const filteredData = applyRoleBasedFiltering(allRecords);

      // Apply selected filter
      let finalData = filteredData;
      if (selectedFilter !== 'all') {
        finalData = filteredData.filter(item =>
          item.type?.toLowerCase() === selectedFilter.toLowerCase() ||
          item.transaction_type?.toLowerCase() === selectedFilter.toLowerCase()
        );
      }

      setTransactions(finalData);
      console.log('📊 Re-filtered to', finalData.length, 'records');
    }
  }, [viewMode, selectedFilter]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      // Initial data loading is now handled by ChamaContext
      // Load chama members for proper name resolution in receipts
      await loadChamaMembers();
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async () => {
    try {
      setLoading(true);

      let allData = [];

      // Fetch ALL chama transactions (no filtering at API level)
      console.log('🔍 Fetching all transactions for chama:', currentChamaId);
      const transactionResponse = await ApiService.getChamaTransactions(currentChamaId, 1000, 0);
      if (transactionResponse.success) {
        const transactionData = transactionResponse.data || [];
        console.log('📊 Fetched transactions:', transactionData.length);
        allData = [...allData, ...transactionData];
      }

      // Fetch ALL chama contributions
      try {
        console.log('🔍 Fetching all contributions for chama:', currentChamaId);
        const contributionResponse = await ApiService.getContributions(currentChamaId);
        if (contributionResponse.success) {
          const contributionData = contributionResponse.data || [];
          console.log('📊 Fetched contributions:', contributionData.length);
          allData = [...allData, ...contributionData];
        }
      } catch (error) {
        console.warn('Contributions API not available:', error);
      }

      // Fetch welfare transactions/contributions
      try {
        console.log('🔍 Fetching welfare data for chama:', currentChamaId);
        const welfareResponse = await ApiService.getWelfareRequests(currentChamaId, 1000, 0);
        if (welfareResponse.success) {
          const welfareData = welfareResponse.data || [];
          console.log('📊 Fetched welfare records:', welfareData.length);
          // Add welfare data with proper type
          const welfareTransactions = welfareData.map(item => ({
            ...item,
            type: 'welfare',
            transaction_type: 'welfare'
          }));
          allData = [...allData, ...welfareTransactions];
        }
      } catch (error) {
        console.warn('Welfare API not available:', error);
      }

      // Fetch merry-go-round data
      try {
        console.log('🔍 Fetching merry-go-round data for chama:', currentChamaId);
        const merryGoRoundResponse = await ApiService.getMerryGoRounds(currentChamaId, 1000, 0);
        if (merryGoRoundResponse.success) {
          const merryGoRoundData = merryGoRoundResponse.data || [];
          console.log('📊 Fetched merry-go-round records:', merryGoRoundData.length);
          // Add merry-go-round data with proper type
          const merryGoRoundTransactions = merryGoRoundData.map(item => ({
            ...item,
            type: 'merry-go-round',
            transaction_type: 'merry-go-round'
          }));
          allData = [...allData, ...merryGoRoundTransactions];
        }
      } catch (error) {
        console.warn('Merry-go-round API not available:', error);
      }

      // Fetch loan data
      try {
        console.log('🔍 Fetching loan data for chama:', currentChamaId);
        const loanResponse = await ApiService.getLoans(currentChamaId, 1000, 0);
        if (loanResponse.success) {
          const loanData = loanResponse.data || [];
          console.log('📊 Fetched loan records:', loanData.length);
          // Add loan data with proper type
          const loanTransactions = loanData.map(item => ({
            ...item,
            type: 'loan',
            transaction_type: 'loan'
          }));
          allData = [...allData, ...loanTransactions];
        }
      } catch (error) {
        console.warn('Loan API not available:', error);
      }

      console.log('📊 Total records fetched:', allData.length);

      // Store all data for role-based filtering
      setAllRecords(allData);

      // Apply role-based filtering for display
      const filteredData = applyRoleBasedFiltering(allData);

      // Apply selected filter
      let finalData = filteredData;
      if (selectedFilter !== 'all') {
        finalData = filteredData.filter(item =>
          item.type?.toLowerCase() === selectedFilter.toLowerCase() ||
          item.transaction_type?.toLowerCase() === selectedFilter.toLowerCase()
        );
      }

      setTransactions(finalData);
      console.log('📊 Displaying records:', finalData.length);

    } catch (error) {
      console.error('Error loading transactions:', error);
      Alert.alert('Error', 'Failed to load transaction data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactions(); // This now loads all data
    setRefreshing(false);
  };

  // Load chama members for member selector (leadership only)
  const loadChamaMembers = async () => {
    try {
      const response = await ApiService.getChamaMembers(currentChamaId);
      if (response.success) {
        setChamaMembers(response.data || []);
      }
    } catch (error) {
      console.warn('Failed to load chama members:', error);
    }
  };

  // Enhanced download functions with professional formatting
  const handleDownload = async (format, scope = 'personal', memberId = null) => {
    try {
      setExportLoading(true);

      // Determine what data to include
      let dataToExport = [];
      let reportTitle = '';
      let memberName = '';

      console.log('🔍 Download data check:', {
        scope,
        allRecordsCount: allRecords?.length || 0,
        transactionsCount: transactions?.length || 0,
        canViewGroupRecords: canViewGroupRecords(),
        memberId
      });

      if (scope === 'all' && canViewGroupRecords()) {
        // Leadership downloading all chama records
        dataToExport = allRecords || [];
        reportTitle = `${selectedChama?.name || 'Chama'} - Complete Transaction Report`;
        console.log('📊 Using all records:', dataToExport.length);
      } else if (scope === 'member' && canViewGroupRecords() && memberId) {
        // Leadership downloading specific member's records
        const member = chamaMembers.find(m => m.user_id === memberId || m.id === memberId);
        memberName = member?.name || member?.user?.name || 'Unknown Member';
        dataToExport = (allRecords || []).filter(record =>
          record.user_id === memberId ||
          record.initiated_by === memberId ||
          record.contributed_by === memberId ||
          record.member_id === memberId
        );
        reportTitle = `${selectedChama?.name || 'Chama'} - ${memberName} Transaction Report`;
        console.log('📊 Using member records:', dataToExport.length);
      } else {
        // Personal records (default for members)
        dataToExport = transactions || []; // Already filtered for user
        reportTitle = `${selectedChama?.name || 'Chama'} - Personal Transaction Report`;
        console.log('📊 Using personal records:', dataToExport.length);
      }

      if (dataToExport.length === 0) {
        Alert.alert(
          'No Data',
          'No transaction records found for the selected scope. Please ensure there are transactions to export.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Group data by type for better organization
      const groupedData = groupTransactionsByType(dataToExport);

      // Generate document based on format
      let result;
      switch (format) {
        case 'pdf':
          result = await ReceiptService.generateChamaTransactionsPDF({
            title: reportTitle,
            chamaName: selectedChama?.name || 'Chama',
            memberName: memberName,
            groupedData,
            allData: dataToExport,
            generatedBy: user?.name || 'User',
            scope,
            dateRange: getDateRange(dataToExport),
            chamaMembers: chamaMembers // Pass chama members for proper name resolution
          });
          break;
        case 'excel':
          result = await ReceiptService.generateChamaTransactionsExcel({
            title: reportTitle,
            chamaName: selectedChama?.name || 'Chama',
            memberName: memberName,
            groupedData,
            allData: dataToExport,
            generatedBy: user?.name || 'User',
            scope,
            chamaMembers: chamaMembers // Pass chama members for proper name resolution
          });
          break;
        case 'word':
          result = await ReceiptService.generateChamaTransactionsWord({
            title: reportTitle,
            chamaName: selectedChama?.name || 'Chama',
            memberName: memberName,
            groupedData,
            allData: dataToExport,
            generatedBy: user?.name || 'User',
            scope,
            chamaMembers: chamaMembers // Pass chama members for proper name resolution
          });
          break;
        default:
          throw new Error('Unsupported format');
      }

      if (result.success) {
        Alert.alert(
          'Download Complete',
          `${format.toUpperCase()} report has been saved successfully.`,
          [{ text: 'OK' }]
        );
      } else {
        throw new Error(result.error || 'Download failed');
      }

    } catch (error) {
      console.error('Download error:', error);
      Alert.alert(
        'Download Failed',
        error.message || 'Failed to generate report. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setExportLoading(false);
      setShowExportModal(false);
    }
  };

  // Helper function to group transactions by type
  const groupTransactionsByType = (data) => {
    const grouped = {
      contributions: [],
      welfare: [],
      'merry-go-round': [],
      loans: [],
      withdrawals: [],
      penalties: [],
      other: []
    };

    data.forEach(item => {
      const type = item.type?.toLowerCase() || item.transaction_type?.toLowerCase() || 'other';
      if (grouped[type]) {
        grouped[type].push(item);
      } else {
        grouped.other.push(item);
      }
    });

    return grouped;
  };

  // Helper function to get date range from data
  const getDateRange = (data) => {
    if (data.length === 0) return { start: new Date(), end: new Date() };

    const dates = data.map(item => new Date(item.created_at || item.date || Date.now()));
    return {
      start: new Date(Math.min(...dates)),
      end: new Date(Math.max(...dates))
    };
  };

  // Export functions (legacy - for simple export without scope selection)
  const handleExport = async (format) => {
    if (!canViewGroupRecords() && viewMode === 'group') {
      Alert.alert('Access Denied', 'Only chairperson, secretary, and treasurer can export group records.');
      return;
    }

    setExportLoading(true);
    try {
      const dataToExport = allRecords.length > 0 ? allRecords : transactions;

      if (dataToExport.length === 0) {
        Alert.alert('No Data', 'No records available to export.');
        return;
      }

      // Use the new download method with proper scope
      const scope = canViewGroupRecords() && viewMode === 'group' ? 'all' : 'personal';
      const result = await handleDownload(format, scope);

      if (result !== false) { // handleDownload handles success/error internally
        setShowExportModal(false);
      }
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Error', 'Failed to export records');
    } finally {
      setExportLoading(false);
    }
  };

  // Individual transaction receipt download
  const handleIndividualReceipt = async (transaction, format = 'pdf') => {
    try {
      setExportLoading(true);

      const result = await ReceiptService.generateTransactionReceipt(transaction, format, {
        chamaName: selectedChama?.name || 'Chama',
        userInfo: {
          name: `${user?.first_name || ''} ${user?.last_name || ''}`.trim() || 'User',
          email: user?.email || '',
          phone: user?.phone || ''
        },
        chamaMembers: chamaMembers // Pass chama members for proper name resolution
      });

      if (result.success) {
        Alert.alert(
          'Receipt Generated',
          `Transaction receipt has been saved successfully.`,
          [{ text: 'OK' }]
        );
      } else {
        throw new Error(result.error || 'Failed to generate receipt');
      }

    } catch (error) {
      console.error('Individual receipt error:', error);
      Alert.alert(
        'Receipt Failed',
        error.message || 'Failed to generate transaction receipt. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setExportLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown Date';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      console.warn('Date formatting error:', error);
      return 'Invalid Date';
    }
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'contribution':
        return 'add-circle';
      case 'withdrawal':
        return 'remove-circle';
      case 'loan':
        return 'card';
      case 'expense':
        return 'receipt';
      default:
        return 'swap-horizontal';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'contribution':
        return colors.success;
      case 'withdrawal':
        return colors.warning;
      case 'loan':
        return colors.info;
      case 'expense':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const renderFilterChips = () => (
    <View style={styles.filtersContainer}>
      <FlatList
        horizontal
        data={filters}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterChip,
              {
                backgroundColor: selectedFilter === item.id ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedFilter(item.id)}
          >
            <Ionicons 
              name={item.icon} 
              size={16} 
              color={selectedFilter === item.id ? colors.white : colors.textSecondary} 
            />
            <Text style={[
              styles.filterText,
              { color: selectedFilter === item.id ? colors.white : colors.textSecondary }
            ]}>
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContent}
      />
    </View>
  );

  const renderTransaction = ({ item }) => (
    <Card style={styles.transactionCard}>
      <View style={styles.transactionHeader}>
        <View style={[
          styles.transactionIcon,
          { backgroundColor: getTransactionColor(item.type) + '20' }
        ]}>
          <Ionicons 
            name={getTransactionIcon(item.type)} 
            size={20} 
            color={getTransactionColor(item.type)} 
          />
        </View>
        
        <View style={styles.transactionInfo}>
          <Text style={[styles.transactionTitle, { color: colors.text }]}>
            {item.description || `${item.type} Transaction`}
          </Text>
          <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>
            {formatDate(item.createdAt || item.created_at)}
          </Text>
          <Text style={[styles.transactionUser, { color: colors.textSecondary }]}>
            By: {(item.metadata?.isAnonymous || item.metadata?.displayName === 'Anonymous')
              ? 'Anonymous'
              : `${item.user?.firstName || item.user?.first_name || 'Unknown'} ${item.user?.lastName || item.user?.last_name || ''}`.trim()
            }
          </Text>
        </View>
        
        <View style={styles.transactionAmount}>
          <Text style={[
            styles.amountText,
            {
              color: item.type === 'contribution' ? colors.success :
                     item.type === 'withdrawal' ? colors.warning : colors.text
            }
          ]}>
            {item.type === 'contribution' ? '+' : '-'}{formatCurrency(item.amount)}
          </Text>
          <Text style={[styles.transactionType, { color: colors.textSecondary }]}>
            {item.type.toUpperCase()}
          </Text>
        </View>

        {/* Individual Receipt Download Button */}
        <TouchableOpacity
          style={[styles.receiptButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => handleIndividualReceipt(item)}
          disabled={exportLoading}
        >
          <Ionicons
            name="receipt-outline"
            size={16}
            color={colors.primary}
          />
        </TouchableOpacity>
      </View>
      
      {item.status && (
        <View style={[
          styles.statusBadge,
          { backgroundColor: item.status === 'completed' ? colors.success + '20' : colors.warning + '20' }
        ]}>
          <Text style={[
            styles.statusText,
            { color: item.status === 'completed' ? colors.success : colors.warning }
          ]}>
            {item.status.toUpperCase()}
          </Text>
        </View>
      )}
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="receipt-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Transactions Found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {selectedFilter === 'all' 
          ? 'No transactions have been made yet'
          : `No ${selectedFilter} transactions found`
        }
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <View style={styles.headerContent}>
        <View style={styles.headerText}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {viewMode === 'personal' ? 'My Records' : 'Group Records'}
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {selectedChama?.name || 'Chama'} • {transactions.length} records
          </Text>
        </View>

        <View style={styles.headerActions}>
          {/* View Mode Toggle - Only show for leadership roles */}
          {canViewGroupRecords() && (
            <TouchableOpacity
              style={[styles.toggleButton, { backgroundColor: colors.backgroundSecondary }]}
              onPress={() => setViewMode(viewMode === 'personal' ? 'group' : 'personal')}
            >
              <Ionicons
                name={viewMode === 'personal' ? 'person' : 'people'}
                size={16}
                color={colors.primary}
              />
              <Text style={[styles.toggleText, { color: colors.primary }]}>
                {viewMode === 'personal' ? 'Personal' : 'Group'}
              </Text>
            </TouchableOpacity>
          )}

          {/* Export Button */}
          <TouchableOpacity
            style={[styles.exportButton, { backgroundColor: colors.primary }]}
            onPress={() => setShowExportModal(true)}
            disabled={transactions.length === 0}
          >
            <Ionicons name="download" size={16} color={colors.white} />
            <Text style={[styles.exportText, { color: colors.white }]}>
              Export
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  // Show loading or error state if no chama is selected
  if (!currentChamaId) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.centerContainer}>
          <Ionicons name="business" size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No Chama Selected
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            Please select a chama from the dashboard to view transactions
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={[styles.backButtonText, { color: colors.white }]}>
              Go Back
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {renderHeader()}

      {renderFilterChips()}
      
      <FlatList
        data={transactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.transactionsList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={!loading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
      />

      {/* Export Modal */}
      <Modal
        visible={showExportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowExportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Export Records
              </Text>
              <TouchableOpacity
                onPress={() => setShowExportModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <Text style={[styles.modalSubtitle, { color: colors.textSecondary }]}>
              Choose export format for {transactions.length} records
            </Text>

            <View style={styles.exportOptions}>
              <TouchableOpacity
                style={[styles.exportOption, { backgroundColor: colors.backgroundSecondary }]}
                onPress={() => handleExport('pdf')}
                disabled={exportLoading}
              >
                <Ionicons name="document-text" size={24} color={colors.error} />
                <Text style={[styles.exportOptionText, { color: colors.text }]}>
                  PDF Report
                </Text>
                <Text style={[styles.exportOptionDesc, { color: colors.textSecondary }]}>
                  Professional formatted report
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.exportOption, { backgroundColor: colors.backgroundSecondary }]}
                onPress={() => handleExport('excel')}
                disabled={exportLoading}
              >
                <Ionicons name="grid" size={24} color={colors.success} />
                <Text style={[styles.exportOptionText, { color: colors.text }]}>
                  Excel Spreadsheet
                </Text>
                <Text style={[styles.exportOptionDesc, { color: colors.textSecondary }]}>
                  Data analysis and calculations
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.exportOption, { backgroundColor: colors.backgroundSecondary }]}
                onPress={() => handleExport('word')}
                disabled={exportLoading}
              >
                <Ionicons name="document" size={24} color={colors.info} />
                <Text style={[styles.exportOptionText, { color: colors.text }]}>
                  Word Document
                </Text>
                <Text style={[styles.exportOptionDesc, { color: colors.textSecondary }]}>
                  Editable document format
                </Text>
              </TouchableOpacity>
            </View>

            {exportLoading && (
              <View style={styles.loadingContainer}>
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Generating export...
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Member Selector Modal */}
      <Modal
        visible={showMemberSelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMemberSelector(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select Member
              </Text>
              <TouchableOpacity
                onPress={() => setShowMemberSelector(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <Text style={[styles.modalSubtitle, { color: colors.textSecondary }]}>
              Choose a member to download their transaction records
            </Text>

            <FlatList
              data={chamaMembers}
              keyExtractor={(item) => item.id || item.user_id}
              renderItem={({ item }) => {
                const memberName = item.name || item.user?.name || `${item.user?.first_name || ''} ${item.user?.last_name || ''}`.trim() || 'Unknown Member';
                const memberEmail = item.email || item.user?.email || '';

                return (
                  <TouchableOpacity
                    style={[styles.memberOption, { borderColor: colors.border }]}
                    onPress={() => {
                      setSelectedMember(item.user_id || item.id);
                      setShowMemberSelector(false);
                    }}
                  >
                    <View style={styles.memberInfo}>
                      <View style={[styles.memberAvatar, { backgroundColor: colors.primary }]}>
                        <Text style={[styles.memberAvatarText, { color: colors.white }]}>
                          {memberName.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                      <View style={styles.memberDetails}>
                        <Text style={[styles.memberName, { color: colors.text }]}>
                          {memberName}
                        </Text>
                        {memberEmail && (
                          <Text style={[styles.memberEmail, { color: colors.textSecondary }]}>
                            {memberEmail}
                          </Text>
                        )}
                        <Text style={[styles.memberRole, { color: colors.textSecondary }]}>
                          {item.role || 'Member'}
                        </Text>
                      </View>
                    </View>
                    <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
                  </TouchableOpacity>
                );
              }}
              style={styles.memberList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  backButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    marginTop: spacing.lg,
  },
  backButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    ...shadows.sm,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.base,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  toggleText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  exportText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  filtersContainer: {
    paddingVertical: spacing.md,
    backgroundColor: 'transparent',
  },
  filtersContent: {
    paddingHorizontal: spacing.md,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    borderWidth: 1,
  },
  filterText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  transactionsList: {
    padding: spacing.md,
  },
  transactionCard: {
    marginBottom: spacing.md,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  transactionDate: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  transactionUser: {
    fontSize: typography.fontSize.xs,
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  transactionType: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    marginTop: spacing.sm,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
  },
  modalCloseButton: {
    padding: spacing.sm,
  },
  modalSubtitle: {
    fontSize: typography.fontSize.base,
    marginBottom: spacing.xl,
  },
  exportOptions: {
    gap: spacing.md,
  },
  exportOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    gap: spacing.md,
  },
  exportOptionText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  exportOptionDesc: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    fontStyle: 'italic',
  },
  // Enhanced modal styles
  scopeSection: {
    marginBottom: spacing.lg,
  },
  formatSection: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  scopeOption: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  scopeOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scopeOptionText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  scopeOptionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  scopeOptionSubtitle: {
    fontSize: typography.fontSize.sm,
  },
  // Member selector styles
  memberList: {
    maxHeight: 300,
  },
  memberOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
  },
  memberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  memberAvatarText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
  },
  memberDetails: {
    marginLeft: spacing.md,
    flex: 1,
  },
  memberName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  memberEmail: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  memberRole: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
  },
  // Individual receipt button styles
  receiptButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
});

export default ChamaTransactionsScreen;
