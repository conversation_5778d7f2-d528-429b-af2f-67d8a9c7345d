import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  SafeAreaView,
  StatusBar,
  BackHandler,
  ActivityIndicator,
} from 'react-native';
import { useApp } from '../../context/AppContext';
import VideoCallView from '../../components/VideoCallView';
import livekitService from '../../services/livekitService';
import api from '../../services/api';
import Toast from 'react-native-toast-message';

const OnlineMeetingScreen = ({ route, navigation }) => {
  const {
    meetingId,
    meetingTitle,
    userRole = 'member',
    isPreview = false,
    previewData = null
  } = route.params;
  const { theme, user } = useApp();

  // Simple color scheme
  const colors = {
    background: theme === 'dark' ? '#000000' : '#ffffff',
    text: theme === 'dark' ? '#ffffff' : '#000000',
    textSecondary: theme === 'dark' ? '#cccccc' : '#666666',
    primary: '#007AFF',
    error: '#FF3B30'
  };
  
  const [isConnecting, setIsConnecting] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [isCameraEnabled, setIsCameraEnabled] = useState(true); // Enable camera by default
  const [isMicrophoneEnabled, setIsMicrophoneEnabled] = useState(true);
  const [meetingData, setMeetingData] = useState(null);
  const [connectionError, setConnectionError] = useState(null);
  
  const hasJoinedRef = useRef(false);
  const reconnectTimeoutRef = useRef(null);

  useEffect(() => {
    initializeMeeting();
    
    // Handle back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    
    return () => {
      backHandler.remove();
      cleanup();
    };
  }, []);

  useEffect(() => {
    // Set up LiveKit event listeners
    const setupEventListeners = () => {
      livekitService.on('participantConnected', handleParticipantConnected);
      livekitService.on('participantDisconnected', handleParticipantDisconnected);
      livekitService.on('trackSubscribed', handleTrackSubscribed);
      livekitService.on('trackUnsubscribed', handleTrackUnsubscribed);
      livekitService.on('connectionStateChanged', handleConnectionStateChanged);
      livekitService.on('disconnected', handleDisconnected);
    };

    const removeEventListeners = () => {
      livekitService.off('participantConnected', handleParticipantConnected);
      livekitService.off('participantDisconnected', handleParticipantDisconnected);
      livekitService.off('trackSubscribed', handleTrackSubscribed);
      livekitService.off('trackUnsubscribed', handleTrackUnsubscribed);
      livekitService.off('connectionStateChanged', handleConnectionStateChanged);
      livekitService.off('disconnected', handleDisconnected);
    };

    setupEventListeners();
    return removeEventListeners;
  }, []);

  const initializeMeeting = async () => {
    try {
      setIsConnecting(true);
      setConnectionError(null);

      let connectionData;

      if (isPreview && previewData) {
        // Use preview data for chairperson/secretary preview
        // Map preview data fields to expected format
        connectionData = {
          token: previewData.accessToken, // Preview uses 'accessToken'
          roomName: previewData.roomName,
          wsUrl: previewData.wsURL, // Preview uses 'wsURL' (uppercase)
          userRole: previewData.userRole,
          isPreview: true,
        };
      } else {
        // Get LiveKit connection data from backend
        const response = await api.makeRequest(`/meetings/${meetingId}/join-livekit`, {
          method: 'POST',
          body: { userRole },
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to get meeting connection data');
        }

        connectionData = response.data; // Join endpoint already has correct field names
      }

      console.log('🎬 Connection data:', {
        hasToken: !!connectionData.token,
        hasWsUrl: !!connectionData.wsUrl,
        roomName: connectionData.roomName,
        userRole: connectionData.userRole || userRole,
        isPreview: connectionData.isPreview || false,
      });

      // Connect to LiveKit room with the token and room data
      // For previews, always try to use real LiveKit if we have the necessary data
      const hasLiveKitData = connectionData.token && connectionData.wsUrl && connectionData.roomName;
      const shouldUseRealLiveKit = isPreview && hasLiveKitData && !connectionData.fallbackMode;

      if (shouldUseRealLiveKit) {
        console.log('🎬 Preview mode: Using real LiveKit connection');
        // Force real LiveKit for previews when we have the data
        livekitService.isRealLiveKit = true;
      }

      await livekitService.connectToRoom({
        token: connectionData.token,
        roomName: connectionData.roomName,
        wsUrl: connectionData.wsUrl,
        userRole: userRole,
        forceReal: shouldUseRealLiveKit,
      });

      // Mark attendance (skip for preview mode)
      if (!isPreview) {
        await markAttendance();
      }

      // Update participants list
      updateParticipantsList();

      setIsConnected(true);
      setIsConnecting(false);
      hasJoinedRef.current = true;

      Toast.show({
        type: 'success',
        text1: isPreview ? 'Preview mode active' : 'Connected to meeting',
        text2: isPreview ? 'You are previewing the meeting room' : 'You have successfully joined the meeting',
      });

      // Initialize camera and microphone states after successful connection
      try {
        console.log('🎬 Initializing camera and microphone...');

        // Wait a bit for the connection to stabilize
        await new Promise(resolve => setTimeout(resolve, 500));

        // Get current states from LiveKit service
        const participants = livekitService.getParticipants();
        const localParticipant = participants.find(p => p.isLocal);

        if (localParticipant) {
          console.log('🎬 Local participant found:', {
            identity: localParticipant.identity,
            name: localParticipant.name,
            camera: localParticipant.isCameraEnabled,
            mic: localParticipant.isMicrophoneEnabled
          });

          setIsCameraEnabled(localParticipant.isCameraEnabled);
          setIsMicrophoneEnabled(localParticipant.isMicrophoneEnabled);

          console.log('🎬 Camera/Mic states updated:', {
            camera: localParticipant.isCameraEnabled,
            mic: localParticipant.isMicrophoneEnabled
          });
        } else {
          console.warn('🎬 No local participant found, using default states');
          // Set default states if no local participant found
          setIsCameraEnabled(true);
          setIsMicrophoneEnabled(true);
        }
      } catch (error) {
        console.warn('🎬 Failed to initialize camera/microphone states:', error);
        // Set default states on error
        setIsCameraEnabled(true);
        setIsMicrophoneEnabled(true);
      }

    } catch (error) {
      console.error('Failed to initialize meeting:', error);
      setConnectionError(error.message);
      setIsConnecting(false);

      Toast.show({
        type: 'error',
        text1: 'Connection failed',
        text2: error.message,
      });
    }
  };

  const markAttendance = async () => {
    try {
      await api.makeRequest(`/meetings/${meetingId}/attendance`, {
        method: 'POST',
        body: {
          attendanceType: 'virtual',
          isPresent: true,
        },
      });
    } catch (error) {
      console.error('Failed to mark attendance:', error);
    }
  };

  const updateParticipantsList = () => {
    const currentParticipants = livekitService.getParticipants();
    setParticipants(currentParticipants);

    // Also update local camera/microphone states
    const localParticipant = currentParticipants.find(p => p.isLocal);
    if (localParticipant) {
      setIsCameraEnabled(localParticipant.isCameraEnabled);
      setIsMicrophoneEnabled(localParticipant.isMicrophoneEnabled);
    }
  };

  // Event handlers
  const handleParticipantConnected = (participant) => {
    console.log('Participant joined:', participant.identity);
    updateParticipantsList();
    
    Toast.show({
      type: 'info',
      text1: 'Participant joined',
      text2: `${participant.name || participant.identity} joined the meeting`,
    });
  };

  const handleParticipantDisconnected = (participant) => {
    console.log('Participant left:', participant.identity);
    updateParticipantsList();
    
    Toast.show({
      type: 'info',
      text1: 'Participant left',
      text2: `${participant.name || participant.identity} left the meeting`,
    });
  };

  const handleTrackSubscribed = ({ track, publication, participant }) => {
    updateParticipantsList();
  };

  const handleTrackUnsubscribed = ({ track, publication, participant }) => {
    updateParticipantsList();
  };

  const handleConnectionStateChanged = (state) => {
    console.log('Connection state changed:', state);
    
    if (state === 'connected') {
      setIsConnected(true);
      setConnectionError(null);
    } else if (state === 'disconnected') {
      setIsConnected(false);
    }
  };

  const handleDisconnected = (reason) => {
    console.log('Disconnected from meeting:', reason);
    setIsConnected(false);
    
    if (hasJoinedRef.current && reason !== 'user_initiated') {
      // Attempt to reconnect if not user-initiated
      attemptReconnect();
    }
  };

  const attemptReconnect = () => {
    if (reconnectTimeoutRef.current) return;
    
    Toast.show({
      type: 'info',
      text1: 'Reconnecting...',
      text2: 'Attempting to reconnect to the meeting',
    });
    
    reconnectTimeoutRef.current = setTimeout(() => {
      initializeMeeting();
      reconnectTimeoutRef.current = null;
    }, 3000);
  };

  // Control handlers
  const handleToggleCamera = async () => {
    try {
      console.log('🎬 Toggling camera, current state:', isCameraEnabled);
      const newState = await livekitService.toggleCamera();
      console.log('🎬 Camera toggled to:', newState);
      setIsCameraEnabled(newState);

      // Update participants list to reflect changes
      updateParticipantsList();

      Toast.show({
        type: 'success',
        text1: newState ? 'Camera enabled' : 'Camera disabled',
        text2: newState ? 'Your camera is now on' : 'Your camera is now off',
      });
    } catch (error) {
      console.error('Failed to toggle camera:', error);
      Toast.show({
        type: 'error',
        text1: 'Camera error',
        text2: 'Failed to toggle camera. Please check permissions.',
      });
    }
  };

  const handleToggleMicrophone = async () => {
    try {
      console.log('🎬 Toggling microphone, current state:', isMicrophoneEnabled);
      const newState = await livekitService.toggleMicrophone();
      console.log('🎬 Microphone toggled to:', newState);
      setIsMicrophoneEnabled(newState);

      // Update participants list to reflect changes
      updateParticipantsList();

      Toast.show({
        type: 'success',
        text1: newState ? 'Microphone enabled' : 'Microphone disabled',
        text2: newState ? 'Your microphone is now on' : 'Your microphone is now off',
      });
    } catch (error) {
      console.error('Failed to toggle microphone:', error);
      Toast.show({
        type: 'error',
        text1: 'Microphone error',
        text2: 'Failed to toggle microphone. Please check permissions.',
      });
    }
  };

  const handleSwitchCamera = async () => {
    try {
      console.log('🎬 Switching camera...');
      await livekitService.switchCamera();

      Toast.show({
        type: 'success',
        text1: 'Camera switched',
        text2: 'Camera view has been switched',
      });
    } catch (error) {
      console.error('Failed to switch camera:', error);
      Toast.show({
        type: 'error',
        text1: 'Camera error',
        text2: 'Failed to switch camera. Feature may not be available.',
      });
    }
  };

  const handleEndCall = () => {
    Alert.alert(
      'End Meeting',
      'Are you sure you want to leave the meeting?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Leave', style: 'destructive', onPress: leaveMeeting },
      ]
    );
  };

  const handleBackPress = () => {
    handleEndCall();
    return true; // Prevent default back action
  };

  const leaveMeeting = async () => {
    try {
      hasJoinedRef.current = false;
      await livekitService.disconnect();
      navigation.goBack();
    } catch (error) {
      console.error('Failed to leave meeting:', error);
      navigation.goBack(); // Go back anyway
    }
  };

  const cleanup = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (hasJoinedRef.current) {
      livekitService.disconnect();
    }
  };

  // Render loading state
  if (isConnecting) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {isPreview ? 'Loading preview...' : 'Connecting to meeting...'}
          </Text>
          <Text style={[styles.loadingSubtext, { color: colors.textSecondary }]}>
            {meetingTitle}
          </Text>
          {isPreview && (
            <Text style={[styles.previewBadge, { color: colors.primary }]}>
              PREVIEW MODE
            </Text>
          )}
        </View>
      </SafeAreaView>
    );
  }

  // Render error state
  if (connectionError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            Failed to connect to meeting
          </Text>
          <Text style={[styles.errorSubtext, { color: colors.textSecondary }]}>
            {connectionError}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
      
      <VideoCallView
        participants={participants}
        onToggleCamera={handleToggleCamera}
        onToggleMicrophone={handleToggleMicrophone}
        onSwitchCamera={handleSwitchCamera}
        onEndCall={handleEndCall}
        isCameraEnabled={isCameraEnabled}
        isMicrophoneEnabled={isMicrophoneEnabled}
        userRole={userRole}
        isPreview={isPreview}
        meetingTitle={meetingTitle}
      />

      {/* Debug overlay for development */}
      {__DEV__ && (
        <View style={styles.debugOverlay}>
          <Text style={styles.debugText}>
            Camera: {isCameraEnabled ? '✅' : '❌'} | Mic: {isMicrophoneEnabled ? '✅' : '❌'}
          </Text>
          <Text style={styles.debugText}>
            Participants: {participants.length} | Connected: {isConnected ? '✅' : '❌'}
          </Text>
          <Text style={styles.debugText}>
            LiveKit: {livekitService.isRealLiveKit ? 'Real' : 'Mock'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  previewBadge: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
    letterSpacing: 1,
  },
  debugOverlay: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 8,
    borderRadius: 4,
    zIndex: 1000,
  },
  debugText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'monospace',
  },
});

export default OnlineMeetingScreen;
