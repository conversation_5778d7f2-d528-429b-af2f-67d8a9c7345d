import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  ScrollView,
  TextInput,
  Animated,
  Dimensions,
  Platform,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
// Removed unused import - using local formatters
import { spacing, typography, borderRadius } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import ApiService from '../../services/api';

// 🔐 SECURE CHAMA ACCOUNT MANAGEMENT SYSTEM
// Central financial control panel for all money movements
// Built with transparency, security, and auditability

const AccountManagementScreen = ({ route, navigation }) => {
  const { chamaId } = route.params;
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  // 🔐 CORE STATE MANAGEMENT
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userRole, setUserRole] = useState('member');
  const [activePanel, setActivePanel] = useState('disbursement'); // 'disbursement', 'transparency', 'audit'

  // 💰 DISBURSEMENT PANEL STATE
  const [showDisbursementPanel, setShowDisbursementPanel] = useState(false);
  const [disbursementType, setDisbursementType] = useState('loan'); // 'loan', 'welfare', 'dividend'

  // 🎯 INDIVIDUAL DISBURSEMENT (Loans & Welfare)
  const [eligibleMembers, setEligibleMembers] = useState([]);
  const [selectedMember, setSelectedMember] = useState(null);
  const [individualDisbursementForm, setIndividualDisbursementForm] = useState({
    memberId: '',
    memberName: '',
    amount: '',
    purpose: '',
    privateNote: '',
  });

  // 📊 BULK DISBURSEMENT (Dividends/Shares)
  const [bulkDisbursementData, setBulkDisbursementData] = useState({
    totalAmount: 0,
    eligibleMembers: [],
    dividendPerShare: 0,
    description: '',
  });

  // Legacy form for backward compatibility
  const [disbursementForm, setDisbursementForm] = useState({
    amount: '',
    fromAccount: 'chama_main',
    toAccount: '',
    purpose: '',
    category: 'loan',
    privateNote: '',
    requiresApproval: true
  });

  // 📊 LIVE TRANSPARENCY FEED STATE
  const [transparencyFeed, setTransparencyFeed] = useState([]);
  const [feedFilters, setFeedFilters] = useState({
    dateRange: 'all',
    category: 'all',
    role: 'all',
    status: 'all',
    amountRange: { min: '', max: '' }
  });

  // 🔍 AUDIT & RECEIPT STATE
  const [showAuditPanel, setShowAuditPanel] = useState(false);
  const [auditFilters, setAuditFilters] = useState({});
  const [selectedReceipts, setSelectedReceipts] = useState([]);

  // 🔔 NOTIFICATION STATE
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);

  // 🎨 ANIMATION REFS
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    initializeSecureSystem();
  }, [chamaId]);

  useEffect(() => {
    // Real-time feed updates every 30 seconds
    const interval = setInterval(loadTransparencyFeed, 30000);
    return () => clearInterval(interval);
  }, []);

  // 🔐 INITIALIZE SECURE SYSTEM
  const initializeSecureSystem = async () => {
    try {
      setLoading(true);
      console.log('🔐 Initializing secure account management system...');

      await Promise.all([
        loadUserRole(),
        loadTransparencyFeed(),
        loadNotifications(),
        loadEligibleMembers(),
        validateSystemSecurity()
      ]);

      console.log('✅ Secure system initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize secure system:', error);
      Alert.alert('Security Error', 'Failed to initialize secure system. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // 🔐 ROLE-BASED SECURITY FUNCTIONS
  const loadUserRole = async () => {
    try {
      const response = await ApiService.getMemberRole(chamaId, user.id);
      if (response.success) {
        const role = response.data?.role || 'member';
        setUserRole(role);
        console.log('🔐 User role loaded:', role);

        // Log security access
        await logSecurityEvent('role_access', { role, userId: user.id });
      }
    } catch (error) {
      console.error('🔐 Error loading user role:', error);
      setUserRole('member'); // Default to most restrictive
    }
  };

  // 🔐 PERMISSION CHECKS
  const canInitiateDisbursements = () => {
    return ['treasurer', 'secretary', 'chairperson'].includes(userRole.toLowerCase());
  };

  const canApproveDisbursements = () => {
    return ['chairperson', 'treasurer'].includes(userRole.toLowerCase());
  };

  const canViewFullAudit = () => {
    return ['chairperson', 'auditor', 'treasurer'].includes(userRole.toLowerCase());
  };

  const canFreezeAccounts = () => {
    return userRole.toLowerCase() === 'chairperson';
  };

  const canGenerateReceipts = () => {
    return ['treasurer', 'secretary', 'chairperson', 'auditor'].includes(userRole.toLowerCase());
  };

  // 🎯 ELIGIBLE MEMBERS LOADING
  const loadEligibleMembers = async () => {
    try {
      console.log('🎯 Loading eligible members for disbursement...');

      // Load different member lists based on disbursement type
      const [loanEligible, welfareEligible, dividendEligible] = await Promise.all([
        ApiService.getEligibleLoanMembers(chamaId),
        ApiService.getEligibleWelfareMembers(chamaId),
        ApiService.getEligibleDividendMembers(chamaId)
      ]);

      const eligibleData = {
        loan: loanEligible.data || [],
        welfare: welfareEligible.data || [],
        dividend: dividendEligible.data || []
      };

      setEligibleMembers(eligibleData);
      console.log('✅ Eligible members loaded:', eligibleData);
    } catch (error) {
      console.error('🎯 Error loading eligible members:', error);
      setEligibleMembers({ loan: [], welfare: [], dividend: [] });
    }
  };

  // 🔐 SECURITY & AUDIT FUNCTIONS
  const logSecurityEvent = async (eventType, metadata) => {
    try {
      const securityLog = {
        eventType,
        userId: user.id,
        userRole,
        timestamp: new Date().toISOString(),
        chamaId,
        metadata: {
          ...metadata,
          ip: await getDeviceIP(),
          device: Platform.OS,
          appVersion: '1.0.0'
        }
      };

      await ApiService.logSecurityEvent(securityLog);
      console.log('🔐 Security event logged:', eventType);
    } catch (error) {
      console.error('🔐 Failed to log security event:', error);
    }
  };

  const validateSystemSecurity = async () => {
    try {
      const response = await ApiService.validateSystemSecurity(chamaId, user.id);
      if (!response.success) {
        throw new Error('Security validation failed');
      }
      return true;
    } catch (error) {
      console.error('🔐 Security validation failed:', error);
      return false;
    }
  };

  const getDeviceIP = async () => {
    try {
      // In a real app, you'd get the actual IP
      return 'device_ip_placeholder';
    } catch (error) {
      return 'unknown';
    }
  };

  // 📊 LIVE TRANSPARENCY FEED
  const loadTransparencyFeed = async () => {
    try {
      console.log('📊 Loading live transparency feed...');
      const response = await ApiService.getTransparencyFeed(chamaId, feedFilters);

      if (response.success) {
        const feed = response.data || [];
        setTransparencyFeed(feed);
        console.log('📊 Transparency feed loaded:', feed.length, 'transactions');
      }
    } catch (error) {
      console.error('📊 Failed to load transparency feed:', error);
      setTransparencyFeed([]);
    }
  };

  // 🔔 NOTIFICATIONS
  const loadNotifications = async () => {
    try {
      const response = await ApiService.getAccountNotifications(chamaId, user.id);
      if (response.success) {
        setNotifications(response.data || []);
      }
    } catch (error) {
      console.error('🔔 Failed to load notifications:', error);
      setNotifications([]);
    }
  };

  const sendSystemNotification = async (message, transactionData) => {
    try {
      const notification = {
        message,
        transactionData,
        timestamp: new Date().toISOString(),
        initiatedBy: userRole,
        chamaId
      };

      await ApiService.sendSystemNotification(notification);
      console.log('🔔 System notification sent');
    } catch (error) {
      console.error('🔔 Failed to send notification:', error);
    }
  };

  // 🎯 INDIVIDUAL DISBURSEMENT (Loans & Welfare)
  const handleIndividualDisbursement = async () => {
    if (!canInitiateDisbursements()) {
      Alert.alert('Access Denied', 'You do not have permission to initiate disbursements.');
      return;
    }

    // Validate form
    if (!selectedMember || !individualDisbursementForm.amount || !individualDisbursementForm.purpose) {
      Alert.alert('Validation Error', 'Please select a member and fill in all required fields.');
      return;
    }

    try {
      console.log('🎯 Initiating individual disbursement...');

      const disbursementData = {
        type: 'individual',
        category: disbursementType,
        memberId: selectedMember.id,
        memberName: selectedMember.name,
        amount: parseFloat(individualDisbursementForm.amount),
        purpose: individualDisbursementForm.purpose,
        privateNote: individualDisbursementForm.privateNote,
        fromAccount: disbursementType === 'loan' ? 'loan_fund' : 'welfare',
        toAccount: 'member_wallet',
        initiatedBy: userRole,
        initiatedById: user.id,
        timestamp: new Date().toISOString(),
        status: 'pending',
        transactionId: generateTransactionId(),
        securityHash: await generateSecurityHash(individualDisbursementForm)
      };

      const response = await ApiService.createIndividualDisbursement(chamaId, disbursementData);

      if (response.success) {
        // Log security event
        await logSecurityEvent('individual_disbursement_initiated', disbursementData);

        // Send notification
        const notificationMessage = `KES ${formatCurrency(disbursementData.amount)} ${disbursementType} disbursement initiated to ${selectedMember.name} by ${userRole}`;
        await sendSystemNotification(notificationMessage, disbursementData);

        // Reset form and refresh
        resetIndividualDisbursementForm();
        await loadTransparencyFeed();
        await loadEligibleMembers();

        Alert.alert('Success', `${disbursementType.charAt(0).toUpperCase() + disbursementType.slice(1)} disbursement initiated successfully.`);
      } else {
        Alert.alert('Error', response.error || 'Failed to initiate disbursement.');
      }
    } catch (error) {
      console.error('🎯 Individual disbursement error:', error);
      Alert.alert('Error', 'Failed to process disbursement. Please try again.');
    }
  };

  // 📊 BULK DISBURSEMENT (Dividends/Shares)
  const handleBulkDisbursement = async () => {
    if (!canInitiateDisbursements()) {
      Alert.alert('Access Denied', 'You do not have permission to initiate disbursements.');
      return;
    }

    // Validate bulk disbursement data
    if (!bulkDisbursementData.dividendPerShare || bulkDisbursementData.eligibleMembers.length === 0) {
      Alert.alert('Validation Error', 'Please set dividend per share and ensure eligible members are loaded.');
      return;
    }

    Alert.alert(
      'Confirm Bulk Disbursement',
      `This will disburse dividends to ${bulkDisbursementData.eligibleMembers.length} members.\n\nTotal Amount: KES ${formatCurrency(bulkDisbursementData.totalAmount)}\n\nContinue?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Proceed',
          onPress: async () => {
            try {
              console.log('📊 Initiating bulk disbursement...');

              const disbursementData = {
                type: 'bulk',
                category: 'dividend',
                dividendPerShare: bulkDisbursementData.dividendPerShare,
                totalAmount: bulkDisbursementData.totalAmount,
                description: bulkDisbursementData.description,
                eligibleMembers: bulkDisbursementData.eligibleMembers,
                fromAccount: 'chama_main',
                initiatedBy: userRole,
                initiatedById: user.id,
                timestamp: new Date().toISOString(),
                status: 'pending',
                transactionId: generateTransactionId(),
                securityHash: await generateSecurityHash(bulkDisbursementData)
              };

              const response = await ApiService.createBulkDisbursement(chamaId, disbursementData);

              if (response.success) {
                // Log security event
                await logSecurityEvent('bulk_disbursement_initiated', disbursementData);

                // Send notification
                const notificationMessage = `Bulk dividend disbursement of KES ${formatCurrency(disbursementData.totalAmount)} initiated to ${disbursementData.eligibleMembers.length} members by ${userRole}`;
                await sendSystemNotification(notificationMessage, disbursementData);

                // Reset and refresh
                resetBulkDisbursementForm();
                await loadTransparencyFeed();

                Alert.alert('Success', 'Bulk dividend disbursement initiated successfully.');
              } else {
                Alert.alert('Error', response.error || 'Failed to initiate bulk disbursement.');
              }
            } catch (error) {
              console.error('📊 Bulk disbursement error:', error);
              Alert.alert('Error', 'Failed to process bulk disbursement. Please try again.');
            }
          }
        }
      ]
    );
  };

  // 💰 LEGACY DISBURSEMENT FUNCTION (for backward compatibility)
  const handleDisbursementSubmit = async () => {
    // Route to appropriate disbursement handler based on type
    if (disbursementType === 'dividend') {
      return handleBulkDisbursement();
    } else {
      return handleIndividualDisbursement();
    }
  };

  // 🔄 FORM RESET FUNCTIONS
  const resetDisbursementForm = () => {
    setDisbursementForm({
      amount: '',
      fromAccount: 'chama_main',
      toAccount: '',
      purpose: '',
      category: 'loan',
      privateNote: '',
      requiresApproval: true
    });
  };

  const resetIndividualDisbursementForm = () => {
    setIndividualDisbursementForm({
      memberId: '',
      memberName: '',
      amount: '',
      purpose: '',
      privateNote: '',
    });
    setSelectedMember(null);
  };

  const resetBulkDisbursementForm = () => {
    setBulkDisbursementData({
      totalAmount: 0,
      eligibleMembers: [],
      dividendPerShare: 0,
      description: '',
    });
  };

  const generateTransactionId = () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `TXN_${timestamp}_${random}`.toUpperCase();
  };

  const generateSecurityHash = async (data) => {
    // In a real app, this would be a proper cryptographic hash
    const hashInput = JSON.stringify(data) + user.id + Date.now();
    return btoa(hashInput).substring(0, 32);
  };

  // 🔐 APPROVAL & ACCOUNT MANAGEMENT
  const handleApproveDisbursement = async (transactionId) => {
    if (!canApproveDisbursements()) {
      Alert.alert('Access Denied', 'You do not have permission to approve disbursements.');
      return;
    }

    try {
      const response = await ApiService.approveDisbursement(chamaId, transactionId, {
        approvedBy: userRole,
        approvedById: user.id,
        timestamp: new Date().toISOString()
      });

      if (response.success) {
        await logSecurityEvent('disbursement_approved', { transactionId });
        await loadTransparencyFeed();
        Alert.alert('Success', 'Disbursement approved successfully.');
      }
    } catch (error) {
      console.error('🔐 Approval error:', error);
      Alert.alert('Error', 'Failed to approve disbursement.');
    }
  };

  const handleFreezeAccount = async (accountType) => {
    if (!canFreezeAccounts()) {
      Alert.alert('Access Denied', 'Only chairperson can freeze accounts.');
      return;
    }

    Alert.alert(
      'Freeze Account',
      `Are you sure you want to freeze the ${accountType} account? This will prevent all transactions.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Freeze',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await ApiService.freezeAccount(chamaId, accountType, {
                frozenBy: userRole,
                frozenById: user.id,
                timestamp: new Date().toISOString()
              });

              if (response.success) {
                await logSecurityEvent('account_frozen', { accountType });
                Alert.alert('Success', `${accountType} account has been frozen.`);
              }
            } catch (error) {
              console.error('🔐 Freeze error:', error);
              Alert.alert('Error', 'Failed to freeze account.');
            }
          }
        }
      ]
    );
  };

  // 📋 RECEIPT & AUDIT FUNCTIONS
  const generateReceipt = async (transactionId, receiptType) => {
    if (!canGenerateReceipts()) {
      Alert.alert('Access Denied', 'You do not have permission to generate receipts.');
      return;
    }

    try {
      console.log('📋 Generating receipt...', receiptType);

      const receiptData = {
        transactionId,
        receiptType,
        generatedBy: userRole,
        generatedById: user.id,
        timestamp: new Date().toISOString(),
        chamaId
      };

      const response = await ApiService.generateReceipt(receiptData);

      if (response.success) {
        await logSecurityEvent('receipt_generated', receiptData);

        // Share or download receipt
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          await Share.share({
            title: `${receiptType} Receipt`,
            message: `Receipt generated for transaction ${transactionId}`,
            url: response.data.receiptUrl
          });
        }

        Alert.alert('Success', 'Receipt generated successfully.');
      }
    } catch (error) {
      console.error('📋 Receipt error:', error);
      Alert.alert('Error', 'Failed to generate receipt.');
    }
  };

  // 🎨 UI RENDERING FUNCTIONS
  const renderSecurityHeader = () => (
    <View style={[styles.securityHeader, { backgroundColor: colors.surface }]}>
      {/* Top Row - Title and Role */}
      <View style={styles.headerTopRow}>
        <View style={styles.headerLeft}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Account Management
          </Text>
        </View>

        <View style={styles.headerRight}>
          <View style={[styles.roleBadge, { backgroundColor: getRoleColor(userRole) + '20' }]}>
            <Ionicons name={getRoleIcon(userRole)} size={14} color={getRoleColor(userRole)} />
            <Text style={[styles.roleText, { color: getRoleColor(userRole) }]}>
              {userRole.toUpperCase()}
            </Text>
          </View>

          {notifications.length > 0 && (
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={() => setShowNotifications(true)}
            >
              <Ionicons name="notifications" size={20} color={colors.warning} />
              <View style={[styles.notificationBadge, { backgroundColor: colors.error }]}>
                <Text style={[styles.notificationCount, { color: colors.white }]}>
                  {notifications.length}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Bottom Row - Panel Navigation Tabs */}
      <View style={styles.headerBottomRow}>
        <View style={styles.headerTabs}>
          {[
            { key: 'disbursement', label: 'Disbursement', icon: 'send', color: colors.primary },
            { key: 'transparency', label: 'Live Feed', icon: 'eye', color: colors.info },
            { key: 'audit', label: 'Audit', icon: 'document-text', color: colors.warning }
          ].map((panel) => (
            <TouchableOpacity
              key={panel.key}
              style={[
                styles.headerTab,
                activePanel === panel.key && {
                  backgroundColor: panel.color + '20',
                  borderBottomWidth: 2,
                  borderBottomColor: panel.color
                }
              ]}
              onPress={() => setActivePanel(panel.key)}
            >
              <Ionicons
                name={panel.icon}
                size={14}
                color={activePanel === panel.key ? panel.color : colors.textSecondary}
              />
              <Text
                style={[
                  styles.headerTabText,
                  { color: activePanel === panel.key ? panel.color : colors.textSecondary }
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {panel.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const getRoleColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'chairperson': return colors.error;
      case 'treasurer': return colors.warning;
      case 'secretary': return colors.info;
      case 'auditor': return colors.success;
      default: return colors.textSecondary;
    }
  };

  const getRoleIcon = (role) => {
    switch (role?.toLowerCase()) {
      case 'chairperson': return 'crown';
      case 'treasurer': return 'wallet';
      case 'secretary': return 'document-text';
      case 'auditor': return 'search';
      default: return 'person';
    }
  };

  // Panel navigation now integrated into header - removed standalone function

  const renderDisbursementPanel = () => (
    <Card style={[styles.disbursementCard, { backgroundColor: colors.surface }]}>
      <View style={styles.cardHeader}>
        <View style={styles.cardTitleRow}>
          <Ionicons name="send" size={24} color={colors.primary} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Disbursement Panel
          </Text>
          {canInitiateDisbursements() && (
            <View style={[styles.permissionBadge, { backgroundColor: colors.success + '20' }]}>
              <Ionicons name="checkmark-circle" size={14} color={colors.success} />
              <Text style={[styles.permissionText, { color: colors.success }]}>
                AUTHORIZED
              </Text>
            </View>
          )}
        </View>
        <Text style={[styles.cardSubtitle, { color: colors.textSecondary }]}>
          Secure disbursements to qualified chama members only
        </Text>
      </View>

      {/* Disbursement Type Selection */}
      <View style={styles.disbursementTypeSelector}>
        <Text style={[styles.formLabel, { color: colors.text }]}>
          Disbursement Type
        </Text>
        <View style={styles.typeButtons}>
          {[
            { key: 'loan', label: 'Loan', icon: 'card', color: colors.primary, description: 'Individual qualified members' },
            { key: 'welfare', label: 'Welfare', icon: 'heart', color: colors.warning, description: 'Individual eligible members' },
            { key: 'dividend', label: 'Dividend', icon: 'cash', color: colors.success, description: 'Bulk to all shareholders' }
          ].map((type) => (
            <TouchableOpacity
              key={type.key}
              style={[
                styles.typeButton,
                { borderColor: type.color },
                disbursementType === type.key && { backgroundColor: type.color + '20' }
              ]}
              onPress={() => setDisbursementType(type.key)}
            >
              <Ionicons
                name={type.icon}
                size={20}
                color={disbursementType === type.key ? type.color : colors.textSecondary}
              />
              <View style={styles.typeButtonContent}>
                <Text style={[
                  styles.typeButtonText,
                  { color: disbursementType === type.key ? type.color : colors.textSecondary }
                ]}>
                  {type.label}
                </Text>
                <Text style={[styles.typeButtonDescription, { color: colors.textSecondary }]}>
                  {type.description}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {!canInitiateDisbursements() ? (
        <View style={[styles.accessDenied, { backgroundColor: colors.error + '10' }]}>
          <Ionicons name="lock-closed" size={32} color={colors.error} />
          <Text style={[styles.accessDeniedTitle, { color: colors.error }]}>
            Access Restricted
          </Text>
          <Text style={[styles.accessDeniedText, { color: colors.textSecondary }]}>
            Only Treasurer, Secretary, and Chairperson can initiate disbursements
          </Text>
        </View>
      ) : (
        <View style={styles.disbursementForm}>
          {/* Render Individual Disbursement Form (Loans & Welfare) */}
          {(disbursementType === 'loan' || disbursementType === 'welfare') && (
            <View style={styles.individualDisbursementForm}>
              {/* Member Selection */}
              <View style={styles.formGroup}>
                <Text style={[styles.formLabel, { color: colors.text }]}>
                  Select {disbursementType === 'loan' ? 'Qualified' : 'Eligible'} Member *
                </Text>
                {eligibleMembers[disbursementType] && eligibleMembers[disbursementType].length > 0 ? (
                  <ScrollView style={styles.membersList} showsVerticalScrollIndicator={false}>
                    {eligibleMembers[disbursementType].map((member) => (
                      <TouchableOpacity
                        key={member.id}
                        style={[
                          styles.memberItem,
                          { borderColor: colors.border },
                          selectedMember?.id === member.id && { backgroundColor: colors.primary + '20', borderColor: colors.primary }
                        ]}
                        onPress={() => {
                          setSelectedMember(member);
                          setIndividualDisbursementForm(prev => ({
                            ...prev,
                            memberId: member.id,
                            memberName: member.name
                          }));
                        }}
                      >
                        <View style={styles.memberInfo}>
                          <Text style={[styles.memberName, { color: colors.text }]}>
                            {member.name}
                          </Text>
                          <Text style={[styles.memberDetails, { color: colors.textSecondary }]}>
                            {disbursementType === 'loan'
                              ? `Approved: KES ${formatCurrency(member.approvedAmount)} | Status: ${member.status}`
                              : `Contribution Complete: KES ${formatCurrency(member.contributionAmount)}`
                            }
                          </Text>
                        </View>
                        {selectedMember?.id === member.id && (
                          <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
                        )}
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                ) : (
                  <View style={[styles.emptyMembers, { backgroundColor: colors.warning + '10' }]}>
                    <Ionicons name="person-outline" size={32} color={colors.warning} />
                    <Text style={[styles.emptyMembersText, { color: colors.text }]}>
                      No {disbursementType === 'loan' ? 'qualified loan' : 'eligible welfare'} members found
                    </Text>
                    <Text style={[styles.emptyMembersSubtext, { color: colors.textSecondary }]}>
                      {disbursementType === 'loan'
                        ? 'Members must be approved and not yet disbursed'
                        : 'Members must have completed contribution period'
                      }
                    </Text>
                  </View>
                )}
              </View>

              {/* Amount Input */}
              {selectedMember && (
                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Amount (KES) *
                  </Text>
                  <TextInput
                    style={[styles.formInput, {
                      backgroundColor: colors.background,
                      borderColor: colors.border,
                      color: colors.text
                    }]}
                    value={individualDisbursementForm.amount}
                    onChangeText={(text) => setIndividualDisbursementForm(prev => ({ ...prev, amount: text }))}
                    placeholder={disbursementType === 'loan'
                      ? `Max: ${formatCurrency(selectedMember.approvedAmount)}`
                      : `Suggested: ${formatCurrency(selectedMember.contributionAmount)}`
                    }
                    placeholderTextColor={colors.textSecondary}
                    keyboardType="numeric"
                  />
                  {disbursementType === 'loan' && selectedMember.approvedAmount && (
                    <Text style={[styles.formHint, { color: colors.textSecondary }]}>
                      Maximum disbursable: KES {formatCurrency(selectedMember.approvedAmount)}
                    </Text>
                  )}
                </View>
              )}

              {/* Purpose Input */}
              {selectedMember && (
                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Purpose *
                  </Text>
                  <TextInput
                    style={[styles.formInput, {
                      backgroundColor: colors.background,
                      borderColor: colors.border,
                      color: colors.text
                    }]}
                    value={individualDisbursementForm.purpose}
                    onChangeText={(text) => setIndividualDisbursementForm(prev => ({ ...prev, purpose: text }))}
                    placeholder={`Enter ${disbursementType} disbursement purpose`}
                    placeholderTextColor={colors.textSecondary}
                    multiline
                    numberOfLines={2}
                  />
                </View>
              )}

              {/* Private Note */}
              {selectedMember && (
                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Private Note (Audit Trail)
                  </Text>
                  <TextInput
                    style={[styles.formInput, {
                      backgroundColor: colors.background,
                      borderColor: colors.border,
                      color: colors.text
                    }]}
                    value={individualDisbursementForm.privateNote}
                    onChangeText={(text) => setIndividualDisbursementForm(prev => ({ ...prev, privateNote: text }))}
                    placeholder="Optional note for audit purposes"
                    placeholderTextColor={colors.textSecondary}
                    multiline
                    numberOfLines={2}
                  />
                </View>
              )}

              {/* Submit Button */}
              {selectedMember && (
                <Button
                  title={`Disburse ${disbursementType.charAt(0).toUpperCase() + disbursementType.slice(1)}`}
                  onPress={handleIndividualDisbursement}
                  style={[styles.submitButton, { backgroundColor: disbursementType === 'loan' ? colors.primary : colors.warning }]}
                  icon={<Ionicons name="send" size={20} color={colors.white} />}
                  disabled={!individualDisbursementForm.amount || !individualDisbursementForm.purpose}
                />
              )}
            </View>
          )}

          {/* Render Bulk Disbursement Form (Dividends) */}
          {disbursementType === 'dividend' && (
            <View style={styles.bulkDisbursementForm}>
              {/* Dividend Per Share */}
              <View style={styles.formGroup}>
                <Text style={[styles.formLabel, { color: colors.text }]}>
                  Dividend Per Share (KES) *
                </Text>
                <TextInput
                  style={[styles.formInput, {
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    color: colors.text
                  }]}
                  value={bulkDisbursementData.dividendPerShare.toString()}
                  onChangeText={(text) => {
                    const perShare = parseFloat(text) || 0;
                    const totalShares = eligibleMembers.dividend?.reduce((sum, member) => sum + member.sharesOwned, 0) || 0;
                    setBulkDisbursementData(prev => ({
                      ...prev,
                      dividendPerShare: perShare,
                      totalAmount: perShare * totalShares,
                      eligibleMembers: eligibleMembers.dividend || []
                    }));
                  }}
                  placeholder="Enter dividend per share"
                  placeholderTextColor={colors.textSecondary}
                  keyboardType="numeric"
                />
              </View>

              {/* Eligible Members Summary */}
              {eligibleMembers.dividend && eligibleMembers.dividend.length > 0 && (
                <View style={[styles.eligibleMembersSummary, { backgroundColor: colors.success + '10' }]}>
                  <View style={styles.summaryHeader}>
                    <Ionicons name="people" size={20} color={colors.success} />
                    <Text style={[styles.summaryTitle, { color: colors.success }]}>
                      Eligible Shareholders: {eligibleMembers.dividend.length}
                    </Text>
                  </View>
                  <Text style={[styles.summaryText, { color: colors.textSecondary }]}>
                    Total Shares: {eligibleMembers.dividend.reduce((sum, member) => sum + member.sharesOwned, 0).toLocaleString()}
                  </Text>
                  {bulkDisbursementData.dividendPerShare > 0 && (
                    <Text style={[styles.summaryAmount, { color: colors.success }]}>
                      Total Disbursement: KES {formatCurrency(bulkDisbursementData.totalAmount)}
                    </Text>
                  )}
                </View>
              )}

              {/* Description */}
              <View style={styles.formGroup}>
                <Text style={[styles.formLabel, { color: colors.text }]}>
                  Description
                </Text>
                <TextInput
                  style={[styles.formInput, {
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    color: colors.text
                  }]}
                  value={bulkDisbursementData.description}
                  onChangeText={(text) => setBulkDisbursementData(prev => ({ ...prev, description: text }))}
                  placeholder="Enter dividend description (optional)"
                  placeholderTextColor={colors.textSecondary}
                  multiline
                  numberOfLines={2}
                />
              </View>

              {/* Submit Button */}
              <Button
                title="Process Bulk Dividend Disbursement"
                onPress={handleBulkDisbursement}
                style={[styles.submitButton, { backgroundColor: colors.success }]}
                icon={<Ionicons name="cash" size={20} color={colors.white} />}
                disabled={!bulkDisbursementData.dividendPerShare || bulkDisbursementData.eligibleMembers.length === 0}
              />
            </View>
          )}

          {/* Security Notice */}
          <View style={[styles.securityNotice, { backgroundColor: colors.info + '10' }]}>
            <Ionicons name="shield-checkmark" size={16} color={colors.info} />
            <Text style={[styles.securityNoticeText, { color: colors.textSecondary }]}>
              All disbursements are logged with timestamp, role, and digital signature for audit purposes.
              {disbursementType === 'dividend' && ' Bulk disbursements process all eligible members automatically.'}
            </Text>
          </View>
        </View>
      )}
    </Card>
  );

  const getAccountDisplayName = (accountKey) => {
    const accounts = {
      'chama_main': 'Chama Main Account',
      'welfare': 'Welfare Fund',
      'investment': 'Investment Fund',
      'loan_fund': 'Loan Fund',
      'member_wallet': 'Member Wallet',
      'external_vendor': 'External Vendor'
    };
    return accounts[accountKey] || accountKey;
  };

  const showAccountPicker = (type) => {
    const accounts = [
      { key: 'chama_main', label: 'Chama Main Account', icon: 'business' },
      { key: 'welfare', label: 'Welfare Fund', icon: 'heart' },
      { key: 'investment', label: 'Investment Fund', icon: 'trending-up' },
      { key: 'loan_fund', label: 'Loan Fund', icon: 'card' },
      { key: 'member_wallet', label: 'Member Wallet', icon: 'wallet' },
      { key: 'external_vendor', label: 'External Vendor', icon: 'business-outline' }
    ];

    Alert.alert(
      `Select ${type === 'from' ? 'Source' : 'Destination'} Account`,
      'Choose the account for this transaction',
      accounts.map(account => ({
        text: account.label,
        onPress: () => {
          if (type === 'from') {
            setDisbursementForm(prev => ({ ...prev, fromAccount: account.key }));
          } else {
            setDisbursementForm(prev => ({ ...prev, toAccount: account.key }));
          }
        }
      })).concat([{ text: 'Cancel', style: 'cancel' }])
    );
  };

  const renderTransparencyFeed = () => (
    <Card style={[styles.transparencyCard, { backgroundColor: colors.surface }]}>
      <View style={styles.cardHeader}>
        <View style={styles.cardTitleRow}>
          <Ionicons name="eye" size={24} color={colors.info} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Live Transparency Feed
          </Text>
          <View style={[styles.liveBadge, { backgroundColor: colors.success + '20' }]}>
            <View style={[styles.liveIndicator, { backgroundColor: colors.success }]} />
            <Text style={[styles.liveText, { color: colors.success }]}>
              LIVE
            </Text>
          </View>
        </View>
        <Text style={[styles.cardSubtitle, { color: colors.textSecondary }]}>
          Real-time ledger of all financial transactions
        </Text>
      </View>

      {/* Feed Filters */}
      <View style={styles.feedFilters}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {[
            { key: 'all', label: 'All', icon: 'list' },
            { key: 'loan', label: 'Loans', icon: 'card', color: colors.primary },
            { key: 'dividend', label: 'Dividends', icon: 'cash', color: colors.success },
            { key: 'welfare', label: 'Welfare', icon: 'heart', color: colors.warning },
            { key: 'expense', label: 'Expenses', icon: 'receipt', color: colors.error }
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                { borderColor: filter.color || colors.textSecondary },
                feedFilters.category === filter.key && {
                  backgroundColor: (filter.color || colors.textSecondary) + '20'
                }
              ]}
              onPress={() => setFeedFilters(prev => ({ ...prev, category: filter.key }))}
            >
              <Ionicons
                name={filter.icon}
                size={14}
                color={feedFilters.category === filter.key ? (filter.color || colors.textSecondary) : colors.textSecondary}
              />
              <Text style={[
                styles.filterButtonText,
                { color: feedFilters.category === filter.key ? (filter.color || colors.textSecondary) : colors.textSecondary }
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Transaction Feed */}
      <View style={styles.transactionFeed}>
        {transparencyFeed.length === 0 ? (
          <View style={styles.emptyFeed}>
            <Ionicons name="document-outline" size={48} color={colors.textTertiary} />
            <Text style={[styles.emptyFeedTitle, { color: colors.text }]}>
              No Transactions Yet
            </Text>
            <Text style={[styles.emptyFeedText, { color: colors.textSecondary }]}>
              Financial transactions will appear here in real-time
            </Text>
          </View>
        ) : (
          <FlatList
            data={transparencyFeed}
            keyExtractor={(item) => item.id || item.transactionId}
            renderItem={renderTransactionItem}
            showsVerticalScrollIndicator={false}
            style={styles.feedList}
          />
        )}
      </View>

      {/* Privacy Notice */}
      <View style={[styles.privacyNotice, { backgroundColor: colors.warning + '10' }]}>
        <Ionicons name="shield" size={16} color={colors.warning} />
        <Text style={[styles.privacyNoticeText, { color: colors.textSecondary }]}>
          Privacy Protected: Only roles are shown publicly. Full details available to authorized personnel.
        </Text>
      </View>
    </Card>
  );

  const renderTransactionItem = ({ item }) => {
    const categoryColor = getCategoryColor(item.category);
    const statusIcon = item.status === 'completed' ? 'checkmark-circle' :
                      item.status === 'failed' ? 'close-circle' : 'time';
    const statusColor = item.status === 'completed' ? colors.success :
                       item.status === 'failed' ? colors.error : colors.warning;

    return (
      <View style={[styles.transactionItem, { borderLeftColor: categoryColor }]}>
        <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>
            <Text style={[styles.transactionTime, { color: colors.textSecondary }]}>
              {formatDate(item.timestamp)}
            </Text>
            <Text style={[styles.transactionAmount, { color: colors.text }]}>
              KES {formatCurrency(item.amount)}
            </Text>
          </View>
          <View style={styles.transactionStatus}>
            <Ionicons name={statusIcon} size={16} color={statusColor} />
            <Text style={[styles.transactionStatusText, { color: statusColor }]}>
              {item.status?.toUpperCase()}
            </Text>
          </View>
        </View>

        <View style={styles.transactionDetails}>
          <Text style={[styles.transactionFlow, { color: colors.text }]}>
            {getAccountDisplayName(item.fromAccount)} → {getAccountDisplayName(item.toAccount)}
          </Text>
          <Text style={[styles.transactionPurpose, { color: colors.textSecondary }]}>
            {item.purpose}
          </Text>
        </View>

        <View style={styles.transactionFooter}>
          <View style={styles.transactionMeta}>
            <Text style={[styles.transactionRole, { color: colors.textSecondary }]}>
              Initiated by {item.initiatedBy || 'System'}
            </Text>
            <View style={[styles.categoryTag, { backgroundColor: categoryColor + '20' }]}>
              <Text style={[styles.categoryTagText, { color: categoryColor }]}>
                {item.category?.toUpperCase()}
              </Text>
            </View>
          </View>

          {canViewFullAudit() && (
            <TouchableOpacity
              style={styles.auditButton}
              onPress={() => viewTransactionAudit(item.id)}
            >
              <Ionicons name="document-text" size={14} color={colors.info} />
              <Text style={[styles.auditButtonText, { color: colors.info }]}>
                Audit
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const getCategoryColor = (category) => {
    switch (category?.toLowerCase()) {
      case 'loan': return colors.primary;
      case 'dividend': return colors.success;
      case 'welfare': return colors.warning;
      case 'expense': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const viewTransactionAudit = (transactionId) => {
    // Navigate to detailed audit view
    Alert.alert('Audit Details', `Viewing audit trail for transaction ${transactionId}`);
  };

  const renderAuditPanel = () => (
    <Card style={[styles.auditCard, { backgroundColor: colors.surface }]}>
      <TouchableOpacity
        style={styles.auditHeader}
        onPress={() => setShowAuditPanel(!showAuditPanel)}
      >
        <View style={styles.cardTitleRow}>
          <Ionicons name="document-text" size={24} color={colors.warning} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Audit & Receipt System
          </Text>
          {canGenerateReceipts() && (
            <View style={[styles.permissionBadge, { backgroundColor: colors.success + '20' }]}>
              <Ionicons name="checkmark-circle" size={14} color={colors.success} />
              <Text style={[styles.permissionText, { color: colors.success }]}>
                AUTHORIZED
              </Text>
            </View>
          )}
        </View>
        <View style={styles.auditHeaderRight}>
          <Text style={[styles.cardSubtitle, { color: colors.textSecondary }]}>
            Generate receipts and audit reports
          </Text>
          <Ionicons
            name={showAuditPanel ? "chevron-up" : "chevron-down"}
            size={20}
            color={colors.textSecondary}
          />
        </View>
      </TouchableOpacity>

      {showAuditPanel && (
        <Animated.View style={styles.auditContent}>
          {!canGenerateReceipts() ? (
            <View style={[styles.accessDenied, { backgroundColor: colors.error + '10' }]}>
              <Ionicons name="lock-closed" size={32} color={colors.error} />
              <Text style={[styles.accessDeniedTitle, { color: colors.error }]}>
                Access Restricted
              </Text>
              <Text style={[styles.accessDeniedText, { color: colors.textSecondary }]}>
                Only authorized roles can generate receipts and audit reports
              </Text>
            </View>
          ) : (
            <View style={styles.receiptCategories}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Receipt Categories
              </Text>

              <View style={styles.receiptGrid}>
                {[
                  {
                    key: 'revenue',
                    label: 'Revenue Receipts',
                    icon: 'trending-up',
                    color: colors.success,
                    description: 'Contributions, fundraising, loan interest, donations'
                  },
                  {
                    key: 'expense',
                    label: 'Expense Receipts',
                    icon: 'trending-down',
                    color: colors.error,
                    description: 'Rent, services, supplies'
                  },
                  {
                    key: 'loan_disbursement',
                    label: 'Loan Disbursements',
                    icon: 'card',
                    color: colors.primary,
                    description: 'From chama to loan account'
                  },
                  {
                    key: 'loan_repayment',
                    label: 'Loan Repayments',
                    icon: 'card-outline',
                    color: colors.info,
                    description: 'From member to chama'
                  },
                  {
                    key: 'dividend',
                    label: 'Dividend Distribution',
                    icon: 'cash',
                    color: colors.success,
                    description: 'Dividends/shares distributions'
                  },
                  {
                    key: 'welfare',
                    label: 'Welfare Support',
                    icon: 'heart',
                    color: colors.warning,
                    description: 'Welfare support disbursements'
                  },
                  {
                    key: 'transfer',
                    label: 'Account Transfers',
                    icon: 'swap-horizontal',
                    color: colors.info,
                    description: 'Internal account transfers'
                  },
                  {
                    key: 'profit_sharing',
                    label: 'Profit Sharing',
                    icon: 'pie-chart',
                    color: colors.success,
                    description: 'Profit sharing reports'
                  }
                ].map((category) => (
                  <TouchableOpacity
                    key={category.key}
                    style={[styles.receiptCategory, { borderColor: category.color }]}
                    onPress={() => generateReceipt(null, category.key)}
                  >
                    <View style={[styles.receiptCategoryIcon, { backgroundColor: category.color + '20' }]}>
                      <Ionicons name={category.icon} size={24} color={category.color} />
                    </View>
                    <Text style={[styles.receiptCategoryTitle, { color: colors.text }]}>
                      {category.label}
                    </Text>
                    <Text style={[styles.receiptCategoryDesc, { color: colors.textSecondary }]}>
                      {category.description}
                    </Text>
                    <View style={styles.receiptCategoryFooter}>
                      <Ionicons name="document" size={14} color={category.color} />
                      <Text style={[styles.receiptCategoryAction, { color: category.color }]}>
                        Generate
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Export Options */}
              <View style={styles.exportSection}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Export Options
                </Text>
                <View style={styles.exportButtons}>
                  <TouchableOpacity
                    style={[styles.exportButton, { backgroundColor: colors.error + '20' }]}
                    onPress={() => exportData('pdf')}
                  >
                    <Ionicons name="document-text" size={20} color={colors.error} />
                    <Text style={[styles.exportButtonText, { color: colors.error }]}>
                      PDF
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.exportButton, { backgroundColor: colors.success + '20' }]}
                    onPress={() => exportData('csv')}
                  >
                    <Ionicons name="grid" size={20} color={colors.success} />
                    <Text style={[styles.exportButtonText, { color: colors.success }]}>
                      CSV
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.exportButton, { backgroundColor: colors.info + '20' }]}
                    onPress={() => exportData('json')}
                  >
                    <Ionicons name="code" size={20} color={colors.info} />
                    <Text style={[styles.exportButtonText, { color: colors.info }]}>
                      JSON
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Security Features */}
              <View style={[styles.securityFeatures, { backgroundColor: colors.primary + '10' }]}>
                <Ionicons name="shield-checkmark" size={20} color={colors.primary} />
                <View style={styles.securityFeaturesText}>
                  <Text style={[styles.securityFeaturesTitle, { color: colors.text }]}>
                    Security Features
                  </Text>
                  <Text style={[styles.securityFeaturesDesc, { color: colors.textSecondary }]}>
                    • QR code verification • Digital signatures • Encrypted transaction IDs • Immutable audit trail
                  </Text>
                </View>
              </View>
            </View>
          )}
        </Animated.View>
      )}
    </Card>
  );

  const exportData = async (format) => {
    try {
      console.log(`📋 Exporting data in ${format} format...`);

      const exportData = {
        format,
        chamaId,
        filters: feedFilters,
        requestedBy: userRole,
        requestedById: user.id,
        timestamp: new Date().toISOString()
      };

      const response = await ApiService.exportFinancialData(exportData);

      if (response.success) {
        await logSecurityEvent('data_export', exportData);

        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          await Share.share({
            title: `Financial Data Export (${format.toUpperCase()})`,
            message: `Financial data exported in ${format.toUpperCase()} format`,
            url: response.data.downloadUrl
          });
        }

        Alert.alert('Success', `Data exported in ${format.toUpperCase()} format successfully.`);
      }
    } catch (error) {
      console.error('📋 Export error:', error);
      Alert.alert('Error', 'Failed to export data. Please try again.');
    }
  };

  // 🛠️ UTILITY FUNCTIONS
  const formatCurrency = (amount) => {
    if (!amount) return '0.00';
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(amount).replace('KES', '').trim();
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const loadData = async () => {
    await Promise.all([
      loadTransparencyFeed(),
      loadNotifications(),
    ]);
  };

  // 📊 OLD RENDER FUNCTIONS (for compatibility)
  const handleDownloadReport = async (reportId) => {
    try {
      const response = await ApiService.downloadFinancialReport(chamaId, reportId);
      if (response.success) {
        Alert.alert('Download', 'Report download started. You will be notified when complete.');
      } else {
        Alert.alert('Error', response.error || 'Failed to download report');
      }
    } catch (error) {
      console.error('Error downloading report:', error);
      Alert.alert('Error', 'Failed to download report');
    }
  };

  const handleGenerateReport = async (reportType) => {
    try {
      const reportData = {
        reportType,
        reportPeriodStart: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // Start of current month
        reportPeriodEnd: new Date(), // Current date
      };

      const response = await ApiService.generateFinancialReport(chamaId, reportData);
      if (response.success) {
        Alert.alert('Success', 'Report generation started. You will be notified when ready.');
        await loadReports();
      } else {
        Alert.alert('Error', response.error || 'Failed to generate report');
      }
    } catch (error) {
      console.error('Error generating report:', error);
      Alert.alert('Error', 'Failed to generate report');
    }
  };

  const canManageFinances = () => {
    return ['chairperson', 'secretary', 'treasurer'].includes(userRole);
  };

  const canApproveFinances = () => {
    return ['chairperson', 'treasurer'].includes(userRole);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return colors.warning;
      case 'approved': return colors.info;
      case 'processing': return colors.primary;
      case 'completed': return colors.success;
      case 'failed': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getBatchTypeIcon = (type) => {
    switch (type) {
      case 'dividend': return 'cash';
      case 'shares': return 'pie-chart';
      case 'savings': return 'wallet';
      case 'loan': return 'card';
      default: return 'document';
    }
  };

  const getActivityTypeIcon = (type) => {
    switch (type) {
      case 'disbursement': return 'arrow-up';
      case 'revenue': return 'arrow-down';
      case 'expense': return 'arrow-up';
      case 'contribution': return 'arrow-down';
      default: return 'document';
    }
  };

  const getReportTypeIcon = (type) => {
    switch (type) {
      case 'monthly_statement': return 'calendar';
      case 'dividend_report': return 'cash';
      case 'transparency_report': return 'eye';
      case 'disbursement_report': return 'send';
      default: return 'document-text';
    }
  };

  const renderDisbursementItem = ({ item }) => (
    <Card style={[styles.itemCard, { backgroundColor: colors.surface }]}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <View style={styles.titleRow}>
            <Ionicons
              name={getBatchTypeIcon(item.batchType)}
              size={20}
              color={colors.primary}
              style={styles.itemIcon}
            />
            <Text style={[styles.itemTitle, { color: colors.text }]}>{item.title}</Text>
          </View>
          <Text style={[styles.itemSubtitle, { color: colors.textSecondary }]}>
            Initiated by {item.initiatedBy}
          </Text>
        </View>
        <View style={styles.itemValues}>
          <Text style={[styles.itemAmount, { color: colors.primary }]}>
            {formatCurrency(item.totalAmount)}
          </Text>
          <Text style={[styles.itemRecipients, { color: colors.textSecondary }]}>
            {item.totalRecipients} recipients
          </Text>
        </View>
      </View>

      {item.description && (
        <Text style={[styles.itemDescription, { color: colors.textSecondary }]}>
          {item.description}
        </Text>
      )}

      <View style={styles.itemMeta}>
        <Text style={[styles.itemDate, { color: colors.textSecondary }]}>
          Created: {formatDate(item.createdAt, 'date')}
        </Text>
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(item.status) + '15' }
        ]}>
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status}
          </Text>
        </View>
      </View>

      {canManageFinances() && (
        <View style={styles.actionButtons}>
          {item.status === 'pending' && canApproveFinances() && (
            <Button
              title="Approve"
              onPress={() => handleApproveDisbursement(item.id)}
              style={[styles.actionButton, { backgroundColor: colors.success }]}
              textStyle={{ fontSize: 14 }}
            />
          )}
          {item.status === 'approved' && (
            <Button
              title="Process Payments"
              onPress={() => Alert.alert('Process Disbursement', 'Processing functionality will be implemented in the backend.')}
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              textStyle={{ fontSize: 14 }}
            />
          )}
        </View>
      )}

      {/* Recipients Preview */}
      {item.recipients && item.recipients.length > 0 && (
        <View style={styles.recipientsPreview}>
          <Text style={[styles.recipientsTitle, { color: colors.text }]}>
            Recipients ({item.recipients.length})
          </Text>
          {item.recipients.slice(0, 3).map((recipient, index) => (
            <View key={recipient.id} style={styles.recipientItem}>
              <Text style={[styles.recipientName, { color: colors.textSecondary }]}>
                {recipient.name}
              </Text>
              <Text style={[styles.recipientAmount, { color: colors.text }]}>
                {formatCurrency(recipient.amount)}
              </Text>
            </View>
          ))}
          {item.recipients.length > 3 && (
            <Text style={[styles.moreRecipients, { color: colors.primary }]}>
              +{item.recipients.length - 3} more recipients
            </Text>
          )}
        </View>
      )}
    </Card>
  );

  const renderTransparencyItem = ({ item }) => (
    <Card style={[styles.itemCard, { backgroundColor: colors.surface }]}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <View style={styles.titleRow}>
            <Ionicons
              name={getActivityTypeIcon(item.activityType)}
              size={20}
              color={item.transactionType === 'credit' ? colors.success : colors.error}
              style={styles.itemIcon}
            />
            <Text style={[styles.itemTitle, { color: colors.text }]}>{item.title}</Text>
          </View>
          <Text style={[styles.itemSubtitle, { color: colors.textSecondary }]}>
            by {item.performedBy}
          </Text>
        </View>
        <View style={styles.itemValues}>
          <Text style={[
            styles.itemAmount,
            { color: item.transactionType === 'credit' ? colors.success : colors.error }
          ]}>
            {item.transactionType === 'credit' ? '+' : '-'}{formatCurrency(item.amount)}
          </Text>
          <Text style={[styles.itemType, { color: colors.textSecondary }]}>
            {item.activityType}
          </Text>
        </View>
      </View>

      {item.description && (
        <Text style={[styles.itemDescription, { color: colors.textSecondary }]}>
          {item.description}
        </Text>
      )}

      {item.affectedMembers && (
        <View style={styles.affectedMembers}>
          <Text style={[styles.affectedTitle, { color: colors.text }]}>
            Affected Members:
          </Text>
          <Text style={[styles.affectedList, { color: colors.textSecondary }]}>
            {item.affectedMembers.join(', ')}
          </Text>
        </View>
      )}

      <View style={styles.itemMeta}>
        <Text style={[styles.itemDate, { color: colors.textSecondary }]}>
          {formatDate(item.createdAt, 'datetime')}
        </Text>
        <View style={[
          styles.typeBadge,
          { backgroundColor: colors.info + '15' }
        ]}>
          <Text style={[styles.typeText, { color: colors.info }]}>
            {item.activityType}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderReportItem = ({ item }) => (
    <Card style={[styles.itemCard, { backgroundColor: colors.surface }]}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <View style={styles.titleRow}>
            <Ionicons
              name={getReportTypeIcon(item.reportType)}
              size={20}
              color={colors.primary}
              style={styles.itemIcon}
            />
            <Text style={[styles.itemTitle, { color: colors.text }]}>{item.title}</Text>
          </View>
          <Text style={[styles.itemSubtitle, { color: colors.textSecondary }]}>
            Generated by {item.generatedBy}
          </Text>
        </View>
        <View style={styles.itemValues}>
          <Text style={[styles.fileSize, { color: colors.textSecondary }]}>
            {item.fileSize} MB
          </Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) + '15' }
          ]}>
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {item.status}
            </Text>
          </View>
        </View>
      </View>

      {item.description && (
        <Text style={[styles.itemDescription, { color: colors.textSecondary }]}>
          {item.description}
        </Text>
      )}

      <View style={styles.reportMeta}>
        <Text style={[styles.reportPeriod, { color: colors.textSecondary }]}>
          Period: {formatDate(item.reportPeriodStart, 'date')} - {formatDate(item.reportPeriodEnd, 'date')}
        </Text>
        <Text style={[styles.downloadCount, { color: colors.textSecondary }]}>
          Downloaded {item.downloadCount} times
        </Text>
      </View>

      <View style={styles.itemMeta}>
        <Text style={[styles.itemDate, { color: colors.textSecondary }]}>
          Generated: {formatDate(item.createdAt, 'date')}
        </Text>
        {item.isPublic && (
          <View style={[styles.publicBadge, { backgroundColor: colors.success + '15' }]}>
            <Ionicons name="globe" size={12} color={colors.success} />
            <Text style={[styles.publicText, { color: colors.success }]}>
              Public
            </Text>
          </View>
        )}
      </View>

      {item.status === 'ready' && (
        <View style={styles.actionButtons}>
          <Button
            title="Download"
            onPress={() => handleDownloadReport(item.id)}
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            textStyle={{ fontSize: 14 }}
          />
        </View>
      )}
    </Card>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* 🔐 SECURE HEADER WITH INTEGRATED NAVIGATION */}
      {renderSecurityHeader()}

      {/* 📋 MAIN CONTENT */}
      <ScrollView
        style={styles.mainContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={async () => {
              setRefreshing(true);
              await initializeSecureSystem();
              setRefreshing(false);
            }}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {activePanel === 'disbursement' && renderDisbursementPanel()}
        {activePanel === 'transparency' && renderTransparencyFeed()}
        {activePanel === 'audit' && renderAuditPanel()}
      </ScrollView>

    </SafeAreaView>
  );
};

// 🎨 SECURE ACCOUNT MANAGEMENT STYLES
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // 🔐 SECURITY HEADER STYLES
  securityHeader: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    gap: spacing.md,
  },
  headerTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerBottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flex: 1,
  },
  headerLeft: {
    flex: 1,
  },
  securityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    marginBottom: spacing.xs,
    alignSelf: 'flex-start',
    gap: spacing.xs,
  },
  securityText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
  },
  headerRight: {
    alignItems: 'flex-end',
    gap: spacing.sm,
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  roleText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.semibold,
  },
  notificationButton: {
    position: 'relative',
    padding: spacing.sm,
  },
  notificationBadge: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationCount: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },

  // 📱 HEADER TABS STYLES
  headerTabs: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    gap: spacing.xs,
    marginLeft: spacing.md,
  },
  headerTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
    minWidth: 0, // Allow shrinking
  },
  headerTabText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
    flexShrink: 1,
  },

  // 📱 PANEL NAVIGATION STYLES (Legacy - can be removed)
  panelNavigation: {
    flexDirection: 'row',
    marginHorizontal: spacing.sm, // Reduced from spacing.lg to make wider
    marginVertical: spacing.md,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  panelTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    gap: spacing.xs,
  },
  panelTabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },

  // 📋 MAIN CONTENT STYLES
  mainContent: {
    flex: 1,
    paddingHorizontal: spacing.sm, // Reduced from spacing.lg to make cards wider
  },

  // 💰 DISBURSEMENT PANEL STYLES
  disbursementCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs, // Minimal horizontal margin
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  cardHeader: {
    marginBottom: spacing.lg,
  },
  cardTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
    gap: spacing.sm,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    flex: 1,
  },
  cardSubtitle: {
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
  },
  permissionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  permissionText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  accessDenied: {
    alignItems: 'center',
    padding: spacing.xl,
    borderRadius: borderRadius.lg,
    gap: spacing.md,
  },
  accessDeniedTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
  accessDeniedText: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
    lineHeight: 20,
  },
  disbursementForm: {
    gap: spacing.lg,
  },

  // 🎯 DISBURSEMENT TYPE SELECTOR STYLES
  disbursementTypeSelector: {
    marginBottom: spacing.lg,
  },
  typeButtons: {
    gap: spacing.md,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    gap: spacing.md,
  },
  typeButtonContent: {
    flex: 1,
  },
  typeButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  typeButtonDescription: {
    fontSize: typography.fontSize.sm,
  },

  // 🎯 INDIVIDUAL DISBURSEMENT STYLES
  individualDisbursementForm: {
    gap: spacing.lg,
  },
  membersList: {
    maxHeight: 200,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing.sm,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  memberDetails: {
    fontSize: typography.fontSize.sm,
  },
  emptyMembers: {
    alignItems: 'center',
    padding: spacing.xl,
    borderRadius: borderRadius.md,
    gap: spacing.md,
  },
  emptyMembersText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
  },
  emptyMembersSubtext: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
    lineHeight: 18,
  },

  // 📊 BULK DISBURSEMENT STYLES
  bulkDisbursementForm: {
    gap: spacing.lg,
  },
  eligibleMembersSummary: {
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    gap: spacing.sm,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xs,
  },
  summaryTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
  },
  summaryText: {
    fontSize: typography.fontSize.sm,
  },
  summaryAmount: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginTop: spacing.xs,
  },

  formGroup: {
    gap: spacing.xs,
  },
  formLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  formHint: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.base,
    minHeight: 44,
  },
  formRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  formSelect: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 44,
  },
  formSelectText: {
    fontSize: typography.fontSize.base,
    flex: 1,
  },
  categoryButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
    minHeight: 36,
  },
  categoryButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  submitButton: {
    marginTop: spacing.md,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
  },
  securityNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    gap: spacing.sm,
    marginTop: spacing.md,
  },
  securityNoticeText: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
  },

  // 📊 TRANSPARENCY FEED STYLES
  transparencyCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs, // Minimal horizontal margin
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  liveBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  liveIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  liveText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  feedFilters: {
    marginBottom: spacing.lg,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
    marginRight: spacing.sm,
    minHeight: 36,
  },
  filterButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  transactionFeed: {
    maxHeight: 400,
  },
  emptyFeed: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.md,
  },
  emptyFeedTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
  emptyFeedText: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
    lineHeight: 20,
  },
  feedList: {
    maxHeight: 350,
  },
  transactionItem: {
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: borderRadius.md,
    borderLeftWidth: 4,
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTime: {
    fontSize: typography.fontSize.xs,
    marginBottom: spacing.xs,
  },
  transactionAmount: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  transactionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  transactionStatusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  transactionDetails: {
    marginBottom: spacing.sm,
  },
  transactionFlow: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  transactionPurpose: {
    fontSize: typography.fontSize.sm,
    lineHeight: 18,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    flex: 1,
  },
  transactionRole: {
    fontSize: typography.fontSize.xs,
  },
  categoryTag: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  categoryTagText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  auditButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    gap: spacing.xs,
  },
  auditButtonText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  privacyNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    gap: spacing.sm,
    marginTop: spacing.md,
  },
  privacyNoticeText: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
  },

  // 🔍 AUDIT PANEL STYLES
  auditCard: {
    marginBottom: spacing.lg,
    marginHorizontal: spacing.xs, // Minimal horizontal margin
    borderRadius: borderRadius.lg,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  auditHeader: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  auditHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  auditContent: {
    padding: spacing.lg,
  },
  receiptCategories: {
    gap: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.md,
  },
  receiptGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  receiptCategory: {
    flex: 1,
    minWidth: '45%',
    padding: spacing.md,
    borderWidth: 1,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    gap: spacing.sm,
    minHeight: 120,
  },
  receiptCategoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  receiptCategoryTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
  receiptCategoryDesc: {
    fontSize: typography.fontSize.xs,
    textAlign: 'center',
    lineHeight: 16,
  },
  receiptCategoryFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginTop: 'auto',
  },
  receiptCategoryAction: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  exportSection: {
    marginTop: spacing.lg,
  },
  exportButtons: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  exportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  exportButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
  },
  securityFeatures: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    gap: spacing.sm,
    marginTop: spacing.lg,
  },
  securityFeaturesText: {
    flex: 1,
  },
  securityFeaturesTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  securityFeaturesDesc: {
    fontSize: typography.fontSize.sm,
    lineHeight: 18,
  },

  // 📋 ITEM CARD STYLES (for old render functions)
  itemCard: {
    padding: spacing.md,
    marginBottom: spacing.md,
    marginHorizontal: spacing.xs, // Minimal horizontal margin
    borderRadius: borderRadius.md,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  itemInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  itemIcon: {
    marginRight: spacing.sm,
  },
  itemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    flex: 1,
  },
  itemSubtitle: {
    fontSize: typography.fontSize.sm,
  },
  itemValues: {
    alignItems: 'flex-end',
  },
  itemAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  itemRecipients: {
    fontSize: typography.fontSize.xs,
  },
  itemStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
  },
  itemDate: {
    fontSize: typography.fontSize.xs,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.md,
  },
  actionButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
  },
  recipientsPreview: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  recipientsTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.sm,
  },
  recipientItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  recipientName: {
    fontSize: typography.fontSize.sm,
  },
  recipientAmount: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  moreRecipients: {
    fontSize: typography.fontSize.xs,
    fontStyle: 'italic',
    marginTop: spacing.xs,
  },
  affectedMembers: {
    marginBottom: spacing.sm,
  },
  affectedTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  affectedList: {
    fontSize: typography.fontSize.sm,
  },

});

export default AccountManagementScreen;
