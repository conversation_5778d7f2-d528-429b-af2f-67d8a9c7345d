import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';
import { formatCurrency } from '../../utils/formatters';

const ContributeScreen = ({ route, navigation }) => {
  // Extract all parameters - handle both direct chamaId and nested params
  const chamaId = route.params?.chamaId || route.params?.id;
  const contributionType = route.params?.contributionType || 'regular';
  const roundId = route.params?.roundId;
  const roundName = route.params?.roundName;
  const proposalId = route.params?.proposalId;
  const proposalTitle = route.params?.proposalTitle;
  const requestedAmount = route.params?.requestedAmount;
  const { theme, user, refreshSpecificData } = useApp();
  const colors = getThemeColors(theme);

  // console.log('🔍 Route params:', route.params);
  // console.log('🔍 Extracted chamaId:', chamaId);
  //  // console.log('🔍 Contribution type:', contributionType);
  // console.log('🔍 Round info:', { roundId, roundName });
  // console.log('🔍 Welfare proposal info:', { proposalId, proposalTitle, requestedAmount });
  
  const [chama, setChama] = useState(null);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [chamaLoading, setChamaLoading] = useState(true);
  const [paymentMethod, setPaymentMethod] = useState('wallet'); // 'wallet' or 'mpesa'
  const [walletBalance, setWalletBalance] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isAnonymous, setIsAnonymous] = useState(false); // For anonymous contributions

  useEffect(() => {
    loadChamaDetails();
    loadWalletBalance();
  }, [chamaId]);

  const loadChamaDetails = async () => {
    try {
      setChamaLoading(true);
      const response = await ApiService.getChamaById(chamaId);

      console.log('Chama details response:', response); // Debug log

      // Handle different response structures
      let chamaData = null;

      if (response && response.success) {
        // If response has data property
        chamaData = response.data;
      } else if (response && !response.success && response.error) {
        // Handle API error response
        throw new Error(response.error);
      } else if (response && response.id) {
        // If response is the chama object directly
        chamaData = response;
      } else {
        throw new Error('Invalid response format');
      }

      if (chamaData) {
        setChama(chamaData);
        setAmount(chamaData.contribution_amount?.toString() || '');
      } else {
        throw new Error('No chama data received');
      }
    } catch (error) {
      console.error('Failed to load chama details:', error);
      Alert.alert('Error', `Failed to load chama details: ${error.message}`);
    } finally {
      setChamaLoading(false);
    }
  };

  const loadWalletBalance = async () => {
    try {
      console.log('🔄 Loading wallet balance...');
      const response = await ApiService.getWalletBalance();
      console.log('💰 Wallet balance response:', response);

      if (response.success && response.data) {
        const balance = response.data.balance || 0;
        setWalletBalance(balance);
        console.log('✅ Wallet balance loaded:', balance);
      } else {
        console.log('⚠️ Wallet balance response not successful:', response);
        setWalletBalance(0);
      }
    } catch (error) {
      console.error('❌ Failed to load wallet balance:', error);
      setWalletBalance(0);
    }
  };

  const handleContribute = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid contribution amount');
      return;
    }

    if (!chama) {
      Alert.alert('Error', 'Chama details not loaded. Please try again.');
      return;
    }

    // Validate payment method specific requirements
    if (paymentMethod === 'wallet') {
      const contributionAmount = parseFloat(amount);

      // Check for nil/zero balance
      if (walletBalance <= 0) {
        Alert.alert(
          'No Wallet Balance',
          'Your VaultKe wallet balance is KES 0.00. Please deposit money into your wallet before making a contribution.',
          [
            {
              text: 'Deposit Now',
              onPress: () => {
                // Navigate to deposit screen
                navigation.navigate('WalletScreen', { tab: 'deposit' });
              }
            },
            {
              text: 'Cancel',
              style: 'cancel'
            }
          ]
        );
        return;
      }

      // Check for insufficient balance
      if (contributionAmount > walletBalance) {
        const shortfall = contributionAmount - walletBalance;
        Alert.alert(
          'Insufficient Wallet Balance',
          `Your VaultKe wallet balance is KES ${formatCurrency(walletBalance)}.\n\nYou need KES ${formatCurrency(shortfall)} more to make this contribution.\n\nWould you like to deposit money or reduce the contribution amount?`,
          [
            {
              text: 'Deposit Money',
              onPress: () => {
                navigation.navigate('WalletScreen', { tab: 'deposit' });
              }
            },
            {
              text: 'Reduce Amount',
              onPress: () => {
                setAmount(walletBalance.toString());
              }
            },
            {
              text: 'Cancel',
              style: 'cancel'
            }
          ]
        );
        return;
      }
    } else if (paymentMethod === 'mpesa') {
      if (!user?.phone || user.phone.length < 10) {
        Alert.alert('Phone Number Required', 'Your account does not have a valid phone number. Please update your profile to use M-Pesa payments.');
        return;
      }
    }

    // Show payment confirmation
    setShowPaymentModal(true);
  };

  const confirmContribution = async () => {
    try {
      setLoading(true);
      setShowPaymentModal(false);

      // Ensure chamaId is a string, not an object
      const cleanChamaId = typeof chamaId === 'string' ? chamaId : chamaId?.chamaId || chamaId?.id;

      if (!cleanChamaId) {
        throw new Error('Invalid chama ID');
      }

      if (paymentMethod === 'mpesa') {
        // Handle M-Pesa payment
        await handleMpesaContribution(cleanChamaId);
      } else {
        // Handle wallet contribution
        await handleWalletContribution(cleanChamaId);
      }
    } catch (error) {
      console.error('Contribution failed:', error);
      Toast.show({
        type: 'error',
        text1: 'Contribution Failed',
        text2: error.message || 'An error occurred while processing your contribution',
        position: 'top',
        visibilityTime: 4000,
        topOffset: 60,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleWalletContribution = async (cleanChamaId) => {
    const contributionData = {
      chamaId: cleanChamaId,
      amount: parseFloat(amount),
      description: description || getDefaultDescription(),
      type: contributionType,
      paymentMethod: 'wallet',
      isAnonymous: chama?.category === 'contribution' ? isAnonymous : false, // Only for contribution groups
      ...(roundId && { roundId }), // Include roundId if it exists
      ...(proposalId && { proposalId }), // Include proposalId for welfare contributions
    };

    function getDefaultDescription() {
        switch (contributionType) {
          case 'merry-go-round':
            return `Merry-Go-Round contribution to ${roundName || 'round'}`;
          case 'welfare':
            return proposalTitle
              ? `Welfare support for: ${proposalTitle}`
              : `Welfare contribution to ${chama.name}`;
          default:
            return `Contribution to ${chama.name}`;
        }
      }

    console.log('🔄 Making contribution with data:', contributionData);
    console.log('🔄 Clean chamaId:', cleanChamaId);
    const response = await ApiService.makeContribution(contributionData);

    console.log('🔍 Contribution response:', response);

    if (response.success) {
      console.log('🎉 Contribution successful! Preparing success notification...');
      console.log('💰 Contribution successful, refreshing wallet balance...');

      // Refresh wallet balance after successful contribution
      const oldBalance = walletBalance;
      await loadWalletBalance();
      console.log(`💰 Wallet balance updated: ${oldBalance} → ${walletBalance}`);

      // Also refresh the global wallet data in AppContext
      try {
        await refreshSpecificData('wallet');
        console.log('✅ Global wallet data refreshed');
      } catch (error) {
        console.warn('⚠️ Failed to refresh global wallet data:', error);
      }

      const successTitle = contributionType === 'regular'
        ? 'Contribution Successful!'
        : `${getContributionTitle()} Successful!`;

      const getSuccessMessage = () => {
        const amountText = formatCurrency(parseFloat(amount));
        const chamaName = chama?.name || 'the group';

        console.log('🔍 Success message data:', {
          amountText,
          contributionType,
          chamaName,
          roundName,
          isAnonymous
        });

        let baseMessage;
        switch (contributionType) {
          case 'merry-go-round':
            baseMessage = `You have successfully contributed ${amountText} from your VaultKe wallet to ${roundName || 'the merry-go-round'}`;
            break;
          case 'welfare':
            baseMessage = `You have successfully contributed ${amountText} from your VaultKe wallet to the welfare fund`;
            break;
          case 'loan':
            baseMessage = `You have successfully contributed ${amountText} from your VaultKe wallet to the loan fund`;
            break;
          case 'emergency':
            baseMessage = `You have successfully contributed ${amountText} from your VaultKe wallet to the emergency fund`;
            break;
          default:
            baseMessage = `You have successfully contributed ${amountText} from your VaultKe wallet to ${chamaName}`;
        }

        // Add anonymous note if applicable
        if (isAnonymous) {
          baseMessage += '\n\n🔒 This contribution was made anonymously and will appear as "Anonymous" in transaction records.';
        }

        return baseMessage;
      };

      // Show success toast notification
      console.log('🎉 Showing success toast:', successTitle, getSuccessMessage());

      Toast.show({
        type: 'success',
        text1: successTitle,
        text2: getSuccessMessage(),
        position: 'top',
        visibilityTime: 4000,
        topOffset: 60,
      });

      console.log('🎉 Success toast should be displayed now');

      // Reset form and navigate back after a short delay
      setTimeout(() => {
        setAmount('');
        setDescription('');
        setIsAnonymous(false);
        navigation.goBack();
      }, 2000); // Give user time to see the toast
    } else {
      throw new Error(response.error || 'Wallet contribution failed');
    }
  };

  const handleMpesaContribution = async (cleanChamaId) => {
    // Format user's registered phone number for M-Pesa
    let formattedPhone = user.phone.replace(/\s+/g, '');
    if (formattedPhone.startsWith('0')) {
      formattedPhone = '254' + formattedPhone.substring(1);
    } else if (formattedPhone.startsWith('+254')) {
      formattedPhone = formattedPhone.substring(1);
    } else if (!formattedPhone.startsWith('254')) {
      formattedPhone = '254' + formattedPhone;
    }

    const accountReference = `CHAMA-${cleanChamaId.substring(0, 8)}`;
    const transactionDesc = description || getDefaultDescription();

    function getDefaultDescription() {
      switch (contributionType) {
        case 'merry-go-round':
          return `Merry-Go-Round contribution to ${roundName || 'round'}`;
        case 'welfare':
          return proposalTitle
            ? `Welfare support for: ${proposalTitle}`
            : `Welfare contribution to ${chama.name}`;
        default:
          return `Contribution to ${chama.name}`;
      }
    }

    console.log('🔄 Initiating M-Pesa payment:', {
      phone: formattedPhone,
      amount: parseFloat(amount),
      reference: accountReference,
      description: transactionDesc
    });

    const mpesaResponse = await ApiService.initiateMpesaPayment(
      formattedPhone,
      parseFloat(amount),
      accountReference,
      transactionDesc
    );

    if (mpesaResponse.success) {
      Alert.alert(
        'M-Pesa Payment Initiated',
        `M-Pesa STK push sent to ${formattedPhone} for ${formatCurrency(parseFloat(amount))}. Please check your phone and enter your PIN to complete the payment. The contribution will be processed once payment is confirmed.`,
        [
          {
            text: 'Check Status',
            onPress: () => {
              // Reset form and navigate to transactions to check status
              setAmount('');
              setDescription('');
              navigation.navigate('ChamaTransactions');
            },
          },
          {
            text: 'OK',
            style: 'default',
            onPress: () => {
              // Reset form and navigate back
              setAmount('');
              setDescription('');
              navigation.goBack();
            },
          },
        ]
      );
    } else {
      throw new Error(mpesaResponse.error || 'Failed to initiate M-Pesa payment');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getContributionTitle = () => {
    switch (contributionType) {
      case 'merry-go-round':
        return 'Merry-Go-Round Contribution';
      case 'welfare':
        return 'Welfare Contribution';
      case 'loan':
        return 'Loan Contribution';
      case 'emergency':
        return 'Emergency Contribution';
      default:
        return 'Make Contribution';
    }
  };

  const getContributionSubtitle = () => {
    switch (contributionType) {
      case 'merry-go-round':
        return `Contribute to ${roundName || 'Merry-Go-Round'}`;
      case 'welfare':
        return proposalTitle
          ? `Support: ${proposalTitle}`
          : `Welfare fund for ${chama?.name || 'group'}`;
      case 'loan':
        return `Loan fund for ${chama?.name || 'group'}`;
      case 'emergency':
        return `Emergency fund for ${chama?.name || 'group'}`;
      default:
        return `Contribute to ${chama?.name || 'Loading...'}`;
    }
  };

  const getContributionIcon = () => {
    switch (contributionType) {
      case 'merry-go-round':
        return 'refresh-circle';
      case 'welfare':
        return 'heart';
      case 'loan':
        return 'card';
      case 'emergency':
        return 'warning';
      default:
        return 'people';
    }
  };

  const getContributionColor = () => {
    switch (contributionType) {
      case 'merry-go-round':
        return colors.warning;
      case 'welfare':
        return '#EC4899'; // Pink
      case 'loan':
        return '#6366F1'; // Indigo
      case 'emergency':
        return colors.error;
      default:
        return colors.primary;
    }
  };

  if (chamaLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading chama details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {getContributionTitle()}
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {getContributionSubtitle()}
          </Text>
        </View>

        <Card style={styles.chamaInfoCard}>
          <View style={styles.chamaInfo}>
            <View style={[styles.chamaIcon, { backgroundColor: getContributionColor() }]}>
              <Ionicons
                name={getContributionIcon()}
                size={24}
                color={colors.white}
              />
            </View>
            <View style={styles.chamaDetails}>
              <Text style={[styles.chamaName, { color: colors.text }]}>
                {contributionType === 'merry-go-round'
                  ? roundName
                  : contributionType === 'welfare' && proposalTitle
                    ? proposalTitle
                    : chama?.name
                }
              </Text>
              <Text style={[styles.chamaType, { color: colors.textSecondary }]}>
                {contributionType === 'merry-go-round'
                  ? `Merry-Go-Round • ${chama?.name || 'Group'}`
                  : contributionType === 'welfare' && proposalTitle
                    ? `Welfare Support • ${chama?.name || 'Group'}`
                    : contributionType === 'regular'
                      ? `${chama?.type || 'Community'} • ${chama?.contributionFrequency || 'regular'} contributions`
                      : `${getContributionTitle()} • ${chama?.name || 'Group'}`
                }
              </Text>
              <Text style={[styles.chamaAmount, { color: getContributionColor() }]}>
                {contributionType === 'regular'
                  ? `Regular: ${amount ? formatCurrency(parseFloat(amount)) : formatCurrency(chama?.contributionAmount || 0)}`
                  : contributionType === 'welfare' && requestedAmount
                    ? `Needed: ${formatCurrency(requestedAmount)}`
                    : contributionType === 'merry-go-round' && amount
                      ? `Contributing: ${formatCurrency(parseFloat(amount))}`
                      : amount
                        ? `Amount: ${formatCurrency(parseFloat(amount))}`
                        : getContributionTitle()
                }
              </Text>
            </View>
          </View>
        </Card>

        <Card style={styles.formCard}>
          <Text style={[styles.formTitle, { color: colors.text }]}>
            {contributionType === 'regular' ? 'Contribution Details' : `${getContributionTitle()} Details`}
          </Text>

          {/* Payment Method Selection */}
          <View style={styles.paymentMethodContainer}>
            <Text style={[styles.paymentMethodLabel, { color: colors.text }]}>
              Payment Method
            </Text>
            <View style={styles.paymentMethodOptions}>
              <TouchableOpacity
                style={[
                  styles.paymentMethodOption,
                  {
                    backgroundColor: colors.surface,
                    borderColor: paymentMethod === 'wallet' ? colors.primary : colors.border,
                    borderWidth: paymentMethod === 'wallet' ? 2 : 1,
                  }
                ]}
                onPress={() => setPaymentMethod('wallet')}
              >
                <Ionicons
                  name="wallet"
                  size={20}
                  color={paymentMethod === 'wallet' ? colors.primary : colors.text}
                />
                <Text
                  style={[
                    styles.paymentMethodText,
                    { color: paymentMethod === 'wallet' ? colors.primary : colors.text }
                  ]}
                >
                  VaultKe Wallet
                </Text>
                {paymentMethod === 'wallet' && (
                  <View style={styles.walletBalanceContainer}>
                    <Text
                      style={[
                        styles.paymentMethodBalance,
                        { color: paymentMethod === 'wallet' ? colors.primary : colors.textSecondary }
                      ]}
                    >
                      Balance: {formatCurrency(walletBalance)}
                    </Text>
                    {walletBalance <= 0 && (
                      <Text
                        style={[
                          styles.balanceWarning,
                          { color: colors.error }
                        ]}
                      >
                        ⚠️ No balance
                      </Text>
                    )}
                    {walletBalance > 0 && amount && parseFloat(amount) > walletBalance && (
                      <Text
                        style={[
                          styles.balanceWarning,
                          { color: colors.error }
                        ]}
                      >
                        ⚠️ Insufficient
                      </Text>
                    )}
                  </View>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.paymentMethodOption,
                  {
                    backgroundColor: colors.surface,
                    borderColor: paymentMethod === 'mpesa' ? colors.success : colors.border,
                    borderWidth: paymentMethod === 'mpesa' ? 2 : 1,
                  }
                ]}
                onPress={() => setPaymentMethod('mpesa')}
              >
                <Ionicons
                  name="phone-portrait"
                  size={20}
                  color={paymentMethod === 'mpesa' ? colors.success : colors.text}
                />
                <Text
                  style={[
                    styles.paymentMethodText,
                    { color: paymentMethod === 'mpesa' ? colors.success : colors.text }
                  ]}
                >
                  M-Pesa
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* M-Pesa Phone Number Display */}
          {paymentMethod === 'mpesa' && (
            <View style={styles.phoneNumberContainer}>
              <Text style={[styles.phoneNumberLabel, { color: colors.text }]}>
                M-Pesa Phone Number
              </Text>
              <View style={[styles.phoneNumberDisplay, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Ionicons name="call" size={20} color={colors.textSecondary} style={styles.phoneIcon} />
                <Text style={[styles.phoneNumberText, { color: colors.text }]}>
                  {user?.phone || 'No phone number registered'}
                </Text>
                <View style={[styles.readOnlyBadge, { borderColor: colors.primary, backgroundColor: 'transparent' }]}>
                  <Text style={[styles.readOnlyText, { color: colors.primary }]}>
                    Registered
                  </Text>
                </View>
              </View>
              <Text style={[styles.phoneNumberHint, { color: colors.textSecondary }]}>
                You will receive the M-Pesa prompt on this number
              </Text>
            </View>
          )}

          <Input
            label="Amount (KES)"
            value={amount}
            onChangeText={setAmount}
            placeholder="Enter contribution amount"
            keyboardType="numeric"
            leftIcon="wallet"
          />

          {/* Real-time validation for wallet payments */}
          {paymentMethod === 'wallet' && amount && (
            <View style={styles.validationContainer}>
              {parseFloat(amount) > walletBalance ? (
                <View style={styles.validationMessage}>
                  <Ionicons name="warning" size={16} color={colors.error} />
                  <Text style={[styles.validationText, { color: colors.error }]}>
                    Insufficient balance. You need KES {formatCurrency(parseFloat(amount) - walletBalance)} more.
                  </Text>
                </View>
              ) : walletBalance <= 0 ? (
                <View style={styles.validationMessage}>
                  <Ionicons name="alert-circle" size={16} color={colors.error} />
                  <Text style={[styles.validationText, { color: colors.error }]}>
                    Your wallet balance is KES 0.00. Please deposit money first.
                  </Text>
                </View>
              ) : (
                <View style={styles.validationMessage}>
                  <Ionicons name="checkmark-circle" size={16} color={colors.success} />
                  <Text style={[styles.validationText, { color: colors.success }]}>
                    Sufficient balance. Remaining: KES {formatCurrency(walletBalance - parseFloat(amount))}
                  </Text>
                </View>
              )}
            </View>
          )}

          <Input
            label="Description (Optional)"
            value={description}
            onChangeText={setDescription}
            placeholder="Add a note for this contribution..."
            multiline
            numberOfLines={3}
            leftIcon="document-text"
          />

          {/* Anonymous Contribution Option - Only for Contribution Groups */}
          {chama?.category === 'contribution' && (
            <View style={styles.anonymousContainer}>
              <TouchableOpacity
                style={styles.anonymousOption}
                onPress={() => setIsAnonymous(!isAnonymous)}
                activeOpacity={0.7}
              >
                <View style={styles.anonymousCheckbox}>
                  <Ionicons
                    name={isAnonymous ? 'checkbox' : 'square-outline'}
                    size={24}
                    color={isAnonymous ? colors.primary : colors.textSecondary}
                  />
                </View>
                <View style={styles.anonymousTextContainer}>
                  <Text style={[styles.anonymousLabel, { color: colors.text }]}>
                    Contribute Anonymously
                  </Text>
                  <Text style={[styles.anonymousDescription, { color: colors.textSecondary }]}>
                    Your name will not be shown in the transaction history
                  </Text>
                </View>
              </TouchableOpacity>

              {isAnonymous && (
                <View style={styles.anonymousNotice}>
                  <Ionicons name="information-circle" size={16} color={colors.info} />
                  <Text style={[styles.anonymousNoticeText, { color: colors.info }]}>
                    This contribution will appear as "Anonymous" in all transaction records
                  </Text>
                </View>
              )}
            </View>
          )}

          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Contribution Amount:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {amount ? formatCurrency(parseFloat(amount)) : formatCurrency(0)}
              </Text>
            </View>
          </View>

          <Button
            title="Make Contribution"
            onPress={handleContribute}
            loading={loading}
            disabled={!amount || parseFloat(amount) <= 0}
            variant="outline"
            style={styles.contributeButton}
            icon={<Ionicons name="add-circle" size={20} color={!amount || parseFloat(amount) <= 0 ? colors.textSecondary : colors.primary} />}
          />
        </Card>
      </ScrollView>

      {/* Payment Confirmation Modal */}
      <Modal
        visible={showPaymentModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPaymentModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Confirm Contribution
              </Text>
              <TouchableOpacity
                onPress={() => setShowPaymentModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <View style={styles.confirmationRow}>
                <Text style={[styles.confirmationLabel, { color: colors.textSecondary }]}>
                  Amount:
                </Text>
                <Text style={[styles.confirmationValue, { color: colors.text }]}>
                  {formatCurrency(parseFloat(amount || 0))}
                </Text>
              </View>

              <View style={styles.confirmationRow}>
                <Text style={[styles.confirmationLabel, { color: colors.textSecondary }]}>
                  Payment Method:
                </Text>
                <Text style={[styles.confirmationValue, { color: colors.text }]}>
                  {paymentMethod === 'wallet' ? 'VaultKe Wallet' : 'M-Pesa'}
                </Text>
              </View>

              {paymentMethod === 'wallet' && (
                <View style={styles.confirmationRow}>
                  <Text style={[styles.confirmationLabel, { color: colors.textSecondary }]}>
                    Wallet Balance:
                  </Text>
                  <Text style={[styles.confirmationValue, { color: colors.text }]}>
                    {formatCurrency(walletBalance)}
                  </Text>
                </View>
              )}

              {paymentMethod === 'mpesa' && (
                <View style={styles.confirmationRow}>
                  <Text style={[styles.confirmationLabel, { color: colors.textSecondary }]}>
                    M-Pesa Number:
                  </Text>
                  <Text style={[styles.confirmationValue, { color: colors.text }]}>
                    {user?.phone || 'Not available'}
                  </Text>
                </View>
              )}

              <View style={styles.confirmationRow}>
                <Text style={[styles.confirmationLabel, { color: colors.textSecondary }]}>
                  Contributing to:
                </Text>
                <Text style={[styles.confirmationValue, { color: colors.text }]}>
                  {chama?.name}
                </Text>
              </View>
            </View>

            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={() => setShowPaymentModal(false)}
                style={styles.modalCancelButton}
              />
              <Button
                title={paymentMethod === 'wallet' ? 'Confirm Transfer' : 'Pay with M-Pesa'}
                onPress={confirmContribution}
                loading={loading}
                variant="outline"
                style={styles.modalConfirmButton}
                icon={
                  <Ionicons
                    name={paymentMethod === 'wallet' ? 'wallet' : 'phone-portrait'}
                    size={20}
                    color={colors.primary}
                  />
                }
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: typography.fontSize.base,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    ...shadows.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.base,
  },
  chamaInfoCard: {
    margin: spacing.md,
  },
  chamaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chamaIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  chamaDetails: {
    flex: 1,
  },
  chamaName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  chamaType: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  chamaAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  formCard: {
    margin: spacing.md,
  },
  formTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.lg,
  },
  summaryContainer: {
    marginVertical: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
  },
  summaryValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  contributeButton: {
    marginTop: spacing.lg,
    minHeight: 50,
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    ...shadows.sm,
  },
  // Payment Method Styles
  paymentMethodContainer: {
    marginBottom: spacing.lg,
  },
  paymentMethodLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
  },
  paymentMethodOptions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  paymentMethodOption: {
    flex: 1,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    minHeight: 90,
    justifyContent: 'center',
    ...shadows.sm,
  },
  paymentMethodText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  paymentMethodBalance: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  walletBalanceContainer: {
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  balanceWarning: {
    fontSize: typography.fontSize.xs,
    marginTop: 2,
    textAlign: 'center',
    fontWeight: typography.fontWeight.medium,
  },
  // Validation Styles
  validationContainer: {
    marginTop: spacing.sm,
    marginBottom: spacing.sm,
  },
  validationMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  validationText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
    flex: 1,
  },
  // Phone Number Display Styles
  phoneNumberContainer: {
    marginBottom: spacing.lg,
  },
  phoneNumberLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
  },
  phoneNumberDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.xs,
  },
  phoneIcon: {
    marginRight: spacing.sm,
  },
  phoneNumberText: {
    fontSize: typography.fontSize.base,
    flex: 1,
  },
  readOnlyBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    borderWidth: 1,
  },
  readOnlyText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  phoneNumberHint: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: borderRadius.lg,
    ...shadows.lg,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  modalCloseButton: {
    padding: spacing.xs,
  },
  modalBody: {
    padding: spacing.lg,
  },
  confirmationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  confirmationLabel: {
    fontSize: typography.fontSize.sm,
    flex: 1,
  },
  confirmationValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  modalActions: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalCancelButton: {
    flex: 1,
    minHeight: 48,
    borderRadius: borderRadius.md,
    borderWidth: 2,
    marginRight: spacing.xs,
  },
  modalConfirmButton: {
    flex: 2,
    minHeight: 48,
    borderRadius: borderRadius.md,
    borderWidth: 2,
    marginLeft: spacing.xs,
  },
  // Anonymous contribution styles
  anonymousContainer: {
    marginVertical: spacing.md,
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  anonymousOption: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  anonymousCheckbox: {
    marginRight: spacing.md,
    marginTop: 2, // Align with text
  },
  anonymousTextContainer: {
    flex: 1,
  },
  anonymousLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  anonymousDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: 18,
  },
  anonymousNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: 'rgba(59, 130, 246, 0.1)', // Light blue background
    marginTop: spacing.sm,
  },
  anonymousNoticeText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
    flex: 1,
    lineHeight: 18,
  },
});

export default ContributeScreen;
