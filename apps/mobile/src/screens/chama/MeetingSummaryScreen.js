import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import api from '../../services/api';
import Toast from 'react-native-toast-message';

const MeetingSummaryScreen = ({ route, navigation }) => {
  const { chamaId, chamaName } = route.params;
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [pastMeetings, setPastMeetings] = useState([]);
  const [selectedMeeting, setSelectedMeeting] = useState(null);
  const [meetingDetails, setMeetingDetails] = useState(null);
  const [attendanceData, setAttendanceData] = useState([]);
  const [meetingMinutes, setMeetingMinutes] = useState(null);
  const [meetingDocuments, setMeetingDocuments] = useState([]);

  useEffect(() => {
    loadPastMeetings();
  }, [chamaId]);

  const loadPastMeetings = async () => {
    try {
      setLoading(true);

      // Try to load from API first - using the same endpoint that works for ChamaMeetingsScreen
      try {
        console.log('🔍 Attempting to load meetings from API...');
        const response = await api.makeRequest(`/meetings?chamaId=${chamaId}`);

        if (response.success && response.data) {
          console.log('✅ Successfully loaded meetings from API:', response.data.length);

          // Filter for completed/past meetings only
          const now = new Date();
          const completedMeetings = response.data.filter(meeting => {
            const meetingDate = new Date(meeting.scheduledAt);
            const meetingDuration = meeting.duration || 60;
            const meetingEndTime = new Date(meetingDate.getTime() + (meetingDuration * 60 * 1000));

            return meeting.status === 'completed' || meetingEndTime <= now;
          });

          console.log('📋 Found', completedMeetings.length, 'past meetings');

          // Sort by most recent first
          completedMeetings.sort((a, b) => new Date(b.scheduledAt) - new Date(a.scheduledAt));

          setPastMeetings(completedMeetings);

          // Auto-select the most recent meeting if available
          if (completedMeetings.length > 0) {
            setSelectedMeeting(completedMeetings[0]);
            loadMeetingDetails(completedMeetings[0].id);
          }

          // Show success message
          Toast.show({
            type: 'success',
            text1: 'Meetings Loaded',
            text2: `Found ${completedMeetings.length} past meetings`,
          });
          return;
        }
      } catch (apiError) {
        console.log('⚠️ API not available, using mock data:', apiError.message);
        Toast.show({
          type: 'info',
          text1: 'Using Demo Data',
          text2: 'API not available, showing sample meetings',
        });
      }

      // Fallback to mock data when API is not available
      console.log('📝 Using mock data for demonstration');
      const mockPastMeetings = [
        {
          id: 'demo-past-1',
          title: '[DEMO] Monthly Financial Review',
          description: 'Review of monthly contributions and investment performance - This is sample data',
          scheduledAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
          duration: 90,
          location: 'Community Center Hall A',
          meetingType: 'physical',
          status: 'completed',
          attendees: 18,
          chamaId: chamaId,
          chamaName: chamaName,
        },
        {
          id: 'demo-past-2',
          title: '[DEMO] Investment Strategy Discussion',
          description: 'Planning for Q2 investment opportunities and risk assessment - This is sample data',
          scheduledAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 2 weeks ago
          duration: 60,
          location: 'Virtual Meeting Room',
          meetingType: 'virtual',
          status: 'completed',
          attendees: 15,
          chamaId: chamaId,
          chamaName: chamaName,
        },
        {
          id: 'demo-past-3',
          title: '[DEMO] New Member Orientation',
          description: 'Welcome session for new chama members and rules overview - This is sample data',
          scheduledAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(), // 3 weeks ago
          duration: 120,
          location: 'Main Office Conference Room',
          meetingType: 'hybrid',
          status: 'completed',
          attendees: 22,
          chamaId: chamaId,
          chamaName: chamaName,
        },
      ];

      setPastMeetings(mockPastMeetings);

      // Auto-select the most recent meeting
      if (mockPastMeetings.length > 0) {
        setSelectedMeeting(mockPastMeetings[0]);
        loadMeetingDetails(mockPastMeetings[0].id);
      }

    } catch (error) {
      console.error('Failed to load past meetings:', error);
      setPastMeetings([]);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load meeting history. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadMeetingDetails = async (meetingId) => {
    try {
      // Try to load from API first
      try {
        console.log('🔍 Loading details for meeting:', meetingId);
        const [detailsResponse, attendanceResponse, minutesResponse, documentsResponse] = await Promise.allSettled([
          api.makeRequest(`/meetings/${meetingId}`),
          api.makeRequest(`/meetings/${meetingId}/attendance`),
          api.makeRequest(`/meetings/${meetingId}/minutes`),
          api.makeRequest(`/meetings/${meetingId}/documents`)
        ]);

        let hasRealData = false;

        // Handle meeting details
        if (detailsResponse.status === 'fulfilled' && detailsResponse.value.success) {
          console.log('✅ Got meeting details from API');
          setMeetingDetails(detailsResponse.value.data);
          hasRealData = true;
        }

        // Handle attendance data
        if (attendanceResponse.status === 'fulfilled' && attendanceResponse.value.success) {
          console.log('✅ Got attendance data from API');
          setAttendanceData(attendanceResponse.value.data || []);
          hasRealData = true;
        }

        // Handle meeting minutes
        if (minutesResponse.status === 'fulfilled' && minutesResponse.value.success) {
          console.log('✅ Got meeting minutes from API');
          setMeetingMinutes(minutesResponse.value.data);
          hasRealData = true;
        }

        // Handle meeting documents
        if (documentsResponse.status === 'fulfilled' && documentsResponse.value.success) {
          console.log('✅ Got meeting documents from API');
          setMeetingDocuments(documentsResponse.value.data || []);
          hasRealData = true;
        }

        // If we got some real data from API, return
        if (hasRealData) {
          console.log('✅ Using real API data for meeting details');
          return;
        }
      } catch (apiError) {
        console.log('⚠️ API not available for meeting details, using mock data');
      }

      // Fallback to mock data for demonstration
      console.log('📝 Using mock data for meeting details');
      const mockAttendance = [
        { id: '1', userId: 'user1', userName: '[DEMO] John Doe', isPresent: true, attendanceType: 'physical' },
        { id: '2', userId: 'user2', userName: '[DEMO] Jane Smith', isPresent: true, attendanceType: 'physical' },
        { id: '3', userId: 'user3', userName: '[DEMO] Mike Johnson', isPresent: false, attendanceType: 'physical' },
        { id: '4', userId: 'user4', userName: '[DEMO] Sarah Wilson', isPresent: true, attendanceType: 'physical' },
        { id: '5', userId: 'user5', userName: '[DEMO] David Brown', isPresent: true, attendanceType: 'physical' },
        { id: '6', userId: 'user6', userName: '[DEMO] Lisa Davis', isPresent: false, attendanceType: 'physical' },
      ];

      const mockMinutes = {
        id: 'minutes-1',
        meetingId: meetingId,
        content: `Meeting Minutes for ${selectedMeeting?.title || 'Meeting'}

1. Meeting called to order at ${selectedMeeting ? formatTime(selectedMeeting.scheduledAt) : '2:00 PM'}

2. Attendance: ${mockAttendance.filter(a => a.isPresent).length} members present, ${mockAttendance.filter(a => !a.isPresent).length} absent

3. Financial Report:
   - Current balance: KES 450,000
   - Monthly contributions: KES 85,000
   - Investment returns: KES 12,500

4. Decisions Made:
   - Approved loan application for member Sarah Wilson (KES 50,000)
   - Agreed to invest additional KES 100,000 in government bonds
   - Next meeting scheduled for ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}

5. Action Items:
   - Treasurer to process approved loan by end of week
   - Secretary to research additional investment opportunities
   - All members to submit updated contact information

Meeting adjourned at ${selectedMeeting ? new Date(new Date(selectedMeeting.scheduledAt).getTime() + (selectedMeeting.duration || 60) * 60 * 1000).toLocaleTimeString() : '4:00 PM'}`,
        status: 'approved',
        takenBy: 'Secretary',
      };

      const mockDocuments = [
        {
          id: 'doc-1',
          name: 'Financial Report January 2024.pdf',
          size: 245760,
          documentType: 'financial_report',
          uploadedAt: '2024-01-15T13:30:00Z',
        },
        {
          id: 'doc-2',
          name: 'Investment Proposal Q2.docx',
          size: 156432,
          documentType: 'proposal',
          uploadedAt: '2024-01-15T13:45:00Z',
        },
        {
          id: 'doc-3',
          name: 'Member Contact List.xlsx',
          size: 89123,
          documentType: 'member_list',
          uploadedAt: '2024-01-15T14:15:00Z',
        },
      ];

      setAttendanceData(mockAttendance);
      setMeetingMinutes(mockMinutes);
      setMeetingDocuments(mockDocuments);

    } catch (error) {
      console.error('Failed to load meeting details:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPastMeetings();
    setRefreshing(false);
  };

  const handleMeetingSelect = (meeting) => {
    setSelectedMeeting(meeting);
    loadMeetingDetails(meeting.id);
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const formatTime = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return 'Invalid Time';
    }
  };

  const getMeetingTypeIcon = (type) => {
    switch (type) {
      case 'virtual':
        return 'videocam';
      case 'physical':
        return 'location';
      case 'hybrid':
        return 'globe';
      default:
        return 'calendar';
    }
  };

  const getAttendanceStats = () => {
    if (!attendanceData || !Array.isArray(attendanceData) || attendanceData.length === 0) {
      return { present: 0, absent: 0, total: 0 };
    }

    const present = attendanceData.filter(att => att && att.isPresent).length;
    const total = attendanceData.length;
    const absent = total - present;

    return { present, absent, total };
  };

  const renderMeetingsList = () => (
    <Card style={styles.meetingsListCard}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Past Meetings ({pastMeetings.length})
      </Text>

      {pastMeetings.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="calendar-outline" size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
            No past meetings found
          </Text>
        </View>
      ) : (
        <View style={styles.meetingsList}>
          {pastMeetings.map((meeting) => (
            <TouchableOpacity
              key={meeting.id}
              style={[
                styles.meetingItem,
                {
                  backgroundColor: selectedMeeting?.id === meeting.id ? colors.primary + '10' : colors.background,
                  borderColor: selectedMeeting?.id === meeting.id ? colors.primary : colors.border,
                }
              ]}
              onPress={() => handleMeetingSelect(meeting)}
            >
              <View style={styles.meetingItemHeader}>
                <View style={[styles.meetingTypeIcon, { backgroundColor: colors.primary + '20' }]}>
                  <Ionicons
                    name={getMeetingTypeIcon(meeting.meetingType)}
                    size={20}
                    color={colors.primary}
                  />
                </View>
                <View style={styles.meetingItemInfo}>
                  <Text style={[styles.meetingItemTitle, { color: colors.text }]} numberOfLines={2}>
                    {meeting.title}
                  </Text>
                  <Text style={[styles.meetingItemDate, { color: colors.textSecondary }]}>
                    {formatDate(meeting.scheduledAt)}
                  </Text>
                  <Text style={[styles.meetingItemTime, { color: colors.textSecondary }]}>
                    {formatTime(meeting.scheduledAt)} • {meeting.meetingType?.charAt(0).toUpperCase() + meeting.meetingType?.slice(1)}
                  </Text>
                </View>
                {selectedMeeting?.id === meeting.id && (
                  <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </Card>
  );

  const renderMeetingDetails = () => {
    if (!selectedMeeting) return null;

    const stats = getAttendanceStats();
    const attendanceRate = stats.total > 0 ? Math.round((stats.present / stats.total) * 100) : 0;

    return (
      <View style={styles.detailsContainer}>
        {/* Meeting Overview */}
        <Card style={styles.overviewCard}>
          <Text style={[styles.detailsTitle, { color: colors.text }]}>
            {selectedMeeting.title}
          </Text>
          <Text style={[styles.detailsDescription, { color: colors.textSecondary }]}>
            {selectedMeeting.description || 'No description provided'}
          </Text>
          
          <View style={styles.meetingMetadata}>
            <View style={styles.metadataItem}>
              <Ionicons name="calendar" size={16} color={colors.primary} />
              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                {formatDate(selectedMeeting.scheduledAt)}
              </Text>
            </View>
            <View style={styles.metadataItem}>
              <Ionicons name="time" size={16} color={colors.primary} />
              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                {formatTime(selectedMeeting.scheduledAt)} ({selectedMeeting.duration || 60} min)
              </Text>
            </View>
            <View style={styles.metadataItem}>
              <Ionicons name={getMeetingTypeIcon(selectedMeeting.meetingType)} size={16} color={colors.primary} />
              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                {selectedMeeting.meetingType?.charAt(0).toUpperCase() + selectedMeeting.meetingType?.slice(1)} Meeting
              </Text>
            </View>
            {selectedMeeting.location && (
              <View style={styles.metadataItem}>
                <Ionicons name="location" size={16} color={colors.primary} />
                <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                  {selectedMeeting.location}
                </Text>
              </View>
            )}
          </View>
        </Card>

        {/* Attendance Summary */}
        <Card style={styles.attendanceCard}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Attendance Summary
          </Text>
          
          <View style={styles.attendanceStats}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.success }]}>{stats.present}</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Present</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.error }]}>{stats.absent}</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Absent</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.text }]}>{stats.total}</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Total</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.primary }]}>{attendanceRate}%</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Rate</Text>
            </View>
          </View>

          {attendanceData.length > 0 && (
            <View style={styles.attendanceList}>
              <Text style={[styles.subsectionTitle, { color: colors.text }]}>
                Attendance Details
              </Text>
              {attendanceData.map((attendance, index) => (
                <View key={index} style={styles.attendanceItem}>
                  <View style={styles.attendanceInfo}>
                    <Text style={[styles.attendeeName, { color: colors.text }]}>
                      {attendance.userName || `User ${attendance.userId?.slice(-4)}`}
                    </Text>
                    <Text style={[styles.attendanceType, { color: colors.textSecondary }]}>
                      {attendance.attendanceType} attendance
                    </Text>
                  </View>
                  <View style={[
                    styles.attendanceStatus,
                    { backgroundColor: attendance.isPresent ? colors.success + '20' : colors.error + '20' }
                  ]}>
                    <Ionicons 
                      name={attendance.isPresent ? "checkmark-circle" : "close-circle"} 
                      size={16} 
                      color={attendance.isPresent ? colors.success : colors.error} 
                    />
                    <Text style={[
                      styles.attendanceStatusText,
                      { color: attendance.isPresent ? colors.success : colors.error }
                    ]}>
                      {attendance.isPresent ? 'Present' : 'Absent'}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}
        </Card>

        {/* Meeting Minutes */}
        {meetingMinutes && (
          <Card style={styles.minutesCard}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Meeting Minutes
            </Text>
            <Text style={[styles.minutesContent, { color: colors.textSecondary }]}>
              {meetingMinutes.content}
            </Text>
            <View style={styles.minutesMetadata}>
              <Text style={[styles.minutesStatus, { color: colors.primary }]}>
                Status: {meetingMinutes.status?.charAt(0).toUpperCase() + meetingMinutes.status?.slice(1)}
              </Text>
              {meetingMinutes.takenBy && (
                <Text style={[styles.minutesTakenBy, { color: colors.textSecondary }]}>
                  Recorded by: {meetingMinutes.takenBy}
                </Text>
              )}
            </View>
          </Card>
        )}

        {/* Meeting Documents */}
        {meetingDocuments.length > 0 && (
          <Card style={styles.documentsCard}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Meeting Documents ({meetingDocuments.length})
            </Text>
            
            {meetingDocuments.map((document, index) => (
              <TouchableOpacity key={index} style={styles.documentItem}>
                <View style={[styles.documentIcon, { backgroundColor: colors.primary + '20' }]}>
                  <Ionicons name="document" size={20} color={colors.primary} />
                </View>
                <View style={styles.documentInfo}>
                  <Text style={[styles.documentName, { color: colors.text }]} numberOfLines={1}>
                    {document.name}
                  </Text>
                  <Text style={[styles.documentType, { color: colors.textSecondary }]}>
                    {document.documentType} • {Math.round(document.size / 1024)} KB
                  </Text>
                </View>
                <Ionicons name="download" size={20} color={colors.primary} />
              </TouchableOpacity>
            ))}
          </Card>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading meeting history...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Meeting Summary
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {chamaName}
          </Text>
        </View>
        <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Mobile-friendly single column layout */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Meetings List */}
        {renderMeetingsList()}

        {/* Selected Meeting Details */}
        {selectedMeeting && renderMeetingDetails()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: spacing.sm,
    marginRight: spacing.sm,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs / 2,
  },
  refreshButton: {
    padding: spacing.sm,
    marginLeft: spacing.sm,
  },
  content: {
    flex: 1,
  },
  meetingsListCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  emptyState: {
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyStateText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginTop: spacing.md,
  },
  meetingsList: {
    // Remove maxHeight for mobile
  },
  meetingItem: {
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    marginBottom: spacing.sm,
  },
  meetingItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  meetingTypeIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  meetingItemInfo: {
    flex: 1,
  },
  meetingItemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs / 2,
  },
  meetingItemDate: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs / 2,
  },
  meetingItemTime: {
    fontSize: typography.fontSize.sm,
  },
  detailsContainer: {
    paddingHorizontal: spacing.md,
  },
  overviewCard: {
    marginBottom: spacing.md,
  },
  detailsTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.sm,
  },
  detailsDescription: {
    fontSize: typography.fontSize.base,
    lineHeight: typography.fontSize.base * 1.5,
    marginBottom: spacing.md,
  },
  meetingMetadata: {
    gap: spacing.sm,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  metadataText: {
    fontSize: typography.fontSize.sm,
  },
  attendanceCard: {
    marginBottom: spacing.md,
  },
  attendanceStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.md,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: 'rgba(0, 212, 170, 0.05)',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs / 2,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
  },
  attendanceList: {
    marginTop: spacing.md,
  },
  subsectionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
  },
  attendanceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  attendanceInfo: {
    flex: 1,
  },
  attendeeName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs / 2,
  },
  attendanceType: {
    fontSize: typography.fontSize.sm,
  },
  attendanceStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    gap: spacing.xs,
  },
  attendanceStatusText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  minutesCard: {
    marginBottom: spacing.md,
  },
  minutesContent: {
    fontSize: typography.fontSize.base,
    lineHeight: typography.fontSize.base * 1.6,
    marginBottom: spacing.md,
    padding: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  minutesMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  minutesStatus: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  minutesTakenBy: {
    fontSize: typography.fontSize.sm,
  },
  documentsCard: {
    marginBottom: spacing.md,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs / 2,
  },
  documentType: {
    fontSize: typography.fontSize.sm,
  },
});

export default MeetingSummaryScreen;
