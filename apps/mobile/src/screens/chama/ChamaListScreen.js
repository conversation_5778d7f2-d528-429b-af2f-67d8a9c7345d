import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';

const ChamaListScreen = ({ navigation, route }) => {
  const { theme, user, switchToChamaDashboard } = useApp();
  const colors = getThemeColors(theme);

  const [chamas, setChamas] = useState([]);
  const [myChamas, setMyChamas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingMyChamas, setLoadingMyChamas] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filters = [
    { id: 'all', name: 'All Chamas', icon: 'grid' },
    { id: 'savings', name: 'Savings', icon: 'wallet' },
    { id: 'investment', name: 'Investment', icon: 'trending-up' },
    { id: 'welfare', name: 'Welfare', icon: 'heart' },
    { id: 'business', name: 'Business', icon: 'briefcase' },
  ];

  useEffect(() => {
    loadChamas();
    loadMyChamas();
  }, [selectedFilter]);

  // Refresh chamas when screen is focused (e.g., after creating a new chama)
  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.refresh) {
        loadChamas();
        loadMyChamas();
        // Clear the refresh param to avoid unnecessary refreshes
        navigation.setParams({ refresh: undefined });
      }
    }, [route.params?.refresh])
  );

  const loadMyChamas = async () => {
    try {
      setLoadingMyChamas(true);
      const response = await ApiService.getUserChamas(10, 0);

      if (response.success) {
        setMyChamas(response.data || []);
      } else {
        setMyChamas([]);
      }
    } catch (error) {
      console.error('Failed to load user chamas:', error);
      setMyChamas([]);
    } finally {
      setLoadingMyChamas(false);
    }
  };

  const loadChamas = async () => {
    try {
      setLoading(true);

      // Get all chamas and filter on frontend for now
      // TODO: Add backend support for filtering
      const response = await ApiService.getChamas(50, 0);
      if (response.success) {
        let filteredChamas = response.data || [];

        // Apply search filter
        if (searchQuery) {
          filteredChamas = filteredChamas.filter(chama =>
            chama.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            chama.description.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }

        // Apply type filter
        if (selectedFilter !== 'all') {
          filteredChamas = filteredChamas.filter(chama => chama.type === selectedFilter);
        }

        // Only show public chamas
        filteredChamas = filteredChamas.filter(chama => chama.is_public);

        setChamas(filteredChamas);
      }
    } catch (error) {
      console.error('Failed to load chamas:', error);
      // Set empty array on error to show empty state
      setChamas([]);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadChamas(), loadMyChamas()]);
    setRefreshing(false);
  };

  const handleJoinChama = async (chama) => {
    try {
      const response = await ApiService.joinChama(chama.id);
      if (response.success) {
        navigation.navigate('ChamaDetails', { chamaId: chama.id });
      }
    } catch (error) {
      console.error('Failed to join chama:', error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const renderMyChamasSection = () => {
    if (myChamas.length === 0) {
      return null;
    }

    return (
      <View style={[styles.myChamasSection, { backgroundColor: colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            My Chamas ({myChamas.length})
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('MyChamas')}>
            <Text style={[styles.seeAllText, { color: colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        </View>

        <FlatList
          horizontal
          data={myChamas.slice(0, 5)}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[styles.myChamaCard, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}
              onPress={() => {
                console.log('🏘️ Opening chama dashboard for:', item.name);
                switchToChamaDashboard(item);
              }}
            >
              <View style={[styles.myChamaAvatar, { backgroundColor: colors.primary }]}>
                <Ionicons name="people" size={20} color={colors.white} />
              </View>
              <Text style={[styles.myChamaName, { color: colors.text }]} numberOfLines={1}>
                {item.name}
              </Text>
              <Text style={[styles.myChamaType, { color: colors.textSecondary }]}>
                {item.type}
              </Text>
              <Text style={[styles.myChamaFunds, { color: colors.success }]}>
                {formatCurrency(item.totalFunds || item.total_funds)}
              </Text>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.id}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.myChamasList}
        />
      </View>
    );
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <Text style={[styles.headerTitle, { color: colors.text }]}>
        Discover Chamas
      </Text>

      <Input
        placeholder="Search chamas..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon="search"
        onSubmitEditing={loadChamas}
        style={styles.searchInput}
      />

      <FlatList
        horizontal
        data={filters}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterChip,
              {
                backgroundColor: selectedFilter === item.id ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedFilter(item.id)}
          >
            <Ionicons
              name={item.icon}
              size={16}
              color={selectedFilter === item.id ? colors.white : colors.textSecondary}
            />
            <Text style={[
              styles.filterText,
              { color: selectedFilter === item.id ? colors.white : colors.textSecondary }
            ]}>
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}
      />
    </View>
  );

  const renderChamaCard = ({ item }) => (
    <Card style={styles.chamaCard}>
      <View style={styles.chamaHeader}>
        <View style={[styles.chamaAvatar, { backgroundColor: colors.primary }]}>
          <Ionicons name="people" size={24} color={colors.white} />
        </View>

        <View style={styles.chamaInfo}>
          <Text style={[styles.chamaName, { color: colors.text }]} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={[styles.chamaType, { color: colors.textSecondary }]}>
            {item.type} • {item.county}, {item.town}
          </Text>
        </View>

        <View style={[styles.statusBadge, { backgroundColor: colors.success + '20' }]}>
          <Text style={[styles.statusText, { color: colors.success }]}>
            ACTIVE
          </Text>
        </View>
      </View>

      <Text style={[styles.chamaDescription, { color: colors.textSecondary }]} numberOfLines={3}>
        {item.description}
      </Text>

      <View style={styles.chamaStats}>
        <View style={styles.statItem}>
          <Ionicons name="people" size={16} color={colors.primary} />
          <Text style={[styles.statText, { color: colors.text }]}>
            {item.current_members}/{item.max_members} members
          </Text>
        </View>

        <View style={styles.statItem}>
          <Ionicons name="wallet" size={16} color={colors.secondary} />
          <Text style={[styles.statText, { color: colors.text }]}>
            {formatCurrency(item.contribution_amount)} {item.contribution_frequency}
          </Text>
        </View>

        <View style={styles.statItem}>
          <Ionicons name="trending-up" size={16} color={colors.warning} />
          <Text style={[styles.statText, { color: colors.text }]}>
            {formatCurrency(item.total_funds)} total
          </Text>
        </View>
      </View>

      <View style={styles.chamaActions}>
        <Button
          title="View Details"
          variant="outline"
          size="small"
          onPress={() => navigation.navigate('ChamaDetails', { chamaId: item.id })}
          style={styles.actionButton}
        />

        <Button
          title="Join Chama"
          size="small"
          onPress={() => handleJoinChama(item)}
          disabled={item.current_members >= item.max_members}
          style={styles.actionButton}
        />
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No chamas found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {searchQuery || selectedFilter !== 'all'
          ? 'Try adjusting your search or filters'
          : 'Discover and join chamas in your area or create your own'
        }
      </Text>

      {/* <Button
        title="Create Chama"
        onPress={() => navigation.navigate('CreateChama')}
        style={styles.createButton}
        icon={<Ionicons name="add" size={20} color={colors.white} />}
      /> */}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Static Header */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        {renderHeader()}
      </View>

      {/* Scrollable Content */}
      <FlatList
        data={chamas}
        renderItem={renderChamaCard}
        keyExtractor={(item) => item.id}
        style={[styles.scrollableContainer, { flex: 1 }]}
        contentContainerStyle={[
          styles.scrollableContent,
          chamas.length === 0 && { flexGrow: 1 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <View>
            {renderMyChamasSection()}
            <View style={styles.discoverSectionHeader}>
              <Text style={[styles.discoverTitle, { color: colors.text }]}>
                Discover New Chamas
              </Text>
            </View>
          </View>
        )}
        ListEmptyComponent={!loading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
      />

      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('CreateChama')}
      >
        <Ionicons name="add" size={24} color={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  scrollableContainer: {
    flex: 1,
  },
  scrollableContent: {
    flexGrow: 1,
    paddingBottom: spacing.xxxl, // Extra space for FAB
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.md,
  },
  searchInput: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    paddingRight: spacing.md,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    borderWidth: 1,
  },
  filterText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  chamasList: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.md,
  },
  chamaCard: {
    marginBottom: spacing.md,
  },
  chamaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  chamaAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  chamaInfo: {
    flex: 1,
  },
  chamaName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  chamaType: {
    fontSize: typography.fontSize.sm,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  chamaDescription: {
    fontSize: typography.fontSize.base,
    lineHeight: typography.lineHeight.relaxed,
    marginBottom: spacing.md,
  },
  chamaStats: {
    marginBottom: spacing.md,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
  },
  chamaActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  createButton: {
    marginTop: spacing.md,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
  },
  // My Chamas Section Styles
  myChamasSection: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  seeAllText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  myChamasList: {
    paddingRight: spacing.md,
  },
  myChamaCard: {
    width: 140,
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    marginRight: spacing.md,
    borderWidth: 1,
    alignItems: 'center',
  },
  myChamaAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  myChamaName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  myChamaType: {
    fontSize: typography.fontSize.xs,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  myChamaFunds: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
  },
  // Discover Section Styles
  discoverSection: {
    flex: 1,
  },
  discoverSectionHeader: {
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
    paddingBottom: spacing.sm,
  },
  discoverTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
});

export default ChamaListScreen;
