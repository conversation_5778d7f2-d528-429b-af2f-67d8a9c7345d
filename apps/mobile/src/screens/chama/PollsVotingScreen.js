import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import { formatDate, formatTimeRemaining } from '../../utils/formatters';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import ApiService from '../../services/api';

const PollsVotingScreen = ({ route, navigation }) => {
  const { chamaId } = route.params;
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('active'); // 'active', 'completed', 'role-escalation'
  const [polls, setPolls] = useState([]);
  const [votes, setVotes] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [userRole, setUserRole] = useState('member');
  const [chamaMembers, setChamaMembers] = useState([]);
  const [filteredMembers, setFilteredMembers] = useState([]);
  const [memberSearchQuery, setMemberSearchQuery] = useState('');

  // Real-time update states
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);
  const [pollStatusChanges, setPollStatusChanges] = useState(new Map());
  const [chamaContext, setChamaContext] = useState(null);

  // Form data for creating polls
  const [pollForm, setPollForm] = useState({
    title: '',
    description: '',
    type: 'general',
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    options: ['', ''],
    isAnonymous: true,
    requiresMajority: true,
    majorityPercentage: 50,
  });

  // Form data for role escalation
  const [roleForm, setRoleForm] = useState({
    candidateId: '',
    requestedRole: 'chairperson',
    justification: '',
    selectedCandidate: null,
  });

  useEffect(() => {
    loadData();
  }, [chamaId, activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadPolls(),
        loadUserRole(),
        loadChamaMembers(),
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadVotes = async () => {
    try {
      // Security: Verify chamaId is valid and user has access
      if (!chamaId || typeof chamaId !== 'string') {
        console.error('🚨 Security: Invalid chamaId provided:', chamaId);
        Alert.alert('Error', 'Invalid chama access. Please try again.');
        return;
      }

      console.log('🔒 Security: Loading votes for chama:', chamaId, 'User:', user?.id, 'activeTab:', activeTab);
      let response;

      if (activeTab === 'active') {
        console.log('📡 Calling getActiveVotes for chama:', chamaId);
        response = await ApiService.getActiveVotes(chamaId);
      } else if (activeTab === 'completed') {
        console.log('📡 Calling getVoteResults for chama:', chamaId);
        response = await ApiService.getVoteResults(chamaId);
      } else {
        console.log('📡 Calling getChamaVotes for chama:', chamaId);
        response = await ApiService.getChamaVotes(chamaId);
      }

      console.log('📡 Votes API response for chama', chamaId, ':', response);

      if (response.success) {
        console.log('✅ Votes loaded successfully for chama', chamaId, ':', response.data?.length || 0, 'votes');

        // Security: Verify all returned votes belong to this chama (additional frontend validation)
        const validVotes = (response.data || []).filter(vote => {
          if (!vote.id || !vote.id.includes('vote-')) {
            console.warn('🚨 Security: Invalid vote ID format:', vote.id);
            return false;
          }
          return true;
        });

        setVotes(validVotes);
        setPolls(validVotes); // Keep setPolls for backward compatibility
      } else {
        console.error('❌ Failed to load votes for chama', chamaId, ':', response.error);
        setVotes([]);
        setPolls([]);

        // Check if it's an access denied error
        if (response.error?.includes('Access denied') || response.error?.includes('not a member')) {
          Alert.alert('Access Denied', 'You do not have permission to view votes for this chama.');
        }
      }
    } catch (error) {
      console.error('❌ Error loading votes:', error);
      setPolls([]);
    }
  };

  // Keep old function name for compatibility but redirect to new one
  const loadPolls = loadVotes;


  const loadUserRole = async () => {
    try {
      const response = await ApiService.getMemberRole(chamaId, user.id);
      if (response.success) {
        setUserRole(response.data?.role || 'member');
      } else {
        setUserRole('member'); // Default to member if role fetch fails
      }
    } catch (error) {
      console.error('Error loading user role:', error);
      setUserRole('member'); // Default to member on error
    }
  };

  const loadChamaMembers = async () => {
    try {
      console.log('🔍 Loading chama members for chamaId:', chamaId);
      const response = await ApiService.getChamaMembers(chamaId);
      console.log('📡 Members API response:', response);

      if (response.success) {
        console.log('✅ Members loaded successfully:', response.data?.length || 0, 'members');
        console.log('🔍 First member structure:', response.data?.[0]);
        setChamaMembers(response.data || []);
        setFilteredMembers(response.data || []);
      } else {
        console.error('❌ Failed to load chama members:', response.error);
      }
    } catch (error) {
      console.error('❌ Error loading chama members:', error);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [activeTab]);

  // Helper functions to get member data from nested structure
  const getMemberName = (member) => {
    const user = member.user || {};
    const firstName = user.first_name || member.firstName || '';
    const lastName = user.last_name || member.lastName || '';
    return `${firstName} ${lastName}`.trim() || 'Unknown Member';
  };

  const getMemberEmail = (member) => {
    const user = member.user || {};
    return user.email || member.email || 'No email';
  };

  const handleCreatePoll = async () => {
    // Validation based on vote type
    if (pollForm.type === 'role_escalation') {
      console.log('🔍 Role escalation validation:', {
        candidateId: roleForm.candidateId,
        requestedRole: roleForm.requestedRole,
        selectedCandidate: roleForm.selectedCandidate,
        justification: roleForm.justification
      });

      if (!roleForm.candidateId || !roleForm.requestedRole || !roleForm.selectedCandidate) {
        Alert.alert('Error', 'Please select a candidate and specify the requested role for role escalation');
        return;
      }
    } else {
      if (!pollForm.title || pollForm.options.some(opt => !opt.trim())) {
        Alert.alert('Error', 'Please fill in all required fields');
        return;
      }
    }

    try {
      if (pollForm.type === 'role_escalation') {
        // Handle role escalation vote creation
        const candidateName = getMemberName(roleForm.selectedCandidate);
        const escalationData = {
          candidateId: roleForm.candidateId,
          requestedRole: roleForm.requestedRole,
          justification: roleForm.justification || `Role change request for ${candidateName} to ${roleForm.requestedRole}`,
        };

        console.log('🔄 Creating role escalation vote:', escalationData);
        const response = await ApiService.createRoleEscalationVote(chamaId, escalationData);
        if (response.success) {
          Alert.alert(
            'Success',
            'Role escalation vote created successfully! Members can now vote on this role change.',
            [{ text: 'OK', onPress: () => {
              setShowCreateModal(false);
              resetPollForm();
              loadVotes();
            }}]
          );
        } else {
          Alert.alert('Error', response.error || 'Failed to create role escalation vote');
        }
      } else {
        // Handle regular vote creation
        const voteData = {
          title: pollForm.title,
          description: pollForm.description,
          type: pollForm.type,
          ends_at: pollForm.endDate,
          options: pollForm.options.map(optionText => ({ option_text: optionText.trim() })),
        };

        const response = await ApiService.createVote(chamaId, voteData);
        if (response.success) {
          Alert.alert('Success', 'Vote created successfully!');
          setShowCreateModal(false);
          resetPollForm();
          await loadVotes();
        } else {
          Alert.alert('Error', response.error || 'Failed to create vote');
        }
      }
    } catch (error) {
      console.error('Error creating vote:', error);
      Alert.alert('Error', 'Failed to create vote');
    }
  };

  const handleCreateRoleEscalation = async () => {
    if (!roleForm.candidateId || !roleForm.requestedRole || !roleForm.selectedCandidate) {
      Alert.alert('Error', 'Please select a candidate and specify the role');
      return;
    }

    const candidateName = roleForm.selectedCandidate.fullName;
    const currentRole = roleForm.selectedCandidate.role;

    // Check if candidate is applying for the same role they already have
    if (currentRole === roleForm.requestedRole) {
      Alert.alert(
        'Confirm Role Retention',
        `${candidateName} is already a ${currentRole}. This vote will confirm their continuation in this role. Continue?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Continue', onPress: () => proceedWithRoleEscalation() }
        ]
      );
    } else {
      Alert.alert(
        'Confirm Role Change',
        `This will create a vote to change ${candidateName} from ${currentRole} to ${roleForm.requestedRole}. If approved, the role change will take effect immediately. Continue?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Continue', onPress: () => proceedWithRoleEscalation() }
        ]
      );
    }
  };

  const proceedWithRoleEscalation = async () => {
    try {
      const escalationData = {
        candidateId: roleForm.candidateId,
        requestedRole: roleForm.requestedRole,
        justification: roleForm.justification,
      };

      const response = await ApiService.createRoleEscalationPoll(chamaId, escalationData);
      if (response.success) {
        Alert.alert(
          'Success',
          'Role escalation poll created successfully! Members can now vote on this role change.',
          [{ text: 'OK', onPress: () => {
            setShowRoleModal(false);
            resetRoleForm();
            loadPolls();
          }}]
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to create role escalation poll');
      }
    } catch (error) {
      console.error('Error creating role escalation:', error);
      Alert.alert('Error', 'Failed to create role escalation poll');
    }
  };

  const handleVote = async (pollId, optionId, poll) => {
    console.log('🗳️ handleVote called with:', { pollId, optionId, poll });

    // Security: Validate vote parameters
    if (!pollId || !optionId || !chamaId) {
      console.error('🚨 Security: Invalid vote parameters:', { pollId, optionId, chamaId });
      Alert.alert('Error', 'Invalid vote parameters. Please try again.');
      return;
    }

    // Security: Verify user is authenticated
    if (!user?.id) {
      console.error('🚨 Security: User not authenticated for voting');
      Alert.alert('Error', 'You must be logged in to vote.');
      return;
    }

    // Security: Verify vote belongs to current chama
    if (!pollId.includes('vote-')) {
      console.error('🚨 Security: Invalid vote ID format:', pollId);
      Alert.alert('Error', 'Invalid vote format.');
      return;
    }

    console.log('🔒 Security: Vote validation passed. User:', user.id, 'Chama:', chamaId, 'Vote:', pollId);
    console.log('🗳️ Starting vote process...');

    try {
              const voteData = { optionId };
              console.log('🗳️ Casting vote:', {
                chamaId,
                pollId,
                optionId,
                voteData
              });

              // Immediately update UI to show vote was cast (optimistic update)
              setVotes(prevVotes =>
                prevVotes.map(vote =>
                  vote.id === pollId
                    ? { ...vote, userVoted: true }
                    : vote
                )
              );

              const response = await ApiService.castVote(chamaId, pollId, voteData);
              console.log('🗳️ Vote response:', response);

              if (response.success) {
                // Check if this vote completed the poll and it's a role escalation
                if (poll.type === 'role_escalation' && response.data?.pollCompleted) {
                  if (response.data?.result === 'passed') {
                    // Get the winning candidate info
                    const candidateName = response.data?.candidateName || 'the candidate';
                    const newRole = response.data?.newRole || 'new role';

                    Alert.alert(
                      '🎉 Congratulations!',
                      `${candidateName} has been successfully elected to the ${newRole} position! The role change has taken effect immediately.`,
                      [{ text: 'OK', onPress: () => loadPolls() }]
                    );
                  } else {
                    Alert.alert(
                      'Vote Complete',
                      'The role escalation vote has been completed. The role change was not approved.',
                      [{ text: 'OK', onPress: () => loadVotes() }]
                    );
                  }
                } else {
                  // Vote state already updated optimistically above

                  // Update vote counts in local state
                  setVotes(prevVotes =>
                    prevVotes.map(vote => {
                      if (vote.id === pollId) {
                        return {
                          ...vote,
                          options: vote.options.map(opt =>
                            opt.id === optionId
                              ? { ...opt, voteCount: opt.voteCount + 1 }
                              : opt
                          ),
                          totalVotes: (vote.totalVotes || 0) + 1
                        };
                      }
                      return vote;
                    })
                  );

                  // Also update polls state for backward compatibility
                  setPolls(prevPolls =>
                    prevPolls.map(poll => {
                      if (poll.id === pollId) {
                        return {
                          ...poll,
                          options: poll.options.map(opt =>
                            opt.id === optionId
                              ? { ...opt, voteCount: opt.voteCount + 1 }
                              : opt
                          ),
                          totalVotes: (poll.totalVotes || 0) + 1
                        };
                      }
                      return poll;
                    })
                  );

                  Alert.alert(
                    'Vote Cast Successfully! 🎉',
                    'Your vote has been recorded and vote counts updated!',
                    [{ text: 'OK' }]
                  );
                }
              } else {
                // Revert optimistic update on failure
                setVotes(prevVotes =>
                  prevVotes.map(vote =>
                    vote.id === pollId
                      ? { ...vote, userVoted: false }
                      : vote
                  )
                );
                Alert.alert('Error', response.error || 'Failed to cast vote');
              }
            } catch (error) {
              console.error('Error casting vote:', error);
              // Revert optimistic update on error
              setVotes(prevVotes =>
                prevVotes.map(vote =>
                  vote.id === pollId
                    ? { ...vote, userVoted: false }
                    : vote
                )
              );
              Alert.alert('Error', 'Failed to cast vote');
            }
  };

  const resetPollForm = () => {
    setPollForm({
      title: '',
      description: '',
      type: 'general',
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      options: ['', ''],
      isAnonymous: true,
      requiresMajority: true,
      majorityPercentage: 50,
    });

    // Also reset role form and member search
    resetRoleForm();
    setMemberSearchQuery('');
    setFilteredMembers(chamaMembers);
  };

  const resetRoleForm = () => {
    setRoleForm({
      candidateId: '',
      requestedRole: 'chairperson',
      justification: '',
      selectedCandidate: null,
    });
    setMemberSearchQuery('');
    setFilteredMembers(chamaMembers);
  };

  const handleMemberSearch = (query) => {
    setMemberSearchQuery(query);
    if (!query.trim()) {
      setFilteredMembers(chamaMembers);
      return;
    }

    const filtered = chamaMembers.filter(member => {
      // Get member name and email using helper functions
      const fullName = getMemberName(member);
      const email = getMemberEmail(member);
      const role = member.role || '';

      return fullName.toLowerCase().includes(query.toLowerCase()) ||
             email.toLowerCase().includes(query.toLowerCase()) ||
             role.toLowerCase().includes(query.toLowerCase());
    });
    setFilteredMembers(filtered);
  };

  const handleSelectCandidate = (member) => {
    setRoleForm(prev => ({
      ...prev,
      candidateId: member.user_id || member.userId,
      selectedCandidate: member,
    }));
    setMemberSearchQuery(getMemberName(member));
    setFilteredMembers([]);
  };

  const addPollOption = () => {
    if (pollForm.options.length < 10) {
      setPollForm(prev => ({
        ...prev,
        options: [...prev.options, '']
      }));
    }
  };

  const removePollOption = (index) => {
    if (pollForm.options.length > 2) {
      setPollForm(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      }));
    }
  };

  const updatePollOption = (index, value) => {
    setPollForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => i === index ? value : opt)
    }));
  };

  const canCreatePolls = () => {
    return true; // Any member can create general polls
  };

  const canCreateRoleEscalation = () => {
    return ['chairperson', 'secretary'].includes(userRole);
  };

  const getVotePercentage = (voteCount, totalVotes) => {
    if (totalVotes === 0) return 0;
    return Math.round((voteCount / totalVotes) * 100);
  };

  const getStatusColor = (status, result) => {
    if (status === 'completed') {
      return result === 'passed' ? colors.success : colors.error;
    }
    return colors.warning;
  };

  const renderPollItem = ({ item }) => (
    <Card style={[styles.pollCard, { backgroundColor: colors.surface }]}>
      <View style={styles.pollHeader}>
        <View style={styles.pollInfo}>
          <Text style={[styles.pollTitle, { color: colors.text }]}>{item.title}</Text>
          <Text style={[styles.pollCreator, { color: colors.textSecondary }]}>
            by {item.createdBy}
          </Text>
        </View>
        <View style={[
          styles.pollTypeBadge,
          { backgroundColor: getPollTypeColor(item.type) + '15' }
        ]}>
          <Text style={[styles.pollTypeText, { color: getPollTypeColor(item.type) }]}>
            {item.type.replace('_', ' ')}
          </Text>
        </View>
      </View>

      {item.description && (
        <Text style={[styles.pollDescription, { color: colors.textSecondary }]}>
          {item.description}
        </Text>
      )}

      <View style={styles.pollStats}>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.primary }]}>
            {item.totalVotesCast}/{item.totalEligibleVoters}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Votes Cast
          </Text>
        </View>
        
        {item.status === 'active' && item.timeRemaining && (
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.warning }]}>
              {formatTimeRemaining(item.timeRemaining)}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Time Left
            </Text>
          </View>
        )}

        {item.status === 'completed' && (
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: getStatusColor(item.status, item.result) }]}>
              {item.result}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Result
            </Text>
          </View>
        )}
      </View>

      {/* Voting Status Message */}
      {item.userVoted && (
        <View style={[styles.votingStatusMessage, { backgroundColor: colors.success + '10', borderColor: colors.success, borderWidth: 1 }]}>
          <Ionicons name="checkmark-circle" size={18} color={colors.success} />
          <Text style={[styles.votingStatusText, { color: colors.success, fontSize: 13, fontWeight: '600' }]}>
            ✓ You have voted on this item
          </Text>
        </View>
      )}

      {item.status === 'completed' && !item.userVoted && (
        <View style={[styles.votingStatusMessage, { backgroundColor: colors.textSecondary + '10', borderColor: colors.textSecondary, borderWidth: 1 }]}>
          <Ionicons name="time" size={18} color={colors.textSecondary} />
          <Text style={[styles.votingStatusText, { color: colors.textSecondary, fontSize: 13 }]}>
            Vote ended - You did not participate
          </Text>
        </View>
      )}

      {/* Poll Options */}
      <View style={styles.optionsContainer}>
        {item.options.map((option, index) => {
          const percentage = getVotePercentage(option.voteCount, item.totalVotes || 0);
          const canVote = item.status === 'active' && !item.userVoted;
          const showVotingInterface = item.status === 'active' && !item.userVoted;

          // Debug vote data structure
          if (index === 0) {
            console.log('🗳️ Vote item structure:', {
              itemId: item.id,
              itemStatus: item.status,
              userVoted: item.userVoted,
              canVote,
              showVotingInterface,
              optionId: option.id,
              optionText: option.text
            });
          }

          return (
            <View
              key={option.id || index}
              style={[
                styles.optionItem,
                { backgroundColor: colors.surface },
                canVote && {
                  borderColor: colors.primary,
                  borderWidth: 2,
                  backgroundColor: colors.primary + '08'
                },
                item.userVoted && {
                  borderColor: colors.success,
                  backgroundColor: colors.success + '08'
                },
                item.status === 'completed' && {
                  borderColor: colors.textSecondary,
                  backgroundColor: colors.textSecondary + '05'
                }
              ]}
              onPress={showVotingInterface ? () => {
                console.log('🗳️ Option row clicked!', {
                  itemId: item.id,
                  optionId: option.id,
                  canVote,
                  showVotingInterface
                });
                handleVote(item.id, option.id, item);
              } : undefined}
              disabled={!showVotingInterface}
              activeOpacity={showVotingInterface ? 0.8 : 1}
            >
              {/* Voting Interface - Only show when user can vote */}
              {showVotingInterface && (
                <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 12 }}>
                  {/* Simple Vote Button */}
                  <TouchableOpacity
                    style={{
                      backgroundColor: colors.primary,
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      borderRadius: 20,
                      marginRight: 8,
                      minWidth: 80,
                      alignItems: 'center'
                    }}
                    onPress={() => {
                      console.log('🗳️ VOTE BUTTON PRESSED!', {
                        itemId: item.id,
                        optionId: option.id,
                        optionText: option.text
                      });
                      handleVote(item.id, option.id, item);
                    }}
                    activeOpacity={0.8}
                  >
                    <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 14 }}>
                      VOTE
                    </Text>
                  </TouchableOpacity>

                  {/* Test button */}
                  <TouchableOpacity
                    style={{
                      backgroundColor: colors.warning,
                      paddingHorizontal: 12,
                      paddingVertical: 8,
                      borderRadius: 16
                    }}
                    onPress={() => {
                      console.log('🧪 TEST BUTTON CLICKED!');
                      Alert.alert('Test', 'Touch events work!');
                    }}
                  >
                    <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
                      TEST
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* User Already Voted Icon - Smaller and cleaner */}
              {item.userVoted && (
                <View style={[styles.voteIconContainer, { backgroundColor: colors.success + '15', borderRadius: 12, padding: 6 }]}>
                  <Ionicons
                    name="checkmark-circle"
                    size={16}
                    color={colors.success}
                  />
                </View>
              )}

              {/* Completed Vote Icon - Smaller */}
              {item.status === 'completed' && !item.userVoted && (
                <View style={[styles.voteIconContainer, { backgroundColor: colors.textSecondary + '15', borderRadius: 12, padding: 6 }]}>
                  <Ionicons
                    name="time"
                    size={16}
                    color={colors.textSecondary}
                  />
                </View>
              )}

              <View style={styles.optionContent}>
                <Text style={[styles.optionText, { color: colors.text }]}>
                  {option.text}
                </Text>
                {/* Show vote count for all polls */}
                <Text style={[styles.optionVotes, { color: colors.textSecondary }]}>
                  {option.voteCount} votes ({percentage}%)
                </Text>
              </View>

              {/* Vote Action Indicator */}
              {showVotingInterface && (
                <View style={[styles.actionIndicator, { backgroundColor: colors.primary }]}>
                  <Text style={[styles.actionText, { color: colors.surface }]}>
                    TAP TO VOTE
                  </Text>
                </View>
              )}

              {/* Voted Indicator - Compact */}
              {item.userVoted && (
                <View style={[styles.actionIndicator, { backgroundColor: colors.success, paddingHorizontal: 6, paddingVertical: 2 }]}>
                  <Text style={[styles.actionText, { color: colors.surface, fontSize: 9 }]}>
                    VOTED
                  </Text>
                </View>
              )}

              {/* Completed Indicator - Compact */}
              {item.status === 'completed' && !item.userVoted && (
                <View style={[styles.actionIndicator, { backgroundColor: colors.textSecondary, paddingHorizontal: 6, paddingVertical: 2 }]}>
                  <Text style={[styles.actionText, { color: colors.surface, fontSize: 9 }]}>
                    ENDED
                  </Text>
                </View>
              )}

              {/* Anonymous Voting Indicator */}
              {item.isAnonymous && (
                <View style={[styles.anonymousIndicator, { backgroundColor: colors.warning + '15', borderRadius: 12, padding: 4 }]}>
                  <Ionicons
                    name="eye-off"
                    size={16}
                    color={colors.warning}
                  />
                </View>
              )}
              
              {item.status === 'completed' && (
                <View style={[
                  styles.progressBar,
                  { backgroundColor: colors.border }
                ]}>
                  <View style={[
                    styles.progressFill,
                    {
                      width: `${percentage}%`,
                      backgroundColor: colors.primary
                    }
                  ]} />
                </View>
              )}
            </View>
          );
        })}
      </View>

      {/* Only show "You have voted" badge for active polls */}
      {item.userVoted && item.status === 'active' && (
        <View style={[styles.votedBadge, { backgroundColor: colors.success + '15' }]}>
          <Ionicons name="checkmark-circle" size={16} color={colors.success} />
          <Text style={[styles.votedText, { color: colors.success }]}>
            You have voted
          </Text>
        </View>
      )}

      <View style={styles.pollMeta}>
        <Text style={[styles.pollDate, { color: colors.textSecondary }]}>
          {item.status === 'active' ? 'Ends' : 'Ended'}: {formatDate(item.endsAt, 'datetime')}
        </Text>

        <View style={styles.pollBadges}>
          {item.isAnonymous && (
            <View style={[styles.anonymousBadge, { backgroundColor: colors.warning + '20', borderColor: colors.warning, borderWidth: 1 }]}>
              <Ionicons name="eye-off" size={12} color={colors.warning} />
              <Text style={[styles.anonymousText, { color: colors.warning }]}>
                Anonymous
              </Text>
            </View>
          )}

          {item.userVoted && (
            <View style={[styles.votedBadge, { backgroundColor: colors.success + '15', borderColor: colors.success, borderWidth: 1 }]}>
              <Ionicons name="checkmark-circle" size={10} color={colors.success} />
              <Text style={[styles.votedText, { color: colors.success, fontSize: 9 }]}>
                VOTED
              </Text>
            </View>
          )}

          {item.status === 'active' && !item.userVoted && (
            <View style={[styles.canVoteBadge, { backgroundColor: colors.primary + '15', borderColor: colors.primary, borderWidth: 1 }]}>
              <Ionicons name="radio-button-off" size={10} color={colors.primary} />
              <Text style={[styles.canVoteText, { color: colors.primary, fontSize: 9 }]}>
                CAN VOTE
              </Text>
            </View>
          )}

          {item.status === 'completed' && (
            <View style={[styles.completedBadge, { backgroundColor: colors.textSecondary + '15', borderColor: colors.textSecondary, borderWidth: 1 }]}>
              <Ionicons name="time" size={10} color={colors.textSecondary} />
              <Text style={[styles.completedText, { color: colors.textSecondary, fontSize: 9 }]}>
                ENDED
              </Text>
            </View>
          )}
        </View>
      </View>
    </Card>
  );

  const getPollTypeColor = (type) => {
    switch (type) {
      case 'general': return colors.info;
      case 'role_escalation': return colors.warning;
      case 'financial_decision': return colors.success;
      default: return colors.textSecondary;
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Polls & Voting
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary, fontSize: 11 }]}>
            🔒 Chama ID: {chamaId?.slice(-8) || 'Unknown'}
          </Text>
        </View>
        {canCreatePolls() && (
          <TouchableOpacity
            onPress={() => setShowCreateModal(true)}
            style={styles.addButton}
          >
            <Ionicons name="add" size={24} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'active' && { backgroundColor: colors.primary + '15' }
          ]}
          onPress={() => setActiveTab('active')}
        >
          <Ionicons
            name="time"
            size={20}
            color={activeTab === 'active' ? colors.primary : colors.textSecondary}
          />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'active' ? colors.primary : colors.textSecondary }
          ]}>
            Active
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'completed' && { backgroundColor: colors.primary + '15' }
          ]}
          onPress={() => setActiveTab('completed')}
        >
          <Ionicons
            name="checkmark-circle"
            size={20}
            color={activeTab === 'completed' ? colors.primary : colors.textSecondary}
          />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'completed' ? colors.primary : colors.textSecondary }
          ]}>
            Completed
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <FlatList
        data={polls}
        renderItem={renderPollItem}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons
              name="checkmark-circle-outline"
              size={64}
              color={colors.textSecondary}
            />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No {activeTab} polls found
            </Text>
            {canCreatePolls() && activeTab === 'active' && (
              <Button
                title="Create Poll"
                onPress={() => setShowCreateModal(true)}
                style={[styles.emptyButton, { backgroundColor: colors.primary }]}
              />
            )}
          </View>
        }
      />

      {/* Create Poll Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: colors.surface }]}>
            <TouchableOpacity
              onPress={() => setShowCreateModal(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Create Poll
            </Text>
            <View style={styles.modalCloseButton} />
          </View>

          <ScrollView style={styles.modalContent} contentContainerStyle={styles.modalContentContainer}>
            {/* Title field - only for non-role-escalation votes */}
            {pollForm.type !== 'role_escalation' && (
              <View style={styles.formGroup}>
                <Text style={[styles.formLabel, { color: colors.text }]}>
                  Poll Title *
                </Text>
                <TextInput
                  style={[styles.formInput, { backgroundColor: colors.surface, color: colors.text }]}
                  value={pollForm.title}
                  onChangeText={(text) => setPollForm(prev => ({ ...prev, title: text }))}
                  placeholder="Enter poll title"
                  placeholderTextColor={colors.textSecondary}
                />
              </View>
            )}

            {/* Description field - optional for all vote types */}
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Description {pollForm.type === 'role_escalation' ? '(Auto-generated)' : '(Optional)'}
              </Text>
              <TextInput
                style={[styles.formInput, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={pollForm.description}
                onChangeText={(text) => setPollForm(prev => ({ ...prev, description: text }))}
                placeholder={pollForm.type === 'role_escalation' ? 'Justification will be used as description' : 'Enter poll description (optional)'}
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
                editable={pollForm.type !== 'role_escalation'}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Poll Type *
              </Text>
              <View style={styles.radioGroup}>
                {[
                  { value: 'general', label: 'General Poll', description: 'For general opinions and decisions' },
                  { value: 'financial_decision', label: 'Financial Decision', description: 'For financial matters requiring approval' },
                  { value: 'role_escalation', label: 'Role Change', description: 'For changing member roles (Chair only)' },
                ].map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.radioOption,
                      type.value === 'role_escalation' && userRole !== 'chairperson' && styles.disabledOption
                    ]}
                    onPress={() => {
                      if (type.value === 'role_escalation' && userRole !== 'chairperson') {
                        Alert.alert('Access Denied', 'Only the chairperson can create role escalation polls');
                        return;
                      }
                      setPollForm(prev => ({ ...prev, type: type.value }));
                    }}
                    disabled={type.value === 'role_escalation' && userRole !== 'chairperson'}
                  >
                    <View style={[
                      styles.radioCircle,
                      { borderColor: colors.primary },
                      pollForm.type === type.value && { backgroundColor: colors.primary },
                      type.value === 'role_escalation' && userRole !== 'chairperson' && { borderColor: colors.textTertiary }
                    ]}>
                      {pollForm.type === type.value && (
                        <View style={[styles.radioInner, { backgroundColor: colors.surface }]} />
                      )}
                    </View>
                    <View style={styles.radioContent}>
                      <Text style={[
                        styles.radioLabel,
                        { color: colors.text },
                        type.value === 'role_escalation' && userRole !== 'chairperson' && { color: colors.textTertiary }
                      ]}>
                        {type.label}
                      </Text>
                      <Text style={[
                        styles.radioDescription,
                        { color: colors.textSecondary },
                        type.value === 'role_escalation' && userRole !== 'chairperson' && { color: colors.textTertiary }
                      ]}>
                        {type.description}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Role Escalation Fields */}
            {pollForm.type === 'role_escalation' && (
              <>
                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Select Candidate Member *
                  </Text>
                  <TextInput
                    style={[styles.formInput, { backgroundColor: colors.surface, color: colors.text }]}
                    value={memberSearchQuery}
                    onChangeText={handleMemberSearch}
                    placeholder="Search members by name, email, or role"
                    placeholderTextColor={colors.textSecondary}
                  />

                  {/* Member Search Results */}
                  {filteredMembers.length > 0 && memberSearchQuery && (
                    <View style={[styles.searchResults, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                      {filteredMembers.slice(0, 5).map((member) => (
                        <TouchableOpacity
                          key={member.user_id || member.userId || member.id}
                          style={[styles.searchResultItem, { borderBottomColor: colors.border }]}
                          onPress={() => handleSelectCandidate(member)}
                        >
                          <View style={styles.memberInfo}>
                            <Text style={[styles.memberName, { color: colors.text }]}>
                              {getMemberName(member)}
                            </Text>
                            <Text style={[styles.memberDetails, { color: colors.textSecondary }]}>
                              {member.role} • {getMemberEmail(member)}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}

                  {/* Selected Candidate Display */}
                  {roleForm.selectedCandidate && (
                    <View style={[styles.selectedCandidate, { backgroundColor: colors.primary + '20', borderColor: colors.primary }]}>
                      <Text style={[styles.selectedCandidateText, { color: colors.text }]}>
                        Selected: {getMemberName(roleForm.selectedCandidate)} ({roleForm.selectedCandidate.role})
                      </Text>
                      <TouchableOpacity
                        onPress={() => {
                          setRoleForm(prev => ({ ...prev, candidateId: '', selectedCandidate: null }));
                          setMemberSearchQuery('');
                          setFilteredMembers(chamaMembers);
                        }}
                      >
                        <Ionicons name="close-circle" size={20} color={colors.primary} />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Requested Role *
                  </Text>
                  <View style={styles.radioGroup}>
                    {[
                      { value: 'chairperson', label: 'Chairperson' },
                      { value: 'secretary', label: 'Secretary' },
                      { value: 'treasurer', label: 'Treasurer' },
                      { value: 'member', label: 'Member (Demotion)' },
                    ].map((role) => (
                      <TouchableOpacity
                        key={role.value}
                        style={styles.radioOption}
                        onPress={() => setRoleForm(prev => ({ ...prev, requestedRole: role.value }))}
                      >
                        <View style={[
                          styles.radioCircle,
                          { borderColor: colors.primary },
                          roleForm.requestedRole === role.value && { backgroundColor: colors.primary }
                        ]}>
                          {roleForm.requestedRole === role.value && (
                            <View style={[styles.radioInner, { backgroundColor: colors.surface }]} />
                          )}
                        </View>
                        <Text style={[styles.radioLabel, { color: colors.text }]}>
                          {role.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Justification
                  </Text>
                  <TextInput
                    style={[styles.formInput, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                    value={roleForm.justification}
                    onChangeText={(text) => setRoleForm(prev => ({ ...prev, justification: text }))}
                    placeholder="Explain why this role change is needed"
                    placeholderTextColor={colors.textSecondary}
                    multiline
                    numberOfLines={4}
                  />
                </View>
              </>
            )}

            {/* Regular Poll Options */}
            {pollForm.type !== 'role_escalation' && (
              <View style={styles.formGroup}>
                <Text style={[styles.formLabel, { color: colors.text }]}>
                  Poll Options *
                </Text>
              {pollForm.options.map((option, index) => (
                <View key={index} style={styles.optionInputContainer}>
                  <TextInput
                    style={[styles.formInput, styles.optionInput, { backgroundColor: colors.surface, color: colors.text }]}
                    value={option}
                    onChangeText={(text) => updatePollOption(index, text)}
                    placeholder={`Option ${index + 1}`}
                    placeholderTextColor={colors.textSecondary}
                  />
                  {pollForm.options.length > 2 && (
                    <TouchableOpacity
                      onPress={() => removePollOption(index)}
                      style={styles.removeOptionButton}
                    >
                      <Ionicons name="close-circle" size={24} color={colors.error} />
                    </TouchableOpacity>
                  )}
                </View>
              ))}

              {pollForm.options.length < 10 && (
                <TouchableOpacity
                  onPress={addPollOption}
                  style={[styles.addOptionButton, { borderColor: colors.primary }]}
                >
                  <Ionicons name="add" size={20} color={colors.primary} />
                  <Text style={[styles.addOptionText, { color: colors.primary }]}>
                    Add Option
                  </Text>
                </TouchableOpacity>
              )}
              </View>
            )}

            <Button
              title="Create Poll"
              onPress={handleCreatePoll}
              style={[styles.submitButton, { backgroundColor: colors.primary }]}
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Role Escalation Modal */}
      <Modal
        visible={showRoleModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: colors.surface }]}>
            <TouchableOpacity
              onPress={() => setShowRoleModal(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Role Escalation
            </Text>
            <View style={styles.modalCloseButton} />
          </View>

          <ScrollView style={styles.modalContent} contentContainerStyle={styles.modalContentContainer}>
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Select Candidate Member *
              </Text>
              <TextInput
                style={[styles.formInput, { backgroundColor: colors.surface, color: colors.text }]}
                value={memberSearchQuery}
                onChangeText={handleMemberSearch}
                placeholder="Search members by name, email, or role"
                placeholderTextColor={colors.textSecondary}
              />

              {/* Member Search Results */}
              {filteredMembers.length > 0 && memberSearchQuery && (
                <View style={[styles.searchResults, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                  {filteredMembers.slice(0, 5).map((member) => (
                    <TouchableOpacity
                      key={member.user_id || member.userId || member.id}
                      style={[styles.searchResultItem, { borderBottomColor: colors.border }]}
                      onPress={() => handleSelectCandidate(member)}
                    >
                      <View style={styles.memberInfo}>
                        <Text style={[styles.memberName, { color: colors.text }]}>
                          {getMemberName(member)}
                        </Text>
                        <Text style={[styles.memberDetails, { color: colors.textSecondary }]}>
                          {member.role} • {getMemberEmail(member)}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {/* Selected Candidate Display */}
              {roleForm.selectedCandidate && (
                <View style={[styles.selectedCandidate, { backgroundColor: colors.primary + '20', borderColor: colors.primary }]}>
                  <Text style={[styles.selectedCandidateText, { color: colors.text }]}>
                    Selected: {getMemberName(roleForm.selectedCandidate)} ({roleForm.selectedCandidate.role})
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      setRoleForm(prev => ({ ...prev, candidateId: '', selectedCandidate: null }));
                      setMemberSearchQuery('');
                      setFilteredMembers(chamaMembers);
                    }}
                  >
                    <Ionicons name="close-circle" size={20} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Requested Role *
              </Text>
              <View style={styles.radioGroup}>
                {[
                  { value: 'chairperson', label: 'Chairperson' },
                  { value: 'secretary', label: 'Secretary' },
                  { value: 'treasurer', label: 'Treasurer' },
                  { value: 'member', label: 'Member (Demotion)' },
                ].map((role) => (
                  <TouchableOpacity
                    key={role.value}
                    style={styles.radioOption}
                    onPress={() => setRoleForm(prev => ({ ...prev, requestedRole: role.value }))}
                  >
                    <View style={[
                      styles.radioCircle,
                      { borderColor: colors.primary },
                      roleForm.requestedRole === role.value && { backgroundColor: colors.primary }
                    ]}>
                      {roleForm.requestedRole === role.value && (
                        <View style={[styles.radioInner, { backgroundColor: colors.surface }]} />
                      )}
                    </View>
                    <Text style={[styles.radioLabel, { color: colors.text }]}>
                      {role.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Justification
              </Text>
              <TextInput
                style={[styles.formInput, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={roleForm.justification}
                onChangeText={(text) => setRoleForm(prev => ({ ...prev, justification: text }))}
                placeholder="Explain why this role change is needed"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={4}
              />
            </View>

            <Button
              title="Create Role Escalation Poll"
              onPress={handleCreateRoleEscalation}
              style={[styles.submitButton, { backgroundColor: colors.warning }]}
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerSubtitle: {
    fontSize: 11,
    fontWeight: '500',
    marginTop: 2,
  },
  addButton: {
    padding: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  tabText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  roleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginLeft: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 193, 7, 0.3)',
  },
  roleButtonText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: '500',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  pollCard: {
    padding: 16,
    marginBottom: 16,
  },
  pollHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  pollInfo: {
    flex: 1,
    marginRight: 12,
  },
  pollTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  pollCreator: {
    fontSize: 14,
  },
  pollTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  pollTypeText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  pollDescription: {
    fontSize: 14,
    marginBottom: 12,
    fontStyle: 'italic',
  },
  pollStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
  },
  optionsContainer: {
    marginBottom: 12,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  votableOption: {
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  voteIconContainer: {
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
    minHeight: 40,
  },
  anonymousIndicator: {
    marginLeft: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionIndicator: {
    marginLeft: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionText: {
    fontSize: 10,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  optionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  optionText: {
    fontSize: 14,
    flex: 1,
  },
  optionVotes: {
    fontSize: 12,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  votedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  votedText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  pollMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pollDate: {
    fontSize: 12,
  },
  pollBadges: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  anonymousBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  anonymousText: {
    marginLeft: 4,
    fontSize: 10,
    fontWeight: '500',
  },

  canVoteBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  canVoteText: {
    marginLeft: 4,
    fontSize: 10,
    fontWeight: '500',
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  completedText: {
    marginLeft: 4,
    fontSize: 10,
    fontWeight: '500',
  },
  votingStatusMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  votingStatusText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCloseButton: {
    padding: 8,
    width: 40,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
  },
  modalContentContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  radioGroup: {
    gap: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  radioLabel: {
    fontSize: 16,
  },
  optionInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionInput: {
    flex: 1,
    marginRight: 8,
  },
  removeOptionButton: {
    padding: 4,
  },
  addOptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 8,
    marginTop: 8,
  },
  addOptionText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    marginTop: 24,
  },
  disabledOption: {
    opacity: 0.5,
  },
  radioContent: {
    flex: 1,
    marginLeft: 8,
  },
  radioDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  searchResults: {
    marginTop: 4,
    borderWidth: 1,
    borderRadius: 8,
    maxHeight: 200,
  },
  searchResultItem: {
    padding: 12,
    borderBottomWidth: 1,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  memberDetails: {
    fontSize: 14,
  },
  selectedCandidate: {
    marginTop: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedCandidateText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
});

export default PollsVotingScreen;
