import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';

const ChamaLoansScreen = ({ route, navigation, onRouteChange }) => {
  const { chamaId } = route.params;
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [loans, setLoans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [userRole, setUserRole] = useState('member'); // Track user role for security
  const [newLoan, setNewLoan] = useState({
    amount: '',
    purpose: '',
    repaymentPeriod: '12',
    interestRate: '5',
    guarantors: [],
    businessPlan: '',
    monthlyIncome: '',
    otherLoans: '',
    security: {},
  });
  const [availableGuarantors, setAvailableGuarantors] = useState([]);
  const [guarantorSearch, setGuarantorSearch] = useState('');
  const [showGuarantorSearch, setShowGuarantorSearch] = useState(false);

  const tabs = [
    { id: 'all', name: 'All Loans', icon: 'list' },
    { id: 'pending', name: 'Pending', icon: 'time' },
    { id: 'approved', name: 'Approved', icon: 'checkmark-circle' },
    { id: 'active', name: 'Active', icon: 'card' },
    { id: 'completed', name: 'Completed', icon: 'checkmark-done' },
  ];

  useEffect(() => {
    loadUserRole();
    loadLoans();
  }, [chamaId, selectedTab]);

  // Load user role for security purposes
  const loadUserRole = async () => {
    try {
      console.log('🔐 Loading user role for security check');
      const response = await ApiService.getMemberRole(chamaId, user.id);
      if (response.success) {
        const role = response.data?.role || 'member';
        setUserRole(role);
        console.log('🔐 User role loaded:', role);
      } else {
        setUserRole('member'); // Default to member for security
        console.log('🔐 Failed to load role, defaulting to member');
      }
    } catch (error) {
      console.error('🔐 Error loading user role:', error);
      setUserRole('member'); // Default to member for security
    }
  };

  // Check if user can view all loans (leadership roles)
  const canViewAllLoans = () => {
    const leadershipRoles = ['chairperson', 'secretary', 'treasurer'];
    return leadershipRoles.includes(userRole.toLowerCase());
  };

  // Check if user can approve/reject loans
  const canManageLoans = () => {
    const managementRoles = ['chairperson', 'secretary', 'treasurer'];
    return managementRoles.includes(userRole.toLowerCase());
  };

  const loadLoans = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading loans for chamaId:', chamaId);
      console.log('🔐 User role for filtering:', userRole);

      const response = await ApiService.getLoans(chamaId, 50, 0);
      console.log('🔍 Loans API response:', response);

      if (response.success) {
        let filteredLoans = response.data || [];
        console.log('🔍 Raw loans data:', filteredLoans.length, 'loans');

        // 🔐 SECURITY: Filter loans based on user role
        if (!canViewAllLoans()) {
          // Regular members can only see their own loans
          filteredLoans = filteredLoans.filter(loan => {
            const loanUserId = loan.user_id || loan.applicant_id || loan.applicant?.id;
            const canView = loanUserId === user.id;
            console.log('🔐 Loan security check:', {
              loanId: loan.id,
              loanUserId,
              currentUserId: user.id,
              canView,
              userRole
            });
            return canView;
          });
          console.log('🔐 Security filtered loans (own only):', filteredLoans.length, 'loans');
        } else {
          console.log('🔐 Leadership role - can view all loans:', filteredLoans.length, 'loans');
        }

        // Filter by status tab
        if (selectedTab !== 'all') {
          filteredLoans = filteredLoans.filter(loan => loan.status === selectedTab);
          console.log(`🔍 Status filtered loans for ${selectedTab}:`, filteredLoans.length, 'loans');
        }

        setLoans(filteredLoans);
        console.log('🔍 Final loans set:', filteredLoans.length, 'loans');
      } else {
        console.log('❌ Loans API failed:', response.error || response.message);
        setLoans([]); // Set empty array for security
      }
    } catch (error) {
      console.error('❌ Failed to load loans:', error);
      setLoans([]); // Set empty array for security
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLoans();
    setRefreshing(false);
  };

  const handleApplyForLoan = () => {
    // 🔐 Security check: Ensure user is authenticated and has valid session
    if (!user || !user.id) {
      Alert.alert(
        'Authentication Required',
        'Please log in to apply for a loan.',
        [{ text: 'OK' }]
      );
      return;
    }

    console.log('🔐 Loan application initiated by user:', user.id);
    // Option 1: Use modal (current behavior)
    setShowCreateModal(true);
    loadAvailableGuarantors();
  };

  const handleNavigateToLoanForm = () => {
    // Option 2: Navigate to dedicated loan application form
    if (onRouteChange) {
      onRouteChange('loan-application', 'LoanApplication');
    } else {
      navigation.navigate('LoanApplication', { chamaId });
    }
  };

  const loadAvailableGuarantors = async () => {
    try {
      const response = await ApiService.searchUsers(guarantorSearch || '');
      if (response.success) {
        // Filter out current user and already selected guarantors
        const filteredUsers = (response.data || []).filter(searchUser =>
          searchUser.id !== user?.id &&
          !newLoan.guarantors.some(g => g.id === searchUser.id)
        );
        setAvailableGuarantors(filteredUsers);
      }
    } catch (error) {
      console.error('Failed to load guarantors:', error);
    }
  };

  const addGuarantor = (guarantor) => {
    if (newLoan.guarantors.length < 5) { // Limit to 5 guarantors
      setNewLoan(prev => ({
        ...prev,
        guarantors: [...prev.guarantors, guarantor]
      }));
      loadAvailableGuarantors(); // Refresh list to remove selected guarantor
    }
  };

  const removeGuarantor = (guarantorId) => {
    setNewLoan(prev => ({
      ...prev,
      guarantors: prev.guarantors.filter(g => g.id !== guarantorId)
    }));
    loadAvailableGuarantors(); // Refresh list to add back removed guarantor
  };

  const handleCreateLoan = async () => {
    if (!newLoan.amount || !newLoan.purpose || !newLoan.monthlyIncome) {
      Alert.alert('Missing Information', 'Please fill in all required fields');
      return;
    }

    if (newLoan.guarantors.length < 2) {
      Alert.alert('Guarantors Required', 'Please select at least 2 guarantors for your loan application');
      return;
    }

    try {
      const loanData = {
        ...newLoan,
        chamaId,
        amount: parseFloat(newLoan.amount),
        repaymentPeriod: parseInt(newLoan.repaymentPeriod),
        interestRate: parseFloat(newLoan.interestRate),
        monthlyIncome: parseFloat(newLoan.monthlyIncome),
        guarantors: newLoan.guarantors.map(g => g.id), // Send guarantor IDs
      };

      const response = await ApiService.createLoanApplication(loanData);
      if (response.success) {
        Alert.alert('Success', 'Loan application submitted successfully! Guarantors will be notified.');
        setShowCreateModal(false);
        setNewLoan({
          amount: '',
          purpose: '',
          repaymentPeriod: '12',
          interestRate: '5',
          guarantors: [],
          businessPlan: '',
          monthlyIncome: '',
          otherLoans: '',
          security: {},
        });
        await loadLoans();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to submit loan application');
    }
  };

  const handleLoanAction = async (loan, action) => {
    try {
      let response;
      
      switch (action) {
        case 'approve':
          response = await ApiService.approveLoan(loan.id, { approved: true });
          break;
        case 'reject':
          response = await ApiService.approveLoan(loan.id, { approved: false });
          break;
        case 'guarantee':
          response = await ApiService.respondToGuaranteeRequest(loan.id, { accepted: true });
          break;
        default:
          return;
      }

      if (response.success) {
        Alert.alert('Success', `Loan ${action} successful`);
        loadLoans();
      }
    } catch (error) {
      console.error(`Failed to ${action} loan:`, error);
      Alert.alert('Error', `Failed to ${action} loan`);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.warning;
      case 'approved':
        return colors.info;
      case 'active':
        return colors.primary;
      case 'completed':
        return colors.success;
      case 'rejected':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabContainer}>
      <FlatList
        horizontal
        data={tabs}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.tab,
              {
                backgroundColor: selectedTab === item.id ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedTab(item.id)}
          >
            <Ionicons 
              name={item.icon} 
              size={16} 
              color={selectedTab === item.id ? colors.white : colors.textSecondary} 
            />
            <Text style={[
              styles.tabText,
              { color: selectedTab === item.id ? colors.white : colors.textSecondary }
            ]}>
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabsContent}
      />
    </View>
  );

  const renderLoan = ({ item }) => {
    // 🔐 Security check: Determine if this is user's own loan
    const loanUserId = item.user_id || item.applicant_id || item.applicant?.id;
    const isOwnLoan = loanUserId === user.id;
    const showApplicantName = canViewAllLoans() || isOwnLoan;

    return (
      <Card style={styles.loanCard}>
        <View style={styles.loanHeader}>
          <View style={styles.loanInfo}>
            <View style={styles.loanTitleRow}>
              <Text style={[styles.loanTitle, { color: colors.text }]}>
                {isOwnLoan ? 'My Loan Application' : 'Loan Application'}
              </Text>
              {isOwnLoan && (
                <View style={[styles.ownLoanBadge, { backgroundColor: colors.primary + '20' }]}>
                  <Ionicons name="person" size={12} color={colors.primary} />
                  <Text style={[styles.ownLoanText, { color: colors.primary }]}>Mine</Text>
                </View>
              )}
            </View>
            {showApplicantName && (
              <Text style={[styles.loanApplicant, { color: colors.textSecondary }]}>
                By: {item.applicant?.first_name || item.user?.first_name || 'Unknown'} {item.applicant?.last_name || item.user?.last_name || 'User'}
              </Text>
            )}
            <Text style={[styles.loanDate, { color: colors.textSecondary }]}>
              Applied: {formatDate(item.created_at)}
            </Text>
          </View>
        
        <View style={styles.loanAmount}>
          <Text style={[styles.amountText, { color: colors.text }]}>
            {formatCurrency(item.amount)}
          </Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) + '20' }
          ]}>
            <Text style={[
              styles.statusText,
              { color: getStatusColor(item.status) }
            ]}>
              {item.status.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.loanDetails}>
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
            Purpose:
          </Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {item.purpose || 'Not specified'}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
            Interest Rate:
          </Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {item.interest_rate || item.interestRate || '5'}% per month
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
            Duration:
          </Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {item.duration_months || item.repayment_period || item.duration || 'Not specified'} months
          </Text>
        </View>

        {item.guarantors && item.guarantors.length > 0 && (
          <View style={styles.guarantorsSection}>
            <Text style={[styles.guarantorsTitle, { color: colors.text }]}>
              Guarantors ({item.guarantors.length})
            </Text>
            {item.guarantors.map((guarantor, index) => (
              <Text key={index} style={[styles.guarantorName, { color: colors.textSecondary }]}>
                • {guarantor.first_name} {guarantor.last_name} 
                {guarantor.status && ` (${guarantor.status})`}
              </Text>
            ))}
          </View>
        )}
      </View>

      {/* 🔐 SECURITY: Action buttons based on user role and loan status */}
      {item.status === 'pending' && canManageLoans() && (
        <View style={styles.actionButtons}>
          <Button
            title="Approve"
            size="small"
            onPress={() => handleLoanAction(item, 'approve')}
            style={styles.actionButton}
          />
          <Button
            title="Reject"
            variant="outline"
            size="small"
            onPress={() => handleLoanAction(item, 'reject')}
            style={styles.actionButton}
          />
        </View>
      )}

      {item.status === 'approved' && item.requires_guarantors && (
        <View style={styles.actionButtons}>
          <Button
            title="Guarantee"
            size="small"
            onPress={() => handleLoanAction(item, 'guarantee')}
            style={styles.actionButton}
          />
        </View>
      )}

      {/* Security indicator for regular members */}
      {!canViewAllLoans() && (
        <View style={styles.securityNotice}>
          <Ionicons name="shield-checkmark" size={14} color={colors.success} />
          <Text style={[styles.securityText, { color: colors.textSecondary }]}>
            You can only view your own loan records
          </Text>
        </View>
      )}
    </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="card-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Loans Found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {selectedTab === 'all'
          ? (canViewAllLoans()
              ? 'No loan applications have been made yet'
              : 'You have no loan applications yet.')
          : `No ${selectedTab} loans found`
        }
      </Text>

      {/* 🔐 Security Privacy Notice */}
      <View style={[styles.privacyNotice, { backgroundColor: colors.info + '10' }]}>
        <Ionicons name="shield-checkmark" size={16} color={colors.info} />
        <Text style={[styles.privacyText, { color: colors.textSecondary }]}>
          {canViewAllLoans()
            ? 'As a chama leader, you can view all member loan records.'
            : 'Your loan information is private and secure. Only you and chama leaders can view your records.'
          }
        </Text>
      </View>

      <Button
        title="Apply for Loan"
        onPress={handleApplyForLoan}
        style={styles.applyButton}
        icon={<Ionicons name="add" size={20} color={colors.white} />}
      />
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Chama Loans
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {canViewAllLoans() ? 'Manage loan applications and approvals' : 'View your loan applications'}
          </Text>
        </View>

        {/* 🔐 Security Access Level Indicator */}
        <View style={[styles.accessLevelBadge, {
          backgroundColor: canViewAllLoans() ? colors.warning + '20' : colors.info + '20'
        }]}>
          <Ionicons
            name={canViewAllLoans() ? "shield" : "person"}
            size={14}
            color={canViewAllLoans() ? colors.warning : colors.info}
          />
          <Text style={[styles.accessLevelText, {
            color: canViewAllLoans() ? colors.warning : colors.info
          }]}>
            {canViewAllLoans() ? `${userRole.toUpperCase()} ACCESS` : 'PERSONAL VIEW'}
          </Text>
        </View>
      </View>

      {renderTabBar()}
      
      <FlatList
        data={loans}
        renderItem={renderLoan}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.loansList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={!loading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
      />

      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={handleApplyForLoan}
      >
        <Ionicons name="add" size={24} color={colors.white} />
      </TouchableOpacity>

      {/* Create Loan Application Modal */}
      <Modal
        visible={showCreateModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Apply for Loan
              </Text>
              <TouchableOpacity onPress={() => setShowCreateModal(false)}>
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              <Input
                label="Loan Amount (KES) *"
                value={newLoan.amount}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, amount: text }))}
                placeholder="Enter loan amount"
                keyboardType="numeric"
              />

              <Input
                label="Purpose *"
                value={newLoan.purpose}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, purpose: text }))}
                placeholder="What will you use this loan for?"
                multiline
                numberOfLines={3}
              />

              <Input
                label="Monthly Income (KES) *"
                value={newLoan.monthlyIncome}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, monthlyIncome: text }))}
                placeholder="Your monthly income"
                keyboardType="numeric"
              />

              <Input
                label="Repayment Period (Months)"
                value={newLoan.repaymentPeriod}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, repaymentPeriod: text }))}
                placeholder="12"
                keyboardType="numeric"
              />

              <Input
                label="Interest Rate (%)"
                value={newLoan.interestRate}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, interestRate: text }))}
                placeholder="5"
                keyboardType="numeric"
              />

              <Input
                label="Business Plan (Optional)"
                value={newLoan.businessPlan}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, businessPlan: text }))}
                placeholder="Describe your business plan..."
                multiline
                numberOfLines={4}
              />

              <Input
                label="Other Loans (Optional)"
                value={newLoan.otherLoans}
                onChangeText={(text) => setNewLoan(prev => ({ ...prev, otherLoans: text }))}
                placeholder="Do you have any other loans?"
                multiline
                numberOfLines={2}
              />

              {/* Guarantors Section */}
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Guarantors ({newLoan.guarantors.length}/5) *
              </Text>
              <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
                Select at least 2 guarantors for your loan
              </Text>

              {/* Selected Guarantors */}
              {newLoan.guarantors.map((guarantor, index) => (
                <View key={guarantor.id} style={[styles.guarantorItem, { backgroundColor: colors.backgroundSecondary }]}>
                  <View style={styles.guarantorInfo}>
                    <Text style={[styles.guarantorName, { color: colors.text }]}>
                      {guarantor.firstName} {guarantor.lastName}
                    </Text>
                    <Text style={[styles.guarantorEmail, { color: colors.textSecondary }]}>
                      {guarantor.email}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => removeGuarantor(guarantor.id)}
                    style={[styles.removeGuarantorBtn, { backgroundColor: colors.error }]}
                  >
                    <Ionicons name="close" size={16} color={colors.white} />
                  </TouchableOpacity>
                </View>
              ))}

              {/* Add Guarantor Button */}
              {newLoan.guarantors.length < 5 && (
                <TouchableOpacity
                  style={[styles.addGuarantorBtn, { borderColor: colors.primary }]}
                  onPress={() => setShowGuarantorSearch(true)}
                >
                  <Ionicons name="add" size={20} color={colors.primary} />
                  <Text style={[styles.addGuarantorText, { color: colors.primary }]}>
                    Add Guarantor
                  </Text>
                </TouchableOpacity>
              )}

              {/* Guarantor Search Modal */}
              <Modal
                visible={showGuarantorSearch}
                transparent
                animationType="slide"
                onRequestClose={() => setShowGuarantorSearch(false)}
              >
                <View style={styles.searchModalOverlay}>
                  <View style={[styles.searchModalContent, { backgroundColor: colors.surface }]}>
                    <View style={styles.searchModalHeader}>
                      <Text style={[styles.searchModalTitle, { color: colors.text }]}>
                        Select Guarantor
                      </Text>
                      <TouchableOpacity onPress={() => setShowGuarantorSearch(false)}>
                        <Ionicons name="close" size={24} color={colors.textSecondary} />
                      </TouchableOpacity>
                    </View>

                    <Input
                      label="Search by name, email, or phone"
                      value={guarantorSearch}
                      onChangeText={(text) => {
                        setGuarantorSearch(text);
                        loadAvailableGuarantors();
                      }}
                      placeholder="Type to search..."
                      icon={<Ionicons name="search" size={20} color={colors.textSecondary} />}
                    />

                    <FlatList
                      data={availableGuarantors}
                      keyExtractor={(item) => item.id}
                      renderItem={({ item }) => (
                        <TouchableOpacity
                          style={[styles.guarantorSearchItem, { borderBottomColor: colors.border }]}
                          onPress={() => {
                            addGuarantor(item);
                            setShowGuarantorSearch(false);
                          }}
                        >
                          <View style={styles.guarantorSearchInfo}>
                            <Text style={[styles.guarantorSearchName, { color: colors.text }]}>
                              {item.firstName} {item.lastName}
                            </Text>
                            <Text style={[styles.guarantorSearchEmail, { color: colors.textSecondary }]}>
                              {item.email}
                            </Text>
                          </View>
                          <Ionicons name="add-circle" size={24} color={colors.primary} />
                        </TouchableOpacity>
                      )}
                      ListEmptyComponent={
                        <View style={styles.emptyGuarantors}>
                          <Text style={[styles.emptyGuarantorsText, { color: colors.textSecondary }]}>
                            No users found. Try a different search term.
                          </Text>
                        </View>
                      }
                      style={styles.guarantorsList}
                    />
                  </View>
                </View>
              </Modal>

              <Button
                title="Submit Application"
                onPress={handleCreateLoan}
                style={styles.submitButton}
                icon={<Ionicons name="send" size={20} color={colors.white} />}
              />
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    ...shadows.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.base,
  },
  tabContainer: {
    paddingVertical: spacing.md,
    backgroundColor: 'transparent',
  },
  tabsContent: {
    paddingHorizontal: spacing.md,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    borderWidth: 1,
  },
  tabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  loansList: {
    padding: spacing.md,
  },
  loanCard: {
    marginBottom: spacing.md,
  },
  loanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  loanInfo: {
    flex: 1,
  },
  loanTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  loanApplicant: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  loanDate: {
    fontSize: typography.fontSize.xs,
  },
  loanAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  loanDetails: {
    marginBottom: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  detailLabel: {
    fontSize: typography.fontSize.sm,
    flex: 1,
  },
  detailValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  guarantorsSection: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  guarantorsTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
  },
  guarantorName: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  applyButton: {
    marginTop: spacing.md,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: spacing.lg,
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
  },
  modalBody: {
    flex: 1,
  },
  submitButton: {
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.md,
  },
  guarantorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
  },
  guarantorInfo: {
    flex: 1,
  },
  guarantorName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  guarantorEmail: {
    fontSize: typography.fontSize.sm,
  },
  removeGuarantorBtn: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addGuarantorBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.md,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  addGuarantorText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm,
  },
  searchModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    paddingHorizontal: spacing.md,
  },
  searchModalContent: {
    borderRadius: borderRadius.xl,
    padding: spacing.lg,
    maxHeight: '80%',
  },
  searchModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  searchModalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  guarantorsList: {
    maxHeight: 300,
    marginTop: spacing.md,
  },
  guarantorSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  guarantorSearchInfo: {
    flex: 1,
  },
  guarantorSearchName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  guarantorSearchEmail: {
    fontSize: typography.fontSize.sm,
  },
  emptyGuarantors: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyGuarantorsText: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
  },

  // 🔐 Security-related styles
  headerContent: {
    flex: 1,
  },
  accessLevelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  accessLevelText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
  },
  loanTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  ownLoanBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.sm,
    gap: 2,
  },
  ownLoanText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  securityNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    gap: spacing.xs,
  },
  securityText: {
    fontSize: typography.fontSize.xs,
    fontStyle: 'italic',
  },
  privacyNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    marginVertical: spacing.md,
    gap: spacing.sm,
  },
  privacyText: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    lineHeight: 20,
  },
});

export default ChamaLoansScreen;
