import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';

export default function BackupMaintenanceScreen() {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  const [refreshing, setRefreshing] = useState(false);
  const [backupSettings, setBackupSettings] = useState({
    autoBackup: true,
    dailyBackup: true,
    weeklyBackup: true,
    cloudBackup: true,
    encryptBackups: true,
    retentionDays: 30,
  });

  const [backupHistory, setBackupHistory] = useState([
    {
      id: '1',
      type: 'full',
      status: 'completed',
      size: '2.4 GB',
      duration: '45 minutes',
      timestamp: '2024-01-20 02:00:00',
      location: 'AWS S3',
    },
    {
      id: '2',
      type: 'incremental',
      status: 'completed',
      size: '156 MB',
      duration: '8 minutes',
      timestamp: '2024-01-19 02:00:00',
      location: 'AWS S3',
    },
    {
      id: '3',
      type: 'full',
      status: 'failed',
      size: '0 MB',
      duration: '12 minutes',
      timestamp: '2024-01-18 02:00:00',
      location: 'AWS S3',
      error: 'Network timeout',
    },
  ]);

  const [systemStatus, setSystemStatus] = useState({
    diskUsage: 67,
    memoryUsage: 45,
    cpuUsage: 23,
    activeConnections: 156,
    uptime: '15 days, 8 hours',
    lastMaintenance: '2024-01-15 03:00:00',
  });

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate data refresh
    setTimeout(() => {
      setSystemStatus(prev => ({
        ...prev,
        diskUsage: Math.floor(Math.random() * 30) + 50,
        memoryUsage: Math.floor(Math.random() * 40) + 30,
        cpuUsage: Math.floor(Math.random() * 50) + 10,
        activeConnections: Math.floor(Math.random() * 100) + 100,
      }));
      setRefreshing(false);
    }, 1500);
  };

  const toggleSetting = (setting) => {
    setBackupSettings(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  const handleBackupAction = (action) => {
    Alert.alert(
      'Confirm Action',
      `Are you sure you want to ${action}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            Alert.alert('Success', `${action} initiated successfully`);
          },
        },
      ]
    );
  };

  const handleMaintenanceAction = (action) => {
    Alert.alert(
      'System Maintenance',
      `This will ${action}. Users may experience temporary service interruption. Continue?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Success', `${action} completed successfully`);
          },
        },
      ]
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return colors.success;
      case 'failed': return colors.error;
      case 'running': return colors.warning;
      default: return colors.textSecondary;
    }
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 80) return colors.error;
    if (percentage >= 60) return colors.warning;
    return colors.success;
  };

  const renderBackupItem = (backup) => (
    <View
      key={backup.id}
      style={[styles.backupCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
    >
      <View style={styles.backupHeader}>
        <View style={styles.backupInfo}>
          <View style={styles.backupType}>
            <Ionicons
              name={backup.type === 'full' ? 'server' : 'layers'}
              size={12}
              color={colors.primary}
            />
            <Text style={[styles.backupTypeText, { color: colors.text }]}>
              {backup.type.charAt(0).toUpperCase() + backup.type.slice(1)} Backup
            </Text>
          </View>
          <Text style={[styles.backupTimestamp, { color: colors.textSecondary }]}>
            {backup.timestamp}
          </Text>
        </View>
        <View style={[
          styles.backupStatus,
          { backgroundColor: getStatusColor(backup.status) + '20' }
        ]}>
          <Text style={[
            styles.backupStatusText,
            { color: getStatusColor(backup.status) }
          ]}>
            {backup.status.toUpperCase()}
          </Text>
        </View>
      </View>

      <View style={styles.backupDetails}>
        <View style={styles.backupStat}>
          <Text style={[styles.backupStatLabel, { color: colors.textSecondary }]}>
            Size:
          </Text>
          <Text style={[styles.backupStatValue, { color: colors.text }]}>
            {backup.size}
          </Text>
        </View>
        <View style={styles.backupStat}>
          <Text style={[styles.backupStatLabel, { color: colors.textSecondary }]}>
            Duration:
          </Text>
          <Text style={[styles.backupStatValue, { color: colors.text }]}>
            {backup.duration}
          </Text>
        </View>
        <View style={styles.backupStat}>
          <Text style={[styles.backupStatLabel, { color: colors.textSecondary }]}>
            Location:
          </Text>
          <Text style={[styles.backupStatValue, { color: colors.text }]}>
            {backup.location}
          </Text>
        </View>
      </View>

      {backup.error && (
        <View style={styles.backupError}>
          <Ionicons name="warning" size={12} color={colors.error} />
          <Text style={[styles.backupErrorText, { color: colors.error }]}>
            {backup.error}
          </Text>
        </View>
      )}
    </View>
  );

  const renderSystemMetric = (label, value, unit, icon, color) => (
    <View style={[styles.metricCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <View style={styles.metricHeader}>
        <Ionicons name={icon} size={14} color={color} />
        <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
          {label}
        </Text>
      </View>
      <Text style={[styles.metricValue, { color: colors.text }]}>
        {value}{unit}
      </Text>
      {typeof value === 'number' && (
        <View style={styles.metricBar}>
          <View
            style={[
              styles.metricProgress,
              {
                width: `${value}%`,
                backgroundColor: getUsageColor(value),
              }
            ]}
          />
        </View>
      )}
    </View>
  );

  return (
    <>
      <View style={[styles.container, { backgroundColor: colors.background }]}>


        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.primary}
            />
          }
        >
          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Quick Actions
            </Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
                onPress={() => handleBackupAction('start full backup')}
              >
                <Ionicons name="server" size={16} color={colors.primary} />
                <Text style={[styles.actionText, { color: colors.primary }]}>
                  Full Backup
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.info + '20' }]}
                onPress={() => handleBackupAction('start incremental backup')}
              >
                <Ionicons name="layers" size={16} color={colors.info} />
                <Text style={[styles.actionText, { color: colors.info }]}>
                  Incremental
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.warning + '20' }]}
                onPress={() => handleMaintenanceAction('restart system')}
              >
                <Ionicons name="refresh" size={16} color={colors.warning} />
                <Text style={[styles.actionText, { color: colors.warning }]}>
                  Restart
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.success + '20' }]}
                onPress={() => handleMaintenanceAction('optimize database')}
              >
                <Ionicons name="build" size={16} color={colors.success} />
                <Text style={[styles.actionText, { color: colors.success }]}>
                  Optimize
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* System Status */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              System Status
            </Text>
            <View style={styles.metricsGrid}>
              {renderSystemMetric('Disk Usage', systemStatus.diskUsage, '%', 'server', getUsageColor(systemStatus.diskUsage))}
              {renderSystemMetric('Memory Usage', systemStatus.memoryUsage, '%', 'hardware-chip', getUsageColor(systemStatus.memoryUsage))}
              {renderSystemMetric('CPU Usage', systemStatus.cpuUsage, '%', 'speedometer', getUsageColor(systemStatus.cpuUsage))}
              {renderSystemMetric('Connections', systemStatus.activeConnections, '', 'people', colors.info)}
            </View>
            <View style={[styles.uptimeCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Text style={[styles.uptimeLabel, { color: colors.textSecondary }]}>
                System Uptime
              </Text>
              <Text style={[styles.uptimeValue, { color: colors.success }]}>
                {systemStatus.uptime}
              </Text>
              <Text style={[styles.lastMaintenance, { color: colors.textSecondary }]}>
                Last maintenance: {systemStatus.lastMaintenance}
              </Text>
            </View>
          </View>

          {/* Backup Settings */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Backup Settings
            </Text>
            <View style={[styles.settingsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    Automatic Backup
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Enable scheduled automatic backups
                  </Text>
                </View>
                <Switch
                  value={backupSettings.autoBackup}
                  onValueChange={() => toggleSetting('autoBackup')}
                  trackColor={{ false: colors.border, true: colors.primary + '50' }}
                  thumbColor={backupSettings.autoBackup ? colors.primary : colors.textSecondary}
                />
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    Cloud Backup
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Store backups in cloud storage
                  </Text>
                </View>
                <Switch
                  value={backupSettings.cloudBackup}
                  onValueChange={() => toggleSetting('cloudBackup')}
                  trackColor={{ false: colors.border, true: colors.primary + '50' }}
                  thumbColor={backupSettings.cloudBackup ? colors.primary : colors.textSecondary}
                />
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    Encrypt Backups
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Encrypt backup files for security
                  </Text>
                </View>
                <Switch
                  value={backupSettings.encryptBackups}
                  onValueChange={() => toggleSetting('encryptBackups')}
                  trackColor={{ false: colors.border, true: colors.primary + '50' }}
                  thumbColor={backupSettings.encryptBackups ? colors.primary : colors.textSecondary}
                />
              </View>
            </View>
          </View>

          {/* Backup History */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Backup History
            </Text>
            {backupHistory.map(renderBackupItem)}
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricLabel: {
    fontSize: 12,
    marginLeft: 8,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  metricBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  metricProgress: {
    height: '100%',
    borderRadius: 2,
  },
  uptimeCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    alignItems: 'center',
  },
  uptimeLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  uptimeValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  lastMaintenance: {
    fontSize: 12,
  },
  settingsCard: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  backupCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  backupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  backupInfo: {
    flex: 1,
  },
  backupType: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  backupTypeText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  backupTimestamp: {
    fontSize: 12,
  },
  backupStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  backupStatusText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  backupDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  backupStat: {
    alignItems: 'center',
  },
  backupStatLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  backupStatValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  backupError: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: 'rgba(218, 54, 51, 0.1)',
    borderRadius: 8,
  },
  backupErrorText: {
    fontSize: 12,
    marginLeft: 8,
  },
});
