import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import Card from '../../components/common/Card';
import ApiService from '../../services/api';

const { width } = Dimensions.get('window');



export default function AdminHomepage() {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeChamas: 0,
    totalTransactions: 0,
    systemHealth: 0,
  });

  useEffect(() => {
    // Load admin statistics on component mount
    loadAdminStatistics();
  }, []);

  const loadAdminStatistics = async () => {
    try {
      console.log('🔍 Loading admin statistics...');
      const response = await ApiService.getAdminStatistics();
      console.log('📊 Admin statistics response:', response);

      if (response.success) {
        const { user_stats, chama_stats, transaction_stats, system_health } = response.data;

        setStats({
          totalUsers: user_stats?.total_users || 0,
          activeChamas: chama_stats?.active_chamas || 0,
          totalTransactions: transaction_stats?.total_transactions || 0,
          systemHealth: system_health || 0,
        });

        console.log('✅ Admin statistics loaded:', {
          totalUsers: user_stats?.total_users || 0,
          activeChamas: chama_stats?.active_chamas || 0,
          totalTransactions: transaction_stats?.total_transactions || 0,
          systemHealth: system_health || 0,
        });
      }
    } catch (error) {
      console.error('❌ Error loading admin statistics:', error);
    }
  };

  const quickActions = [
    {
      id: 1,
      title: 'User Management',
      icon: 'people',
      color: colors.primary,
      action: () => navigation.navigate('UserManagementScreen'),
    },
    {
      id: 2,
      title: 'Chama Management',
      icon: 'business',
      color: colors.secondary,
      action: () => navigation.navigate('ChamaManagementScreen'),
    },
    {
      id: 3,
      title: 'System Analytics',
      icon: 'analytics',
      color: colors.accent,
      action: () => navigation.navigate('SystemAnalyticsScreen'),
    },
    {
      id: 4,
      title: 'Security Center',
      icon: 'shield-checkmark',
      color: colors.success,
      action: () => navigation.navigate('SecurityCenterScreen'),
    },
    {
      id: 5,
      title: 'Payment System',
      icon: 'card',
      color: colors.warning,
      action: () => navigation.navigate('PaymentSystemScreen'),
    },
    {
      id: 6,
      title: 'Learning Hub',
      icon: 'school',
      color: colors.info,
      action: () => navigation.navigate('LearningManagementScreen'),
    },
    {
      id: 7,
      title: 'Support Center',
      icon: 'help-circle',
      color: colors.primary,
      action: () => navigation.navigate('AdminSupport'),
    },
    {
      id: 8,
      title: 'Backup & Maintenance',
      icon: 'server',
      color: colors.error,
      action: () => navigation.navigate('BackupMaintenanceScreen'),
    },
    {
      id: 9,
      title: 'Admin Settings',
      icon: 'settings',
      color: colors.secondary,
      action: () => navigation.navigate('AdminSettingsScreen'),
    },
    {
      id: 10,
      title: 'Financial Reports',
      icon: 'document-text',
      color: colors.accent,
      action: () => navigation.navigate('FinancialReportsScreen'),
    },
    {
      id: 11,
      title: 'Notifications',
      icon: 'notifications',
      color: colors.warning,
      action: () => navigation.navigate('NotificationManagementScreen'),
    },
    {
      id: 12,
      title: 'Audit Logs',
      icon: 'list',
      color: colors.info,
      action: () => navigation.navigate('AuditLogsScreen'),
    },
    {
      id: 13,
      title: 'System Health',
      icon: 'pulse',
      color: colors.success,
      action: () => navigation.navigate('SystemHealthScreen'),
    },
    {
      id: 14,
      title: 'API Management',
      icon: 'code-slash',
      color: colors.primary,
      action: () => navigation.navigate('APIManagementScreen'),
    },
    {
      id: 15,
      title: 'Content Moderation',
      icon: 'eye',
      color: colors.error,
      action: () => navigation.navigate('ContentModerationScreen'),
    },
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Reload admin statistics
      await loadAdminStatistics();
    } catch (error) {
      console.error('Error refreshing admin statistics:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleQuickAction = (action) => {
    if (action.action) {
      action.action();
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={colors.primary}
        />
      }
    >
      {/* Header */}
      
      {/* Overview Stats */}
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Overview
        </Text>
        <View style={[styles.overviewCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statsContainer}>
            <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Ionicons name="people" size={28} color={colors.primary} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {stats.totalUsers.toLocaleString()}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Total Users
              </Text>
            </View>
            <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Ionicons name="business" size={28} color={colors.secondary} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {stats.activeChamas.toString()}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Active Chamas
              </Text>
            </View>
          </View>

          <View style={[styles.statsContainer, { marginBottom: 0 }]}>
            <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Ionicons name="receipt" size={28} color={colors.info} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {stats.totalTransactions.toLocaleString()}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Transactions
              </Text>
            </View>
            <View style={[styles.statCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              <Ionicons name="heart" size={28} color={colors.success} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {`${stats.systemHealth}%`}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                System Health
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.sectionContainer}>
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Quick Actions
          </Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.quickActionItem}
                onPress={() => handleQuickAction(action)}
              >
                <View style={[styles.quickActionIcon, { backgroundColor: action.color }]}>
                  <Ionicons name={action.icon} size={20} color={colors.white} />
                </View>
                <Text style={[styles.quickActionText, { color: colors.text }]}>
                  {action.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingTop: spacing.md,
  },
  greetingText: {
    fontSize: typography.fontSize.base,
    marginBottom: spacing.xs,
  },
  nameText: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
  },
  notificationButton: {
    position: 'relative',
    padding: spacing.sm,
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.sm,
  },
  statCard: {
    width: '47%',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: spacing.xs,
  },
  statLabel: {
    fontSize: 12,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  sectionContainer: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  overviewCard: {
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
  },
  section: {
    marginBottom: spacing.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xs,
  },
  quickActionItem: {
    width: (width - spacing.md * 4 - spacing.xs * 4) / 5, // 5 items per row for 15 total items
    alignItems: 'center',
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.xs,
  },
  quickActionIcon: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  quickActionText: {
    fontSize: typography.fontSize.xs,
    textAlign: 'center',
    fontWeight: typography.fontWeight.medium,
    lineHeight: 14,
  },
});
