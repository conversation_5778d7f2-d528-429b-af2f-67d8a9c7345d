import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import { formatDate, formatOrderDate, formatCurrency } from '../../utils/dateUtils';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const OrderTrackingScreen = ({ route, navigation }) => {
  const { orderId } = route.params;
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const orderStatuses = [
    { id: 'pending', name: 'Order Placed', icon: 'checkmark-circle', description: 'Your order has been placed' },
    { id: 'confirmed', name: 'Confirmed', icon: 'thumbs-up', description: 'Seller confirmed your order' },
    { id: 'preparing', name: 'Preparing', icon: 'construct', description: 'Your order is being prepared' },
    { id: 'shipped', name: 'Shipped', icon: 'car', description: 'Your order is on the way' },
    { id: 'delivered', name: 'Delivered', icon: 'home', description: 'Order delivered successfully' },
  ];

  const deliveryStatuses = [
    { id: 'pending', name: 'Pending Assignment', icon: 'time' },
    { id: 'assigned', name: 'Delivery Assigned', icon: 'person' },
    { id: 'picked_up', name: 'Picked Up', icon: 'checkmark' },
    { id: 'in_transit', name: 'In Transit', icon: 'car' },
    { id: 'delivered', name: 'Delivered', icon: 'home' },
  ];

  useEffect(() => {
    loadOrderDetails();
  }, []);

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getOrderById(orderId);
      if (response.success) {
        setOrder(response.data);
      }
    } catch (error) {
      console.error('Failed to load order details:', error);
      Alert.alert('Error', 'Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrderDetails();
    setRefreshing(false);
  };

  // Date and currency formatting functions are now imported from utils

  const getStatusIndex = (status) => {
    return orderStatuses.findIndex(s => s.id === status);
  };

  const getDeliveryStatusIndex = (status) => {
    return deliveryStatuses.findIndex(s => s.id === status);
  };

  const handleCallDeliveryPerson = () => {
    if (order?.delivery_person?.phone) {
      Linking.openURL(`tel:${order.delivery_person.phone}`);
    }
  };

  const handleChatWithSeller = () => {
    navigation.navigate('ChatRoom', {
      userId: order.seller_id,
      userName: `${order.seller?.first_name} ${order.seller?.last_name}`,
      type: 'private'
    });
  };

  const handleCancelOrder = () => {
    Alert.alert(
      'Cancel Order',
      'Are you sure you want to cancel this order?',
      [
        { text: 'No', style: 'cancel' },
        { text: 'Yes', onPress: () => {
          // Handle order cancellation
          Alert.alert('Order Cancelled', 'Your order has been cancelled');
        }},
      ]
    );
  };

  const renderOrderHeader = () => (
    <Card style={styles.section}>
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <Text style={[styles.orderNumber, { color: colors.text }]}>
            Order #{order?.id?.slice(-8)}
          </Text>
          <Text style={[styles.orderDate, { color: colors.textSecondary }]}>
            Placed on {formatDate(order?.createdAt || order?.created_at, 'datetime')}
          </Text>
        </View>
        
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(order?.status) + '20' }
        ]}>
          <Text style={[styles.statusText, { color: getStatusColor(order?.status) }]}>
            {order?.status?.toUpperCase()}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderOrderProgress = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Order Progress
      </Text>
      
      <View style={styles.progressContainer}>
        {orderStatuses.map((status, index) => {
          const currentIndex = getStatusIndex(order?.status);
          const isCompleted = index <= currentIndex;
          const isCurrent = index === currentIndex;
          
          return (
            <View key={status.id} style={styles.progressStep}>
              <View style={styles.stepIndicator}>
                <View style={[
                  styles.stepIcon,
                  {
                    backgroundColor: isCompleted ? colors.primary : colors.backgroundSecondary,
                    borderColor: isCurrent ? colors.primary : colors.border,
                  }
                ]}>
                  <Ionicons
                    name={status.icon}
                    size={20}
                    color={isCompleted ? colors.white : colors.textTertiary}
                  />
                </View>
                
                {index < orderStatuses.length - 1 && (
                  <View style={[
                    styles.stepLine,
                    { backgroundColor: index < currentIndex ? colors.primary : colors.border }
                  ]} />
                )}
              </View>
              
              <View style={styles.stepContent}>
                <Text style={[
                  styles.stepTitle,
                  { color: isCompleted ? colors.text : colors.textSecondary }
                ]}>
                  {status.name}
                </Text>
                <Text style={[styles.stepDescription, { color: colors.textTertiary }]}>
                  {status.description}
                </Text>
                {isCurrent && (
                  <Text style={[styles.currentTime, { color: colors.primary }]}>
                    {formatOrderDate(order?.updatedAt || order?.updated_at)}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </View>
    </Card>
  );

  const renderDeliveryInfo = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Delivery Information
      </Text>
      
      <View style={styles.deliveryDetails}>
        <View style={styles.addressSection}>
          <Ionicons name="location" size={20} color={colors.primary} />
          <View style={styles.addressText}>
            <Text style={[styles.addressTitle, { color: colors.text }]}>
              Delivery Address
            </Text>
            <Text style={[styles.addressDetails, { color: colors.textSecondary }]}>
              {order?.delivery_address}
            </Text>
            <Text style={[styles.addressDetails, { color: colors.textSecondary }]}>
              {order?.delivery_town}, {order?.delivery_county}
            </Text>
          </View>
        </View>
        
        {order?.delivery_person && (
          <View style={styles.deliveryPersonSection}>
            <View style={styles.deliveryPersonInfo}>
              <Ionicons name="person" size={20} color={colors.primary} />
              <View style={styles.deliveryPersonText}>
                <Text style={[styles.deliveryPersonName, { color: colors.text }]}>
                  {order.delivery_person.name}
                </Text>
                <Text style={[styles.deliveryPersonPhone, { color: colors.textSecondary }]}>
                  {order.delivery_person.phone}
                </Text>
              </View>
            </View>
            
            <Button
              title="Call"
              variant="outline"
              size="small"
              onPress={handleCallDeliveryPerson}
              icon={<Ionicons name="call" size={16} color={colors.primary} />}
            />
          </View>
        )}
        
        <View style={styles.deliveryStatus}>
          <Text style={[styles.deliveryStatusTitle, { color: colors.text }]}>
            Delivery Status
          </Text>
          <View style={styles.deliveryProgress}>
            {deliveryStatuses.map((status, index) => {
              const currentIndex = getDeliveryStatusIndex(order?.delivery_status);
              const isCompleted = index <= currentIndex;
              
              return (
                <View key={status.id} style={styles.deliveryStep}>
                  <Ionicons
                    name={status.icon}
                    size={16}
                    color={isCompleted ? colors.primary : colors.textTertiary}
                  />
                  <Text style={[
                    styles.deliveryStepText,
                    { color: isCompleted ? colors.text : colors.textTertiary }
                  ]}>
                    {status.name}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </Card>
  );

  const renderOrderItems = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Order Items
      </Text>
      
      {order?.items?.map((item, index) => (
        <View key={index} style={styles.orderItem}>
          <View style={styles.itemInfo}>
            <Text style={[styles.itemName, { color: colors.text }]}>
              {item.product?.name}
            </Text>
            <Text style={[styles.itemDetails, { color: colors.textSecondary }]}>
              Qty: {item.quantity} × {formatCurrency(item.price)}
            </Text>
          </View>
          <Text style={[styles.itemTotal, { color: colors.text }]}>
            {formatCurrency(item.price * item.quantity)}
          </Text>
        </View>
      ))}
      
      <View style={[styles.orderSummary, { borderTopColor: colors.border }]}>
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            Subtotal
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {formatCurrency(order?.total_amount - order?.delivery_fee)}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            Delivery Fee
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {formatCurrency(order?.delivery_fee)}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={[styles.totalLabel, { color: colors.text }]}>
            Total
          </Text>
          <Text style={[styles.totalValue, { color: colors.primary }]}>
            {formatCurrency(order?.total_amount)}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderActionButtons = () => (
    <Card style={styles.section}>
      <View style={styles.actionButtons}>
        <Button
          title="Chat with Seller"
          variant="outline"
          onPress={handleChatWithSeller}
          style={styles.actionButton}
          icon={<Ionicons name="chatbubble-outline" size={20} color={colors.primary} />}
        />
        
        {order?.status === 'pending' && (
          <Button
            title="Cancel Order"
            variant="outline"
            onPress={handleCancelOrder}
            style={[styles.actionButton, { borderColor: colors.error }]}
            textStyle={{ color: colors.error }}
            icon={<Ionicons name="close-circle-outline" size={20} color={colors.error} />}
          />
        )}
      </View>
    </Card>
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return colors.warning;
      case 'confirmed': return colors.info;
      case 'preparing': return colors.secondary;
      case 'shipped': return colors.primary;
      case 'delivered': return colors.success;
      case 'cancelled': return colors.error;
      default: return colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading order details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!order) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.text }]}>
            Order not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderOrderHeader()}
        {renderOrderProgress()}
        {renderDeliveryInfo()}
        {renderOrderItems()}
        {renderActionButtons()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  orderDate: {
    fontSize: typography.fontSize.sm,
  },
  statusBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
  },
  progressContainer: {
    paddingLeft: spacing.md,
  },
  progressStep: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },
  stepIndicator: {
    alignItems: 'center',
    marginRight: spacing.md,
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepLine: {
    width: 2,
    height: 30,
    marginTop: spacing.sm,
  },
  stepContent: {
    flex: 1,
    paddingTop: spacing.sm,
  },
  stepTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  stepDescription: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  currentTime: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  deliveryDetails: {
    gap: spacing.lg,
  },
  addressSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  addressText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  addressTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  addressDetails: {
    fontSize: typography.fontSize.sm,
    lineHeight: typography.lineHeight.relaxed,
  },
  deliveryPersonSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deliveryPersonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  deliveryPersonText: {
    marginLeft: spacing.md,
  },
  deliveryPersonName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  deliveryPersonPhone: {
    fontSize: typography.fontSize.sm,
  },
  deliveryStatus: {
    marginTop: spacing.md,
  },
  deliveryStatusTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.md,
  },
  deliveryProgress: {
    gap: spacing.sm,
  },
  deliveryStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryStepText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.sm,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  itemInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  itemName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  itemDetails: {
    fontSize: typography.fontSize.sm,
  },
  itemTotal: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  orderSummary: {
    borderTopWidth: 1,
    marginTop: spacing.md,
    paddingTop: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  actionButtons: {
    gap: spacing.md,
  },
  actionButton: {
    marginBottom: spacing.sm,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: typography.fontSize.base,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: typography.fontSize.lg,
    marginTop: spacing.lg,
  },
});

export default OrderTrackingScreen;
