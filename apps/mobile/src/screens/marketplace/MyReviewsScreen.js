import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import { formatDate, formatCurrency } from '../../utils/formatters';
import ApiService from '../../services/api';
import Card from '../../components/common/Card';

const MyReviewsScreen = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMyReviews();
  }, []);

  const loadMyReviews = async () => {
    try {
      // This would need a new API endpoint to get user's reviews
      // For now, we'll use a placeholder
      const response = await ApiService.getUserReviews();
      if (response.success) {
        setReviews(response.data);
      }
    } catch (error) {
      console.error('Error loading my reviews:', error);
      // For demo purposes, set empty array
      setReviews([]);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMyReviews();
    setRefreshing(false);
  };

  const renderStars = (rating) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= rating ? 'star' : 'star-outline'}
            size={14}
            color={star <= rating ? colors.warning : colors.textSecondary}
            style={styles.star}
          />
        ))}
      </View>
    );
  };

  const renderReviewImages = (images) => {
    if (!images || images.length === 0) return null;

    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.reviewImages}>
        {images.map((image, index) => (
          <TouchableOpacity key={index} style={styles.reviewImageContainer}>
            <Image source={{ uri: image }} style={styles.reviewImage} />
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderReview = (review) => (
    <Card key={review.id} style={styles.reviewCard}>
      <TouchableOpacity
        onPress={() => navigation.navigate('ProductDetails', { productId: review.productId })}
      >
        <View style={styles.productInfo}>
          {review.product?.images && review.product.images.length > 0 && (
            <Image source={{ uri: review.product.images[0] }} style={styles.productImage} />
          )}
          <View style={styles.productDetails}>
            <Text style={[styles.productName, { color: colors.text }]}>
              {review.product?.name || 'Product'}
            </Text>
            <Text style={[styles.productPrice, { color: colors.primary }]}>
              {formatCurrency(review.product?.price || 0)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      <View style={styles.reviewContent}>
        <View style={styles.reviewHeader}>
          <View style={styles.reviewMeta}>
            {renderStars(review.rating)}
            <Text style={[styles.reviewDate, { color: colors.textSecondary }]}>
              {formatDate(review.createdAt)}
            </Text>
          </View>
        </View>

        {review.comment && (
          <Text style={[styles.reviewComment, { color: colors.text }]}>
            {review.comment}
          </Text>
        )}

        {renderReviewImages(review.images)}
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="star-outline" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No reviews yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Purchase products and leave reviews to help other buyers
      </Text>
      <TouchableOpacity
        style={[styles.browseButton, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('Marketplace')}
      >
        <Text style={[styles.browseButtonText, { color: colors.white }]}>
          Browse Products
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading your reviews...
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {reviews.length === 0 ? (
          renderEmptyState()
        ) : (
          <View style={styles.reviewsList}>
            <Text style={[styles.reviewsTitle, { color: colors.text }]}>
              My Reviews ({reviews.length})
            </Text>
            {reviews.map(renderReview)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  reviewsList: {
    marginBottom: 20,
  },
  reviewsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  reviewCard: {
    marginBottom: 16,
  },
  productInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  productImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  productPrice: {
    fontSize: 12,
    fontWeight: '500',
  },
  reviewContent: {
    paddingTop: 4,
  },
  reviewHeader: {
    marginBottom: 8,
  },
  reviewMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  star: {
    marginRight: 1,
  },
  reviewDate: {
    fontSize: 12,
  },
  reviewComment: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  reviewImages: {
    marginTop: 8,
  },
  reviewImageContainer: {
    marginRight: 8,
  },
  reviewImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  browseButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
};

export default MyReviewsScreen;
