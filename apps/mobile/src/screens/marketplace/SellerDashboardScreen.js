import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
// Note: Install react-native-chart-kit for charts
// npm install react-native-chart-kit react-native-svg
// For now, we'll create placeholder chart components
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import ApiService from '../../services/api';
import Card from '../../components/common/Card';

const { width: screenWidth } = Dimensions.get('window');

const SellerDashboardScreen = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [analytics, setAnalytics] = useState({
    totalSales: 0,
    totalRevenue: 0,
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    averageOrderValue: 0,
    topProducts: [],
    recentOrders: [],
    salesTrend: [],
    monthlyRevenue: [],
    ordersByStatus: [],
  });
  const [selectedPeriod, setSelectedPeriod] = useState('7d'); // 7d, 30d, 90d, 1y

  useEffect(() => {
    loadSellerAnalytics();
  }, [selectedPeriod]);

  const loadSellerAnalytics = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getSellerAnalytics(selectedPeriod);
      if (response.success) {
        setAnalytics(response.data);
      }
    } catch (error) {
      console.error('Failed to load seller analytics:', error);
      Alert.alert('Error', 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSellerAnalytics();
    setRefreshing(false);
  };

  const periods = [
    { id: '7d', label: '7 Days' },
    { id: '30d', label: '30 Days' },
    { id: '90d', label: '3 Months' },
    { id: '1y', label: '1 Year' },
  ];

  // Header is now handled by the navigation system with SmartHeader

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              {
                backgroundColor: selectedPeriod === period.id ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedPeriod(period.id)}
          >
            <Text style={[
              styles.periodButtonText,
              { color: selectedPeriod === period.id ? colors.white : colors.textSecondary }
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderStatsCards = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statsRow}>
        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="trending-up" size={24} color={colors.success} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              KES {analytics.totalRevenue?.toLocaleString() || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Revenue
            </Text>
          </View>
        </Card>

        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="bag" size={24} color={colors.primary} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {analytics.totalOrders || 0}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Orders
            </Text>
          </View>
        </Card>
      </View>

      <View style={styles.statsRow}>
        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="time" size={24} color={colors.warning} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {analytics.pendingOrders || 0}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Pending Orders
            </Text>
          </View>
        </Card>

        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="calculator" size={24} color={colors.info} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              KES {analytics.averageOrderValue?.toLocaleString() || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Avg Order Value
            </Text>
          </View>
        </Card>
      </View>
    </View>
  );

  const renderSalesChart = () => {
    if (!analytics.salesTrend || analytics.salesTrend.length === 0) {
      return null;
    }

    return (
      <Card style={[styles.chartCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.chartTitle, { color: colors.text }]}>
          Sales Trend
        </Text>
        <View style={[styles.chartPlaceholder, { backgroundColor: colors.backgroundSecondary }]}>
          <Ionicons name="trending-up" size={48} color={colors.primary} />
          <Text style={[styles.chartPlaceholderText, { color: colors.textSecondary }]}>
            Sales trend chart will be displayed here
          </Text>
          <Text style={[styles.chartPlaceholderSubtext, { color: colors.textSecondary }]}>
            Install react-native-chart-kit for charts
          </Text>
        </View>
      </Card>
    );
  };

  const renderOrderStatusChart = () => {
    if (!analytics.ordersByStatus || analytics.ordersByStatus.length === 0) {
      return null;
    }

    return (
      <Card style={[styles.chartCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.chartTitle, { color: colors.text }]}>
          Orders by Status
        </Text>
        <View style={[styles.chartPlaceholder, { backgroundColor: colors.backgroundSecondary }]}>
          <Ionicons name="pie-chart" size={48} color={colors.primary} />
          <Text style={[styles.chartPlaceholderText, { color: colors.textSecondary }]}>
            Order status distribution chart
          </Text>
          <View style={styles.statusList}>
            {analytics.ordersByStatus.map((item, index) => (
              <View key={index} style={styles.statusItem}>
                <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
                <Text style={[styles.statusItemText, { color: colors.text }]}>
                  {item.status}: {item.count}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </Card>
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return colors.warning;
      case 'confirmed': return colors.info;
      case 'shipped': return colors.primary;
      case 'delivered': return colors.success;
      case 'cancelled': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const renderQuickActions = () => (
    <Card style={[styles.actionsCard, { backgroundColor: colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Quick Actions
      </Text>
      <View style={styles.actionsGrid}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'ManageOrders' })}
        >
          <Ionicons name="list" size={24} color={colors.primary} />
          <Text style={[styles.actionText, { color: colors.primary }]}>
            Manage Orders
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.success + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'AddProduct' })}
        >
          <Ionicons name="add-circle" size={24} color={colors.success} />
          <Text style={[styles.actionText, { color: colors.success }]}>
            Add Product
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.warning + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'DeliveryManagement' })}
        >
          <Ionicons name="car" size={24} color={colors.warning} />
          <Text style={[styles.actionText, { color: colors.warning }]}>
            Deliveries
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.secondary + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'DeliveryContacts' })}
        >
          <Ionicons name="people" size={24} color={colors.secondary} />
          <Text style={[styles.actionText, { color: colors.secondary }]}>
            Delivery Contacts
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.secondary + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'DeliveryContacts' })}
        >
          <Ionicons name="people" size={24} color={colors.secondary} />
          <Text style={[styles.actionText, { color: colors.secondary }]}>
            Delivery Contacts
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.info + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'SellerAnalytics' })}
        >
          <Ionicons name="analytics" size={24} color={colors.info} />
          <Text style={[styles.actionText, { color: colors.info }]}>
            Analytics
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading analytics...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {renderStatsCards()}
        {renderSalesChart()}
        {renderOrderStatusChart()}
        {renderQuickActions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  settingsButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  periodSelector: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 5,
    padding: 15,
    borderRadius: 12,
  },
  statContent: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  chartCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
    borderRadius: 12,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  chartPlaceholder: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    padding: 20,
  },
  chartPlaceholderText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 10,
    textAlign: 'center',
  },
  chartPlaceholderSubtext: {
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
  },
  statusList: {
    marginTop: 15,
    alignItems: 'flex-start',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusItemText: {
    fontSize: 14,
  },
  actionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
};

export default SellerDashboardScreen;
