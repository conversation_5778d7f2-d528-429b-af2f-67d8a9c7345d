import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import ApiService from '../../services/api';
import Card from '../../components/common/Card';

const BuyerDashboardScreen = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [orders, setOrders] = useState([]);
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalSpent: 0,
    pendingOrders: 0,
    deliveredOrders: 0,
    cancelledOrders: 0,
    favoriteCategories: [],
  });

  useEffect(() => {
    loadBuyerData();
  }, []);

  const loadBuyerData = async () => {
    try {
      setLoading(true);
      const [ordersResponse, statsResponse] = await Promise.all([
        ApiService.getOrders({ buyer: true, limit: 10 }),
        ApiService.getBuyerStats(),
      ]);

      if (ordersResponse.success) {
        setOrders(ordersResponse.data || []);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data || stats);
      }
    } catch (error) {
      console.error('Failed to load buyer data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBuyerData();
    setRefreshing(false);
  };

  const getOrderStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return { icon: 'time', color: colors.warning, label: 'Pending' };
      case 'confirmed':
        return { icon: 'checkmark-circle', color: colors.info, label: 'Confirmed' };
      case 'shipped':
        return { icon: 'car', color: colors.primary, label: 'Shipped' };
      case 'delivered':
        return { icon: 'checkmark-done', color: colors.success, label: 'Delivered' };
      case 'cancelled':
        return { icon: 'close-circle', color: colors.error, label: 'Cancelled' };
      default:
        return { icon: 'help-circle', color: colors.textSecondary, label: 'Unknown' };
    }
  };

  const getDeliveryStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return { icon: 'time', color: colors.warning, label: 'Pending Assignment' };
      case 'assigned':
        return { icon: 'person', color: colors.info, label: 'Assigned' };
      case 'in_transit':
        return { icon: 'car', color: colors.primary, label: 'In Transit' };
      case 'delivered':
        return { icon: 'home', color: colors.success, label: 'Delivered' };
      case 'failed':
        return { icon: 'warning', color: colors.error, label: 'Failed' };
      default:
        return { icon: 'help-circle', color: colors.textSecondary, label: 'Unknown' };
    }
  };

  // Header is now handled by the navigation system with SmartHeader

  const renderStatsCards = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statsRow}>
        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="bag" size={24} color={colors.primary} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {stats.totalOrders || 0}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Orders
            </Text>
          </View>
        </Card>

        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="card" size={24} color={colors.success} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              KES {stats.totalSpent?.toLocaleString() || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total Spent
            </Text>
          </View>
        </Card>
      </View>

      <View style={styles.statsRow}>
        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="time" size={24} color={colors.warning} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {stats.pendingOrders || 0}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Pending
            </Text>
          </View>
        </Card>

        <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statContent}>
            <Ionicons name="checkmark-done" size={24} color={colors.success} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {stats.deliveredOrders || 0}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Delivered
            </Text>
          </View>
        </Card>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <Card style={[styles.actionsCard, { backgroundColor: colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Quick Actions
      </Text>
      <View style={styles.actionsGrid}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => navigation.navigate('ManageOrders')}
        >
          <Ionicons name="list" size={24} color={colors.primary} />
          <Text style={[styles.actionText, { color: colors.primary }]}>
            All Orders
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.success + '20' }]}
          onPress={() => navigation.navigate('UserTabs', { screen: 'Marketplace' })}
        >
          <Ionicons name="storefront" size={24} color={colors.success} />
          <Text style={[styles.actionText, { color: colors.success }]}>
            Shop Now
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.warning + '20' }]}
          onPress={() => navigation.navigate('Cart')}
        >
          <Ionicons name="cart" size={24} color={colors.warning} />
          <Text style={[styles.actionText, { color: colors.warning }]}>
            My Cart
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.info + '20' }]}
          onPress={() => navigation.navigate('Wishlist')}
        >
          <Ionicons name="heart" size={24} color={colors.info} />
          <Text style={[styles.actionText, { color: colors.info }]}>
            Wishlist
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderOrderItem = ({ item }) => {
    const statusInfo = getOrderStatusInfo(item.status);
    const deliveryInfo = getDeliveryStatusInfo(item.deliveryStatus);

    return (
      <TouchableOpacity
        style={[styles.orderCard, { backgroundColor: colors.surface }]}
        onPress={() => navigation.navigate('OrderTracking', { orderId: item.id })}
      >
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderId, { color: colors.text }]}>
              Order #{item.id.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.orderDate, { color: colors.textSecondary }]}>
              {new Date(item.createdAt).toLocaleDateString()}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={16} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>

        <View style={styles.orderDetails}>
          <Text style={[styles.sellerName, { color: colors.textSecondary }]}>
            Seller: {item.seller?.firstName} {item.seller?.lastName}
          </Text>
          <Text style={[styles.orderAmount, { color: colors.primary }]}>
            KES {item.totalAmount?.toLocaleString()}
          </Text>
        </View>

        <View style={styles.deliveryInfo}>
          <View style={styles.deliveryStatus}>
            <Ionicons name={deliveryInfo.icon} size={16} color={deliveryInfo.color} />
            <Text style={[styles.deliveryText, { color: deliveryInfo.color }]}>
              {deliveryInfo.label}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.trackButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.navigate('OrderTracking', { orderId: item.id })}
          >
            <Text style={[styles.trackButtonText, { color: colors.white }]}>
              Track
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderRecentOrders = () => (
    <Card style={[styles.ordersCard, { backgroundColor: colors.surface }]}>
      <View style={styles.ordersHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Recent Orders
        </Text>
        <TouchableOpacity onPress={() => navigation.navigate('ManageOrders')}>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>
            View All
          </Text>
        </TouchableOpacity>
      </View>

      {orders.length > 0 ? (
        <FlatList
          data={orders.slice(0, 5)}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="bag-outline" size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No orders yet
          </Text>
          <TouchableOpacity
            style={[styles.shopButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.navigate('MainTabs', { screen: 'Marketplace' })}
          >
            <Text style={[styles.shopButtonText, { color: colors.white }]}>
              Start Shopping
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </Card>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading your dashboard...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderStatsCards()}
        {renderQuickActions()}
        {renderRecentOrders()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 5,
    padding: 15,
    borderRadius: 12,
  },
  statContent: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  actionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  ordersCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
    borderRadius: 12,
  },
  ordersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  orderCard: {
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  orderDate: {
    fontSize: 12,
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  sellerName: {
    fontSize: 14,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  deliveryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deliveryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryText: {
    fontSize: 12,
    marginLeft: 4,
  },
  trackButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  trackButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 10,
    marginBottom: 20,
  },
  shopButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  shopButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
};

export default BuyerDashboardScreen;
