import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  StyleSheet,
  Modal,
  TextInput,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import ApiService from '../../services/api';

const DELIVERY_STATUSES = {
  pending: { label: 'Pending Pickup', color: '#FF9500', icon: 'time' },
  picked_up: { label: 'Picked Up', color: '#007AFF', icon: 'checkmark-circle' },
  in_transit: { label: 'In Transit', color: '#5856D6', icon: 'car' },
  out_for_delivery: { label: 'Out for Delivery', color: '#34C759', icon: 'location' },
  delivered: { label: 'Delivered', color: '#30D158', icon: 'checkmark-circle-2' },
  failed: { label: 'Delivery Failed', color: '#FF3B30', icon: 'close-circle' },
  returned: { label: 'Returned', color: '#FF9500', icon: 'return-up-back' },
};

export default function DeliveryManagementScreen({ navigation }) {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  const [deliveries, setDeliveries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('available'); // available, assigned, completed
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [selectedDelivery, setSelectedDelivery] = useState(null);
  const [updateNotes, setUpdateNotes] = useState('');

  useEffect(() => {
    loadDeliveries();
  }, [activeTab]);

  const loadDeliveries = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getDeliveries({ status: activeTab });
      if (response.success) {
        setDeliveries(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load deliveries:', error);
      Alert.alert('Error', 'Failed to load deliveries');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDeliveries();
    setRefreshing(false);
  };

  const acceptDelivery = async (deliveryId) => {
    try {
      const response = await ApiService.acceptDelivery(deliveryId);
      if (response.success) {
        Alert.alert('Success', 'Delivery accepted successfully');
        loadDeliveries();
      }
    } catch (error) {
      console.error('Failed to accept delivery:', error);
      Alert.alert('Error', 'Failed to accept delivery');
    }
  };

  const updateDeliveryStatus = async (deliveryId, newStatus, notes = '') => {
    try {
      const response = await ApiService.updateDeliveryStatus(deliveryId, {
        status: newStatus,
        notes: notes,
      });
      if (response.success) {
        Alert.alert('Success', 'Delivery status updated successfully');
        setUpdateModalVisible(false);
        setSelectedDelivery(null);
        setUpdateNotes('');
        loadDeliveries();
      }
    } catch (error) {
      console.error('Failed to update delivery status:', error);
      Alert.alert('Error', 'Failed to update delivery status');
    }
  };

  const getStatusInfo = (status) => {
    return DELIVERY_STATUSES[status] || { label: status, color: colors.textSecondary, icon: 'help' };
  };

  const openUpdateModal = (delivery) => {
    setSelectedDelivery(delivery);
    setUpdateModalVisible(true);
  };

  const renderDeliveryItem = ({ item }) => {
    const statusInfo = getStatusInfo(item.deliveryStatus);
    const canAccept = activeTab === 'available' && item.deliveryStatus === 'pending';
    const canUpdate = activeTab === 'assigned' && ['picked_up', 'in_transit', 'out_for_delivery'].includes(item.deliveryStatus);

    return (
      <View style={[styles.deliveryCard, { backgroundColor: colors.surface }]}>
        <View style={styles.deliveryHeader}>
          <View style={styles.deliveryInfo}>
            <Text style={[styles.orderId, { color: colors.text }]}>
              Order #{item.id.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.customerName, { color: colors.textSecondary }]}>
              {item.buyer?.firstName} {item.buyer?.lastName}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={16} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>

        <View style={styles.deliveryDetails}>
          <View style={styles.locationInfo}>
            <Ionicons name="location" size={16} color={colors.primary} />
            <Text style={[styles.locationText, { color: colors.text }]}>
              {item.deliveryAddress}
            </Text>
          </View>
          <Text style={[styles.locationSubtext, { color: colors.textSecondary }]}>
            {item.deliveryTown}, {item.deliveryCounty}
          </Text>
          
          <View style={styles.deliveryMeta}>
            <View style={styles.metaItem}>
              <Ionicons name="call" size={16} color={colors.textSecondary} />
              <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                {item.deliveryPhone}
              </Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="cash" size={16} color={colors.success} />
              <Text style={[styles.metaText, { color: colors.success }]}>
                KES {item.deliveryFee?.toLocaleString() || '0'}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.deliveryActions}>
          {canAccept && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={() => acceptDelivery(item.id)}
            >
              <Ionicons name="checkmark" size={20} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Accept
              </Text>
            </TouchableOpacity>
          )}

          {canUpdate && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.warning }]}
              onPress={() => openUpdateModal(item)}
            >
              <Ionicons name="refresh" size={20} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Update Status
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.info }]}
            onPress={() => navigation.navigate('OrderTracking', { orderId: item.orderId })}
          >
            <Ionicons name="eye" size={20} color={colors.white} />
            <Text style={[styles.actionButtonText, { color: colors.white }]}>
              View Details
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderUpdateModal = () => (
    <Modal
      visible={updateModalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setUpdateModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Update Delivery Status
          </Text>

          <View style={styles.statusOptions}>
            {Object.entries(DELIVERY_STATUSES).map(([status, info]) => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.statusOption,
                  { borderColor: info.color + '40' },
                  selectedDelivery?.deliveryStatus === status && { backgroundColor: info.color + '20' }
                ]}
                onPress={() => {
                  if (status !== selectedDelivery?.deliveryStatus) {
                    updateDeliveryStatus(selectedDelivery?.id, status, updateNotes);
                  }
                }}
              >
                <Ionicons name={info.icon} size={20} color={info.color} />
                <Text style={[styles.statusOptionText, { color: info.color }]}>
                  {info.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <TextInput
            style={[styles.notesInput, { borderColor: colors.border, color: colors.text }]}
            placeholder="Add notes (optional)"
            placeholderTextColor={colors.textSecondary}
            value={updateNotes}
            onChangeText={setUpdateNotes}
            multiline
            numberOfLines={3}
          />

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: colors.textSecondary }]}
              onPress={() => {
                setUpdateModalVisible(false);
                setSelectedDelivery(null);
                setUpdateNotes('');
              }}
            >
              <Text style={[styles.modalButtonText, { color: colors.white }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="car-outline" size={80} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Deliveries Found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {activeTab === 'available' 
          ? "No deliveries available for pickup"
          : activeTab === 'assigned'
          ? "You have no assigned deliveries"
          : "No completed deliveries"
        }
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Tab Selector */}
      <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'available' && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => setActiveTab('available')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'available' ? colors.primary : colors.textSecondary }
          ]}>
            Available
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'assigned' && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => setActiveTab('assigned')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'assigned' ? colors.primary : colors.textSecondary }
          ]}>
            Assigned
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'completed' && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => setActiveTab('completed')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'completed' ? colors.primary : colors.textSecondary }
          ]}>
            Completed
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={deliveries}
        renderItem={renderDeliveryItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          deliveries.length === 0 ? styles.emptyList : styles.list,
          styles.scrollContent
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />

      {renderUpdateModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    padding: 4,
    margin: 16,
    borderRadius: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  list: {
    padding: 16,
    paddingTop: 0,
  },
  emptyList: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  deliveryCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  deliveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  deliveryInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  customerName: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  deliveryDetails: {
    marginBottom: 16,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  locationSubtext: {
    fontSize: 14,
    marginLeft: 24,
    marginBottom: 8,
  },
  deliveryMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 14,
    marginLeft: 4,
  },
  deliveryActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
    minWidth: 100,
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  statusOptions: {
    marginBottom: 20,
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  statusOptionText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  notesInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  modalButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
