import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  FlatList,
  Image,
  TextInput,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { useLightningData, useOptimisticUpdate } from '../../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';

import Button from '../../components/common/Button';
import apiService from '../../services/api';
import webSocketService from '../../services/websocket';
import lightningDataService from '../../services/lightningDataService';
// Registration imports removed - users can now view products without registration

const MarketplaceScreen = ({ navigation }) => {
  const { theme, user, cartItems, isOnline, getCachedData, prefetchForPage } = useApp();
  const colors = getThemeColors(theme);

  // Calculate optimal number of columns based on screen width
  const getOptimalColumns = () => {
    const { width: screenWidth } = Dimensions.get('window');
    const cardMinWidth = 140; // Minimum card width for readability (same as horizontal cards)
    const padding = 16; // Total horizontal padding
    const spacing = 8; // Space between cards

    const availableWidth = screenWidth - padding;
    const maxColumns = Math.floor(availableWidth / (cardMinWidth + spacing));

    // Ensure between 2 and 8 columns for best UX
    return Math.max(2, Math.min(8, maxColumns));
  };

  const optimalColumns = getOptimalColumns();

  // Debug log for responsive columns
  // console.log(`📱 RESPONSIVE GRID: Screen width ${Dimensions.get('window').width}px → ${optimalColumns} columns`);

  // Registration hook removed - users can now browse products freely

  // REAL: Lightning data hooks for instant complete product loading
  const {
    data: allProducts,
    loading: productsLoading,
    refresh: refreshProducts,
    isInstant: productsInstant,
    source: productsSource,
    error: productsError,
  } = useLightningData('products-complete', { forceRefresh: false });

  // REAL: Lightning data hooks for marketplace categories
  const {
    data: allCategories,
    loading: categoriesLoading,
    refresh: refreshCategories,
    source: categoriesSource,
    error: categoriesError,
  } = useLightningData('marketplace-categories', { forceRefresh: false });

  // Optimistic updates for cart operations
  const { addToCart, addToWishlist } = useOptimisticUpdate();

  // REAL: Local state for processed data
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [categorizedProducts, setCategorizedProducts] = useState({});
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState('categories'); // 'categories' or 'grid'
  const [sortBy, setSortBy] = useState('newest'); // 'newest', 'price_low', 'price_high', 'rating'
  const [filters, setFilters] = useState({});
  const [error, setError] = useState(null);
  const [fabMenuOpen, setFabMenuOpen] = useState(false);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [showHeaderMenu, setShowHeaderMenu] = useState(false);

  // REAL: Local loading state management
  const [localLoading, setLocalLoading] = useState(true);
  const [dataReady, setDataReady] = useState(false);
  const isActuallyLoading = localLoading && !dataReady;

  // REAL: Process products when lightning data loads
  useEffect(() => {
    if (allProducts && Array.isArray(allProducts) && allProducts.length > 0) {
      // console.log(`🛒 REAL: Processing ${allProducts.length} products from lightning data`);
      // console.log(`🛒 REAL: Products source: ${productsSource}`);

      try {
        // Remove any remaining duplicates (extra safety)
        const uniqueProducts = allProducts.reduce((acc, product) => {
          if (product && product.id && !acc.find(existing => existing.id === product.id)) {
            acc.push(product);
          }
          return acc;
        }, []);

        // console.log(`🔍 REAL: After deduplication: ${uniqueProducts.length} unique products`);

        setProducts(uniqueProducts);
        setDataReady(true);
        setLocalLoading(false);
        setError(null); // Clear any previous errors

        // console.log('✅ REAL: Products processed for UI');

      } catch (error) {
        console.error('❌ REAL: Error processing products:', error);
        setProducts([]);
        setLocalLoading(false);
        setDataReady(false);
        setError('Error processing products: ' + error.message);
      }
    } else if (productsError) {
      console.error('❌ REAL: Products loading error:', productsError);
      setError('Failed to load products: ' + (productsError.message || 'Unknown error'));
      setLocalLoading(false);
      setDataReady(false);
    } else if (allProducts && Array.isArray(allProducts) && allProducts.length === 0) {
      // console.log('🛒 REAL: No products found in response');
      setProducts([]);
      setDataReady(true);
      setLocalLoading(false);
      setError(null);
    }
  }, [allProducts, productsError, productsSource]);

  // REAL: Process categories when lightning data loads
  useEffect(() => {
    if (allCategories && Array.isArray(allCategories) && allCategories.length > 0) {
      // console.log(`🏷️ REAL: Processing ${allCategories.length} categories from lightning data`);

      try {
        // Add "All" category at the beginning
        const processedCategories = [
          { id: 'all', name: 'All Products', icon: 'apps', productCount: products.length },
          ...allCategories
        ];

        setCategories(processedCategories);
        // console.log('✅ REAL: Categories processed for UI:', processedCategories.map(c => c.name));

      } catch (error) {
        console.error('❌ REAL: Error processing categories:', error);
        setCategories([{ id: 'all', name: 'All Products', icon: 'apps', productCount: products.length }]);
      }
    } else if (categoriesError || !allCategories || allCategories.length === 0) {
      // console.log('🏷️ REAL: Categories failed or empty, using comprehensive fallback categories');

      // Use comprehensive fallback categories
      const fallbackCategories = [
        { id: 'all', name: 'All Products', icon: 'apps', productCount: products.length },
        { id: 'agriculture', name: 'Agriculture', icon: 'leaf', productCount: 0 },
        { id: 'food_beverage', name: 'Food & Beverage', icon: 'restaurant', productCount: 0 },
        { id: 'clothing', name: 'Clothing', icon: 'shirt', productCount: 0 },
        { id: 'electronics', name: 'Electronics', icon: 'phone-portrait', productCount: 0 },
        { id: 'home_garden', name: 'Home & Garden', icon: 'home', productCount: 0 },
        { id: 'health_beauty', name: 'Health & Beauty', icon: 'heart', productCount: 0 },
        { id: 'automotive', name: 'Automotive', icon: 'car', productCount: 0 },
        { id: 'services', name: 'Services', icon: 'construct', productCount: 0 },
        { id: 'other', name: 'Other', icon: 'ellipsis-horizontal', productCount: 0 }
      ];

      setCategories(fallbackCategories);
      // console.log('✅ REAL: Fallback categories set:', fallbackCategories.map(c => c.name));
    }
  }, [allCategories, categoriesError, products.length]);

  // REAL: Force immediate data loading on mount
  useEffect(() => {
    // console.log('🚀 REAL: Triggering immediate marketplace data loading...');
    setLocalLoading(true);
    setDataReady(false);

    const loadImmediate = async () => {
      try {
        // Load products and categories in parallel
        const [productsResult, categoriesResult] = await Promise.all([
          lightningDataService.getData('products-complete', { forceRefresh: true, immediate: true }),
          lightningDataService.getData('marketplace-categories', { forceRefresh: true, immediate: true })
        ]);

        // console.log('🔍 REAL: Immediate marketplace load results:', {
        //   products: {
        //     success: productsResult.success,
        //     dataLength: Array.isArray(productsResult.data) ? productsResult.data.length : 'not array',
        //     source: productsResult.source
        //   },
        //   categories: {
        //     success: categoriesResult.success,
        //     dataLength: Array.isArray(categoriesResult.data) ? categoriesResult.data.length : 'not array',
        //     source: categoriesResult.source
        //   }
        // });

        if (productsResult.success && productsResult.data) {
          // console.log(`⚡ REAL: Got ${productsResult.data.length} products immediately`);
        }

        if (categoriesResult.success && categoriesResult.data) {
          // console.log(`⚡ REAL: Got ${categoriesResult.data.length} categories immediately`);
        }

        if (!productsResult.success && !categoriesResult.success) {
          console.error('❌ REAL: Both immediate loads failed');
          setLocalLoading(false);
          setDataReady(false);
        }

      } catch (error) {
        console.error('❌ REAL: Immediate marketplace load failed:', error.message);
        setLocalLoading(false);
        setDataReady(false);
      }
    };

    loadImmediate();
  }, []);

  // REAL: Filter and categorize products when data changes
  useEffect(() => {
    if (products.length > 0 && categories.length > 0) {
      // console.log(`🔍 REAL: Filtering and categorizing ${products.length} products`);

      try {
        // Apply search filter
        let filtered = products;
        if (searchQuery.trim()) {
          filtered = products.filter(product =>
            product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            product.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            product.description?.toLowerCase().includes(searchQuery.toLowerCase())
          );
          // console.log(`🔍 Search "${searchQuery}" filtered to ${filtered.length} products`);
        }

        // Apply category filter
        if (selectedCategory !== 'all') {
          filtered = filtered.filter(product =>
            product.category === selectedCategory ||
            product.category_id === selectedCategory ||
            (product.category && product.category.toLowerCase() === selectedCategory.toLowerCase())
          );
          // console.log(`🏷️ Category "${selectedCategory}" filtered to ${filtered.length} products`);
        }

        // Apply sorting
        const sorted = sortProducts(filtered);
        setFilteredProducts(sorted);

        // Categorize all products for category view
        const categorized = categorizeProducts(products);
        setCategorizedProducts(categorized);

        // console.log(`✅ REAL: Filtered to ${sorted.length} products, categorized into ${Object.keys(categorized).length} categories`);

      } catch (error) {
        console.error('❌ REAL: Error filtering/categorizing products:', error);
        setFilteredProducts([]);
        setCategorizedProducts({});
      }
    }
  }, [products, categories, searchQuery, selectedCategory, sortBy]);





  const sortOptions = [
    { id: 'newest', name: 'Newest First', icon: 'time' },
    { id: 'price_low', name: 'Price: Low to High', icon: 'arrow-up' },
    { id: 'price_high', name: 'Price: High to Low', icon: 'arrow-down' },
    { id: 'rating', name: 'Highest Rated', icon: 'star' },
    { id: 'popular', name: 'Most Popular', icon: 'trending-up' },
  ];

  // REAL: Initialize marketplace with prefetching
  useEffect(() => {
    // console.log('⚡ REAL: Marketplace initialized with lightning data');

    // Prefetch related data for likely user actions
    if (prefetchForPage) {
      prefetchForPage('product-details', 'normal');
      prefetchForPage('cart', 'low');
      prefetchForPage('wishlist', 'low');
    }

    // Log data source info
    if (productsInstant) {
      // console.log('⚡ REAL: Products loaded instantly from cache');
    } else {
      // console.log(`📡 REAL: Products loaded from ${productsSource} source`);
    }
  }, [productsInstant, productsSource, prefetchForPage]);

  // WebSocket setup for real-time updates (optional)
  useEffect(() => {
    // WebSocket disabled for marketplace to improve performance
    // Real-time updates can be added later if needed
    // console.log('📱 Marketplace loaded without WebSocket for better performance');

    // Set up real-time marketplace handlers
    const handleNewProduct = (product) => {
      // console.log('🛍️ New product added:', product);
      // Refresh products when new product is added
      refreshProducts();
    };

    const handleProductUpdate = (product) => {
      // console.log('📝 Product updated:', product);
      // Refresh products when product is updated
      refreshProducts();
    };

    const handleProductDeleted = (productId) => {
      // console.log('🗑️ Product deleted:', productId);
      // Remove product from local state - this will be handled by lightning data
      refreshProducts();
    };

    // Register WebSocket handlers for marketplace (with safety checks)
    if (webSocketService) {
      webSocketService.onMessage('new_product', handleNewProduct);
      webSocketService.onMessage('product_updated', handleProductUpdate);
      webSocketService.onMessage('product_deleted', handleProductDeleted);
    }

    // Cleanup WebSocket handlers
    return () => {
      if (webSocketService && typeof webSocketService.offMessage === 'function') {
        webSocketService.offMessage('new_product');
        webSocketService.offMessage('product_updated');
        webSocketService.offMessage('product_deleted');
      }
    };
  }, [refreshProducts]);

  // REAL: Load categorized products when switching to category view
  useEffect(() => {
    if (viewMode === 'categories' && products.length > 0 && categories.length > 0) {
      // console.log('🏷️ REAL: Switching to category view, updating categorized products');
      const categorized = categorizeProducts(products);
      setCategorizedProducts(categorized);
    }
  }, [viewMode, products, categories]);


  const sortProducts = (products, sortType) => {
    const sorted = [...products];

    switch (sortType) {
      case 'price_low':
        return sorted.sort((a, b) => a.price - b.price);
      case 'price_high':
        return sorted.sort((a, b) => b.price - a.price);
      case 'rating':
        return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      case 'popular':
        return sorted.sort((a, b) => (b.total_sales || 0) - (a.total_sales || 0));
      case 'newest':
      default:
        return sorted.sort((a, b) => new Date(b.created_at || b.createdAt) - new Date(a.created_at || a.createdAt));
    }
  };

  // REAL: Categorize products using real categories data
  const categorizeProducts = (productsToProcess) => {
    // console.log(`🏷️ REAL: Categorizing ${productsToProcess.length} products with ${categories.length} categories`);
    const categorized = {};

    categories.forEach(category => {
      if (category.id !== 'all') {
        const categoryProducts = productsToProcess.filter(product =>
          product.category === category.id ||
          product.category_id === category.id ||
          (product.category && product.category.toLowerCase() === category.id.toLowerCase())
        );

        categorized[category.id] = {
          category: category,
          products: categoryProducts.slice(0, 20), // Show first 20 products per category
          totalCount: categoryProducts.length // Track total available products in this category
        };

        // console.log(`🏷️ Category ${category.name}: ${categoryProducts.length} products`);
      }
    });

    // console.log(`✅ REAL: Categorized products into ${Object.keys(categorized).length} categories`);
    return categorized;
  };



  // REAL: Refresh marketplace data using lightning data service
  const onRefresh = async () => {
    // console.log('🔄 REAL: Refreshing marketplace data...');
    setRefreshing(true);
    setLocalLoading(true);
    setDataReady(false);

    try {
      // Refresh both products and categories in parallel
      await Promise.all([
        refreshProducts({ forceRefresh: true }),
        refreshCategories({ forceRefresh: true })
      ]);

      // console.log('✅ REAL: Marketplace refresh completed');
    } catch (error) {
      console.error('❌ REAL: Marketplace refresh failed:', error);
      Alert.alert('Error', 'Failed to refresh marketplace: ' + error.message);
    } finally {
      setRefreshing(false);
    }
  };

  // REAL: Search is now handled by the filtering useEffect
  const handleSearch = () => {
    // console.log('🔍 REAL: Search triggered, filtering will be handled automatically');
    // Search filtering is now handled automatically by the useEffect
  };

  // REAL: Test categories API directly
  const testCategoriesAPI = async () => {
    // console.log('🧪 REAL: Testing categories API directly...');
    setLocalLoading(true);

    try {
      const categoriesResult = await apiService.getMarketplaceCategories();
      // console.log('🧪 REAL: Direct categories API test result:', {
      //   success: categoriesResult.success,
      //   dataLength: Array.isArray(categoriesResult.data) ? categoriesResult.data.length : 'not array',
      //   source: categoriesResult.source,
      //   error: categoriesResult.error || 'none'
      // });

      if (categoriesResult.success && categoriesResult.data) {
        // console.log('✅ REAL: Direct categories API test successful');
        // console.log('🏷️ REAL: Categories:', categoriesResult.data.map(c => `${c.name} (${c.productCount || 0})`));

        Alert.alert('Success', `Loaded ${categoriesResult.data.length} categories directly from API\nSource: ${categoriesResult.source}`);
      } else {
        console.error('❌ REAL: Direct categories API test failed:', categoriesResult.error);
        Alert.alert('Categories API Test Failed', categoriesResult.error || 'Unknown error');
      }
    } catch (error) {
      console.error('❌ REAL: Direct categories API test error:', error);
      Alert.alert('Categories API Test Error', error.message);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleAddToCart = async (product) => {
    try {
      // console.log('⚡ Adding to cart with optimistic update:', product.name);

      // Use optimistic update for instant UI feedback
      await addToCart(product.id, 1);

      Alert.alert('Success', `${product.name} added to cart instantly!`);
    } catch (error) {
      console.error('❌ Failed to add to cart:', error);
      Alert.alert('Error', 'Failed to add to cart. Please try again.');
    }
  };

  const handleAddToWishlist = async (product) => {
    try {
      // console.log('⚡ Adding to wishlist with optimistic update:', product.name);

      // Use optimistic update for instant UI feedback
      await addToWishlist(product.id);

      Alert.alert('Success', `${product.name} added to wishlist!`);
    } catch (error) {
      console.error('❌ Failed to add to wishlist:', error);
      Alert.alert('Error', 'Failed to add to wishlist. Please try again.');
    }
  };



  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get category-specific placeholder images optimized for compact cards
  const getPlaceholderImage = (category, productName) => {
    const categoryImages = {
      'agriculture': 'https://via.placeholder.com/200x150/4CAF50/white?text=🌾',
      'food_beverage': 'https://via.placeholder.com/200x150/FF9800/white?text=🍽️',
      'clothing': 'https://via.placeholder.com/200x150/2196F3/white?text=👕',
      'electronics': 'https://via.placeholder.com/200x150/9C27B0/white?text=📱',
      'home_garden': 'https://via.placeholder.com/200x150/4CAF50/white?text=🏡',
      'health_beauty': 'https://via.placeholder.com/200x150/E91E63/white?text=💄',
      'sports_outdoors': 'https://via.placeholder.com/200x150/FF5722/white?text=⚽',
      'automotive': 'https://via.placeholder.com/200x150/607D8B/white?text=🚗',
      'services': 'https://via.placeholder.com/200x150/795548/white?text=🔧',
      'other': 'https://via.placeholder.com/200x150/9E9E9E/white?text=📦'
    };

    return categoryImages[category] || categoryImages['other'];
  };

  // Format product rating with proper handling of different data types
  const formatRating = (item) => {
    // Debug: Log the item structure to understand rating data
    if (item.id === 1) { // Only log for first product to avoid spam
      // console.log('🌟 RATING DEBUG: Product rating data:', {
      //   id: item.id,
      //   name: item.name,
      //   rating: item.rating,
      //   average_rating: item.average_rating,
      //   avgRating: item.avgRating,
      //   stars: item.stars,
      //   reviews: item.reviews ? `${item.reviews.length} reviews` : 'no reviews',
      //   review_count: item.review_count
      // });
    }

    // Check multiple possible rating sources
    let rating = item.rating || item.average_rating || item.avgRating || item.stars;

    // If no direct rating, check if there are reviews to calculate from
    if (!rating && item.reviews && Array.isArray(item.reviews) && item.reviews.length > 0) {
      const totalRating = item.reviews.reduce((sum, review) => sum + (parseFloat(review.rating) || 0), 0);
      rating = totalRating / item.reviews.length;
      // console.log(`🌟 CALCULATED: Rating ${rating.toFixed(1)} from ${item.reviews.length} reviews for ${item.name}`);
    }

    // If still no rating, check review count
    if (!rating && item.review_count > 0) {
      // For products with reviews but no rating data, show a default
      // console.log(`🌟 DEFAULT: Using default rating for ${item.name} (has ${item.review_count} reviews)`);
      return '4.0'; // Conservative default for products with reviews
    }

    if (!rating) {
      // console.log(`🌟 NEW: Product ${item.name} has no rating data - showing as New`);
      return 'New'; // Show 'New' for products without any rating data
    }

    const numRating = parseFloat(rating);
    if (isNaN(numRating)) return 'N/A';

    // Ensure rating is between 0 and 5
    const clampedRating = Math.max(0, Math.min(5, numRating));

    // console.log(`🌟 FINAL: Product ${item.name} rating: ${clampedRating.toFixed(1)}`);
    return clampedRating.toFixed(1);
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <View style={styles.headerContent}>
        {/* Compact Search Box */}
        <View style={[styles.searchBox, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
          <Ionicons name="search" size={18} color={colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search products..."
            placeholderTextColor={colors.textTertiary}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
          />
        </View>

        {/* Three-dot Menu */}
        <TouchableOpacity
          style={[styles.headerMenuButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setShowHeaderMenu(!showHeaderMenu)}
        >
          <Ionicons name="ellipsis-vertical" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Header Menu Dropdown */}
      {showHeaderMenu && (
        <View style={[styles.headerMenuDropdown, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowHeaderMenu(false);
              // Direct navigation to AddProduct - registration will happen automatically if needed
              navigation.navigate('AddProduct');
            }}
          >
            <Ionicons name="add-circle" size={18} color={colors.success} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>Sell Product</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowHeaderMenu(false);
              navigation.navigate('Cart');
            }}
          >
            <Ionicons name="cart" size={18} color={colors.primary} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>
              Cart {cartItems.length > 0 && `(${cartItems.length})`}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowHeaderMenu(false);
              navigation.navigate('Wishlist');
            }}
          >
            <Ionicons name="heart" size={18} color={colors.error} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>Wishlist</Text>
          </TouchableOpacity>

          {/* Test Categories API Button */}
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowHeaderMenu(false);
              testCategoriesAPI();
            }}
          >
            <Ionicons name="flask" size={18} color={colors.warning} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>Test Categories API</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Overlay to close menu */}
      {showHeaderMenu && (
        <TouchableOpacity
          style={styles.menuOverlay}
          onPress={() => setShowHeaderMenu(false)}
          activeOpacity={1}
        />
      )}
    </View>
  );

  const renderControls = () => (
    <View style={styles.controlsContainer}>
      <View style={styles.viewModeContainer}>
        {/* Sort Options */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.sortContainer}
        >
          <TouchableOpacity
            style={[
              styles.viewModeButton,
              {
                backgroundColor: viewMode === 'grid' ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setViewMode('grid')}
          >
            <Ionicons
              name="grid"
              size={16}
              color={viewMode === 'grid' ? colors.white : colors.textSecondary}
            />
            <Text style={[
              styles.viewModeText,
              { color: viewMode === 'grid' ? colors.white : colors.textSecondary }
            ]}>
              Grid
            </Text>
          </TouchableOpacity>
          {sortOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.sortItem,
                {
                  backgroundColor: sortBy === option.id ? colors.success : colors.backgroundSecondary,
                  borderColor: colors.border,
                }
              ]}
              onPress={() => setSortBy(option.id)}
            >
              <Ionicons
                name={option.icon}
                size={14}
                color={sortBy === option.id ? colors.white : colors.textSecondary}
              />
              <Text style={[
                styles.sortText,
                { color: sortBy === option.id ? colors.white : colors.textSecondary }
              ]}>
                {option.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Categories Filter (only show when not in category view mode) */}
      {viewMode === 'grid' && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
        >
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryItem,
                {
                  backgroundColor: selectedCategory === category.id ? colors.primary : colors.backgroundSecondary,
                  borderColor: colors.border,
                }
              ]}
              onPress={() => setSelectedCategory(category.id)}
            >
              <Ionicons
                name={category.icon}
                size={20}
                color={selectedCategory === category.id ? colors.white : colors.textSecondary}
              />
              <Text style={[
                styles.categoryText,
                { color: selectedCategory === category.id ? colors.white : colors.textSecondary }
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </View>
  );

  const renderProduct = ({ item, isHorizontal = false }) => (
    <TouchableOpacity
      style={[
        isHorizontal ? styles.horizontalProductCard : styles.productCard,
        { backgroundColor: colors.surface }
      ]}
      onPress={() => navigation.navigate('ProductDetails', { product: item })}
    >
      {/* Large Product Image - Takes Most Space */}
      <View style={isHorizontal ? styles.horizontalProductImageContainer : styles.productImageContainer}>
        <Image
          source={{
            uri: item.images?.[0] || getPlaceholderImage(item.category, item.name)
          }}
          style={isHorizontal ? styles.horizontalProductImage : styles.productImage}
          resizeMode="cover"
          loadingIndicatorSource={{
            uri: 'https://via.placeholder.com/200x150/f5f5f5/cccccc?text=Loading...'
          }}
          // onLoadStart={() => console.log('🖼️ Loading product image:', item.name)}
          // onLoad={() => console.log('✅ Product image loaded:', item.name)}
          onError={(error) => {
            // console.log('❌ Product image failed:', item.name, error);
          }}
        />

        {/* Wishlist Heart - Top Right */}
        <TouchableOpacity
          style={[styles.wishlistOverlay, { backgroundColor: colors.surface + 'E6' }]}
          onPress={() => handleAddToWishlist(item)}
        >
          <Ionicons name="heart-outline" size={14} color={colors.error} />
        </TouchableOpacity>

        {/* Stock Badge - Top Left */}
        {item.stock <= 5 && item.stock > 0 && (
          <View style={[styles.stockBadge, { backgroundColor: colors.warning + 'E6' }]}>
            <Text style={[styles.stockBadgeText, { color: colors.surface }]}>
              {item.stock}
            </Text>
          </View>
        )}

        {item.stock === 0 && (
          <View style={[styles.stockBadge, { backgroundColor: colors.error + 'E6' }]}>
            <Text style={[styles.stockBadgeText, { color: colors.surface }]}>
              0
            </Text>
          </View>
        )}

      </View>

      {/* Compact Product Info - Essential Details in Description Section */}
      <View style={isHorizontal ? styles.horizontalProductInfo : styles.compactProductInfo}>
        <Text style={[
          isHorizontal ? styles.horizontalProductName : styles.compactProductName,
          { color: colors.text }
        ]} numberOfLines={1}>
          {item.name}
        </Text>

        {/* Price in Description Section */}
        <Text style={[styles.compactPrice, { color: colors.primary }]}>
          {formatCurrency(item.price)}
        </Text>

        {/* Rating and Add Button Row */}
        <View style={styles.compactBottomRow}>
          <View style={styles.compactRatingContainer}>
            {formatRating(item) === 'New' ? (
              <>
                <Ionicons name="sparkles" size={10} color={colors.success} />
                <Text style={[styles.compactRatingText, { color: colors.success }]}>
                  New
                </Text>
              </>
            ) : (
              <>
                <Ionicons name="star" size={10} color={colors.warning} />
                <Text style={[styles.compactRatingText, { color: colors.textSecondary }]}>
                  {formatRating(item)}
                </Text>
              </>
            )}
          </View>

          {/* Quick Add Button - Compact */}
          <TouchableOpacity
            style={[
              styles.compactAddButton,
              {
                backgroundColor: item.stock === 0 ? colors.backgroundSecondary : colors.primary,
                opacity: item.stock === 0 ? 0.5 : 1
              }
            ]}
            onPress={() => handleAddToCart(item)}
            disabled={item.stock === 0}
          >
            <Ionicons
              name={item.stock === 0 ? "ban" : "add"}
              size={12}
              color={item.stock === 0 ? colors.textSecondary : colors.surface}
            />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryRow = ({ item }) => {
    const { category, products, totalCount } = item;

    if (!products || products.length === 0) return null;

    return (
      <View style={styles.categoryRow}>
        <View style={styles.categoryHeader}>
          <View style={styles.categoryTitleContainer}>
            <Ionicons name={category.icon} size={20} color={colors.primary} />
            <Text style={[styles.categoryTitle, { color: colors.text }]}>
              {category.name}
            </Text>
            <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>
              ({products.length}{totalCount && totalCount > products.length ? `/${totalCount}` : ''})
            </Text>
          </View>

          <TouchableOpacity
            onPress={() => {
              setSelectedCategory(category.id);
              setViewMode('grid');
              // Reload products for this specific category
              setProducts([]);
              setHasMoreProducts(true);
            }}
          >
            <Text style={[styles.viewAllText, { color: colors.primary }]}>
              View All {totalCount && totalCount > products.length ? `(${totalCount})` : ''}
            </Text>
          </TouchableOpacity>
        </View>

        <FlatList
          data={products}
          renderItem={({ item }) => renderProduct({ item, isHorizontal: true })}
          keyExtractor={(product) => product.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.horizontalProductsList}
          ItemSeparatorComponent={() => <View style={{ width: spacing.sm }} />}
        />

        {/* Separator line */}
        <View style={[styles.categorySeparator, { backgroundColor: colors.border }]} />
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="storefront-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {error ? 'Unable to Load Products' : 'No Products Found'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {error
          ? 'Please check your internet connection and try again'
          : searchQuery
            ? 'Try adjusting your search terms or browse different categories'
            : 'Be the first to list a product in this marketplace!'
        }
      </Text>


      {error ? (
        <Button
          title="Retry"
          onPress={onRefresh}
          style={styles.listProductButton}
          icon="refresh"
        />
      ) : (
        <Button
          title="+ List Your Product"
          onPress={() => {
            // Direct navigation to AddProduct - registration will happen automatically if needed
            navigation.navigate('AddProduct');
          }}
          style={styles.listProductButton}
          icon="add"
        />
      )}

      {searchQuery && !error && (
        <Button
          title="Clear Search"
          onPress={() => {
            // console.log('🔍 REAL: Clearing search and filters');
            setSearchQuery('');
            setSelectedCategory('all');
            // Filtering will be handled automatically by useEffect
          }}
          variant="outline"
          style={styles.clearSearchButton}
        />
      )}
    </View>
  );

  const renderCategoriesView = () => {
    const categoryData = Object.values(categorizedProducts).filter(cat => cat.products.length > 0);

    // Show loading indicator while data is being loaded
    if (isActuallyLoading && categoryData.length === 0) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading products and categories...
          </Text>
          <Text style={[styles.loadingSubText, { color: colors.textSecondary }]}>
            Source: {productsSource || 'loading'}
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={categoryData}
        renderItem={renderCategoryRow}
        keyExtractor={(item) => item.category.id}
        style={{ flex: 1 }}
        contentContainerStyle={[
          styles.categoriesList,
          categoryData.length === 0 && { flexGrow: 1 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || isActuallyLoading}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={!isActuallyLoading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  const renderGridView = () => {
    return (
      <FlatList
        data={filteredProducts}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        numColumns={optimalColumns} // Responsive columns based on screen size
        key={`grid-${filteredProducts.length}-${optimalColumns}`} // Include columns in key for re-render
        style={{ flex: 1 }}
        contentContainerStyle={[
          styles.productsList,
          filteredProducts.length === 0 && { flexGrow: 1 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={!isActuallyLoading && renderEmptyState()}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={isActuallyLoading && (
          <View style={styles.loadingFooter}>
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading products...
            </Text>
          </View>
        )}
        // Performance optimizations
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={100}
        initialNumToRender={10}
        windowSize={5}
        getItemLayout={(data, index) => ({
          length: 200, // Approximate item height
          offset: 200 * index,
          index,
        })}
      />
    );
  };

  // Removed renderContent function - using direct conditional rendering for better performance

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Static Header */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface }]}>
        {renderHeader()}
        {renderControls()}
      </View>

      {/* Scrollable Content Area */}
      <View style={styles.contentContainer}>
        {viewMode === 'categories' && selectedCategory === 'all' ? (
          <View key="categories-container" style={styles.scrollableContent}>
            {renderCategoriesView()}
          </View>
        ) : (
          <View key="grid-container" style={styles.scrollableContent}>
            {renderGridView()}
          </View>
        )}
      </View>

      {/* Floating Action Menu */}
      {fabMenuOpen && (
        <View style={styles.fabMenuOverlay}>
          <TouchableOpacity
            style={styles.fabMenuBackdrop}
            onPress={() => setFabMenuOpen(false)}
            activeOpacity={1}
          />

          <View style={styles.fabMenuContainer}>
            <TouchableOpacity
              style={[styles.fabMenuItem, { backgroundColor: colors.primary }]}
              onPress={() => {
                setFabMenuOpen(false);
                navigation.navigate('ManageOrders');
              }}
            >
              <Ionicons name="list" size={20} color={colors.white} />
              <Text style={[styles.fabMenuText, { color: colors.white }]}>
                Orders
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.fabMenuItem, { backgroundColor: colors.info }]}
              onPress={() => {
                setFabMenuOpen(false);
                navigation.navigate('SellerAnalytics');
              }}
            >
              <Ionicons name="analytics" size={20} color={colors.white} />
              <Text style={[styles.fabMenuText, { color: colors.white }]}>
                Analytics
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.fabMenuItem, { backgroundColor: colors.info }]}
              onPress={() => {
                setFabMenuOpen(false);
                navigation.navigate('MyPurchases');
              }}
            >
              <Ionicons name="bag" size={20} color={colors.white} />
              <Text style={[styles.fabMenuText, { color: colors.white }]}>
                Purchases
              </Text>
            </TouchableOpacity>
               <TouchableOpacity
              style={[styles.fabMenuItem, { backgroundColor: colors.info }]}
              onPress={() => {
                setFabMenuOpen(false);
                navigation.navigate('SellerDashboard');
              }}
            >
              <Ionicons name="storefront" size={20} color={colors.white} />
              <Text style={[styles.fabMenuText, { color: colors.white }]}>
                Sales
              </Text>
            </TouchableOpacity>         
            <TouchableOpacity
              style={[styles.fabMenuItem, { backgroundColor: colors.warning }]}
              onPress={() => {
                setFabMenuOpen(false);
                navigation.navigate('DeliveryManagement');
              }}
            >
              <Ionicons name="car" size={20} color={colors.white} />
              <Text style={[styles.fabMenuText, { color: colors.white }]}>
                Delivery
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.fabMenuItem, { backgroundColor: colors.success }]}
              onPress={() => {
                setFabMenuOpen(false);
                navigation.navigate('AddProduct');
              }}
            >
              <Ionicons name="add" size={20} color={colors.white} />
              <Text style={[styles.fabMenuText, { color: colors.white }]}>
                Product
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => setFabMenuOpen(!fabMenuOpen)}
        activeOpacity={0.8}
      >
        <Ionicons
          name={fabMenuOpen ? "close" : "menu"}
          size={28}
          color={colors.white}
        />
      </TouchableOpacity>

      {/* Registration modal removed - users can browse products without registration */}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  contentContainer: {
    flex: 1,
  },
  scrollableContent: {
    flex: 1,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    borderWidth: 1,
    height: 36,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing.xs,
    fontSize: typography.fontSize.sm,
  },
  headerMenuButton: {
    width: 36,
    height: 36,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerMenuDropdown: {
    position: 'absolute',
    top: 50,
    right: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    paddingVertical: spacing.xs,
    minWidth: 160,
    zIndex: 1001,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  menuItemText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  menuOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },

  roleNavContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  roleNavButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    ...shadows.sm,
  },
  roleNavContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleNavText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm,
  },
  dashboardContainer: {
    marginBottom: spacing.md,
  },
  dashboardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
  },
  dashboardScroll: {
    marginHorizontal: -spacing.md,
    paddingHorizontal: spacing.md,
  },
  dashboardCard: {
    width: 120,
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    alignItems: 'center',
    borderWidth: 1,
    ...shadows.sm,
  },
  dashboardCardTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  dashboardCardSubtitle: {
    fontSize: typography.fontSize.xs,
    marginTop: spacing.xs,
    textAlign: 'center',
    opacity: 0.8,
  },
  categoriesContainer: {
    marginHorizontal: -spacing.md,
    paddingHorizontal: spacing.md,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    borderWidth: 1,
  },
  categoryText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  controlsContainer: {
    paddingHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  viewModeContainer: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  viewModeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
  },
  viewModeText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  sortContainer: {
    marginBottom: spacing.sm,
  },
  sortItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginRight: spacing.xs,
    borderRadius: borderRadius.md,
    borderWidth: 1,
  },
  sortText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  productsList: {
    padding: spacing.sm, // Reduced padding for tighter grid
    paddingHorizontal: spacing.xs, // Less horizontal padding
  },
  productCard: {
    flex: 1,
    margin: spacing.xs / 2, // Reduced margin for tighter grid
    borderRadius: borderRadius.md,
    overflow: 'hidden',
    ...shadows.sm, // Reduced shadow for cleaner look
    backgroundColor: 'transparent',
    borderWidth: 0.5,
    borderColor: 'rgba(0,0,0,0.05)',
    minWidth: 130, // Minimum width to ensure readability
    maxWidth: 200, // Maximum width to prevent cards from being too wide
  },
  productImageContainer: {
    position: 'relative',
    width: '100%',
    height: 100, // Match horizontal cards height for consistency
    backgroundColor: '#f8f9fa',
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: borderRadius.md,
    borderTopRightRadius: borderRadius.md,
    // Ensure no spacing issues
    margin: 0,
    padding: 0,
  },
  wishlistOverlay: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
    width: 24, // Smaller for compact design
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.sm,
  },
  stockBadge: {
    position: 'absolute',
    top: spacing.xs,
    left: spacing.xs,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.sm,
    minWidth: 20,
    alignItems: 'center',
    ...shadows.sm,
  },
  stockBadgeText: {
    fontSize: 10, // Very small for compact design
    fontWeight: typography.fontWeight.bold,
  },
  // Compact product info - minimal space with price in description
  compactProductInfo: {
    padding: spacing.xs,
    paddingTop: 0, // Remove top padding to eliminate whitespace gap
  },
  compactProductName: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs / 2,
    marginTop: spacing.xs / 2, // Add small top margin for proper spacing from image
    lineHeight: 14,
  },
  compactPrice: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs / 2,
  },
  compactBottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  compactRatingText: {
    fontSize: 10,
    marginLeft: spacing.xs / 2,
  },
  compactAddButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.xs,
  },

  // Legacy styles for horizontal cards (keep for compatibility)
  productInfo: {
    padding: spacing.sm,
    flex: 1,
  },
  productName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
    minHeight: 32,
    lineHeight: 16,
  },
  productPriceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  productPrice: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: typography.fontSize.xs,
    marginLeft: spacing.xs / 2,
  },
  quickAddButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
    marginTop: spacing.xs,
  },
  quickAddText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  // Category row styles
  categoriesList: {
    paddingBottom: spacing.xl,
  },
  categoryRow: {
    marginBottom: spacing.lg,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  categoryTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginLeft: spacing.sm,
  },
  categoryCount: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
  },
  viewAllText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  horizontalProductsList: {
    paddingHorizontal: spacing.md,
  },
  categorySeparator: {
    height: 1,
    marginTop: spacing.md,
    marginHorizontal: spacing.md,
  },
  // Horizontal product card styles
  horizontalProductCard: {
    width: 140, // Reduced width for more cards in horizontal scroll
    borderRadius: borderRadius.md,
    overflow: 'hidden',
    marginBottom: spacing.sm,
    ...shadows.sm,
    backgroundColor: 'transparent',
    borderWidth: 0.5,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  horizontalProductImageContainer: {
    position: 'relative',
    width: '100%',
    height: 100, // Compact height for horizontal cards
    backgroundColor: '#f8f9fa',
  },
  horizontalProductImage: {
    width: '100%',
    height: '100%', // Fill the container completely
    borderTopLeftRadius: borderRadius.md,
    borderTopRightRadius: borderRadius.md,
    margin: 0,
    padding: 0,
  },
  horizontalProductInfo: {
    padding: spacing.xs,
    paddingTop: 0, // Remove top padding to eliminate whitespace gap
  },
  horizontalProductName: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs / 2,
    marginTop: spacing.xs / 2, // Add small top margin for proper spacing from image
    lineHeight: 14,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  listProductButton: {
    marginTop: spacing.md,
  },
  clearSearchButton: {
    marginTop: spacing.sm,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
    elevation: 8,
    zIndex: 1000,
  },
  fabMenuOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  fabMenuBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  fabMenuContainer: {
    position: 'absolute',
    bottom: spacing.xl + 70,
    right: spacing.xl,
    alignItems: 'flex-end',
  },
  fabMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.sm,
    minWidth: 120,
    ...shadows.md,
  },
  fabMenuText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm,
  },
  loadingFooter: {
    padding: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  loadingText: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  loadingSubText: {
    fontSize: typography.fontSize.xs,
    textAlign: 'center',
    marginTop: spacing.xs,
    opacity: 0.7,
  },
});

export default MarketplaceScreen;
