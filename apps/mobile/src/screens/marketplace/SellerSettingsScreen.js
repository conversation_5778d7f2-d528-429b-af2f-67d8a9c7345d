import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  SafeAreaView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius } from '../../utils/theme';
import Card from '../../components/common/Card';

const SellerSettingsScreen = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [userRole, setUserRole] = useState(user?.role || 'buyer');
  const [sellerSettings, setSellerSettings] = useState({
    autoAcceptOrders: false,
    emailNotifications: true,
    smsNotifications: false,
    inventoryAlerts: true,
    lowStockThreshold: 10,
    businessHours: {
      enabled: true,
      start: '09:00',
      end: '18:00',
    },
    deliveryOptions: {
      selfPickup: true,
      homeDelivery: true,
      courierService: false,
    },
    paymentMethods: {
      cash: true,
      mpesa: true,
      bankTransfer: false,
    },
  });

  const [buyerSettings, setBuyerSettings] = useState({
    orderNotifications: true,
    promotionalEmails: false,
    wishlistAlerts: true,
    priceDropAlerts: true,
    deliveryUpdates: true,
    reviewReminders: false,
  });

  useEffect(() => {
    loadUserSettings();
  }, []);

  const loadUserSettings = async () => {
    try {
      // TODO: Implement API call to load user settings
      console.log('Loading user settings...');
    } catch (error) {
      console.error('Failed to load user settings:', error);
    }
  };

  const handleSaveSettings = async () => {
    try {
      // TODO: Implement API call to save settings
      const settingsToSave = userRole === 'seller' ? sellerSettings : buyerSettings;
      console.log('Saving settings:', settingsToSave);
      Alert.alert('Success', 'Settings saved successfully!');
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const handleRoleSwitch = (newRole) => {
    Alert.alert(
      'Switch Role',
      `Switch to ${newRole} mode? This will change your available features and settings.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Switch', 
          onPress: () => {
            setUserRole(newRole);
            // TODO: Update user role in context/API
          }
        },
      ]
    );
  };

  const renderSellerSettings = () => (
    <>
      {/* Business Settings */}
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Business Settings
        </Text>
        
        <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Auto Accept Orders</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Automatically accept new orders without manual review
            </Text>
          </View>
          <Switch
            value={sellerSettings.autoAcceptOrders}
            onValueChange={(value) => setSellerSettings(prev => ({ ...prev, autoAcceptOrders: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={sellerSettings.autoAcceptOrders ? colors.primary : colors.textSecondary}
          />
        </View>

        <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Business Hours</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Set your business operating hours
            </Text>
          </View>
          <Switch
            value={sellerSettings.businessHours.enabled}
            onValueChange={(value) => setSellerSettings(prev => ({ 
              ...prev, 
              businessHours: { ...prev.businessHours, enabled: value }
            }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={sellerSettings.businessHours.enabled ? colors.primary : colors.textSecondary}
          />
        </View>

        <View style={[styles.settingItem, { borderBottomWidth: 0 }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Low Stock Threshold</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Get notified when inventory is low
            </Text>
          </View>
          <TextInput
            style={[styles.numberInput, { backgroundColor: colors.surface, borderColor: colors.border, color: colors.text }]}
            value={sellerSettings.lowStockThreshold.toString()}
            onChangeText={(text) => setSellerSettings(prev => ({ 
              ...prev, 
              lowStockThreshold: parseInt(text) || 0 
            }))}
            keyboardType="numeric"
            placeholder="10"
            placeholderTextColor={colors.textSecondary}
          />
        </View>
      </Card>

      {/* Notifications */}
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Notifications
        </Text>
        
        <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Email Notifications</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Receive order updates via email
            </Text>
          </View>
          <Switch
            value={sellerSettings.emailNotifications}
            onValueChange={(value) => setSellerSettings(prev => ({ ...prev, emailNotifications: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={sellerSettings.emailNotifications ? colors.primary : colors.textSecondary}
          />
        </View>

        <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>SMS Notifications</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Receive urgent updates via SMS
            </Text>
          </View>
          <Switch
            value={sellerSettings.smsNotifications}
            onValueChange={(value) => setSellerSettings(prev => ({ ...prev, smsNotifications: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={sellerSettings.smsNotifications ? colors.primary : colors.textSecondary}
          />
        </View>

        <View style={[styles.settingItem, { borderBottomWidth: 0 }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Inventory Alerts</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Get notified about stock levels
            </Text>
          </View>
          <Switch
            value={sellerSettings.inventoryAlerts}
            onValueChange={(value) => setSellerSettings(prev => ({ ...prev, inventoryAlerts: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={sellerSettings.inventoryAlerts ? colors.primary : colors.textSecondary}
          />
        </View>
      </Card>

      {/* Quick Actions */}
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Quick Actions
        </Text>
        
        <TouchableOpacity
          style={[styles.actionItem, { borderBottomColor: colors.border }]}
          onPress={() => navigation.navigate('SellerAnalytics')}
        >
          <View style={styles.actionContent}>
            <Ionicons name="analytics" size={24} color={colors.primary} />
            <View style={styles.actionText}>
              <Text style={[styles.actionTitle, { color: colors.text }]}>Sales Analytics</Text>
              <Text style={[styles.actionDescription, { color: colors.textSecondary }]}>
                View your sales performance
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionItem, { borderBottomWidth: 0 }]}
          onPress={() => navigation.navigate('DeliveryManagement')}
        >
          <View style={styles.actionContent}>
            <Ionicons name="car" size={24} color={colors.info} />
            <View style={styles.actionText}>
              <Text style={[styles.actionTitle, { color: colors.text }]}>Delivery Management</Text>
              <Text style={[styles.actionDescription, { color: colors.textSecondary }]}>
                Manage deliveries and logistics
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </Card>
    </>
  );

  const renderBuyerSettings = () => (
    <>
      {/* Shopping Preferences */}
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Shopping Preferences
        </Text>
        
        <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Order Notifications</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Get updates about your orders
            </Text>
          </View>
          <Switch
            value={buyerSettings.orderNotifications}
            onValueChange={(value) => setBuyerSettings(prev => ({ ...prev, orderNotifications: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={buyerSettings.orderNotifications ? colors.primary : colors.textSecondary}
          />
        </View>

        <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Wishlist Alerts</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Get notified about wishlist items
            </Text>
          </View>
          <Switch
            value={buyerSettings.wishlistAlerts}
            onValueChange={(value) => setBuyerSettings(prev => ({ ...prev, wishlistAlerts: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={buyerSettings.wishlistAlerts ? colors.primary : colors.textSecondary}
          />
        </View>

        <View style={[styles.settingItem, { borderBottomWidth: 0 }]}>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>Price Drop Alerts</Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Get notified when prices drop
            </Text>
          </View>
          <Switch
            value={buyerSettings.priceDropAlerts}
            onValueChange={(value) => setBuyerSettings(prev => ({ ...prev, priceDropAlerts: value }))}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={buyerSettings.priceDropAlerts ? colors.primary : colors.textSecondary}
          />
        </View>
      </Card>

      {/* Quick Actions */}
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Quick Actions
        </Text>
        
        <TouchableOpacity
          style={[styles.actionItem, { borderBottomColor: colors.border }]}
          onPress={() => navigation.navigate('MyPurchases')}
        >
          <View style={styles.actionContent}>
            <Ionicons name="receipt" size={24} color={colors.primary} />
            <View style={styles.actionText}>
              <Text style={[styles.actionTitle, { color: colors.text }]}>My Purchases</Text>
              <Text style={[styles.actionDescription, { color: colors.textSecondary }]}>
                View your order history
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionItem, { borderBottomWidth: 0 }]}
          onPress={() => navigation.navigate('WishlistScreen')}
        >
          <View style={styles.actionContent}>
            <Ionicons name="heart" size={24} color={colors.error} />
            <View style={styles.actionText}>
              <Text style={[styles.actionTitle, { color: colors.text }]}>Wishlist</Text>
              <Text style={[styles.actionDescription, { color: colors.textSecondary }]}>
                Manage your saved items
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </Card>
    </>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Ionicons name="settings" size={32} color={colors.primary} />
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {userRole === 'seller' ? 'Seller Settings' : 'Buyer Settings'}
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            Customize your {userRole} experience
          </Text>
        </View>

        {/* Role Switcher */}
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Account Type
          </Text>
          <View style={styles.roleSwitcher}>
            <TouchableOpacity
              style={[
                styles.roleButton,
                {
                  backgroundColor: userRole === 'buyer' ? colors.primary : colors.surface,
                  borderColor: userRole === 'buyer' ? colors.primary : colors.border,
                },
              ]}
              onPress={() => handleRoleSwitch('buyer')}
            >
              <Text
                style={[
                  styles.roleButtonText,
                  { color: userRole === 'buyer' ? colors.white : colors.text },
                ]}
              >
                Buyer
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.roleButton,
                {
                  backgroundColor: userRole === 'seller' ? colors.primary : colors.surface,
                  borderColor: userRole === 'seller' ? colors.primary : colors.border,
                },
              ]}
              onPress={() => handleRoleSwitch('seller')}
            >
              <Text
                style={[
                  styles.roleButtonText,
                  { color: userRole === 'seller' ? colors.white : colors.text },
                ]}
              >
                Seller
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Role-specific Settings */}
        {userRole === 'seller' ? renderSellerSettings() : renderBuyerSettings()}

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
          onPress={handleSaveSettings}
        >
          <Text style={[styles.saveButtonText, { color: colors.white }]}>
            Save Settings
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: spacing.md,
  },
  scrollContent: {
    paddingBottom: spacing.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    paddingVertical: spacing.lg,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
  },
  section: {
    marginBottom: spacing.lg,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  roleSwitcher: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  roleButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    alignItems: 'center',
  },
  roleButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  settingContent: {
    flex: 1,
    marginRight: spacing.md,
  },
  settingTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  settingDescription: {
    fontSize: typography.fontSize.sm,
    lineHeight: 18,
  },
  numberInput: {
    borderWidth: 1,
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    fontSize: typography.fontSize.base,
    width: 60,
    textAlign: 'center',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  actionContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    flex: 1,
    marginLeft: spacing.md,
  },
  actionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  actionDescription: {
    fontSize: typography.fontSize.sm,
  },
  saveButton: {
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
  },
  saveButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
});

export default SellerSettingsScreen;
