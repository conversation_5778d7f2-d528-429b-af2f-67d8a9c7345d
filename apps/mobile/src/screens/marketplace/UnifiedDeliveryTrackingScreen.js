import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import ApiService from '../../services/api';

const DELIVERY_STATUSES = {
  pending: { 
    label: 'Pending Assignment', 
    color: '#FF9500', 
    icon: 'time-outline',
    description: 'Waiting for delivery person assignment'
  },
  assigned: { 
    label: 'Assigned', 
    color: '#007AFF', 
    icon: 'person-add-outline',
    description: 'Delivery person assigned'
  },
  picked_up: { 
    label: 'Picked Up', 
    color: '#5856D6', 
    icon: 'checkmark-circle-outline',
    description: 'Package picked up from seller'
  },
  in_transit: { 
    label: 'In Transit', 
    color: '#34C759', 
    icon: 'car-outline',
    description: 'Package is on the way'
  },
  out_for_delivery: { 
    label: 'Out for Delivery', 
    color: '#30D158', 
    icon: 'location-outline',
    description: 'Delivery person is nearby'
  },
  delivered: { 
    label: 'Delivered', 
    color: '#32D74B', 
    icon: 'checkmark-done-outline',
    description: 'Package delivered successfully'
  },
  failed: { 
    label: 'Delivery Failed', 
    color: '#FF3B30', 
    icon: 'close-circle-outline',
    description: 'Delivery attempt failed'
  },
};

const UnifiedDeliveryTrackingScreen = ({ route, navigation }) => {
  const { orderId, userRole } = route.params; // userRole: 'buyer', 'seller', 'delivery_person'
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [deliveryData, setDeliveryData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDeliveryTracking();
  }, []);

  const loadDeliveryTracking = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getUnifiedDeliveryTracking(orderId, userRole);
      if (response.success) {
        setDeliveryData(response.data);
      } else {
        Alert.alert('Error', 'Failed to load delivery tracking');
      }
    } catch (error) {
      console.error('Failed to load delivery tracking:', error);
      Alert.alert('Error', 'Failed to load delivery tracking');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDeliveryTracking();
    setRefreshing(false);
  };

  const callContact = (phoneNumber) => {
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const openMaps = (address) => {
    const encodedAddress = encodeURIComponent(address);
    Linking.openURL(`https://maps.google.com/?q=${encodedAddress}`);
  };

  const handleActionButton = async (action) => {
    try {
      let response;
      switch (action) {
        case 'accept_delivery':
          response = await ApiService.acceptDelivery(deliveryData.deliveryId);
          break;
        case 'pickup_complete':
          response = await ApiService.updateDeliveryStatus(deliveryData.deliveryId, {
            status: 'picked_up',
            notes: 'Package picked up from seller',
          });
          break;
        case 'start_transit':
          response = await ApiService.updateDeliveryStatus(deliveryData.deliveryId, {
            status: 'in_transit',
            notes: 'Package in transit',
          });
          break;
        case 'out_for_delivery':
          response = await ApiService.updateDeliveryStatus(deliveryData.deliveryId, {
            status: 'out_for_delivery',
            notes: 'Out for delivery',
          });
          // Notify buyer
          await ApiService.notifyBuyerDeliveryArrival(deliveryData.deliveryId);
          break;
        case 'confirm_delivery':
          navigation.navigate('DeliveryConfirmation', { 
            deliveryId: deliveryData.deliveryId,
            orderId: orderId 
          });
          return;
        default:
          return;
      }

      if (response && response.success) {
        Alert.alert('Success', 'Status updated successfully');
        loadDeliveryTracking();
      } else {
        Alert.alert('Error', 'Failed to update status');
      }
    } catch (error) {
      console.error('Failed to handle action:', error);
      Alert.alert('Error', 'Failed to perform action');
    }
  };

  const renderStatusTimeline = () => {
    const statusKeys = Object.keys(DELIVERY_STATUSES);
    const currentStatusIndex = statusKeys.indexOf(deliveryData.currentStatus);

    return (
      <Card style={[styles.timelineCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Delivery Timeline
        </Text>
        
        {statusKeys.map((statusKey, index) => {
          const status = DELIVERY_STATUSES[statusKey];
          const isCompleted = index <= currentStatusIndex;
          const isCurrent = index === currentStatusIndex;
          const isLast = index === statusKeys.length - 1;

          return (
            <View key={statusKey} style={styles.timelineItem}>
              <View style={styles.timelineLeft}>
                <View style={[
                  styles.timelineIcon,
                  { 
                    backgroundColor: isCompleted ? status.color : colors.border,
                    borderColor: isCurrent ? status.color : colors.border,
                    borderWidth: isCurrent ? 2 : 1,
                  }
                ]}>
                  <Ionicons 
                    name={status.icon} 
                    size={16} 
                    color={isCompleted ? colors.white : colors.textSecondary} 
                  />
                </View>
                {!isLast && (
                  <View style={[
                    styles.timelineLine,
                    { backgroundColor: isCompleted ? status.color : colors.border }
                  ]} />
                )}
              </View>
              
              <View style={styles.timelineContent}>
                <Text style={[
                  styles.timelineTitle,
                  { 
                    color: isCompleted ? colors.text : colors.textSecondary,
                    fontWeight: isCurrent ? '600' : '400'
                  }
                ]}>
                  {status.label}
                </Text>
                <Text style={[styles.timelineDescription, { color: colors.textSecondary }]}>
                  {status.description}
                </Text>
                {deliveryData.statusHistory && deliveryData.statusHistory[statusKey] && (
                  <Text style={[styles.timelineTime, { color: colors.textTertiary }]}>
                    {new Date(deliveryData.statusHistory[statusKey]).toLocaleString()}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </Card>
    );
  };

  const renderUserSpecificInfo = () => {
    switch (userRole) {
      case 'buyer':
        return (
          <Card style={[styles.infoCard, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Delivery Information
            </Text>
            
            {deliveryData.deliveryPerson && (
              <View style={styles.contactInfo}>
                <View style={styles.contactHeader}>
                  <Ionicons name="person" size={20} color={colors.primary} />
                  <Text style={[styles.contactName, { color: colors.text }]}>
                    {deliveryData.deliveryPerson.name}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.callButton}
                  onPress={() => callContact(deliveryData.deliveryPerson.phone)}
                >
                  <Ionicons name="call" size={16} color={colors.success} />
                  <Text style={[styles.callText, { color: colors.success }]}>
                    Call Delivery Person
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.addressInfo}>
              <Ionicons name="location" size={20} color={colors.primary} />
              <View style={styles.addressDetails}>
                <Text style={[styles.addressText, { color: colors.text }]}>
                  {deliveryData.deliveryAddress}
                </Text>
                <Text style={[styles.locationText, { color: colors.textSecondary }]}>
                  {deliveryData.deliveryTown}, {deliveryData.deliveryCounty}
                </Text>
              </View>
            </View>
          </Card>
        );

      case 'seller':
        return (
          <Card style={[styles.infoCard, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Order Details
            </Text>
            
            <View style={styles.buyerInfo}>
              <View style={styles.contactHeader}>
                <Ionicons name="person" size={20} color={colors.primary} />
                <Text style={[styles.contactName, { color: colors.text }]}>
                  {deliveryData.buyer.name}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.callButton}
                onPress={() => callContact(deliveryData.buyer.phone)}
              >
                <Ionicons name="call" size={16} color={colors.success} />
                <Text style={[styles.callText, { color: colors.success }]}>
                  Call Buyer
                </Text>
              </TouchableOpacity>
            </View>

            {deliveryData.deliveryPerson && (
              <View style={styles.deliveryPersonInfo}>
                <View style={styles.contactHeader}>
                  <Ionicons name="car" size={20} color={colors.warning} />
                  <Text style={[styles.contactName, { color: colors.text }]}>
                    {deliveryData.deliveryPerson.name}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.callButton}
                  onPress={() => callContact(deliveryData.deliveryPerson.phone)}
                >
                  <Ionicons name="call" size={16} color={colors.warning} />
                  <Text style={[styles.callText, { color: colors.warning }]}>
                    Call Delivery Person
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </Card>
        );

      case 'delivery_person':
        return (
          <Card style={[styles.infoCard, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Delivery Assignment
            </Text>
            
            <View style={styles.assignmentInfo}>
              <View style={styles.sellerInfo}>
                <View style={styles.contactHeader}>
                  <Ionicons name="storefront" size={20} color={colors.primary} />
                  <Text style={[styles.contactName, { color: colors.text }]}>
                    Pickup: {deliveryData.seller.name}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.callButton}
                  onPress={() => callContact(deliveryData.seller.phone)}
                >
                  <Ionicons name="call" size={16} color={colors.primary} />
                  <Text style={[styles.callText, { color: colors.primary }]}>
                    Call Seller
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.buyerInfo}>
                <View style={styles.contactHeader}>
                  <Ionicons name="person" size={20} color={colors.success} />
                  <Text style={[styles.contactName, { color: colors.text }]}>
                    Deliver to: {deliveryData.buyer.name}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.callButton}
                  onPress={() => callContact(deliveryData.buyer.phone)}
                >
                  <Ionicons name="call" size={16} color={colors.success} />
                  <Text style={[styles.callText, { color: colors.success }]}>
                    Call Buyer
                  </Text>
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.mapButton}
                onPress={() => openMaps(deliveryData.deliveryAddress)}
              >
                <Ionicons name="map" size={20} color={colors.info} />
                <Text style={[styles.mapText, { color: colors.info }]}>
                  Open in Maps
                </Text>
              </TouchableOpacity>
            </View>
          </Card>
        );

      default:
        return null;
    }
  };

  const renderActionButtons = () => {
    if (userRole !== 'delivery_person') return null;

    const currentStatus = deliveryData.currentStatus;
    let actionButton = null;

    switch (currentStatus) {
      case 'assigned':
        actionButton = (
          <Button
            title="Accept Delivery"
            onPress={() => handleActionButton('accept_delivery')}
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
          />
        );
        break;
      case 'accepted':
        actionButton = (
          <Button
            title="Mark as Picked Up"
            onPress={() => handleActionButton('pickup_complete')}
            style={[styles.actionButton, { backgroundColor: colors.warning }]}
          />
        );
        break;
      case 'picked_up':
        actionButton = (
          <Button
            title="Start Transit"
            onPress={() => handleActionButton('start_transit')}
            style={[styles.actionButton, { backgroundColor: colors.info }]}
          />
        );
        break;
      case 'in_transit':
        actionButton = (
          <Button
            title="Out for Delivery"
            onPress={() => handleActionButton('out_for_delivery')}
            style={[styles.actionButton, { backgroundColor: colors.success }]}
          />
        );
        break;
      case 'out_for_delivery':
        actionButton = (
          <Button
            title="Confirm Delivery"
            onPress={() => handleActionButton('confirm_delivery')}
            style={[styles.actionButton, { backgroundColor: colors.success }]}
          />
        );
        break;
      default:
        break;
    }

    return actionButton ? (
      <View style={styles.actionContainer}>
        {actionButton}
      </View>
    ) : null;
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!deliveryData) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            Delivery information not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentStatusInfo = DELIVERY_STATUSES[deliveryData.currentStatus];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Delivery Tracking
        </Text>
        <TouchableOpacity onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Current Status Card */}
        <Card style={[styles.statusCard, { backgroundColor: colors.surface }]}>
          <View style={styles.statusHeader}>
            <View style={[styles.statusIcon, { backgroundColor: currentStatusInfo.color + '20' }]}>
              <Ionicons name={currentStatusInfo.icon} size={32} color={currentStatusInfo.color} />
            </View>
            <View style={styles.statusInfo}>
              <Text style={[styles.statusTitle, { color: colors.text }]}>
                {currentStatusInfo.label}
              </Text>
              <Text style={[styles.statusDescription, { color: colors.textSecondary }]}>
                {currentStatusInfo.description}
              </Text>
              <Text style={[styles.orderNumber, { color: colors.textTertiary }]}>
                Order #{deliveryData.orderId?.slice(-8).toUpperCase()}
              </Text>
            </View>
          </View>
        </Card>

        {/* Timeline */}
        {renderStatusTimeline()}

        {/* User-specific Information */}
        {renderUserSpecificInfo()}

        {/* Products Information */}
        <Card style={[styles.productsCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Products ({deliveryData.products?.length || 0})
          </Text>
          {deliveryData.products?.map((product, index) => (
            <View key={index} style={styles.productItem}>
              <Text style={[styles.productName, { color: colors.text }]}>
                {product.name}
              </Text>
              <Text style={[styles.productQuantity, { color: colors.textSecondary }]}>
                Qty: {product.quantity}
              </Text>
            </View>
          ))}
        </Card>

        {/* Action Buttons */}
        {renderActionButtons()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  statusCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  statusDescription: {
    fontSize: 14,
    marginBottom: spacing.xs,
  },
  orderNumber: {
    fontSize: 12,
  },
  timelineCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: spacing.md,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineLine: {
    width: 2,
    height: 40,
    marginTop: spacing.xs,
  },
  timelineContent: {
    flex: 1,
    paddingTop: spacing.xs,
  },
  timelineTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  timelineDescription: {
    fontSize: 12,
    marginBottom: spacing.xs,
  },
  timelineTime: {
    fontSize: 11,
  },
  infoCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  contactInfo: {
    marginBottom: spacing.md,
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: spacing.sm,
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  callText: {
    fontSize: 14,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  addressInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  addressDetails: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  addressText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  locationText: {
    fontSize: 12,
  },
  assignmentInfo: {
    gap: spacing.md,
  },
  sellerInfo: {
    marginBottom: spacing.md,
  },
  buyerInfo: {
    marginBottom: spacing.md,
  },
  deliveryPersonInfo: {
    marginBottom: spacing.md,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  mapText: {
    fontSize: 14,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  productsCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  productQuantity: {
    fontSize: 12,
  },
  actionContainer: {
    marginTop: spacing.md,
    marginBottom: spacing.xl,
  },
  actionButton: {
    marginVertical: spacing.sm,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: spacing.md,
    textAlign: 'center',
  },
});

export default UnifiedDeliveryTrackingScreen;
