import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  Modal,
  TextInput,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import ApiService from '../../services/api';

const DeliveryContactsScreen = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingContact, setEditingContact] = useState(null);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    userId: '',
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: '',
    isActive: true,
  });

  useEffect(() => {
    loadDeliveryContacts();
  }, []);

  const loadDeliveryContacts = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getDeliveryContacts();
      if (response.success) {
        setContacts(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load delivery contacts:', error);
      Alert.alert('Error', 'Failed to load delivery contacts');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDeliveryContacts();
    setRefreshing(false);
  };

  const resetForm = () => {
    setFormData({
      userId: '',
      name: '',
      phone: '',
      email: '',
      address: '',
      notes: '',
      isActive: true,
    });
    setEditingContact(null);
    setSelectedUser(null);
    setSearchQuery('');
    setSearchResults([]);
  };

  const searchUsers = async (query) => {
    if (!query.trim() || query.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      setSearchLoading(true);
      // Use the exact same method as CreateChama form - this works!
      const response = await ApiService.makeRequest(`/users/?q=${encodeURIComponent(query.trim())}`);

      if (response.success) {
        const allResults = response.data || [];
        console.log('🔍 Search found', allResults.length, 'users');

        // Filter out current user and already added contacts
        const filteredResults = allResults.filter(searchUser => {
          const isCurrentUser = searchUser.id === user?.id;
          const isAlreadyContact = contacts.some(contact => contact.userId === searchUser.id);
          return !isCurrentUser && !isAlreadyContact;
        });

        console.log('✅ Showing', filteredResults.length, 'filtered results');
        setSearchResults(filteredResults);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Failed to search users:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const selectUser = async (user) => {
    setSelectedUser(user);

    // Auto-fill form with user's basic data
    setFormData(prev => ({
      ...prev,
      userId: user.id,
      name: `${user.firstName} ${user.lastName}`,
      phone: user.phoneNumber || user.phone || '',
      email: user.email || '',
      address: '', // Will be filled from full profile if available
      notes: '', // Keep empty for manual input
      isActive: true,
    }));

    setSearchQuery(`${user.firstName} ${user.lastName}`);
    setSearchResults([]);

    // Try to fetch full user profile for additional details like address
    try {
      const profileResponse = await ApiService.makeRequest(`/users/${user.id}/profile`);
      if (profileResponse.success && profileResponse.data) {
        const fullProfile = profileResponse.data;

        // Update form with additional profile data if available
        setFormData(prev => ({
          ...prev,
          phone: fullProfile.phone || fullProfile.phoneNumber || prev.phone,
          email: fullProfile.email || prev.email,
          address: fullProfile.address || '', // Use address if available
          // Could also use bio as notes if desired
          // notes: fullProfile.bio || prev.notes,
        }));

        console.log('✅ Auto-filled form with user profile data');
      }
    } catch (error) {
      console.log('ℹ️ Could not fetch full profile, using basic data only');
      // Not a critical error - basic data is already filled
    }
  };

  const openAddModal = () => {
    resetForm();
    setShowUserSearch(true);
    setShowAddModal(true);
  };

  const openEditModal = (contact) => {
    setFormData({
      name: contact.name || '',
      phone: contact.phone || '',
      email: contact.email || '',
      address: contact.address || '',
      notes: contact.notes || '',
      isActive: contact.isActive !== false,
    });
    setEditingContact(contact);
    setShowAddModal(true);
  };

  const handleSaveContact = async () => {
    if (!editingContact && !formData.userId) {
      Alert.alert('Error', 'Please select a user from the search results');
      return;
    }

    if (!formData.name.trim()) {
      Alert.alert('Error', 'Contact name is required');
      return;
    }

    try {
      let response;
      if (editingContact) {
        response = await ApiService.updateDeliveryContact(editingContact.id, formData);
      } else {
        response = await ApiService.createDeliveryContact({
          ...formData,
          userId: formData.userId,
        });
      }

      if (response.success) {
        Alert.alert('Success', `Contact ${editingContact ? 'updated' : 'added'} successfully`);
        setShowAddModal(false);
        setShowUserSearch(false);
        resetForm();
        loadDeliveryContacts();
      } else {
        Alert.alert('Error', response.error || 'Failed to save contact');
      }
    } catch (error) {
      console.error('Failed to save contact:', error);
      Alert.alert('Error', 'Failed to save contact');
    }
  };

  const handleDeleteContact = (contact) => {
    Alert.alert(
      'Delete Contact',
      `Are you sure you want to delete ${contact.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await ApiService.deleteDeliveryContact(contact.id);
              if (response.success) {
                Alert.alert('Success', 'Contact deleted successfully');
                loadDeliveryContacts();
              } else {
                Alert.alert('Error', 'Failed to delete contact');
              }
            } catch (error) {
              console.error('Failed to delete contact:', error);
              Alert.alert('Error', 'Failed to delete contact');
            }
          },
        },
      ]
    );
  };

  const toggleContactStatus = async (contact) => {
    try {
      const response = await ApiService.updateDeliveryContact(contact.id, {
        ...contact,
        isActive: !contact.isActive,
      });
      if (response.success) {
        loadDeliveryContacts();
      }
    } catch (error) {
      console.error('Failed to update contact status:', error);
      Alert.alert('Error', 'Failed to update contact status');
    }
  };

  const renderContactItem = ({ item }) => (
    <Card style={[styles.contactCard, { backgroundColor: colors.surface }]}>
      <View style={styles.contactHeader}>
        <View style={styles.contactInfo}>
          <Text style={[styles.contactName, { color: colors.text }]}>
            {item.name}
          </Text>
          <Text style={[styles.contactPhone, { color: colors.textSecondary }]}>
            {item.phone}
          </Text>
          {item.email && (
            <Text style={[styles.contactEmail, { color: colors.textSecondary }]}>
              {item.email}
            </Text>
          )}
        </View>
        <View style={[
          styles.statusBadge,
          { backgroundColor: item.isActive ? colors.success + '20' : colors.error + '20' }
        ]}>
          <Text style={[
            styles.statusText,
            { color: item.isActive ? colors.success : colors.error }
          ]}>
            {item.isActive ? 'Active' : 'Inactive'}
          </Text>
        </View>
      </View>

      {item.address && (
        <View style={styles.addressContainer}>
          <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
          <Text style={[styles.addressText, { color: colors.textSecondary }]}>
            {item.address}
          </Text>
        </View>
      )}

      {item.notes && (
        <Text style={[styles.notesText, { color: colors.textSecondary }]}>
          {item.notes}
        </Text>
      )}

      <View style={styles.contactActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => openEditModal(item)}
        >
          <Ionicons name="pencil" size={16} color={colors.primary} />
          <Text style={[styles.actionText, { color: colors.primary }]}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: item.isActive ? colors.warning + '20' : colors.success + '20' }]}
          onPress={() => toggleContactStatus(item)}
        >
          <Ionicons 
            name={item.isActive ? 'pause' : 'play'} 
            size={16} 
            color={item.isActive ? colors.warning : colors.success} 
          />
          <Text style={[styles.actionText, { color: item.isActive ? colors.warning : colors.success }]}>
            {item.isActive ? 'Deactivate' : 'Activate'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.error + '20' }]}
          onPress={() => handleDeleteContact(item)}
        >
          <Ionicons name="trash" size={16} color={colors.error} />
          <Text style={[styles.actionText, { color: colors.error }]}>Delete</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Delivery Contacts
        </Text>
        <TouchableOpacity onPress={openAddModal}>
          <Ionicons name="add" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <FlatList
        data={contacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No delivery contacts yet
            </Text>
            <Text style={[styles.emptySubtext, { color: colors.textTertiary }]}>
              Add trusted people who can help deliver your products
            </Text>
            <Button
              title="Add First Contact"
              onPress={openAddModal}
              style={styles.addButton}
            />
          </View>
        }
      />

      {/* Add/Edit Contact Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={[styles.cancelText, { color: colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {editingContact ? 'Edit Contact' : 'Add Contact'}
            </Text>
            <TouchableOpacity onPress={handleSaveContact}>
              <Text style={[styles.saveText, { color: colors.primary }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.formContainer}>
            {/* User Search Section */}
            {!editingContact && (
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>
                  Search User {selectedUser ? '✓' : '*'}
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    { backgroundColor: colors.surface, color: colors.text },
                    selectedUser && { borderColor: colors.success, borderWidth: 2 }
                  ]}
                  value={searchQuery}
                  onChangeText={(text) => {
                    setSearchQuery(text);
                    searchUsers(text);
                  }}
                  placeholder="Search by name, email, or phone"
                  placeholderTextColor={colors.textSecondary}
                  autoCapitalize="none"
                />

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <View style={[styles.searchResults, { backgroundColor: colors.surface }]}>
                    {searchResults.map((user) => (
                      <TouchableOpacity
                        key={user.id}
                        style={styles.searchResultItem}
                        onPress={() => selectUser(user)}
                      >
                        <View style={styles.userInfo}>
                          <Text style={[styles.userName, { color: colors.text }]}>
                            {user.firstName} {user.lastName}
                          </Text>
                          <Text style={[styles.userDetails, { color: colors.textSecondary }]}>
                            {user.email}
                          </Text>
                          {user.phoneNumber && (
                            <Text style={[styles.userDetails, { color: colors.textSecondary }]}>
                              {user.phoneNumber}
                            </Text>
                          )}
                        </View>
                        <Ionicons name="add-circle" size={24} color={colors.primary} />
                      </TouchableOpacity>
                    ))}
                  </View>
                )}

                {searchLoading && (
                  <View style={styles.searchLoading}>
                    <Text style={[styles.searchLoadingText, { color: colors.textSecondary }]}>
                      Searching users...
                    </Text>
                  </View>
                )}

                {selectedUser && (
                  <View style={[styles.selectedUser, { backgroundColor: colors.success + '10' }]}>
                    <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                    <View style={{ flex: 1 }}>
                      <Text style={[styles.selectedUserText, { color: colors.success }]}>
                        Selected: {selectedUser.firstName} {selectedUser.lastName}
                      </Text>
                      <Text style={[styles.selectedUserSubtext, { color: colors.success, opacity: 0.8 }]}>
                        Form auto-filled with user data
                      </Text>
                    </View>
                  </View>
                )}
              </View>
            )}

            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Display Name *</Text>
                {selectedUser && formData.name && (
                  <View style={styles.autoFilledIndicator}>
                    <Ionicons name="checkmark-circle" size={14} color={colors.success} />
                    <Text style={[styles.autoFilledText, { color: colors.success }]}>Auto-filled</Text>
                  </View>
                )}
              </View>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.surface, color: colors.text },
                  selectedUser && formData.name && { borderColor: colors.success + '40', borderWidth: 1 }
                ]}
                value={formData.name}
                onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                placeholder="How this contact will be displayed"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Phone Number *</Text>
                {selectedUser && formData.phone && (
                  <View style={styles.autoFilledIndicator}>
                    <Ionicons name="checkmark-circle" size={14} color={colors.success} />
                    <Text style={[styles.autoFilledText, { color: colors.success }]}>Auto-filled</Text>
                  </View>
                )}
              </View>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.surface, color: colors.text },
                  selectedUser && formData.phone && { borderColor: colors.success + '40', borderWidth: 1 }
                ]}
                value={formData.phone}
                onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
                placeholder="Enter phone number"
                placeholderTextColor={colors.textSecondary}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Email</Text>
                {selectedUser && formData.email && (
                  <View style={styles.autoFilledIndicator}>
                    <Ionicons name="checkmark-circle" size={14} color={colors.success} />
                    <Text style={[styles.autoFilledText, { color: colors.success }]}>Auto-filled</Text>
                  </View>
                )}
              </View>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.surface, color: colors.text },
                  selectedUser && formData.email && { borderColor: colors.success + '40', borderWidth: 1 }
                ]}
                value={formData.email}
                onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                placeholder="Enter email address"
                placeholderTextColor={colors.textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Address</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.address}
                onChangeText={(text) => setFormData(prev => ({ ...prev, address: text }))}
                placeholder="Enter address"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Notes</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.notes}
                onChangeText={(text) => setFormData(prev => ({ ...prev, notes: text }))}
                placeholder="Add any notes about this contact"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  listContainer: {
    padding: spacing.md,
  },
  contactCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  contactPhone: {
    fontSize: 14,
    marginBottom: spacing.xs,
  },
  contactEmail: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  addressText: {
    fontSize: 14,
    marginLeft: spacing.xs,
    flex: 1,
  },
  notesText: {
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: spacing.sm,
  },
  contactActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  actionText: {
    fontSize: 12,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  addButton: {
    marginTop: spacing.md,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cancelText: {
    fontSize: 16,
  },
  saveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  formContainer: {
    padding: spacing.md,
  },
  inputGroup: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  searchResults: {
    maxHeight: 200,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: borderRadius.md,
    marginTop: spacing.xs,
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  userDetails: {
    fontSize: 14,
    marginBottom: spacing.xs,
  },
  searchLoading: {
    padding: spacing.md,
    alignItems: 'center',
  },
  searchLoadingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  selectedUser: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    marginTop: spacing.xs,
  },
  selectedUserText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  selectedUserSubtext: {
    fontSize: 12,
    marginLeft: spacing.xs,
    marginTop: 2,
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  autoFilledIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  autoFilledText: {
    fontSize: 10,
    marginLeft: 4,
    fontWeight: '500',
  },
});

export default DeliveryContactsScreen;
