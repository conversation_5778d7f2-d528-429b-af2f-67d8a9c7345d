import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { useLightningData, useOptimisticUpdate } from '../../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const CartScreen = ({ navigation }) => {
  const { theme, cartItems, loadLocalData, loadCartFromAPI } = useApp();
  const colors = getThemeColors(theme);

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedItems, setSelectedItems] = useState(new Set());

  useEffect(() => {
    // Load cart from API when screen loads
    console.log('CartScreen: Loading cart from API...');
    loadCartFromAPI();
  }, []);

  useEffect(() => {
    // Select all items by default
    console.log('CartScreen: Cart items updated:', cartItems.length, cartItems);
    setSelectedItems(new Set(cartItems.map(item => item.id)));
  }, [cartItems]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadCartFromAPI();
    } catch (error) {
      console.error('Failed to refresh cart:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleUpdateQuantity = async (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      handleRemoveItem(itemId);
      return;
    }

    try {
      // Update quantity logic would go here
      // For now, we'll just update locally
      await loadLocalData();
    } catch (error) {
      Alert.alert('Error', 'Failed to update quantity');
    }
  };

  const handleRemoveItem = async (itemId) => {
    try {
      const response = await ApiService.removeFromCart(itemId);
      if (response.success) {
        await loadLocalData();
        setSelectedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to remove item');
    }
  };

  const handleSelectItem = (itemId) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedItems.size === cartItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(cartItems.map(item => item.id)));
    }
  };

  const getSelectedItems = () => {
    return cartItems.filter(item => selectedItems.has(item.id));
  };

  const getTotalPrice = () => {
    return getSelectedItems().reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  };

  const handleCheckout = () => {
    const selectedCartItems = getSelectedItems();
    if (selectedCartItems.length === 0) {
      Alert.alert('No Items Selected', 'Please select items to checkout');
      return;
    }

    navigation.navigate('Checkout', {
      items: selectedCartItems,
      total: getTotalPrice(),
    });
  };

  const renderCartItem = (item) => (
    <Card key={item.id} style={styles.cartItem}>
      <View style={styles.itemContent}>
        <TouchableOpacity
          style={styles.checkbox}
          onPress={() => handleSelectItem(item.id)}
        >
          <Ionicons
            name={selectedItems.has(item.id) ? 'checkbox' : 'square-outline'}
            size={24}
            color={selectedItems.has(item.id) ? colors.primary : colors.textSecondary}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.itemDetails}
          onPress={() => navigation.navigate('ProductDetails', { product: item.product })}
        >
          <Image
            source={{ uri: item.product?.images?.[0] || 'https://via.placeholder.com/80' }}
            style={styles.itemImage}
            resizeMode="cover"
          />

          <View style={styles.itemInfo}>
            <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={2}>
              {item.product?.name}
            </Text>
            
            <Text style={[styles.itemPrice, { color: colors.primary }]}>
              {formatCurrency(item.price)}
            </Text>
            
            <View style={styles.sellerInfo}>
              <Ionicons name="person" size={12} color={colors.textSecondary} />
              <Text style={[styles.sellerName, { color: colors.textSecondary }]} numberOfLines={1}>
                {item.product?.seller?.first_name} {item.product?.seller?.last_name}
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        <View style={styles.itemActions}>
          <View style={styles.quantityControls}>
            <TouchableOpacity
              style={[styles.quantityButton, { backgroundColor: colors.backgroundSecondary }]}
              onPress={() => handleUpdateQuantity(item.id, item.quantity - 1)}
            >
              <Ionicons name="remove" size={16} color={colors.text} />
            </TouchableOpacity>
            
            <Text style={[styles.quantityText, { color: colors.text }]}>
              {item.quantity}
            </Text>
            
            <TouchableOpacity
              style={[styles.quantityButton, { backgroundColor: colors.backgroundSecondary }]}
              onPress={() => handleUpdateQuantity(item.id, item.quantity + 1)}
            >
              <Ionicons name="add" size={16} color={colors.text} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveItem(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color={colors.error} />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  const renderEmptyCart = () => (
    <View style={styles.emptyCart}>
      <Ionicons name="cart-outline" size={64} color={colors.textTertiary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        Your cart is empty
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Add some products to get started
      </Text>
      <Button
        title="Browse Products"
        onPress={() => navigation.navigate('MainTabs', { screen: 'Marketplace' })}
        style={styles.browseButton}
      />
    </View>
  );

  const renderCartSummary = () => (
    <Card style={styles.cartSummary}>
      <View style={styles.summaryHeader}>
        <TouchableOpacity
          style={styles.selectAllButton}
          onPress={handleSelectAll}
        >
          <Ionicons
            name={selectedItems.size === cartItems.length ? 'checkbox' : 'square-outline'}
            size={20}
            color={selectedItems.size === cartItems.length ? colors.primary : colors.textSecondary}
          />
          <Text style={[styles.selectAllText, { color: colors.text }]}>
            Select All ({cartItems.length})
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.summaryDetails}>
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            Selected Items ({selectedItems.size})
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {formatCurrency(getTotalPrice())}
          </Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            Delivery Fee
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {selectedItems.size > 0 ? formatCurrency(100) : formatCurrency(0)}
          </Text>
        </View>

        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={[styles.totalLabel, { color: colors.text }]}>
            Total
          </Text>
          <Text style={[styles.totalValue, { color: colors.primary }]}>
            {formatCurrency(getTotalPrice() + (selectedItems.size > 0 ? 100 : 0))}
          </Text>
        </View>
      </View>

      <Button
        title={`Checkout (${selectedItems.size})`}
        onPress={handleCheckout}
        disabled={selectedItems.size === 0}
        style={styles.checkoutButton}
      />
    </Card>
  );

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        {renderEmptyCart()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.cartItems}>
          {cartItems.map(renderCartItem)}
        </View>
        
        {/* Spacer for fixed summary */}
        <View style={styles.spacer} />
      </ScrollView>

      {renderCartSummary()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  cartItems: {
    padding: spacing.md,
  },
  cartItem: {
    marginBottom: spacing.md,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    marginRight: spacing.md,
  },
  itemDetails: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: borderRadius.md,
    marginRight: spacing.md,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  itemPrice: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  sellerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerName: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
    flex: 1,
  },
  itemActions: {
    alignItems: 'center',
    marginLeft: spacing.md,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginHorizontal: spacing.md,
    minWidth: 20,
    textAlign: 'center',
  },
  removeButton: {
    padding: spacing.xs,
  },
  emptyCart: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  browseButton: {
    marginTop: spacing.md,
  },
  spacer: {
    height: 200,
  },
  cartSummary: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    margin: spacing.md,
    ...shadows.lg,
  },
  summaryHeader: {
    marginBottom: spacing.md,
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.sm,
  },
  summaryDetails: {
    marginBottom: spacing.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: spacing.sm,
    marginTop: spacing.sm,
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  checkoutButton: {
    marginTop: spacing.md,
  },
});

export default CartScreen;
