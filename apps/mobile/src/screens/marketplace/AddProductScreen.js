import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';

const AddProductScreen = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [loading, setLoading] = useState(false);
  const [images, setImages] = useState([]);
  const [productData, setProductData] = useState({
    name: '',
    description: '',
    category: '',
    price: '',
    stock: '',
    min_order: '1',
    max_order: '',
    tags: '',
    county: user?.county || '',
    town: user?.town || '',
    address: '',
  });

  // Categories must match exactly with backend ProductCategory constants and marketplace categories
  const categories = [
    { id: 'agriculture', name: 'Agriculture', icon: 'leaf' },
    { id: 'food_beverage', name: 'Food & Beverage', icon: 'restaurant' },
    { id: 'clothing', name: 'Clothing', icon: 'shirt' },
    { id: 'electronics', name: 'Electronics', icon: 'phone-portrait' },
    { id: 'services', name: 'Services', icon: 'construct' },
    { id: 'crafts', name: 'Crafts', icon: 'color-palette' },
    { id: 'beauty', name: 'Beauty', icon: 'flower' },
    { id: 'home_garden', name: 'Home & Garden', icon: 'home' },
    { id: 'automotive', name: 'Automotive', icon: 'car' },
    { id: 'other', name: 'Other', icon: 'ellipsis-horizontal' },
  ];

  const handleInputChange = (field, value) => {
    setProductData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to add images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled) {
        setImages(prev => [...prev, ...result.assets.slice(0, 5 - prev.length)]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const removeImage = (index) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const required = ['name', 'description', 'category', 'price', 'stock', 'county', 'town'];
    const missing = required.filter(field => !productData[field]);

    if (missing.length > 0) {
      Alert.alert('Missing Information', `Please fill in: ${missing.join(', ')}`);
      return false;
    }

    if (parseFloat(productData.price) <= 0) {
      Alert.alert('Invalid Price', 'Price must be greater than 0');
      return false;
    }

    if (parseInt(productData.stock) <= 0) {
      Alert.alert('Invalid Stock', 'Stock must be greater than 0');
      return false;
    }

    const minOrder = parseInt(productData.min_order) || 1;
    if (minOrder <= 0) {
      Alert.alert('Invalid Minimum Order', 'Minimum order must be greater than 0');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const formData = {
        name: productData.name,
        description: productData.description,
        category: productData.category, // This will now be the category ID (e.g., 'food_beverage')
        price: parseFloat(productData.price),
        stock: parseInt(productData.stock),
        minOrder: parseInt(productData.min_order) || 1, // Backend expects camelCase
        maxOrder: productData.max_order ? parseInt(productData.max_order) : null,
        county: productData.county,
        town: productData.town,
        address: productData.address || null,
        images: images.map(img => img.uri), // In real app, upload to cloud storage first
        tags: productData.tags
          ? productData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
          : [], // Convert comma-separated string to array of strings
      };

      let response = await ApiService.createProduct(formData);

      // If creation fails due to seller registration requirement, auto-register and retry
      if (!response.success && response.error && response.error.includes('seller')) {
        try {
          const registerResponse = await ApiService.autoRegisterAsSeller(user.id);
          if (registerResponse.success) {
            // Retry creating product after registration
            response = await ApiService.createProduct(formData);
            if (response.success) {
              Alert.alert(
                'Product Added Successfully!',
                'You have been automatically registered as a seller and your product has been listed in the marketplace.',
                [
                  {
                    text: 'View in Marketplace',
                    onPress: () => navigation.navigate('MainTabs', { screen: 'Marketplace' }),
                  },
                  {
                    text: 'Add Another Product',
                    onPress: () => {
                      setProductData({
                        name: '',
                        description: '',
                        category: '',
                        price: '',
                        stock: '',
                        min_order: '1',
                        max_order: '',
                        tags: '',
                        county: user?.county || '',
                        town: user?.town || '',
                        address: '',
                      });
                      setImages([]);
                    },
                  },
                ]
              );
              return;
            }
          }
        } catch (registerError) {
          Alert.alert('Error', 'Failed to setup seller account. Please try again.');
          return;
        }
      }

      if (response.success) {
        Alert.alert(
          'Product Added Successfully!',
          'Your product has been listed in the marketplace and is now available for purchase.',
          [
            {
              text: 'View in Marketplace',
              onPress: () => navigation.navigate('MainTabs', { screen: 'Marketplace' }),
            },
            {
              text: 'Add Another Product',
              onPress: () => {
                setProductData({
                  name: '',
                  description: '',
                  category: '',
                  price: '',
                  stock: '',
                  min_order: '1',
                  max_order: '',
                  tags: '',
                  county: user?.county || '',
                  town: user?.town || '',
                  address: '',
                });
                setImages([]);
              },
            },
          ]
        );
      } else {
        throw new Error(response.error || 'Failed to add product');
      }
    } catch (error) {
      console.error('Product creation failed:', error);
      Alert.alert('Error', error.message || 'Failed to add product. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderImagePicker = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Product Images
      </Text>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageContainer}>
        {images.map((image, index) => (
          <View key={index} style={styles.imageWrapper}>
            <Image source={{ uri: image.uri }} style={styles.productImage} />
            <TouchableOpacity
              style={[styles.removeImageButton, { backgroundColor: colors.error }]}
              onPress={() => removeImage(index)}
            >
              <Ionicons name="close" size={16} color={colors.white} />
            </TouchableOpacity>
          </View>
        ))}

        {images.length < 5 && (
          <TouchableOpacity
            style={[styles.addImageButton, { borderColor: colors.border, backgroundColor: colors.backgroundSecondary }]}
            onPress={pickImage}
          >
            <Ionicons name="camera" size={32} color={colors.textSecondary} />
            <Text style={[styles.addImageText, { color: colors.textSecondary }]}>
              Add Image
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>

      <Text style={[styles.imageHint, { color: colors.textTertiary }]}>
        Add up to 5 images. First image will be the main product image.
      </Text>
    </Card>
  );

  const renderBasicInfo = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Basic Information
      </Text>

      <Input
        label="Product Name *"
        value={productData.name}
        onChangeText={(text) => handleInputChange('name', text)}
        placeholder="Enter product name"
      />

      <Input
        label="Description *"
        value={productData.description}
        onChangeText={(text) => handleInputChange('description', text)}
        placeholder="Describe your product..."
        multiline
        numberOfLines={4}
      />

      <View style={styles.categoryContainer}>
        <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>
          Category *
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryChip,
                {
                  backgroundColor: productData.category === category.id ? colors.primary : colors.backgroundSecondary,
                  borderColor: colors.border,
                }
              ]}
              onPress={() => handleInputChange('category', category.id)}
            >
              <Ionicons
                name={category.icon}
                size={16}
                color={productData.category === category.id ? colors.white : colors.textSecondary}
                style={styles.categoryIcon}
              />
              <Text style={[
                styles.categoryText,
                { color: productData.category === category.id ? colors.white : colors.text }
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <Input
        label="Tags (Optional)"
        value={productData.tags}
        onChangeText={(text) => handleInputChange('tags', text)}
        placeholder="e.g., organic, handmade, local"
        helperText="Separate tags with commas"
      />
    </Card>
  );

  const renderPricingStock = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Pricing & Stock
      </Text>

      <View style={styles.row}>
        <Input
          label="Price (KES) *"
          value={productData.price}
          onChangeText={(text) => handleInputChange('price', text)}
          placeholder="0"
          keyboardType="numeric"
          style={styles.halfInput}
        />

        <Input
          label="Stock Quantity *"
          value={productData.stock}
          onChangeText={(text) => handleInputChange('stock', text)}
          placeholder="0"
          keyboardType="numeric"
          style={styles.halfInput}
        />
      </View>

      <View style={styles.row}>
        <Input
          label="Minimum Order"
          value={productData.min_order}
          onChangeText={(text) => handleInputChange('min_order', text || '1')}
          placeholder="1"
          keyboardType="numeric"
          style={styles.halfInput}
        />

        <Input
          label="Maximum Order"
          value={productData.max_order}
          onChangeText={(text) => handleInputChange('max_order', text)}
          placeholder="No limit"
          keyboardType="numeric"
          style={styles.halfInput}
        />
      </View>
    </Card>
  );

  const renderLocation = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Location
      </Text>

      <View style={styles.row}>
        <Input
          label="County *"
          value={productData.county}
          onChangeText={(text) => handleInputChange('county', text)}
          placeholder="Select county"
          style={styles.halfInput}
        />

        <Input
          label="Town *"
          value={productData.town}
          onChangeText={(text) => handleInputChange('town', text)}
          placeholder="Select town"
          style={styles.halfInput}
        />
      </View>

      <Input
        label="Specific Address (Optional)"
        value={productData.address}
        onChangeText={(text) => handleInputChange('address', text)}
        placeholder="Building, street, area..."
      />
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderImagePicker()}
        {renderBasicInfo()}
        {renderPricingStock()}
        {renderLocation()}

        {/* Spacer for fixed button */}
        <View style={styles.spacer} />
      </ScrollView>

      <View style={[styles.footer, { backgroundColor: colors.surface }]}>
        <Button
          title="Add Product"
          onPress={handleSubmit}
          loading={loading}
          style={styles.submitButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  imageContainer: {
    marginBottom: spacing.md,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: spacing.md,
  },
  productImage: {
    width: 100,
    height: 100,
    borderRadius: borderRadius.md,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: borderRadius.md,
    borderWidth: 2,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageText: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
  },
  imageHint: {
    fontSize: typography.fontSize.sm,
    textAlign: 'center',
  },
  categoryContainer: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
  },
  categoryScroll: {
    marginHorizontal: -spacing.md,
    paddingHorizontal: spacing.md,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    marginRight: spacing.sm,
    borderWidth: 1,
  },
  categoryIcon: {
    marginRight: spacing.xs,
  },
  categoryText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  row: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  halfInput: {
    flex: 1,
  },
  spacer: {
    height: 100,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
    ...shadows.lg,
  },
  submitButton: {
    marginTop: spacing.sm,
  },
});

export default AddProductScreen;
