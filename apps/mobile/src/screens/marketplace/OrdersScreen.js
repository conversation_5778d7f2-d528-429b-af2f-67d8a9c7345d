import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  StyleSheet,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import ApiService from '../../services/api';

const ORDER_STATUSES = {
  pending: { label: 'Pending', color: '#FF9500', icon: 'time' },
  confirmed: { label: 'Confirmed', color: '#007AFF', icon: 'checkmark-circle' },
  processing: { label: 'Processing', color: '#5856D6', icon: 'cog' },
  shipped: { label: 'Shipped', color: '#34C759', icon: 'airplane' },
  delivered: { label: 'Delivered', color: '#30D158', icon: 'checkmark-circle-2' },
  cancelled: { label: 'Cancelled', color: '#FF3B30', icon: 'close-circle' },
  refunded: { label: 'Refunded', color: '#FF9500', icon: 'return-up-back' },
};

const DELIVERY_STATUSES = {
  pending: { label: 'Pending Pickup', color: '#FF9500' },
  picked_up: { label: 'Picked Up', color: '#007AFF' },
  in_transit: { label: 'In Transit', color: '#5856D6' },
  out_for_delivery: { label: 'Out for Delivery', color: '#34C759' },
  delivered: { label: 'Delivered', color: '#30D158' },
  failed: { label: 'Delivery Failed', color: '#FF3B30' },
  returned: { label: 'Returned', color: '#FF9500' },
};

export default function OrdersScreen({ navigation }) {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('buyer'); // buyer or seller

  useEffect(() => {
    loadOrders();
  }, [activeTab]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getOrders({ role: activeTab });
      if (response.success) {
        setOrders(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load orders:', error);
      Alert.alert('Error', 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const getStatusInfo = (status) => {
    return ORDER_STATUSES[status] || { label: status, color: colors.textSecondary, icon: 'help' };
  };

  const getDeliveryStatusInfo = (status) => {
    return DELIVERY_STATUSES[status] || { label: status, color: colors.textSecondary };
  };

  const renderOrderItem = ({ item }) => {
    const statusInfo = getStatusInfo(item.status);
    const deliveryInfo = getDeliveryStatusInfo(item.deliveryStatus);
    const otherUser = activeTab === 'buyer' ? item.seller : item.buyer;

    return (
      <TouchableOpacity
        style={[styles.orderCard, { backgroundColor: colors.surface }]}
        onPress={() => navigation.navigate('OrderDetails', { orderId: item.id })}
      >
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderId, { color: colors.text }]}>
              Order #{item.id.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.orderDate, { color: colors.textSecondary }]}>
              {new Date(item.createdAt).toLocaleDateString()}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={16} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>

        <View style={styles.orderDetails}>
          <Text style={[styles.otherUser, { color: colors.textSecondary }]}>
            {activeTab === 'buyer' ? 'Seller' : 'Buyer'}: {otherUser?.firstName} {otherUser?.lastName}
          </Text>
          <Text style={[styles.orderAmount, { color: colors.primary }]}>
            KES {item.totalAmount?.toLocaleString()}
          </Text>
        </View>

        <View style={styles.deliveryInfo}>
          <View style={styles.deliveryStatus}>
            <Ionicons name="location" size={16} color={deliveryInfo.color} />
            <Text style={[styles.deliveryText, { color: deliveryInfo.color }]}>
              {deliveryInfo.label}
            </Text>
          </View>
          <Text style={[styles.deliveryLocation, { color: colors.textSecondary }]}>
            {item.deliveryCounty}, {item.deliveryTown}
          </Text>
        </View>

        <View style={styles.orderFooter}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
            onPress={() => navigation.navigate('OrderTracking', { orderId: item.id })}
          >
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Track Order
            </Text>
          </TouchableOpacity>
          
          {item.status === 'pending' && activeTab === 'seller' && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.success + '20' }]}
              onPress={() => confirmOrder(item.id)}
            >
              <Text style={[styles.actionButtonText, { color: colors.success }]}>
                Confirm
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const confirmOrder = async (orderId) => {
    try {
      const response = await ApiService.updateOrder(orderId, { status: 'confirmed' });
      if (response.success) {
        Alert.alert('Success', 'Order confirmed successfully');
        loadOrders();
      }
    } catch (error) {
      console.error('Failed to confirm order:', error);
      Alert.alert('Error', 'Failed to confirm order');
    }
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="receipt-outline" size={80} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Orders Found
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {activeTab === 'buyer' 
          ? "You haven't placed any orders yet"
          : "You haven't received any orders yet"
        }
      </Text>
      {activeTab === 'buyer' && (
        <TouchableOpacity
          style={[styles.browseButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Marketplace' })}
        >
          <Text style={[styles.browseButtonText, { color: colors.white }]}>
            Start Shopping
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Tab Selector */}
      <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'buyer' && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => setActiveTab('buyer')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'buyer' ? colors.primary : colors.textSecondary }
          ]}>
            My Orders
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'seller' && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => setActiveTab('seller')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'seller' ? colors.primary : colors.textSecondary }
          ]}>
            Sales
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          orders.length === 0 ? styles.emptyList : styles.list,
          styles.scrollContent
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    padding: 4,
    margin: 16,
    borderRadius: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  list: {
    padding: 16,
    paddingTop: 0,
  },
  emptyList: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  orderCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  otherUser: {
    fontSize: 14,
  },
  orderAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  deliveryInfo: {
    marginBottom: 12,
  },
  deliveryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  deliveryText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  deliveryLocation: {
    fontSize: 14,
    marginLeft: 20,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  browseButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
