import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import ApiService from '../../services/api';

const CheckoutScreen = ({ route, navigation }) => {
  const { items, total } = route.params;
  const { theme, user, wallets } = useApp();
  const colors = getThemeColors(theme);
  
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('wallet');
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState({
    county: user?.county || '',
    town: user?.town || '',
    address: '',
    phone: user?.phone || '',
    notes: '',
  });

  const paymentMethods = [
    { id: 'wallet', name: 'VaultKe Wallet', icon: 'wallet', available: true },
    { id: 'mpesa', name: 'M-Pesa', icon: 'phone-portrait', available: true },
    { id: 'bank', name: 'Bank Transfer', icon: 'card', available: false },
  ];

  useEffect(() => {
    // Set default wallet
    const personalWallet = wallets.find(w => w.type === 'personal' && w.is_active);
    if (personalWallet) {
      setSelectedWallet(personalWallet);
    }
  }, [wallets]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const deliveryFee = 100;
  const totalAmount = total + deliveryFee;

  const handlePlaceOrder = async () => {
    // Validate delivery address
    if (!deliveryAddress.county || !deliveryAddress.town || !deliveryAddress.address || !deliveryAddress.phone) {
      Alert.alert('Incomplete Address', 'Please fill in all delivery address fields');
      return;
    }

    // Validate payment method
    if (paymentMethod === 'wallet' && (!selectedWallet || selectedWallet.balance < totalAmount)) {
      Alert.alert('Insufficient Funds', 'Your wallet balance is insufficient for this order');
      return;
    }

    try {
      setLoading(true);

      // First, ensure cart is loaded from API to get proper cart item IDs
      console.log('Refreshing cart from API before placing order...');
      const cartResponse = await ApiService.getCart();
      // console.log('Cart API response:', cartResponse);

      if (!cartResponse.success || !cartResponse.data?.items || cartResponse.data.items.length === 0) {
        Alert.alert('Cart Empty', 'Your cart is empty. Please add items to your cart first.');
        return;
      }

      const apiCartItems = cartResponse.data.items;
      console.log('API cart items:', apiCartItems);
      console.log('First API cart item structure:', apiCartItems[0]);

      console.log('Original cart items for order:', items);
      console.log('First original cart item structure:', items[0]);

      // Use the API cart items which have proper cart item IDs
      const cartItemIds = apiCartItems.map(item => item.id);
      console.log('Cart item IDs being sent:', cartItemIds);

      const orderData = {
        items: cartItemIds, // Send cart item IDs, not product data
        paymentMethod: paymentMethod,
        deliveryCounty: deliveryAddress.county,
        deliveryTown: deliveryAddress.town,
        deliveryAddress: deliveryAddress.address,
        deliveryPhone: deliveryAddress.phone,
        deliveryFee: deliveryFee,
        notes: deliveryAddress.notes,
      };

      const response = await ApiService.createOrder(orderData);
      
      if (response.success) {
        Alert.alert(
          'Order Placed Successfully!',
          'Your order has been placed and the seller will be notified.',
          [
            {
              text: 'View Order',
              onPress: () => navigation.navigate('OrderDetails', { orderId: response.data.id }),
            },
            {
              text: 'Continue Shopping',
              onPress: () => navigation.navigate('MainTabs', { screen: 'Marketplace' }),
            },
          ]
        );
      } else {
        throw new Error(response.error || 'Failed to place order');
      }
    } catch (error) {
      console.error('Order placement failed:', error);
      Alert.alert('Order Failed', error.message || 'Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderOrderSummary = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Order Summary
      </Text>
      
      {items.map((item, index) => (
        <View key={index} style={styles.orderItem}>
          <View style={styles.itemInfo}>
            <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={1}>
              {item.product?.name || item.name}
            </Text>
            <Text style={[styles.itemDetails, { color: colors.textSecondary }]}>
              Qty: {item.quantity} × {formatCurrency(item.price)}
            </Text>
          </View>
          <Text style={[styles.itemTotal, { color: colors.text }]}>
            {formatCurrency(item.price * item.quantity)}
          </Text>
        </View>
      ))}
      
      <View style={[styles.summaryRow, { borderTopColor: colors.border }]}>
        <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
          Subtotal
        </Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>
          {formatCurrency(total)}
        </Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
          Delivery Fee
        </Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>
          {formatCurrency(deliveryFee)}
        </Text>
      </View>
      
      <View style={[styles.summaryRow, styles.totalRow]}>
        <Text style={[styles.totalLabel, { color: colors.text }]}>
          Total
        </Text>
        <Text style={[styles.totalValue, { color: colors.primary }]}>
          {formatCurrency(totalAmount)}
        </Text>
      </View>
    </Card>
  );

  const renderDeliveryAddress = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Delivery Address
      </Text>
      
      <View style={styles.addressForm}>
        <View style={styles.addressRow}>
          <Input
            label="County"
            value={deliveryAddress.county}
            onChangeText={(text) => setDeliveryAddress(prev => ({ ...prev, county: text }))}
            style={styles.halfInput}
          />
          <Input
            label="Town"
            value={deliveryAddress.town}
            onChangeText={(text) => setDeliveryAddress(prev => ({ ...prev, town: text }))}
            style={styles.halfInput}
          />
        </View>
        
        <Input
          label="Street Address"
          value={deliveryAddress.address}
          onChangeText={(text) => setDeliveryAddress(prev => ({ ...prev, address: text }))}
          placeholder="Building, street, area..."
        />
        
        <Input
          label="Phone Number"
          value={deliveryAddress.phone}
          onChangeText={(text) => setDeliveryAddress(prev => ({ ...prev, phone: text }))}
          keyboardType="phone-pad"
        />
        
        <Input
          label="Delivery Notes (Optional)"
          value={deliveryAddress.notes}
          onChangeText={(text) => setDeliveryAddress(prev => ({ ...prev, notes: text }))}
          placeholder="Special instructions for delivery..."
          multiline
          numberOfLines={3}
        />
      </View>
    </Card>
  );

  const renderPaymentMethod = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Payment Method
      </Text>
      
      {paymentMethods.map((method) => (
        <TouchableOpacity
          key={method.id}
          style={[
            styles.paymentOption,
            {
              backgroundColor: paymentMethod === method.id ? colors.primary + '20' : 'transparent',
              borderColor: paymentMethod === method.id ? colors.primary : colors.border,
            }
          ]}
          onPress={() => method.available && setPaymentMethod(method.id)}
          disabled={!method.available}
        >
          <View style={styles.paymentInfo}>
            <Ionicons 
              name={method.icon} 
              size={24} 
              color={method.available ? colors.text : colors.textTertiary} 
            />
            <View style={styles.paymentDetails}>
              <Text style={[
                styles.paymentName,
                { color: method.available ? colors.text : colors.textTertiary }
              ]}>
                {method.name}
              </Text>
              {method.id === 'wallet' && selectedWallet && (
                <Text style={[styles.walletBalance, { color: colors.textSecondary }]}>
                  Balance: {formatCurrency(selectedWallet.balance)}
                </Text>
              )}
              {!method.available && (
                <Text style={[styles.unavailableText, { color: colors.textTertiary }]}>
                  Coming Soon
                </Text>
              )}
            </View>
          </View>
          
          <Ionicons
            name={paymentMethod === method.id ? 'radio-button-on' : 'radio-button-off'}
            size={20}
            color={paymentMethod === method.id ? colors.primary : colors.textSecondary}
          />
        </TouchableOpacity>
      ))}
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderOrderSummary()}
        {renderDeliveryAddress()}
        {renderPaymentMethod()}
        
        {/* Spacer for fixed button */}
        <View style={styles.spacer} />
      </ScrollView>
      
      <View style={[styles.checkoutFooter, { backgroundColor: colors.surface }]}>
        <View style={styles.totalSection}>
          <Text style={[styles.footerTotal, { color: colors.text }]}>
            Total: {formatCurrency(totalAmount)}
          </Text>
        </View>
        
        <Button
          title="Place Order"
          onPress={handlePlaceOrder}
          loading={loading}
          style={styles.placeOrderButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  itemInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  itemName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  itemDetails: {
    fontSize: typography.fontSize.sm,
  },
  itemTotal: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderTopWidth: 1,
    marginTop: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  totalRow: {
    borderTopWidth: 2,
    marginTop: spacing.md,
    paddingTop: spacing.md,
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  addressForm: {
    gap: spacing.sm,
  },
  addressRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  halfInput: {
    flex: 1,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    marginBottom: spacing.sm,
  },
  paymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentDetails: {
    marginLeft: spacing.md,
    flex: 1,
  },
  paymentName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.xs,
  },
  walletBalance: {
    fontSize: typography.fontSize.sm,
  },
  unavailableText: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
  },
  spacer: {
    height: 120,
  },
  checkoutFooter: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
    ...shadows.lg,
  },
  totalSection: {
    marginBottom: spacing.md,
  },
  footerTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
  placeOrderButton: {
    marginTop: spacing.sm,
  },
});

export default CheckoutScreen;
