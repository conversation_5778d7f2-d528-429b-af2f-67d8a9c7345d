import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  FlatList,
  Modal,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import ApiService from '../../services/api';
import Card from '../../components/common/Card';

const ManageOrdersScreen = ({ navigation }) => {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [updateNotes, setUpdateNotes] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [deliveryContacts, setDeliveryContacts] = useState([]);
  const [loadingContacts, setLoadingContacts] = useState(false);

  const tabs = [
    { id: 'all', label: 'All Orders', count: 0 },
    { id: 'pending', label: 'Pending', count: 0 },
    { id: 'confirmed', label: 'Confirmed', count: 0 },
    { id: 'shipped', label: 'Shipped', count: 0 },
    { id: 'delivered', label: 'Delivered', count: 0 },
  ];

  useEffect(() => {
    loadOrders();
  }, []);

  useEffect(() => {
    filterOrders();
  }, [orders, activeTab]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getOrders({ seller: true });
      if (response.success) {
        setOrders(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load orders:', error);
      Alert.alert('Error', 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const filterOrders = () => {
    let filtered = orders;
    if (activeTab !== 'all') {
      filtered = orders.filter(order => order.status === activeTab);
    }
    setFilteredOrders(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const getOrderStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return { icon: 'time', color: colors.warning, label: 'Pending' };
      case 'confirmed':
        return { icon: 'checkmark-circle', color: colors.info, label: 'Confirmed' };
      case 'shipped':
        return { icon: 'car', color: colors.primary, label: 'Shipped' };
      case 'delivered':
        return { icon: 'checkmark-done', color: colors.success, label: 'Delivered' };
      case 'cancelled':
        return { icon: 'close-circle', color: colors.error, label: 'Cancelled' };
      default:
        return { icon: 'help-circle', color: colors.textSecondary, label: 'Unknown' };
    }
  };

  const getDeliveryStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return { icon: 'time', color: colors.warning, label: 'Pending Assignment' };
      case 'assigned':
        return { icon: 'person', color: colors.info, label: 'Assigned' };
      case 'in_transit':
        return { icon: 'car', color: colors.primary, label: 'In Transit' };
      case 'delivered':
        return { icon: 'home', color: colors.success, label: 'Delivered' };
      case 'failed':
        return { icon: 'warning', color: colors.error, label: 'Failed' };
      default:
        return { icon: 'help-circle', color: colors.textSecondary, label: 'Unknown' };
    }
  };

  const updateOrderStatus = async (orderId, status, notes = '') => {
    try {
      const response = await ApiService.updateOrderStatus(orderId, status, notes);
      if (response.success) {
        Alert.alert('Success', 'Order status updated successfully');
        loadOrders();
        setUpdateModalVisible(false);
      } else {
        Alert.alert('Error', 'Failed to update order status');
      }
    } catch (error) {
      console.error('Failed to update order status:', error);
      Alert.alert('Error', 'Failed to update order status');
    }
  };

  const loadDeliveryContacts = async () => {
    try {
      setLoadingContacts(true);
      const response = await ApiService.getDeliveryContacts();
      if (response.success) {
        setDeliveryContacts(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load delivery contacts:', error);
      Alert.alert('Error', 'Failed to load delivery contacts');
    } finally {
      setLoadingContacts(false);
    }
  };

  const assignDeliveryPerson = async (orderId) => {
    setSelectedOrder(orders.find(order => order.id === orderId));
    await loadDeliveryContacts();
    setAssignModalVisible(true);
  };

  const confirmAssignDelivery = async (contactId) => {
    try {
      const response = await ApiService.assignDeliveryPerson(selectedOrder.id, contactId);
      if (response.success) {
        Alert.alert('Success', 'Delivery person assigned successfully');
        setAssignModalVisible(false);
        setSelectedOrder(null);
        loadOrders();
      } else {
        Alert.alert('Error', 'Failed to assign delivery person');
      }
    } catch (error) {
      console.error('Failed to assign delivery person:', error);
      Alert.alert('Error', 'Failed to assign delivery person');
    }
  };

  const openUpdateModal = (order) => {
    setSelectedOrder(order);
    setNewStatus(order.status);
    setUpdateNotes('');
    setUpdateModalVisible(true);
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <View style={styles.headerContent}>
        <View style={styles.headerText}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Manage Orders
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            Track and update your sales
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.filterButton, { backgroundColor: colors.primary }]}
          onPress={() => {/* Add filter functionality */}}
        >
          <Ionicons name="filter" size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderTabs = () => {
    const tabsWithCounts = tabs.map(tab => ({
      ...tab,
      count: tab.id === 'all' ? orders.length : orders.filter(o => o.status === tab.id).length
    }));

    return (
      <View style={styles.tabsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {tabsWithCounts.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                {
                  backgroundColor: activeTab === tab.id ? colors.primary : colors.backgroundSecondary,
                  borderColor: colors.border,
                }
              ]}
              onPress={() => setActiveTab(tab.id)}
            >
              <Text style={[
                styles.tabText,
                { color: activeTab === tab.id ? colors.white : colors.textSecondary }
              ]}>
                {tab.label}
              </Text>
              <View style={[
                styles.tabBadge,
                { backgroundColor: activeTab === tab.id ? colors.white : colors.primary }
              ]}>
                <Text style={[
                  styles.tabBadgeText,
                  { color: activeTab === tab.id ? colors.primary : colors.white }
                ]}>
                  {tab.count}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderOrderItem = ({ item }) => {
    const statusInfo = getOrderStatusInfo(item.status);
    const deliveryInfo = getDeliveryStatusInfo(item.deliveryStatus);

    return (
      <Card style={[styles.orderCard, { backgroundColor: colors.surface }]}>
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderId, { color: colors.text }]}>
              Order #{item.id.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.orderDate, { color: colors.textSecondary }]}>
              {new Date(item.createdAt).toLocaleDateString()}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={16} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>

        <View style={styles.orderDetails}>
          <Text style={[styles.buyerName, { color: colors.textSecondary }]}>
            Buyer: {item.buyer?.firstName} {item.buyer?.lastName}
          </Text>
          <Text style={[styles.orderAmount, { color: colors.primary }]}>
            KES {item.totalAmount?.toLocaleString()}
          </Text>
        </View>

        <View style={styles.deliveryInfo}>
          <View style={styles.deliveryStatus}>
            <Ionicons name={deliveryInfo.icon} size={16} color={deliveryInfo.color} />
            <Text style={[styles.deliveryText, { color: deliveryInfo.color }]}>
              {deliveryInfo.label}
            </Text>
          </View>
          <Text style={[styles.deliveryLocation, { color: colors.textSecondary }]}>
            {item.deliveryCounty}, {item.deliveryTown}
          </Text>
        </View>

        <View style={styles.orderActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.info }]}
            onPress={() => navigation.navigate('OrderTracking', { orderId: item.id })}
          >
            <Ionicons name="eye" size={16} color={colors.white} />
            <Text style={[styles.actionButtonText, { color: colors.white }]}>
              View
            </Text>
          </TouchableOpacity>

          {item.status === 'pending' && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.success }]}
              onPress={() => updateOrderStatus(item.id, 'confirmed')}
            >
              <Ionicons name="checkmark" size={16} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Confirm
              </Text>
            </TouchableOpacity>
          )}

          {item.status === 'confirmed' && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={() => updateOrderStatus(item.id, 'shipped')}
            >
              <Ionicons name="car" size={16} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Ship
              </Text>
            </TouchableOpacity>
          )}

          {item.deliveryStatus === 'pending' && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.warning }]}
              onPress={() => assignDeliveryPerson(item.id)}
            >
              <Ionicons name="person-add" size={16} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Assign
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.textSecondary }]}
            onPress={() => openUpdateModal(item)}
          >
            <Ionicons name="create" size={16} color={colors.white} />
            <Text style={[styles.actionButtonText, { color: colors.white }]}>
              Update
            </Text>
          </TouchableOpacity>
        </View>
      </Card>
    );
  };

  const renderUpdateModal = () => (
    <Modal
      visible={updateModalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setUpdateModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Update Order Status
          </Text>

          <View style={styles.statusOptions}>
            {['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'].map((status) => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.statusOption,
                  {
                    backgroundColor: newStatus === status ? colors.primary : colors.backgroundSecondary,
                    borderColor: colors.border,
                  }
                ]}
                onPress={() => setNewStatus(status)}
              >
                <Text style={[
                  styles.statusOptionText,
                  { color: newStatus === status ? colors.white : colors.text }
                ]}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <TextInput
            style={[styles.notesInput, { backgroundColor: colors.backgroundSecondary, color: colors.text }]}
            placeholder="Add notes (optional)"
            placeholderTextColor={colors.textSecondary}
            value={updateNotes}
            onChangeText={setUpdateNotes}
            multiline
            numberOfLines={3}
          />

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: colors.textSecondary }]}
              onPress={() => setUpdateModalVisible(false)}
            >
              <Text style={[styles.modalButtonText, { color: colors.white }]}>
                Cancel
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: colors.primary }]}
              onPress={() => updateOrderStatus(selectedOrder?.id, newStatus, updateNotes)}
            >
              <Text style={[styles.modalButtonText, { color: colors.white }]}>
                Update
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading orders...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {renderHeader()}
      {renderTabs()}
      
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.ordersList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="bag-outline" size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No orders found
            </Text>
          </View>
        }
        showsVerticalScrollIndicator={false}
      />

      {renderUpdateModal()}

      {/* Delivery Assignment Modal */}
      <Modal
        visible={assignModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setAssignModalVisible(false)}>
              <Text style={[styles.cancelText, { color: colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Assign Delivery Person
            </Text>
            <TouchableOpacity onPress={() => navigation.navigate('DeliveryContacts')}>
              <Text style={[styles.addText, { color: colors.primary }]}>Add New</Text>
            </TouchableOpacity>
          </View>

          {selectedOrder && (
            <View style={[styles.orderSummary, { backgroundColor: colors.surface }]}>
              <Text style={[styles.orderSummaryTitle, { color: colors.text }]}>
                Order #{selectedOrder.id?.slice(-8).toUpperCase()}
              </Text>
              <Text style={[styles.orderSummaryDetails, { color: colors.textSecondary }]}>
                Deliver to: {selectedOrder.deliveryAddress}
              </Text>
              <Text style={[styles.orderSummaryDetails, { color: colors.textSecondary }]}>
                Customer: {selectedOrder.buyer?.firstName} {selectedOrder.buyer?.lastName}
              </Text>
            </View>
          )}

          <View style={styles.contactsList}>
            <Text style={[styles.contactsTitle, { color: colors.text }]}>
              Select Delivery Contact
            </Text>

            {loadingContacts ? (
              <View style={styles.loadingContainer}>
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Loading contacts...
                </Text>
              </View>
            ) : deliveryContacts.length === 0 ? (
              <View style={styles.emptyContacts}>
                <Ionicons name="people-outline" size={48} color={colors.textSecondary} />
                <Text style={[styles.emptyContactsText, { color: colors.textSecondary }]}>
                  No delivery contacts found
                </Text>
                <TouchableOpacity
                  style={[styles.addContactButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    setAssignModalVisible(false);
                    navigation.navigate('DeliveryContacts');
                  }}
                >
                  <Text style={[styles.addContactButtonText, { color: colors.white }]}>
                    Add Delivery Contact
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                data={deliveryContacts.filter(contact => contact.isActive)}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[styles.contactItem, { backgroundColor: colors.surface }]}
                    onPress={() => confirmAssignDelivery(item.userId || item.id)}
                  >
                    <View style={styles.contactInfo}>
                      <Text style={[styles.contactName, { color: colors.text }]}>
                        {item.name}
                      </Text>
                      <Text style={[styles.contactPhone, { color: colors.textSecondary }]}>
                        {item.phone}
                      </Text>
                      {item.notes && (
                        <Text style={[styles.contactNotes, { color: colors.textTertiary }]}>
                          {item.notes}
                        </Text>
                      )}
                    </View>
                    <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 15,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  tabBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  ordersList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  orderCard: {
    padding: 15,
    borderRadius: 12,
    marginBottom: 15,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  orderDate: {
    fontSize: 12,
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  buyerName: {
    fontSize: 14,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  deliveryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  deliveryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryText: {
    fontSize: 12,
    marginLeft: 4,
  },
  deliveryLocation: {
    fontSize: 12,
  },
  orderActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    padding: 20,
    borderRadius: 12,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  statusOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  statusOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
    borderWidth: 1,
  },
  statusOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  notesInput: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  cancelText: {
    fontSize: 16,
  },
  addText: {
    fontSize: 16,
    fontWeight: '600',
  },
  orderSummary: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
  },
  orderSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  orderSummaryDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  contactsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  contactsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  emptyContacts: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyContactsText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  addContactButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addContactButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
    marginBottom: 2,
  },
  contactNotes: {
    fontSize: 12,
    fontStyle: 'italic',
  },
};

export default ManageOrdersScreen;
