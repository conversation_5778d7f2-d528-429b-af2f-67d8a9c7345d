import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  Dimensions,
  Share,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useApp } from '../../context/AppContext';
// Removed useOptimisticUpdate - not needed for instant switching
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const { width } = Dimensions.get('window');

// Responsive Image Component that adapts to natural image dimensions
const ResponsiveImage = ({ uri, screenWidth }) => {
  const [imageDimensions, setImageDimensions] = useState({ width: screenWidth, height: 300 });
  // Removed imageLoaded state - not needed for instant display

  React.useEffect(() => {
    if (uri && uri !== 'https://via.placeholder.com/800x600/f5f5f5/cccccc?text=No+Image+Available') {
      // Get natural image dimensions
      Image.getSize(
        uri,
        (naturalWidth, naturalHeight) => {
          // Calculate responsive dimensions maintaining aspect ratio
          const aspectRatio = naturalHeight / naturalWidth;
          const maxHeight = 500; // Maximum height to prevent extremely tall images
          const minHeight = 200; // Minimum height for very wide images

          let displayWidth = screenWidth;
          let displayHeight = displayWidth * aspectRatio;

          // Constrain height within reasonable bounds
          if (displayHeight > maxHeight) {
            displayHeight = maxHeight;
            displayWidth = displayHeight / aspectRatio;
          } else if (displayHeight < minHeight) {
            displayHeight = minHeight;
            displayWidth = displayHeight / aspectRatio;
          }

          setImageDimensions({
            width: displayWidth,
            height: displayHeight
          });

          console.log(`📐 IMAGE DIMENSIONS: ${naturalWidth}x${naturalHeight} → ${Math.round(displayWidth)}x${Math.round(displayHeight)}`);
        },
        () => {
          console.log('📐 IMAGE DIMENSIONS: Failed to get image size, using defaults');
          setImageDimensions({ width: screenWidth, height: 300 });
        }
      );
    }
  }, [uri, screenWidth]);

  return (
    <View style={{
      width: screenWidth,
      height: imageDimensions.height,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#ffffff'
    }}>
      <Image
        source={{ uri }}
        style={{
          width: imageDimensions.width,
          height: imageDimensions.height,
        }}
        resizeMode="contain" // Maintain aspect ratio, no cropping
      />
    </View>
  );
};

const ProductDetailsScreen = ({ route, navigation }) => {
  const { product: initialProduct, productId } = route.params || {};
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [product, setProduct] = useState(initialProduct); // Use initial product immediately
  // Removed loading state - instant switching only
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);

  // Refs for instant product tracking
  const scrollViewRef = useRef(null);
  const autoScrollTimer = useRef(null);
  const currentProductId = useRef(initialProduct?.id || null);

  // Auto-scroll functionality for image carousel
  const startAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }

    if (product?.images && product.images.length > 1) {
      autoScrollTimer.current = setInterval(() => {
        setSelectedImageIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % product.images.length;
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({
              x: nextIndex * width,
              animated: true,
            });
          }
          return nextIndex;
        });
      }, 3000); // Auto-scroll every 3 seconds
    }
  }, [product?.images, width]);

  const stopAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
      autoScrollTimer.current = null;
    }
  }, []);

  // Instant product switching - no loading delays
  const loadProductDetails = useCallback(async (productToLoad) => {
    if (!productToLoad?.id) return;

    const targetProductId = productToLoad.id;

    // If we already have this product, don't reload
    if (currentProductId.current === targetProductId && product?.id === targetProductId) {
      console.log(`⚡ INSTANT: Product ${targetProductId} already loaded`);
      return;
    }

    console.log(`⚡ INSTANT: Switching to product ${targetProductId}`);

    // Set product immediately if we have initial data
    if (productToLoad.name) {
      setProduct(productToLoad);
      currentProductId.current = targetProductId;
      setSelectedImageIndex(0);
      setQuantity(1);
    }

    // Load enhanced data in background without showing loading
    try {
      const response = await ApiService.getProductWithOwnership(targetProductId);
      if (response.success && currentProductId.current === targetProductId) {
        setProduct(response.data);
        console.log(`✅ INSTANT: Enhanced data loaded for product ${targetProductId}`);
      }
    } catch (error) {
      console.log(`⚠️ INSTANT: Enhanced data failed for product ${targetProductId}, using initial data`);
    }
  }, [product]);

  // Check wishlist status
  const checkWishlistStatus = useCallback(async (productToCheck) => {
    if (!productToCheck?.id) return;

    try {
      const response = await ApiService.getWishlist();
      if (response.success && response.data && Array.isArray(response.data)) {
        const isInList = response.data.some(item => item.product && item.product.id === productToCheck.id);
        setIsInWishlist(isInList);
      } else {
        // If wishlist is empty or null, product is not in wishlist
        setIsInWishlist(false);
      }
    } catch (error) {
      console.error('Failed to check wishlist status:', error);
      // On error, assume product is not in wishlist
      setIsInWishlist(false);
    }
  }, []);

  // Instant focus effect - no delays
  useFocusEffect(
    useCallback(() => {
      const productToLoad = initialProduct || (productId ? { id: productId } : null);

      if (!productToLoad) {
        Alert.alert('Error', 'Product not found.', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
        return;
      }

      const targetProductId = productToLoad.id;

      // Switch instantly if different product
      if (currentProductId.current !== targetProductId) {
        console.log(`⚡ INSTANT SWITCH: ${currentProductId.current} → ${targetProductId}`);
        loadProductDetails(productToLoad);
        checkWishlistStatus(productToLoad);
      }

      // Start auto-scroll immediately
      const timer = setTimeout(() => {
        if (product?.images?.length > 1) {
          startAutoScroll();
        }
      }, 500); // Minimal delay

      return () => {
        clearTimeout(timer);
        stopAutoScroll();
      };
    }, [initialProduct, productId, navigation, loadProductDetails, checkWishlistStatus, startAutoScroll, stopAutoScroll, product])
  );

  // Instant route change handling
  React.useEffect(() => {
    const targetProduct = initialProduct || (productId ? { id: productId } : null);
    const targetProductId = targetProduct?.id;

    if (targetProductId && targetProductId !== currentProductId.current) {
      console.log(`⚡ ROUTE CHANGE: Instant switch ${currentProductId.current} → ${targetProductId}`);
      loadProductDetails(targetProduct);
      checkWishlistStatus(targetProduct);
    }
  }, [initialProduct?.id, productId, loadProductDetails, checkWishlistStatus]);

  // Cleanup timer on unmount
  React.useEffect(() => {
    return () => {
      stopAutoScroll();
    };
  }, [stopAutoScroll]);



  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleAddToCart = async () => {
    try {
      const response = await ApiService.addToCart(product.id, quantity);
      if (response.success) {
        Alert.alert('Success', 'Product added to cart');
      } else if (response.error && response.error.includes('buyer')) {
        // Auto-register as buyer if needed
        try {
          const registerResponse = await ApiService.autoRegisterAsBuyer(user.id);
          if (registerResponse.success) {
            // Retry adding to cart after registration
            const retryResponse = await ApiService.addToCart(product.id, quantity);
            if (retryResponse.success) {
              Alert.alert('Success', 'Product added to cart! You have been automatically registered as a buyer.');
            } else {
              Alert.alert('Error', 'Failed to add to cart after registration');
            }
          } else {
            Alert.alert('Error', 'Failed to setup buyer account. Please try again.');
          }
        } catch (registerError) {
          Alert.alert('Error', 'Failed to setup buyer account. Please try again.');
        }
      } else {
        Alert.alert('Error', response.error || 'Failed to add to cart');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to add to cart');
    }
  };

  const handleBuyNow = async () => {
    try {
      // Check if user can proceed to checkout, auto-register if needed
      const checkResponse = await ApiService.checkBuyerEligibility(user.id);

      if (!checkResponse.success && checkResponse.error && checkResponse.error.includes('buyer')) {
        // Auto-register as buyer
        const registerResponse = await ApiService.autoRegisterAsBuyer(user.id);
        if (!registerResponse.success) {
          Alert.alert('Error', 'Failed to setup buyer account. Please try again.');
          return;
        }
      }

      // Proceed to checkout
      navigation.navigate('Checkout', {
        items: [{ ...product, quantity }],
        total: product.price * quantity
      });
    } catch (error) {
      // If API calls fail, still allow checkout - registration can happen there
      navigation.navigate('Checkout', {
        items: [{ ...product, quantity }],
        total: product.price * quantity
      });
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out ${product.name} for ${formatCurrency(product.price)} on VaultKe Marketplace!`,
        url: `vaultke://product/${product.id}`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleWishlistToggle = async () => {
    if (wishlistLoading) return;

    try {
      setWishlistLoading(true);

      if (isInWishlist) {
        const response = await ApiService.removeFromWishlist(product.id);
        if (response.success) {
          setIsInWishlist(false);
          Alert.alert('Success', 'Removed from wishlist');
        }
      } else {
        const response = await ApiService.addToWishlist(product.id);
        if (response.success) {
          setIsInWishlist(true);
          Alert.alert('Success', 'Added to wishlist');
        }
      }
    } catch (error) {
      console.error('Failed to toggle wishlist:', error);
      Alert.alert('Error', 'Failed to update wishlist');
    } finally {
      setWishlistLoading(false);
    }
  };

  const handleContactSeller = async () => {
    try {
      // Use sellerId or seller.id as fallback
      const sellerId = product.sellerId || product.seller?.id;

      if (!sellerId) {
        Alert.alert('Error', 'Seller information not available');
        return;
      }

      // Check if user is trying to contact themselves
      if (sellerId === user?.id) {
        Alert.alert('Info', 'This is your own product. You cannot start a chat with yourself.');
        return;
      }

      // Create or get existing private chat room with the seller about this product
      const response = await ApiService.createPrivateChat(sellerId, {
        productId: product.id,
        productName: product.name,
        context: 'product_inquiry'
      });

      if (response.success) {
        navigation.navigate('ChatRoom', {
          roomId: response.data.id,
          roomName: `${product.seller?.firstName} ${product.seller?.lastName}`,
          roomType: 'private',
          productContext: {
            id: product.id,
            name: product.name,
            image: product.images?.[0]
          }
        });
      } else {
        Alert.alert('Error', 'Failed to start conversation with seller');
      }
    } catch (error) {
      console.error('Failed to create chat with seller:', error);
      Alert.alert('Error', 'Failed to start conversation with seller');
    }
  };

  const renderImageGallery = (productData = product) => (
    <View style={styles.imageGallery}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(event.nativeEvent.contentOffset.x / width);
          setSelectedImageIndex(index);
        }}
        onScrollBeginDrag={() => {
          // Stop auto-scroll when user manually scrolls
          stopAutoScroll();
        }}
        onScrollEndDrag={() => {
          // Restart auto-scroll after user stops scrolling
          setTimeout(() => {
            startAutoScroll();
          }, 2000); // Wait 2 seconds before restarting auto-scroll
        }}
      >
        {(productData.images || ['https://via.placeholder.com/800x600/f5f5f5/cccccc?text=No+Image+Available']).map((image, index) => (
          <ResponsiveImage
            key={`image-${index}-${productData.id || 'default'}`}
            uri={image}
            screenWidth={width}
          />
        ))}
      </ScrollView>

      {productData.images && productData.images.length > 1 && (
        <View style={styles.imageIndicators}>
          {productData.images.map((_, index) => (
            <TouchableOpacity
              key={`indicator-${index}-${productData.id || 'default'}`}
              style={[
                styles.indicator,
                {
                  backgroundColor: index === selectedImageIndex ? colors.primary : colors.textTertiary,
                }
              ]}
              onPress={() => {
                setSelectedImageIndex(index);
                if (scrollViewRef.current) {
                  scrollViewRef.current.scrollTo({
                    x: index * width,
                    animated: true,
                  });
                }
                // Stop and restart auto-scroll when user taps indicator
                stopAutoScroll();
                setTimeout(() => {
                  startAutoScroll();
                }, 2000);
              }}
            />
          ))}
        </View>
      )}
    </View>
  );

  const renderProductInfo = (productData = product) => (
    <Card style={styles.productInfo}>
      <View style={styles.productHeader}>
        <View style={styles.productTitleSection}>
          <Text style={[styles.productName, { color: colors.text }]}>
            {productData.name}
          </Text>
          <Text style={[styles.productPrice, { color: colors.primary }]}>
            {formatCurrency(productData.price)}
          </Text>
        </View>

        <View style={styles.headerActions}>
          <TouchableOpacity
            onPress={handleWishlistToggle}
            style={styles.actionButton}
            disabled={wishlistLoading}
          >
            <Ionicons
              name={isInWishlist ? "heart" : "heart-outline"}
              size={24}
              color={isInWishlist ? colors.error : colors.textSecondary}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={handleShare} style={styles.actionButton}>
            <Ionicons name="share-outline" size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.productMeta}>
        <View style={styles.ratingSection}>
          <TouchableOpacity
            style={styles.ratingContainer}
            onPress={() => navigation.navigate('ProductReviews', {
              productId: product.id,
              productName: product.name
            })}
          >
            <Ionicons name="star" size={16} color={colors.warning} />
            <Text style={[styles.ratingText, { color: colors.textSecondary }]}>
              {product.rating || 0} ({product.total_ratings || 0} reviews)
            </Text>
            <Ionicons name="chevron-forward" size={16} color={colors.textSecondary} style={styles.chevron} />
          </TouchableOpacity>
        </View>

        <Text style={[styles.stockText, { color: product.stock > 0 ? colors.success : colors.error }]}>
          {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
        </Text>
      </View>

      {/* Enhanced Product Description */}
      <View style={styles.descriptionContainer}>
        <Text style={[styles.descriptionTitle, { color: colors.text }]}>
          Description
        </Text>
        <Text style={[styles.productDescription, { color: colors.textSecondary }]}>
          {product.description || 'No description available for this product.'}
        </Text>
      </View>

      {product.tags && product.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {(Array.isArray(product.tags)
            ? product.tags
            : (typeof product.tags === 'string' ? product.tags.split(',') : [])
          ).map((tag, index) => (
            <View key={index} style={[styles.tag, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.tagText, { color: colors.textSecondary }]}>
                {typeof tag === 'string' ? tag.trim() : String(tag).trim()}
              </Text>
            </View>
          ))}
        </View>
      )}
    </Card>
  );

  const renderSellerInfo = () => (
    <Card style={styles.sellerInfo}>
      <View style={styles.sellerHeader}>
        <View style={styles.sellerDetails}>
          <Text style={[styles.sellerLabel, { color: colors.textSecondary }]}>
            Sold by
          </Text>
          <Text style={[styles.sellerName, { color: colors.text }]}>
            {product.seller?.first_name} {product.seller?.last_name}
          </Text>
          <View style={styles.sellerRating}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={[styles.sellerRatingText, { color: colors.textSecondary }]}>
              {product.seller?.rating || 0} seller rating
            </Text>
          </View>
        </View>

        <Button
          title="Contact"
          variant="outline"
          size="small"
          onPress={handleContactSeller}
          icon={<Ionicons name="chatbubble-outline" size={16} color={colors.primary} />}
        />
      </View>

      <Text style={[styles.sellerLocation, { color: colors.textTertiary }]}>
        <Ionicons name="location-outline" size={14} color={colors.textTertiary} />
        {' '}{product.town}, {product.county}
      </Text>
    </Card>
  );

  const renderQuantitySelector = () => (
    <Card style={styles.quantitySelector}>
      <Text style={[styles.quantityLabel, { color: colors.text }]}>
        Quantity
      </Text>
      <View style={styles.quantityControls}>
        <TouchableOpacity
          style={[styles.quantityButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setQuantity(Math.max(1, quantity - 1))}
          disabled={quantity <= 1}
        >
          <Ionicons name="remove" size={20} color={colors.text} />
        </TouchableOpacity>

        <Text style={[styles.quantityText, { color: colors.text }]}>
          {quantity}
        </Text>

        <TouchableOpacity
          style={[styles.quantityButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setQuantity(Math.min(product.stock, quantity + 1))}
          disabled={quantity >= product.stock}
        >
          <Ionicons name="add" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderActionButtons = () => {
    const buyerEligibility = product?.buyerEligibility;
    const isOwner = buyerEligibility?.isOwner;
    // Removed canBuy - not used in current logic

    // Don't show buy buttons for own products
    if (isOwner) {
      return (
        <View style={[styles.actionButtons, { backgroundColor: colors.surface }]}>
          <View style={styles.ownerMessage}>
            <Ionicons name="information-circle" size={20} color={colors.primary} />
            <Text style={[styles.ownerMessageText, { color: colors.text }]}>
              This is your product
            </Text>
          </View>
        </View>
      );
    }

    // Registration requirement removed - users can now view and purchase products directly
    // Registration will happen automatically during the purchase process if needed

    // Show normal buy buttons for eligible buyers
    return (
      <View style={[styles.actionButtons, { backgroundColor: colors.surface }]}>
        <Button
          title="Add to Cart"
          variant="outline"
          onPress={handleAddToCart}
          disabled={product.stock === 0}
          style={styles.addToCartButton}
          icon={<Ionicons name="cart-outline" size={20} color={colors.primary} />}
        />

        <Button
          title="Buy Now"
          onPress={handleBuyNow}
          disabled={product.stock === 0}
          style={styles.buyNowButton}
        />
      </View>
    );
  };

  // Handle missing product - use fallback or route params
  const displayProduct = product || initialProduct || {
    id: productId,
    name: 'Product Details',
    price: 0,
    images: ['https://via.placeholder.com/600x400/f5f5f5/cccccc?text=Loading...'],
    description: 'Loading product information...',
    stock: 0
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderImageGallery(displayProduct)}
        {renderProductInfo(displayProduct)}
        {renderSellerInfo(displayProduct)}
        {renderQuantitySelector(displayProduct)}

        {/* Spacer for fixed action buttons */}
        <View style={styles.spacer} />
      </ScrollView>

      {renderActionButtons(displayProduct)}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  imageGallery: {
    position: 'relative',
    marginBottom: spacing.md,
    backgroundColor: '#ffffff', // Pure white for image clarity
    // Minimal styling for instant display
    borderRadius: borderRadius.md,
    overflow: 'hidden',
  },
  // Removed fixed productImage styles - now using ResponsiveImage component
  imageIndicators: {
    position: 'absolute',
    bottom: spacing.md,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  productInfo: {
    margin: spacing.md,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  productTitleSection: {
    flex: 1,
    marginRight: spacing.md,
  },
  productName: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.sm,
  },
  productPrice: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: spacing.sm,
    marginLeft: spacing.xs,
  },
  productMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  ratingSection: {
    flex: 1,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
  },
  chevron: {
    marginLeft: spacing.xs,
  },
  stockText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  descriptionContainer: {
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  descriptionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
  },
  productDescription: {
    fontSize: typography.fontSize.base,
    lineHeight: 24, // Increased line height for better readability
    textAlign: 'justify', // Justify text for better appearance
    // Allow text to expand based on content length
    minHeight: 60, // Minimum height to ensure consistency
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.sm,
    backgroundColor: 'rgba(0,0,0,0.02)', // Very light background for better readability
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.md,
  },
  tag: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  tagText: {
    fontSize: typography.fontSize.sm,
  },
  sellerInfo: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  sellerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  sellerDetails: {
    flex: 1,
  },
  sellerLabel: {
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.xs,
  },
  sellerName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.xs,
  },
  sellerRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerRatingText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
  },
  sellerLocation: {
    fontSize: typography.fontSize.sm,
  },
  quantitySelector: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  quantityLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.md,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginHorizontal: spacing.xl,
    minWidth: 40,
    textAlign: 'center',
  },
  spacer: {
    height: 100,
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: spacing.md,
    ...shadows.lg,
  },
  addToCartButton: {
    flex: 1,
    marginRight: spacing.sm,
  },
  buyNowButton: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  fullWidthButton: {
    flex: 1,
  },
  ownerMessage: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    borderRadius: borderRadius.md,
  },
  ownerMessageText: {
    marginLeft: spacing.sm,
    fontSize: typography.fontSize.base,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  loadingText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  loadingSubText: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ProductDetailsScreen;
