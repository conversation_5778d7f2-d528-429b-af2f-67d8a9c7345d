import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import ApiService from '../../services/api';

const DELIVERY_STATUSES = {
  assigned: { label: 'Assigned', color: '#FF9500', icon: 'person-add', nextStatus: 'picked_up' },
  picked_up: { label: 'Picked Up', color: '#007AFF', icon: 'checkmark-circle', nextStatus: 'in_transit' },
  in_transit: { label: 'In Transit', color: '#5856D6', icon: 'car', nextStatus: 'out_for_delivery' },
  out_for_delivery: { label: 'Out for Delivery', color: '#34C759', icon: 'location', nextStatus: 'delivered' },
  delivered: { label: 'Delivered', color: '#30D158', icon: 'checkmark-circle-2', nextStatus: null },
  failed: { label: 'Delivery Failed', color: '#FF3B30', icon: 'close-circle', nextStatus: null },
};

const DeliveryPersonDashboard = ({ navigation }) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [deliveries, setDeliveries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('assigned'); // assigned, in_progress, completed
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [selectedDelivery, setSelectedDelivery] = useState(null);
  const [otpCode, setOtpCode] = useState('');
  const [deliveryNotes, setDeliveryNotes] = useState('');

  useEffect(() => {
    loadMyDeliveries();
  }, [activeTab]);

  const loadMyDeliveries = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getMyDeliveries(activeTab);
      if (response.success) {
        setDeliveries(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load deliveries:', error);
      Alert.alert('Error', 'Failed to load deliveries');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMyDeliveries();
    setRefreshing(false);
  };

  const updateDeliveryStatus = async (deliveryId, newStatus, notes = '') => {
    try {
      const response = await ApiService.updateDeliveryStatus(deliveryId, {
        status: newStatus,
        notes: notes,
        updatedBy: user.id,
      });
      
      if (response.success) {
        Alert.alert('Success', 'Delivery status updated successfully');
        loadMyDeliveries();
        
        // Send notification to buyer if out for delivery
        if (newStatus === 'out_for_delivery') {
          await ApiService.notifyBuyerDeliveryArrival(deliveryId);
        }
      } else {
        Alert.alert('Error', response.error || 'Failed to update delivery status');
      }
    } catch (error) {
      console.error('Failed to update delivery status:', error);
      Alert.alert('Error', 'Failed to update delivery status');
    }
  };

  const handleDeliveryComplete = (delivery) => {
    setSelectedDelivery(delivery);
    setShowOtpModal(true);
  };

  const confirmDeliveryWithOtp = async () => {
    if (!otpCode.trim()) {
      Alert.alert('Error', 'Please enter the OTP code from the buyer');
      return;
    }

    try {
      const response = await ApiService.confirmDeliveryWithOtp(selectedDelivery.id, {
        otpCode: otpCode.trim(),
        notes: deliveryNotes,
        deliveredBy: user.id,
      });

      if (response.success) {
        Alert.alert('Success', 'Delivery confirmed successfully!');
        setShowOtpModal(false);
        setOtpCode('');
        setDeliveryNotes('');
        setSelectedDelivery(null);
        loadMyDeliveries();
      } else {
        Alert.alert('Error', response.error || 'Invalid OTP code');
      }
    } catch (error) {
      console.error('Failed to confirm delivery:', error);
      Alert.alert('Error', 'Failed to confirm delivery');
    }
  };

  const callBuyer = (phoneNumber) => {
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const openMaps = (address) => {
    const encodedAddress = encodeURIComponent(address);
    Linking.openURL(`https://maps.google.com/?q=${encodedAddress}`);
  };

  const getFilteredDeliveries = () => {
    switch (activeTab) {
      case 'assigned':
        return deliveries.filter(d => d.status === 'assigned');
      case 'in_progress':
        return deliveries.filter(d => ['picked_up', 'in_transit', 'out_for_delivery'].includes(d.status));
      case 'completed':
        return deliveries.filter(d => ['delivered', 'failed'].includes(d.status));
      default:
        return deliveries;
    }
  };

  const renderTabButton = (tab, label) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        { backgroundColor: activeTab === tab ? colors.primary : 'transparent' }
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <Text style={[
        styles.tabText,
        { color: activeTab === tab ? colors.white : colors.textSecondary }
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderDeliveryItem = ({ item }) => {
    const statusInfo = DELIVERY_STATUSES[item.status] || { label: item.status, color: colors.textSecondary, icon: 'help' };
    const canUpdateStatus = statusInfo.nextStatus && activeTab !== 'completed';

    return (
      <Card style={[styles.deliveryCard, { backgroundColor: colors.surface }]}>
        <View style={styles.deliveryHeader}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderId, { color: colors.text }]}>
              Order #{item.orderId?.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.sellerName, { color: colors.textSecondary }]}>
              From: {item.sellerName}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={16} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>

        <View style={styles.buyerInfo}>
          <View style={styles.buyerDetails}>
            <Ionicons name="person" size={16} color={colors.primary} />
            <Text style={[styles.buyerName, { color: colors.text }]}>
              {item.buyerName}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.callButton}
            onPress={() => callBuyer(item.buyerPhone)}
          >
            <Ionicons name="call" size={16} color={colors.success} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.addressContainer}
          onPress={() => openMaps(item.deliveryAddress)}
        >
          <Ionicons name="location" size={16} color={colors.primary} />
          <View style={styles.addressInfo}>
            <Text style={[styles.addressText, { color: colors.text }]}>
              {item.deliveryAddress}
            </Text>
            <Text style={[styles.locationText, { color: colors.textSecondary }]}>
              {item.deliveryTown}, {item.deliveryCounty}
            </Text>
          </View>
          <Ionicons name="map" size={16} color={colors.primary} />
        </TouchableOpacity>

        <View style={styles.productInfo}>
          <Text style={[styles.productTitle, { color: colors.text }]}>
            Products ({item.products?.length || 1})
          </Text>
          {item.products?.slice(0, 2).map((product, index) => (
            <Text key={index} style={[styles.productName, { color: colors.textSecondary }]}>
              • {product.name} (Qty: {product.quantity})
            </Text>
          ))}
          {item.products?.length > 2 && (
            <Text style={[styles.moreProducts, { color: colors.primary }]}>
              +{item.products.length - 2} more items
            </Text>
          )}
        </View>

        <View style={styles.deliveryActions}>
          {canUpdateStatus && (
            <Button
              title={`Mark as ${DELIVERY_STATUSES[statusInfo.nextStatus]?.label}`}
              onPress={() => {
                if (statusInfo.nextStatus === 'delivered') {
                  handleDeliveryComplete(item);
                } else {
                  updateDeliveryStatus(item.id, statusInfo.nextStatus);
                }
              }}
              style={[styles.actionButton, { backgroundColor: statusInfo.color }]}
              textStyle={{ color: colors.white }}
            />
          )}
          
          {item.status === 'out_for_delivery' && (
            <Button
              title="Notify Arrival"
              onPress={() => ApiService.notifyBuyerDeliveryArrival(item.id)}
              style={[styles.actionButton, { backgroundColor: colors.warning }]}
              textStyle={{ color: colors.white }}
            />
          )}
        </View>

        {item.notes && (
          <View style={styles.notesContainer}>
            <Text style={[styles.notesLabel, { color: colors.textSecondary }]}>Notes:</Text>
            <Text style={[styles.notesText, { color: colors.text }]}>{item.notes}</Text>
          </View>
        )}
      </Card>
    );
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  const filteredDeliveries = getFilteredDeliveries();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          My Deliveries
        </Text>
        <TouchableOpacity onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
        {renderTabButton('assigned', 'Assigned')}
        {renderTabButton('in_progress', 'In Progress')}
        {renderTabButton('completed', 'Completed')}
      </View>

      <FlatList
        data={filteredDeliveries}
        renderItem={renderDeliveryItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="car-outline" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No {activeTab} deliveries
            </Text>
            <Text style={[styles.emptySubtext, { color: colors.textTertiary }]}>
              {activeTab === 'assigned' 
                ? 'New delivery assignments will appear here'
                : activeTab === 'in_progress'
                ? 'Deliveries in progress will appear here'
                : 'Completed deliveries will appear here'
              }
            </Text>
          </View>
        }
      />

      {/* OTP Confirmation Modal */}
      <Modal
        visible={showOtpModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowOtpModal(false)}>
              <Text style={[styles.cancelText, { color: colors.primary }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Confirm Delivery
            </Text>
            <TouchableOpacity onPress={confirmDeliveryWithOtp}>
              <Text style={[styles.confirmText, { color: colors.primary }]}>Confirm</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.otpContainer}>
              <Ionicons name="shield-checkmark" size={48} color={colors.success} />
              <Text style={[styles.otpTitle, { color: colors.text }]}>
                Enter OTP from Buyer
              </Text>
              <Text style={[styles.otpSubtitle, { color: colors.textSecondary }]}>
                Ask the buyer to provide the OTP code they received
              </Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>OTP Code</Text>
              <TextInput
                style={[styles.otpInput, { backgroundColor: colors.surface, color: colors.text }]}
                value={otpCode}
                onChangeText={setOtpCode}
                placeholder="Enter 6-digit OTP"
                placeholderTextColor={colors.textSecondary}
                keyboardType="number-pad"
                maxLength={6}
                textAlign="center"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Delivery Notes (Optional)</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={deliveryNotes}
                onChangeText={setDeliveryNotes}
                placeholder="Add any delivery notes"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    marginHorizontal: spacing.xs,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContainer: {
    padding: spacing.md,
  },
  deliveryCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  deliveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  sellerName: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  buyerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  buyerDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  buyerName: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  callButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: '#E8F5E8',
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    padding: spacing.sm,
    backgroundColor: '#F8F9FA',
    borderRadius: borderRadius.md,
  },
  addressInfo: {
    flex: 1,
    marginLeft: spacing.xs,
  },
  addressText: {
    fontSize: 14,
    fontWeight: '500',
  },
  locationText: {
    fontSize: 12,
    marginTop: spacing.xs,
  },
  productInfo: {
    marginBottom: spacing.sm,
  },
  productTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  productName: {
    fontSize: 12,
    marginBottom: spacing.xs,
  },
  moreProducts: {
    fontSize: 12,
    fontWeight: '500',
  },
  deliveryActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  notesContainer: {
    marginTop: spacing.sm,
    padding: spacing.sm,
    backgroundColor: '#F8F9FA',
    borderRadius: borderRadius.md,
  },
  notesLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  notesText: {
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cancelText: {
    fontSize: 16,
  },
  confirmText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    padding: spacing.md,
  },
  otpContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  otpTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  otpSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: spacing.md,
  },
  inputGroup: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: 16,
  },
  otpInput: {
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
    fontSize: 24,
    fontWeight: '600',
    letterSpacing: 4,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
});

export default DeliveryPersonDashboard;
