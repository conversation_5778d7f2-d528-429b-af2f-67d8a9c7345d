import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const REGISTRATION_TYPES = {
  buyer: {
    title: 'Register as Buyer',
    description: 'Shop products from verified sellers',
    icon: 'bag-outline',
    color: '#007AFF',
    benefits: [
      'Browse and purchase products',
      'Track your orders in real-time',
      'Rate and review products',
      'Secure payment options',
      'Customer support'
    ]
  },
  seller: {
    title: 'Register as Seller',
    description: 'Start selling your products',
    icon: 'storefront-outline',
    color: '#34C759',
    benefits: [
      'List and sell your products',
      'Manage orders and inventory',
      'Access to delivery network',
      'Sales analytics and reports',
      'Marketing tools'
    ]
  },
  delivery_person: {
    title: 'Register as Delivery Person',
    description: 'Earn money by delivering packages',
    icon: 'car-outline',
    color: '#FF9500',
    benefits: [
      'Flexible working hours',
      'Earn money per delivery',
      'GPS-guided deliveries',
      'Real-time order tracking',
      'Performance bonuses'
    ]
  }
};

const MarketplaceRegistrationModal = ({ 
  visible, 
  onClose, 
  onRegistrationComplete,
  requiredRole = null, // If set, shows only this role
  triggerAction = null // Action that triggered the modal (e.g., 'buy_product', 'sell_product')
}) => {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);
  
  const [selectedRole, setSelectedRole] = useState(requiredRole);
  const [currentStep, setCurrentStep] = useState(requiredRole ? 'form' : 'selection'); // 'selection', 'form', 'verification'
  const [loading, setLoading] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    // Common fields
    businessName: '',
    businessAddress: '',
    phoneNumber: user?.phoneNumber || '',
    
    // Seller specific
    businessType: '',
    businessDescription: '',
    businessLicense: '',
    taxNumber: '',
    
    // Delivery person specific
    vehicleType: '',
    vehicleRegistration: '',
    drivingLicense: '',
    workingAreas: '',
    
    // Buyer specific (minimal)
    preferredPaymentMethod: '',
    deliveryPreferences: '',
  });

  const handleRoleSelection = (role) => {
    setSelectedRole(role);
    setCurrentStep('form');
  };

  const handleFormSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const registrationData = {
        role: selectedRole,
        userId: user.id,
        ...formData,
        triggerAction,
      };

      const response = await ApiService.registerMarketplaceRole(registrationData);
      
      if (response.success) {
        setCurrentStep('verification');
        
        // Auto-close after 3 seconds and trigger callback
        setTimeout(() => {
          onRegistrationComplete(selectedRole, response.data);
          onClose();
        }, 3000);
      } else {
        Alert.alert('Registration Failed', response.error || 'Please try again');
      }
    } catch (error) {
      console.error('Registration failed:', error);
      Alert.alert('Error', 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const requiredFields = {
      buyer: ['phoneNumber'],
      seller: ['businessName', 'businessAddress', 'phoneNumber', 'businessType'],
      delivery_person: ['phoneNumber', 'vehicleType', 'workingAreas']
    };

    const required = requiredFields[selectedRole] || [];
    
    for (const field of required) {
      if (!formData[field]?.trim()) {
        Alert.alert('Missing Information', `Please fill in all required fields`);
        return false;
      }
    }
    
    return true;
  };

  const renderRoleSelection = () => (
    <View style={styles.selectionContainer}>
      <Text style={[styles.modalTitle, { color: colors.text }]}>
        Choose Your Role
      </Text>
      <Text style={[styles.modalSubtitle, { color: colors.textSecondary }]}>
        Select how you want to use the marketplace
      </Text>

      {Object.entries(REGISTRATION_TYPES).map(([role, config]) => (
        <TouchableOpacity
          key={role}
          style={[styles.roleCard, { backgroundColor: colors.surface }]}
          onPress={() => handleRoleSelection(role)}
        >
          <View style={[styles.roleIcon, { backgroundColor: config.color + '20' }]}>
            <Ionicons name={config.icon} size={32} color={config.color} />
          </View>
          
          <View style={styles.roleInfo}>
            <Text style={[styles.roleTitle, { color: colors.text }]}>
              {config.title}
            </Text>
            <Text style={[styles.roleDescription, { color: colors.textSecondary }]}>
              {config.description}
            </Text>
            
            <View style={styles.benefitsList}>
              {config.benefits.slice(0, 3).map((benefit, index) => (
                <View key={index} style={styles.benefitItem}>
                  <Ionicons name="checkmark" size={12} color={config.color} />
                  <Text style={[styles.benefitText, { color: colors.textSecondary }]}>
                    {benefit}
                  </Text>
                </View>
              ))}
            </View>
          </View>
          
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderRegistrationForm = () => {
    const roleConfig = REGISTRATION_TYPES[selectedRole];
    
    return (
      <ScrollView style={styles.formContainer}>
        <View style={styles.formHeader}>
          <View style={[styles.roleIcon, { backgroundColor: roleConfig.color + '20' }]}>
            <Ionicons name={roleConfig.icon} size={24} color={roleConfig.color} />
          </View>
          <Text style={[styles.formTitle, { color: colors.text }]}>
            {roleConfig.title}
          </Text>
        </View>

        {/* Common Fields */}
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>Phone Number *</Text>
          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
            value={formData.phoneNumber}
            onChangeText={(text) => setFormData(prev => ({ ...prev, phoneNumber: text }))}
            placeholder="Enter your phone number"
            placeholderTextColor={colors.textSecondary}
            keyboardType="phone-pad"
          />
        </View>

        {/* Seller Specific Fields */}
        {selectedRole === 'seller' && (
          <>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Business Name *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.businessName}
                onChangeText={(text) => setFormData(prev => ({ ...prev, businessName: text }))}
                placeholder="Enter your business name"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Business Address *</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.businessAddress}
                onChangeText={(text) => setFormData(prev => ({ ...prev, businessAddress: text }))}
                placeholder="Enter your business address"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Business Type *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.businessType}
                onChangeText={(text) => setFormData(prev => ({ ...prev, businessType: text }))}
                placeholder="e.g., Electronics, Clothing, Food"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Business Description</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.businessDescription}
                onChangeText={(text) => setFormData(prev => ({ ...prev, businessDescription: text }))}
                placeholder="Describe your business"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </>
        )}

        {/* Delivery Person Specific Fields */}
        {selectedRole === 'delivery_person' && (
          <>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Vehicle Type *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.vehicleType}
                onChangeText={(text) => setFormData(prev => ({ ...prev, vehicleType: text }))}
                placeholder="e.g., Motorcycle, Car, Bicycle"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Vehicle Registration</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.vehicleRegistration}
                onChangeText={(text) => setFormData(prev => ({ ...prev, vehicleRegistration: text }))}
                placeholder="Enter vehicle registration number"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Working Areas *</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.workingAreas}
                onChangeText={(text) => setFormData(prev => ({ ...prev, workingAreas: text }))}
                placeholder="Areas where you can deliver (e.g., Nairobi, Kiambu)"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </>
        )}

        {/* Buyer Specific Fields */}
        {selectedRole === 'buyer' && (
          <>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Preferred Payment Method</Text>
              <TextInput
                style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.preferredPaymentMethod}
                onChangeText={(text) => setFormData(prev => ({ ...prev, preferredPaymentMethod: text }))}
                placeholder="e.g., M-Pesa, Bank Transfer"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Delivery Preferences</Text>
              <TextInput
                style={[styles.input, styles.textArea, { backgroundColor: colors.surface, color: colors.text }]}
                value={formData.deliveryPreferences}
                onChangeText={(text) => setFormData(prev => ({ ...prev, deliveryPreferences: text }))}
                placeholder="Any special delivery instructions"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </>
        )}

        <Button
          title={loading ? 'Registering...' : 'Complete Registration'}
          onPress={handleFormSubmit}
          disabled={loading}
          style={[styles.submitButton, { backgroundColor: roleConfig.color }]}
        />
      </ScrollView>
    );
  };

  const renderVerification = () => {
    const roleConfig = REGISTRATION_TYPES[selectedRole];
    
    return (
      <View style={styles.verificationContainer}>
        <View style={[styles.successIcon, { backgroundColor: roleConfig.color + '20' }]}>
          <Ionicons name="checkmark-circle" size={64} color={roleConfig.color} />
        </View>
        
        <Text style={[styles.successTitle, { color: colors.text }]}>
          Registration Successful!
        </Text>
        
        <Text style={[styles.successMessage, { color: colors.textSecondary }]}>
          You have been successfully registered as a {selectedRole.replace('_', ' ')}. 
          You can now access all {selectedRole} features.
        </Text>

        <View style={styles.nextSteps}>
          <Text style={[styles.nextStepsTitle, { color: colors.text }]}>
            What's Next?
          </Text>
          {roleConfig.benefits.map((benefit, index) => (
            <View key={index} style={styles.nextStepItem}>
              <Ionicons name="arrow-forward" size={16} color={roleConfig.color} />
              <Text style={[styles.nextStepText, { color: colors.textSecondary }]}>
                {benefit}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <KeyboardAvoidingView 
          style={styles.container}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            
            {currentStep !== 'selection' && (
              <TouchableOpacity 
                onPress={() => setCurrentStep('selection')}
                style={styles.backButton}
              >
                <Ionicons name="arrow-back" size={20} color={colors.primary} />
                <Text style={[styles.backText, { color: colors.primary }]}>Back</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.content}>
            {currentStep === 'selection' && renderRoleSelection()}
            {currentStep === 'form' && renderRegistrationForm()}
            {currentStep === 'verification' && renderVerification()}
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backText: {
    fontSize: 16,
    marginLeft: spacing.xs,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  selectionContainer: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  modalSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  roleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    ...shadows.sm,
  },
  roleIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  roleInfo: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  roleDescription: {
    fontSize: 14,
    marginBottom: spacing.sm,
  },
  benefitsList: {
    gap: spacing.xs,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  benefitText: {
    fontSize: 12,
    marginLeft: spacing.xs,
  },
  formContainer: {
    flex: 1,
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: spacing.sm,
  },
  inputGroup: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
  },
  verificationContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.xl,
  },
  successIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  successMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 24,
  },
  nextSteps: {
    width: '100%',
  },
  nextStepsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  nextStepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  nextStepText: {
    fontSize: 14,
    marginLeft: spacing.sm,
    flex: 1,
  },
});

export default MarketplaceRegistrationModal;
