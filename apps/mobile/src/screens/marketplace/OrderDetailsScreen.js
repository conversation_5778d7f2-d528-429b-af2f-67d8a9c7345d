import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import ApiService from '../../services/api';

const ORDER_STATUSES = {
  pending: { label: 'Pending', color: '#FF9500', icon: 'time' },
  confirmed: { label: 'Confirmed', color: '#007AFF', icon: 'checkmark-circle' },
  processing: { label: 'Processing', color: '#5856D6', icon: 'cog' },
  shipped: { label: 'Shipped', color: '#34C759', icon: 'airplane' },
  delivered: { label: 'Delivered', color: '#30D158', icon: 'checkmark-circle-2' },
  cancelled: { label: 'Cancelled', color: '#FF3B30', icon: 'close-circle' },
  refunded: { label: 'Refunded', color: '#FF9500', icon: 'return-up-back' },
};

export default function OrderDetailsScreen({ route, navigation }) {
  const { orderId } = route.params;
  const { colors, spacing, typography } = useTheme();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getOrder(orderId);
      if (response.success) {
        setOrder(response.data);
      }
    } catch (error) {
      console.error('Failed to load order details:', error);
      Alert.alert('Error', 'Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (newStatus) => {
    try {
      const response = await ApiService.updateOrder(orderId, { status: newStatus });
      if (response.success) {
        setOrder(prev => ({ ...prev, status: newStatus }));
        Alert.alert('Success', `Order ${newStatus} successfully`);
      }
    } catch (error) {
      console.error('Failed to update order:', error);
      Alert.alert('Error', 'Failed to update order status');
    }
  };

  const callDeliveryPerson = () => {
    if (order?.deliveryPersonPhone) {
      Linking.openURL(`tel:${order.deliveryPersonPhone}`);
    }
  };

  const getStatusInfo = (status) => {
    return ORDER_STATUSES[status] || { label: status, color: colors.textSecondary, icon: 'help' };
  };

  if (loading || !order) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading order details...</Text>
      </View>
    );
  }

  const statusInfo = getStatusInfo(order.status);

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Order Header */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <View style={styles.orderHeader}>
          <View>
            <Text style={[styles.orderId, { color: colors.text }]}>
              Order #{order.id.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.orderDate, { color: colors.textSecondary }]}>
              Placed on {new Date(order.createdAt).toLocaleDateString()}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={20} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>
      </View>

      {/* Order Items */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Order Items</Text>
        {order.items?.map((item, index) => (
          <View key={index} style={styles.orderItem}>
            <Image
              source={{ uri: item.product?.images?.[0] || 'https://via.placeholder.com/60' }}
              style={styles.itemImage}
            />
            <View style={styles.itemDetails}>
              <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={2}>
                {item.name}
              </Text>
              <Text style={[styles.itemPrice, { color: colors.primary }]}>
                KES {item.price?.toLocaleString()} × {item.quantity}
              </Text>
            </View>
            <Text style={[styles.itemTotal, { color: colors.text }]}>
              KES {(item.price * item.quantity)?.toLocaleString()}
            </Text>
          </View>
        ))}
        
        <View style={styles.orderSummary}>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Subtotal</Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              KES {(order.totalAmount - (order.deliveryFee || 0))?.toLocaleString()}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Delivery Fee</Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              KES {order.deliveryFee?.toLocaleString() || '0'}
            </Text>
          </View>
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={[styles.totalLabel, { color: colors.text }]}>Total</Text>
            <Text style={[styles.totalValue, { color: colors.primary }]}>
              KES {order.totalAmount?.toLocaleString()}
            </Text>
          </View>
        </View>
      </View>

      {/* Delivery Information */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Delivery Information</Text>
        <View style={styles.deliveryInfo}>
          <View style={styles.infoRow}>
            <Ionicons name="location" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Delivery Address</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {order.deliveryAddress}
              </Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {order.deliveryTown}, {order.deliveryCounty}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="call" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Contact Phone</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {order.deliveryPhone}
              </Text>
            </View>
          </View>

          {order.deliveryPersonId && (
            <View style={styles.infoRow}>
              <Ionicons name="person" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Delivery Person</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {order.deliveryPerson?.firstName} {order.deliveryPerson?.lastName}
                </Text>
                {order.deliveryPersonPhone && (
                  <TouchableOpacity onPress={callDeliveryPerson}>
                    <Text style={[styles.phoneLink, { color: colors.primary }]}>
                      {order.deliveryPersonPhone}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          )}
        </View>
      </View>

      {/* Payment Information */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Payment Information</Text>
        <View style={styles.paymentInfo}>
          <View style={styles.infoRow}>
            <Ionicons name="card" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Payment Method</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {order.paymentMethod?.toUpperCase()}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Payment Status</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {order.paymentStatus?.charAt(0).toUpperCase() + order.paymentStatus?.slice(1)}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate('OrderTracking', { orderId: order.id })}
        >
          <Ionicons name="location" size={20} color={colors.white} />
          <Text style={[styles.actionButtonText, { color: colors.white }]}>
            Track Order
          </Text>
        </TouchableOpacity>

        {order.status === 'pending' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.error }]}
            onPress={() => updateOrderStatus('cancelled')}
          >
            <Ionicons name="close" size={20} color={colors.white} />
            <Text style={[styles.actionButtonText, { color: colors.white }]}>
              Cancel Order
            </Text>
          </TouchableOpacity>
        )}

        {order.status === 'delivered' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.success }]}
            onPress={() => navigation.navigate('ReviewProduct', { orderId: order.id })}
          >
            <Ionicons name="star" size={20} color={colors.white} />
            <Text style={[styles.actionButtonText, { color: colors.white }]}>
              Leave Review
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderId: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  orderSummary: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  deliveryInfo: {
    gap: 16,
  },
  paymentInfo: {
    gap: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  phoneLink: {
    fontSize: 16,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  actionContainer: {
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
