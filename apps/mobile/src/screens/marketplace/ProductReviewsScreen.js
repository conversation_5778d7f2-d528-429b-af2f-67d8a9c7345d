import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import { formatDate } from '../../utils/formatters';
import ApiService from '../../services/api';
import Card from '../../components/common/Card';

const ProductReviewsScreen = ({ route, navigation }) => {
  const { productId, productName } = route.params;
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [reviews, setReviews] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      title: `${productName} Reviews`,
    });
    loadReviews();
    loadStats();
  }, [productId, productName]);

  const loadReviews = async () => {
    try {
      const response = await ApiService.getProductReviews(productId);
      if (response.success) {
        setReviews(response.data || []);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      setReviews([]);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await ApiService.getProductReviewStats(productId);
      if (response.success) {
        setStats(response.data || {
          totalReviews: 0,
          averageRating: 0,
          fiveStar: 0,
          fourStar: 0,
          threeStar: 0,
          twoStar: 0,
          oneStar: 0,
        });
      }
    } catch (error) {
      console.error('Error loading review stats:', error);
      // Set default stats on error
      setStats({
        totalReviews: 0,
        averageRating: 0,
        fiveStar: 0,
        fourStar: 0,
        threeStar: 0,
        twoStar: 0,
        oneStar: 0,
      });
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadReviews(), loadStats()]);
    setRefreshing(false);
  };

  const renderStars = (rating) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= rating ? 'star' : 'star-outline'}
            size={14}
            color={star <= rating ? colors.warning : colors.textSecondary}
            style={styles.star}
          />
        ))}
      </View>
    );
  };

  const renderReviewStats = () => {
    if (!stats) return null;

    return (
      <Card style={styles.statsCard}>
        <View style={styles.statsHeader}>
          <View style={styles.averageRating}>
            <Text style={[styles.averageNumber, { color: colors.text }]}>
              {stats.averageRating.toFixed(1)}
            </Text>
            {renderStars(Math.round(stats.averageRating))}
            <Text style={[styles.totalReviews, { color: colors.textSecondary }]}>
              {stats.totalReviews} review{stats.totalReviews !== 1 ? 's' : ''}
            </Text>
          </View>

          <View style={styles.ratingBreakdown}>
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = stats[`${['', 'one', 'two', 'three', 'four', 'five'][rating]}Star`] || 0;
              const percentage = stats.totalReviews > 0 ? (count / stats.totalReviews) * 100 : 0;

              return (
                <View key={rating} style={styles.ratingRow}>
                  <Text style={[styles.ratingNumber, { color: colors.textSecondary }]}>
                    {rating}
                  </Text>
                  <Ionicons name="star" size={12} color={colors.warning} />
                  <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                    <View
                      style={[
                        styles.progressFill,
                        { backgroundColor: colors.warning, width: `${percentage}%` },
                      ]}
                    />
                  </View>
                  <Text style={[styles.ratingCount, { color: colors.textSecondary }]}>
                    {count}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
      </Card>
    );
  };

  const renderReviewImages = (images) => {
    if (!images || images.length === 0) return null;

    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.reviewImages}>
        {images.map((image, index) => (
          <TouchableOpacity key={index} style={styles.reviewImageContainer}>
            <Image source={{ uri: image }} style={styles.reviewImage} />
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderReview = (review) => (
    <Card key={review.id} style={styles.reviewCard}>
      <View style={styles.reviewHeader}>
        <View style={styles.reviewerInfo}>
          {review.reviewer?.avatar ? (
            <Image source={{ uri: review.reviewer.avatar }} style={styles.reviewerAvatar} />
          ) : (
            <View style={[styles.reviewerAvatarPlaceholder, { backgroundColor: colors.border }]}>
              <Ionicons name="person" size={20} color={colors.textSecondary} />
            </View>
          )}
          <View style={styles.reviewerDetails}>
            <Text style={[styles.reviewerName, { color: colors.text }]}>
              {review.reviewer?.firstName} {review.reviewer?.lastName}
            </Text>
            <View style={styles.reviewMeta}>
              {renderStars(review.rating)}
              <Text style={[styles.reviewDate, { color: colors.textSecondary }]}>
                {formatDate(review.createdAt)}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {review.comment && (
        <Text style={[styles.reviewComment, { color: colors.text }]}>
          {review.comment}
        </Text>
      )}

      {renderReviewImages(review.images)}
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="star-outline" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No reviews yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Be the first to review this product
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading reviews...
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderReviewStats()}

        {reviews.length === 0 ? (
          renderEmptyState()
        ) : (
          <View style={styles.reviewsList}>
            <Text style={[styles.reviewsTitle, { color: colors.text }]}>
              Customer Reviews
            </Text>
            {reviews.map(renderReview)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  statsCard: {
    marginBottom: 20,
  },
  statsHeader: {
    flexDirection: 'row',
  },
  averageRating: {
    flex: 1,
    alignItems: 'center',
    paddingRight: 20,
  },
  averageNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  totalReviews: {
    fontSize: 12,
    marginTop: 4,
  },
  ratingBreakdown: {
    flex: 2,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingNumber: {
    fontSize: 12,
    width: 12,
    marginRight: 4,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  ratingCount: {
    fontSize: 12,
    width: 20,
    textAlign: 'right',
  },
  reviewsList: {
    marginBottom: 20,
  },
  reviewsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  reviewCard: {
    marginBottom: 16,
  },
  reviewHeader: {
    marginBottom: 12,
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  reviewerAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reviewerDetails: {
    flex: 1,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  reviewMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  star: {
    marginRight: 1,
  },
  reviewDate: {
    fontSize: 12,
  },
  reviewComment: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  reviewImages: {
    marginTop: 8,
  },
  reviewImageContainer: {
    marginRight: 8,
  },
  reviewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
};

export default ProductReviewsScreen;
