import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import ApiService from '../../services/api';
import Card from '../../components/common/Card';

const { width: screenWidth } = Dimensions.get('window');

const SellerAnalyticsScreen = ({ navigation }) => {
  const { theme } = useApp();
  const colors = getThemeColors(theme);
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [analytics, setAnalytics] = useState({
    overview: {
      totalRevenue: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      conversionRate: 0,
      totalProducts: 0,
      activeProducts: 0,
      totalViews: 0,
      totalCustomers: 0,
    },
    trends: {
      revenueGrowth: 0,
      orderGrowth: 0,
      customerGrowth: 0,
    },
    topProducts: [],
    recentActivity: [],
    customerInsights: {
      newCustomers: 0,
      returningCustomers: 0,
      averageOrdersPerCustomer: 0,
    },
    deliveryMetrics: {
      onTimeDeliveries: 0,
      averageDeliveryTime: 0,
      deliverySuccessRate: 0,
    },
  });

  const periods = [
    { id: '7d', label: '7 Days' },
    { id: '30d', label: '30 Days' },
    { id: '90d', label: '3 Months' },
    { id: '1y', label: '1 Year' },
  ];

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getSellerAnalytics(selectedPeriod);
      if (response.success) {
        setAnalytics(response.data);
      }
    } catch (error) {
      console.error('Failed to load analytics:', error);
      Alert.alert('Error', 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <View style={styles.headerContent}>
        <View style={styles.headerText}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Analytics
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            Detailed insights and metrics
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: colors.primary }]}
          onPress={() => Alert.alert('Export', 'Export functionality coming soon')}
        >
          <Ionicons name="download" size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              {
                backgroundColor: selectedPeriod === period.id ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedPeriod(period.id)}
          >
            <Text style={[
              styles.periodButtonText,
              { color: selectedPeriod === period.id ? colors.white : colors.textSecondary }
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderOverviewCards = () => (
    <View style={styles.overviewContainer}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Overview
      </Text>
      
      <View style={styles.overviewContainer}>
        <View style={styles.overviewRow}>
          <Card style={[styles.overviewCard, { backgroundColor: colors.surface }]}>
            <View style={styles.cardContent}>
              <Ionicons name="trending-up" size={28} color={colors.success} />
              <Text style={[styles.cardValue, { color: colors.text }]}>
                KES {analytics.overview.totalRevenue?.toLocaleString()}
              </Text>
              <Text style={[styles.cardLabel, { color: colors.textSecondary }]}>
                Total Revenue
              </Text>
              <View style={styles.trendIndicator}>
                <Ionicons name="arrow-up" size={12} color={colors.success} />
                <Text style={[styles.trendText, { color: colors.success }]}>
                  +{analytics.trends.revenueGrowth}%
                </Text>
              </View>
            </View>
          </Card>

          <Card style={[styles.overviewCard, { backgroundColor: colors.surface }]}>
            <View style={styles.cardContent}>
              <Ionicons name="bag" size={28} color={colors.primary} />
              <Text style={[styles.cardValue, { color: colors.text }]}>
                {analytics.overview.totalOrders}
              </Text>
              <Text style={[styles.cardLabel, { color: colors.textSecondary }]}>
                Total Orders
              </Text>
              <View style={styles.trendIndicator}>
                <Ionicons name="arrow-up" size={12} color={colors.success} />
                <Text style={[styles.trendText, { color: colors.success }]}>
                  +{analytics.trends.orderGrowth}%
                </Text>
              </View>
            </View>
          </Card>
        </View>

        <View style={styles.overviewRow}>
          <Card style={[styles.overviewCard, { backgroundColor: colors.surface }]}>
            <View style={styles.cardContent}>
              <Ionicons name="calculator" size={28} color={colors.info} />
              <Text style={[styles.cardValue, { color: colors.text }]}>
                KES {analytics.overview.averageOrderValue?.toLocaleString()}
              </Text>
              <Text style={[styles.cardLabel, { color: colors.textSecondary }]}>
                Avg Order Value
              </Text>
            </View>
          </Card>

          <Card style={[styles.overviewCard, { backgroundColor: colors.surface }]}>
            <View style={styles.cardContent}>
              <Ionicons name="people" size={28} color={colors.warning} />
              <Text style={[styles.cardValue, { color: colors.text }]}>
                {analytics.overview.totalCustomers}
              </Text>
              <Text style={[styles.cardLabel, { color: colors.textSecondary }]}>
                Total Customers
              </Text>
              <View style={styles.trendIndicator}>
                <Ionicons name="arrow-up" size={12} color={colors.success} />
                <Text style={[styles.trendText, { color: colors.success }]}>
                  +{analytics.trends.customerGrowth}%
                </Text>
              </View>
            </View>
          </Card>
        </View>
      </View>
    </View>
  );

  const renderTopProducts = () => {
    const topProducts = analytics.topProducts || [];

    return (
      <Card style={[styles.sectionCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Top Performing Products
        </Text>

        {topProducts.length > 0 ? (
          topProducts.map((product, index) => (
            <View key={product.id} style={styles.productItem}>
              <View style={styles.productRank}>
                <Text style={[styles.rankNumber, { color: colors.primary }]}>
                  #{index + 1}
                </Text>
              </View>
              <View style={styles.productInfo}>
                <Text style={[styles.productName, { color: colors.text }]}>
                  {product.name || 'Unknown Product'}
                </Text>
                <Text style={[styles.productStats, { color: colors.textSecondary }]}>
                  {product.sales || 0} sales • KES {(product.revenue || 0).toLocaleString()}
                </Text>
              </View>
              <TouchableOpacity
                style={[styles.viewButton, { backgroundColor: colors.primary }]}
                onPress={() => navigation.navigate('ProductDetails', { productId: product.id })}
              >
                <Ionicons name="eye" size={16} color={colors.white} />
              </TouchableOpacity>
            </View>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No product data available
            </Text>
          </View>
        )}
      </Card>
    );
  };

  const renderCustomerInsights = () => {
    const customerInsights = analytics.customerInsights || {
      newCustomers: 0,
      returningCustomers: 0,
      averageOrdersPerCustomer: 0,
    };

    return (
      <Card style={[styles.sectionCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Customer Insights
        </Text>

        <View style={styles.insightsContainer}>
          <View style={styles.insightsRow}>
            <View style={styles.insightItem}>
              <Text style={[styles.insightValue, { color: colors.success }]}>
                {customerInsights.newCustomers || 0}
              </Text>
              <Text style={[styles.insightLabel, { color: colors.textSecondary }]}>
                New Customers
              </Text>
            </View>

            <View style={styles.insightItem}>
              <Text style={[styles.insightValue, { color: colors.primary }]}>
                {customerInsights.returningCustomers || 0}
              </Text>
              <Text style={[styles.insightLabel, { color: colors.textSecondary }]}>
                Returning Customers
              </Text>
            </View>
          </View>

          <View style={styles.insightsRow}>
            <View style={styles.insightItem}>
              <Text style={[styles.insightValue, { color: colors.info }]}>
                {customerInsights.averageOrdersPerCustomer || 0}
              </Text>
              <Text style={[styles.insightLabel, { color: colors.textSecondary }]}>
                Avg Orders/Customer
              </Text>
            </View>
            <View style={styles.insightItemPlaceholder} />
          </View>
        </View>
      </Card>
    );
  };

  const renderDeliveryMetrics = () => {
    const deliveryMetrics = analytics.deliveryMetrics || {
      onTimeDeliveries: 0,
      averageDeliveryTime: 0,
      deliverySuccessRate: 0,
    };

    return (
      <Card style={[styles.sectionCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Delivery Performance
        </Text>

        <View style={styles.deliveryGrid}>
          <View style={styles.deliveryItem}>
            <View style={styles.deliveryIconContainer}>
              <Ionicons name="time" size={24} color={colors.success} />
            </View>
            <Text style={[styles.deliveryValue, { color: colors.text }]}>
              {deliveryMetrics.onTimeDeliveries || 0}%
            </Text>
            <Text style={[styles.deliveryLabel, { color: colors.textSecondary }]}>
              On-Time Deliveries
            </Text>
          </View>

          <View style={styles.deliveryItem}>
            <View style={styles.deliveryIconContainer}>
              <Ionicons name="speedometer" size={24} color={colors.primary} />
            </View>
            <Text style={[styles.deliveryValue, { color: colors.text }]}>
              {deliveryMetrics.averageDeliveryTime || 0}h
            </Text>
            <Text style={[styles.deliveryLabel, { color: colors.textSecondary }]}>
              Avg Delivery Time
            </Text>
          </View>

          <View style={styles.deliveryItem}>
            <View style={styles.deliveryIconContainer}>
              <Ionicons name="checkmark-circle" size={24} color={colors.info} />
            </View>
            <Text style={[styles.deliveryValue, { color: colors.text }]}>
              {deliveryMetrics.deliverySuccessRate || 0}%
            </Text>
            <Text style={[styles.deliveryLabel, { color: colors.textSecondary }]}>
              Success Rate
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading analytics...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {renderHeader()}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {renderOverviewCards()}
        {renderTopProducts()}
        {renderCustomerInsights()}
        {renderDeliveryMetrics()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 15,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  exportButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  periodSelector: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  overviewContainer: {
    paddingHorizontal: 20,
    marginBottom: 24, // Increased spacing
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  overviewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20, // Increased spacing between rows
    gap: 12, // Add gap between cards
  },
  // Insights grid styles
  insightsContainer: {
    marginTop: 20, // Increased top margin
  },
  insightsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20, // Increased spacing
    gap: 12, // Add gap between items
  },
  insightItemPlaceholder: {
    width: '48%',
    height: 1,
  },
  overviewCard: {
    width: '48%',
    padding: 20, // Increased padding for better spacing
    borderRadius: 16, // More rounded corners
    minHeight: 140, // Increased height for better proportions
    marginHorizontal: 2, // Small horizontal margin for breathing room
    elevation: 3, // Increased elevation for better depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardContent: {
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '100%',
  },
  cardValue: {
    fontSize: 20, // Larger font
    fontWeight: 'bold',
    marginTop: 10, // Increased spacing
    textAlign: 'center',
  },
  cardLabel: {
    fontSize: 13, // Slightly larger
    marginTop: 6, // Increased spacing
    textAlign: 'center',
    opacity: 0.8,
  },
  trendIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 2,
  },
  sectionCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
    borderRadius: 12,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  productRank: {
    width: 40,
    alignItems: 'center',
  },
  rankNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productInfo: {
    flex: 1,
    marginLeft: 15,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
  },
  productStats: {
    fontSize: 14,
    marginTop: 2,
  },
  viewButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  insightsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  insightItem: {
    alignItems: 'center',
    width: '48%', // Consistent width
    padding: 18, // Increased padding for better spacing
    backgroundColor: 'rgba(0,0,0,0.02)', // Subtle background
    borderRadius: 16, // More rounded corners to match cards
    minHeight: 90, // Increased height for better proportions
    justifyContent: 'center',
    marginHorizontal: 2, // Small horizontal margin for breathing room
  },
  insightValue: {
    fontSize: 26, // Larger font
    fontWeight: 'bold',
    marginBottom: 6, // Add spacing
  },
  insightLabel: {
    fontSize: 13, // Slightly larger
    textAlign: 'center',
    opacity: 0.8,
  },
  deliveryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  deliveryItem: {
    alignItems: 'center',
  },
  deliveryIconContainer: {
    marginBottom: 8,
  },
  deliveryValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  deliveryLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
};

export default SellerAnalyticsScreen;
