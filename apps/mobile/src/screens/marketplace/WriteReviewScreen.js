import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import { formatCurrency } from '../../utils/formatters';
import ApiService from '../../services/api';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

const WriteReviewScreen = ({ route, navigation }) => {
  const { product, order } = route.params;
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleStarPress = (starRating) => {
    setRating(starRating);
  };

  const handleAddImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant camera roll permissions to add photos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets[0]) {
        if (images.length >= 5) {
          Alert.alert('Limit reached', 'You can only add up to 5 photos per review.');
          return;
        }
        setImages([...images, result.assets[0]]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const handleRemoveImage = (index) => {
    const newImages = images.filter((_, i) => i !== index);
    setImages(newImages);
  };

  const handleSubmitReview = async () => {
    if (rating === 0) {
      Alert.alert('Rating required', 'Please select a star rating for this product.');
      return;
    }

    if (comment.trim().length < 10) {
      Alert.alert('Review too short', 'Please write at least 10 characters about your experience.');
      return;
    }

    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('productId', product.id);
      formData.append('orderId', order.id);
      formData.append('rating', rating.toString());
      formData.append('comment', comment.trim());

      // Add images to form data
      images.forEach((image, index) => {
        formData.append('images', {
          uri: image.uri,
          type: image.type || 'image/jpeg',
          name: `review_image_${index}.jpg`,
        });
      });

      const response = await ApiService.createReview(formData);

      if (response.success) {
        Alert.alert(
          'Review submitted!',
          'Thank you for your feedback. Your review will help other buyers.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStarRating = () => (
    <View style={styles.starContainer}>
      <Text style={[styles.ratingLabel, { color: colors.text }]}>
        Rate this product *
      </Text>
      <View style={styles.starsRow}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => handleStarPress(star)}
            style={styles.starButton}
          >
            <Ionicons
              name={star <= rating ? 'star' : 'star-outline'}
              size={32}
              color={star <= rating ? colors.warning : colors.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </View>
      {rating > 0 && (
        <Text style={[styles.ratingText, { color: colors.textSecondary }]}>
          {rating === 1 && 'Poor'}
          {rating === 2 && 'Fair'}
          {rating === 3 && 'Good'}
          {rating === 4 && 'Very Good'}
          {rating === 5 && 'Excellent'}
        </Text>
      )}
    </View>
  );

  const renderProductInfo = () => (
    <Card style={styles.productCard}>
      <View style={styles.productInfo}>
        {product.images && product.images.length > 0 && (
          <Image source={{ uri: product.images[0] }} style={styles.productImage} />
        )}
        <View style={styles.productDetails}>
          <Text style={[styles.productName, { color: colors.text }]}>
            {product.name}
          </Text>
          <Text style={[styles.productPrice, { color: colors.primary }]}>
            {formatCurrency(product.price)}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderCommentSection = () => (
    <View style={styles.commentSection}>
      <Text style={[styles.commentLabel, { color: colors.text }]}>
        Write your review *
      </Text>
      <TextInput
        style={[
          styles.commentInput,
          {
            color: colors.text,
            borderColor: colors.border,
            backgroundColor: colors.surface,
          },
        ]}
        placeholder="Share your experience with this product..."
        placeholderTextColor={colors.textSecondary}
        value={comment}
        onChangeText={setComment}
        multiline
        numberOfLines={4}
        textAlignVertical="top"
        maxLength={500}
      />
      <Text style={[styles.characterCount, { color: colors.textSecondary }]}>
        {comment.length}/500 characters
      </Text>
    </View>
  );

  const renderImageSection = () => (
    <View style={styles.imageSection}>
      <Text style={[styles.imageLabel, { color: colors.text }]}>
        Add photos (optional)
      </Text>
      <Text style={[styles.imageSubtext, { color: colors.textSecondary }]}>
        Help other buyers by showing the actual product
      </Text>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageScroll}>
        {images.map((image, index) => (
          <View key={index} style={styles.imageContainer}>
            <Image source={{ uri: image.uri }} style={styles.reviewImage} />
            <TouchableOpacity
              style={[styles.removeImageButton, { backgroundColor: colors.error }]}
              onPress={() => handleRemoveImage(index)}
            >
              <Ionicons name="close" size={16} color={colors.white} />
            </TouchableOpacity>
          </View>
        ))}

        {images.length < 5 && (
          <TouchableOpacity
            style={[styles.addImageButton, { borderColor: colors.border }]}
            onPress={handleAddImage}
          >
            <Ionicons name="camera" size={24} color={colors.textSecondary} />
            <Text style={[styles.addImageText, { color: colors.textSecondary }]}>
              Add Photo
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderProductInfo()}
        {renderStarRating()}
        {renderCommentSection()}
        {renderImageSection()}

        <View style={styles.spacer} />
      </ScrollView>

      <View style={[styles.footer, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
        <Button
          title={loading ? 'Submitting...' : 'Submit Review'}
          onPress={handleSubmitReview}
          disabled={loading || rating === 0 || comment.trim().length < 10}
          style={styles.submitButton}
          icon={loading ? <ActivityIndicator size="small" color={colors.white} /> : null}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  productCard: {
    marginBottom: 20,
  },
  productInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    fontWeight: '500',
  },
  starContainer: {
    marginBottom: 24,
  },
  ratingLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  starsRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  starButton: {
    marginRight: 8,
  },
  ratingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  commentSection: {
    marginBottom: 24,
  },
  commentLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  commentInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  imageSection: {
    marginBottom: 24,
  },
  imageLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  imageSubtext: {
    fontSize: 12,
    marginBottom: 12,
  },
  imageScroll: {
    flexDirection: 'row',
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  reviewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addImageButton: {
    width: 80,
    height: 80,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addImageText: {
    fontSize: 10,
    marginTop: 4,
  },
  spacer: {
    height: 100,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  submitButton: {
    width: '100%',
  },
};

export default WriteReviewScreen;
