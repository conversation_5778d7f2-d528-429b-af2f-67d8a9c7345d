# Reminders Feature

## Overview
The Reminders feature allows users to create, manage, and receive notifications for different types of reminders including one-time, daily, weekly, and monthly reminders.

## Features
- ✅ Create reminders with title and description
- ✅ Set date and time for reminders
- ✅ Choose reminder types: One-time, Daily, Weekly, Monthly
- ✅ Enable/disable notifications for each reminder
- ✅ Edit existing reminders
- ✅ Delete reminders
- ✅ Local storage using AsyncStorage
- ✅ Local notifications using expo-notifications
- ✅ Past reminder indicators
- ✅ Responsive UI with theme support

## Navigation
- Added to UserDashboardStack as a hidden tab screen
- Accessible via "Reminders" quick action on the user dashboard
- Navigation path: `navigation.navigate('Reminders')`

## Components Used
- **ReminderScreen**: Main screen component
- **CustomDateTimePicker**: For date/time selection
- **Card**: For reminder item display
- **Button**: For actions
- **Modal**: For add/edit reminder form

## Storage
- Uses AsyncStorage with key: `user_reminders`
- Data structure:
```json
{
  "id": "reminder_timestamp_randomid",
  "title": "Reminder title",
  "description": "Optional description",
  "dateTime": "2024-01-01T10:00:00.000Z",
  "type": "once|daily|weekly|monthly",
  "isEnabled": true,
  "createdAt": "2024-01-01T09:00:00.000Z",
  "notificationIds": ["notification_id_1", "notification_id_2"]
}
```

## Notifications
- Uses expo-notifications for local notifications
- Requests permissions on first use
- Schedules up to 10 future occurrences for recurring reminders
- Automatically cancels notifications when reminders are disabled/deleted

## Testing Checklist

### Basic Functionality
- [ ] Navigate to Reminders from dashboard quick action
- [ ] Create a new reminder with title and description
- [ ] Set future date/time for reminder
- [ ] Select different reminder types (once, daily, weekly, monthly)
- [ ] Save reminder and verify it appears in the list
- [ ] Edit an existing reminder
- [ ] Delete a reminder
- [ ] Toggle reminder on/off

### Notifications
- [ ] Grant notification permissions when prompted
- [ ] Create a reminder for 1-2 minutes in the future
- [ ] Verify notification appears at scheduled time
- [ ] Test recurring reminders (daily/weekly/monthly)
- [ ] Disable a reminder and verify notifications are cancelled

### UI/UX
- [ ] Verify responsive design on different screen sizes
- [ ] Test theme support (light/dark modes)
- [ ] Check empty state when no reminders exist
- [ ] Verify past reminder indicators
- [ ] Test form validation (empty title, past date)
- [ ] Check loading states and error handling

### Edge Cases
- [ ] Create reminder with very long title/description
- [ ] Test with device date/time changes
- [ ] Test app restart with existing reminders
- [ ] Test notification permissions denied scenario
- [ ] Test storage errors (full storage, etc.)

## Known Limitations
1. Recurring reminders schedule only 10 future occurrences
2. Notifications require app to be installed (local notifications only)
3. Past reminders are shown but notifications are not scheduled
4. No cloud sync - reminders are stored locally only

## Future Enhancements
- [ ] Cloud sync for reminders
- [ ] Reminder categories/tags
- [ ] Snooze functionality
- [ ] Reminder templates
- [ ] Location-based reminders
- [ ] Voice reminders
- [ ] Reminder sharing
- [ ] Analytics and insights
