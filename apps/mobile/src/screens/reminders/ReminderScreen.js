import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Switch,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import * as Notifications from 'expo-notifications';
import { useApp } from '../../context/AppContext';
import { getThemeColors } from '../../utils/theme';
import { formatDate } from '../../utils/dateUtils';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import CustomDateTimePicker from '../../components/common/DateTimePicker';
import ReminderService from '../../services/reminderService';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

const REMINDER_STORAGE_KEY = 'user_reminders';

const ReminderScreen = () => {
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [reminders, setReminders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingReminder, setEditingReminder] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    dateTime: new Date(),
    type: 'once', // 'once', 'daily', 'weekly', 'monthly'
    isEnabled: true,
  });

  // Request notification permissions on mount
  useEffect(() => {
    requestNotificationPermissions();
    loadReminders();
  }, []);

  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please enable notifications to receive reminders.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
    }
  };

  const loadReminders = async () => {
    try {
      setLoading(true);

      // Check if backend service is available
      const isBackendAvailable = await ReminderService.isServiceAvailable();

      if (isBackendAvailable) {
        // Load from backend
        const backendReminders = await ReminderService.getUserReminders();
        const formattedReminders = backendReminders.map(reminder =>
          ReminderService.formatReminderForFrontend(reminder)
        );

        // Sort by date/time
        const sortedReminders = formattedReminders.sort((a, b) =>
          new Date(a.dateTime) - new Date(b.dateTime)
        );
        setReminders(sortedReminders);

        // Migrate local reminders if any exist
        await migrateLocalReminders();
      } else {
        // Fallback to local storage
        await loadLocalReminders();
      }
    } catch (error) {
      console.error('Error loading reminders:', error);
      // Fallback to local storage on error
      await loadLocalReminders();
    } finally {
      setLoading(false);
    }
  };

  const loadLocalReminders = async () => {
    try {
      const storedReminders = await AsyncStorage.getItem(REMINDER_STORAGE_KEY);
      if (storedReminders) {
        const parsedReminders = JSON.parse(storedReminders);
        // Sort by date/time
        const sortedReminders = parsedReminders.sort((a, b) =>
          new Date(a.dateTime) - new Date(b.dateTime)
        );
        setReminders(sortedReminders);
      }
    } catch (error) {
      console.error('Error loading local reminders:', error);
      Alert.alert('Error', 'Failed to load reminders');
    }
  };

  const migrateLocalReminders = async () => {
    try {
      const storedReminders = await AsyncStorage.getItem(REMINDER_STORAGE_KEY);
      if (storedReminders) {
        const localReminders = JSON.parse(storedReminders);
        if (localReminders.length > 0) {
          console.log('Migrating local reminders to backend...');
          await ReminderService.syncLocalReminders(localReminders);
          // Clear local storage after successful migration
          await AsyncStorage.removeItem(REMINDER_STORAGE_KEY);
          console.log('Local reminders migrated successfully');
        }
      }
    } catch (error) {
      console.warn('Failed to migrate local reminders:', error);
    }
  };

  const saveReminders = async (updatedReminders) => {
    try {
      // Update local state immediately for better UX
      setReminders(updatedReminders);

      // Try to sync with backend, fallback to local storage
      const isBackendAvailable = await ReminderService.isServiceAvailable();
      if (!isBackendAvailable) {
        await AsyncStorage.setItem(REMINDER_STORAGE_KEY, JSON.stringify(updatedReminders));
      }
    } catch (error) {
      console.error('Error saving reminders:', error);
      Alert.alert('Error', 'Failed to save reminders');
    }
  };

  const generateReminderId = () => {
    return `reminder_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  };

  const scheduleNotification = async (reminder) => {
    try {
      const trigger = new Date(reminder.dateTime);

      // Don't schedule if the time has already passed
      if (trigger <= new Date()) {
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: reminder.title,
          body: reminder.description || 'Reminder notification',
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: trigger,
        },
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  };

  const scheduleRecurringNotifications = async (reminder) => {
    try {
      const notificationIds = [];
      const baseDate = new Date(reminder.dateTime);
      const now = new Date();

      // Schedule up to 10 future occurrences
      for (let i = 0; i < 10; i++) {
        let nextDate = new Date(baseDate);

        switch (reminder.type) {
          case 'daily':
            nextDate.setDate(baseDate.getDate() + i);
            break;
          case 'weekly':
            nextDate.setDate(baseDate.getDate() + (i * 7));
            break;
          case 'monthly':
            nextDate.setMonth(baseDate.getMonth() + i);
            break;
          default:
            continue;
        }

        // Only schedule future notifications
        if (nextDate > now) {
          const notificationId = await Notifications.scheduleNotificationAsync({
            content: {
              title: reminder.title,
              body: reminder.description || 'Recurring reminder',
              sound: true,
              priority: Notifications.AndroidNotificationPriority.HIGH,
            },
            trigger: {
              date: nextDate,
            },
          });

          if (notificationId) {
            notificationIds.push(notificationId);
          }
        }
      }

      return notificationIds;
    } catch (error) {
      console.error('Error scheduling recurring notifications:', error);
      return [];
    }
  };

  const cancelNotifications = async (notificationIds) => {
    try {
      if (Array.isArray(notificationIds)) {
        for (const id of notificationIds) {
          await Notifications.cancelScheduledNotificationAsync(id);
        }
      } else if (notificationIds) {
        await Notifications.cancelScheduledNotificationAsync(notificationIds);
      }
    } catch (error) {
      console.error('Error canceling notifications:', error);
    }
  };

  const handleAddReminder = async () => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter a reminder title');
      return;
    }

    if (new Date(formData.dateTime) <= new Date()) {
      Alert.alert('Error', 'Please select a future date and time');
      return;
    }

    try {
      // Try to create reminder via backend first
      const isBackendAvailable = await ReminderService.isServiceAvailable();

      if (isBackendAvailable) {
        const createdReminder = await ReminderService.createReminder(formData);
        const formattedReminder = ReminderService.formatReminderForFrontend(createdReminder);

        // Schedule local notifications
        if (formData.isEnabled) {
          if (formData.type === 'once') {
            await scheduleNotification(formattedReminder);
          } else {
            await scheduleRecurringNotifications(formattedReminder);
          }
        }

        // Reload reminders from backend
        await loadReminders();
      } else {
        // Fallback to local storage
        const newReminder = {
          id: generateReminderId(),
          ...formData,
          createdAt: new Date().toISOString(),
          notificationIds: [],
        };

        // Schedule notifications
        if (formData.isEnabled) {
          if (formData.type === 'once') {
            const notificationId = await scheduleNotification(newReminder);
            if (notificationId) {
              newReminder.notificationIds = [notificationId];
            }
          } else {
            const notificationIds = await scheduleRecurringNotifications(newReminder);
            newReminder.notificationIds = notificationIds;
          }
        }

        const updatedReminders = [...reminders, newReminder].sort((a, b) =>
          new Date(a.dateTime) - new Date(b.dateTime)
        );

        await saveReminders(updatedReminders);
      }

      resetForm();
      setShowAddModal(false);
      Alert.alert('Success', 'Reminder added successfully!');
    } catch (error) {
      console.error('Error adding reminder:', error);
      Alert.alert('Error', 'Failed to add reminder');
    }
  };

  const handleEditReminder = async () => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter a reminder title');
      return;
    }

    try {
      // Try to update reminder via backend first
      const isBackendAvailable = await ReminderService.isServiceAvailable();

      if (isBackendAvailable) {
        const updatedReminder = await ReminderService.updateReminder(editingReminder.id, formData);
        const formattedReminder = ReminderService.formatReminderForFrontend(updatedReminder);

        // Cancel existing notifications
        if (editingReminder.notificationIds) {
          await cancelNotifications(editingReminder.notificationIds);
        }

        // Schedule new notifications if enabled and future date
        if (formData.isEnabled && new Date(formData.dateTime) > new Date()) {
          if (formData.type === 'once') {
            await scheduleNotification(formattedReminder);
          } else {
            await scheduleRecurringNotifications(formattedReminder);
          }
        }

        // Reload reminders from backend
        await loadReminders();
      } else {
        // Fallback to local storage
        // Cancel existing notifications
        if (editingReminder.notificationIds) {
          await cancelNotifications(editingReminder.notificationIds);
        }

        const updatedReminder = {
          ...editingReminder,
          ...formData,
          notificationIds: [],
        };

        // Schedule new notifications if enabled and future date
        if (formData.isEnabled && new Date(formData.dateTime) > new Date()) {
          if (formData.type === 'once') {
            const notificationId = await scheduleNotification(updatedReminder);
            if (notificationId) {
              updatedReminder.notificationIds = [notificationId];
            }
          } else {
            const notificationIds = await scheduleRecurringNotifications(updatedReminder);
            updatedReminder.notificationIds = notificationIds;
          }
        }

        const updatedReminders = reminders.map(reminder =>
          reminder.id === editingReminder.id ? updatedReminder : reminder
        ).sort((a, b) => new Date(a.dateTime) - new Date(b.dateTime));

        await saveReminders(updatedReminders);
      }

      resetForm();
      setShowAddModal(false);
      setEditingReminder(null);
      Alert.alert('Success', 'Reminder updated successfully!');
    } catch (error) {
      console.error('Error updating reminder:', error);
      Alert.alert('Error', 'Failed to update reminder');
    }
  };

  const handleDeleteReminder = (reminder) => {
    Alert.alert(
      'Delete Reminder',
      'Are you sure you want to delete this reminder?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Try to delete via backend first
              const isBackendAvailable = await ReminderService.isServiceAvailable();

              if (isBackendAvailable) {
                await ReminderService.deleteReminder(reminder.id);

                // Cancel local notifications
                if (reminder.notificationIds) {
                  await cancelNotifications(reminder.notificationIds);
                }

                // Reload reminders from backend
                await loadReminders();
              } else {
                // Fallback to local storage
                // Cancel notifications
                if (reminder.notificationIds) {
                  await cancelNotifications(reminder.notificationIds);
                }

                const updatedReminders = reminders.filter(r => r.id !== reminder.id);
                await saveReminders(updatedReminders);
              }

              Alert.alert('Success', 'Reminder deleted successfully!');
            } catch (error) {
              console.error('Error deleting reminder:', error);
              Alert.alert('Error', 'Failed to delete reminder');
            }
          }
        }
      ]
    );
  };

  const handleToggleReminder = async (reminder) => {
    try {
      // Try to toggle via backend first
      const isBackendAvailable = await ReminderService.isServiceAvailable();

      if (isBackendAvailable) {
        const updatedReminder = await ReminderService.toggleReminder(reminder.id);
        const formattedReminder = ReminderService.formatReminderForFrontend(updatedReminder);

        // Handle local notifications
        if (formattedReminder.isEnabled) {
          // Enable: schedule notifications
          if (new Date(formattedReminder.dateTime) > new Date()) {
            if (formattedReminder.type === 'once') {
              await scheduleNotification(formattedReminder);
            } else {
              await scheduleRecurringNotifications(formattedReminder);
            }
          }
        } else {
          // Disable: cancel notifications
          if (reminder.notificationIds) {
            await cancelNotifications(reminder.notificationIds);
          }
        }

        // Reload reminders from backend
        await loadReminders();
      } else {
        // Fallback to local storage
        const updatedReminder = { ...reminder, isEnabled: !reminder.isEnabled };

        if (updatedReminder.isEnabled) {
          // Enable: schedule notifications
          if (new Date(reminder.dateTime) > new Date()) {
            if (reminder.type === 'once') {
              const notificationId = await scheduleNotification(updatedReminder);
              if (notificationId) {
                updatedReminder.notificationIds = [notificationId];
              }
            } else {
              const notificationIds = await scheduleRecurringNotifications(updatedReminder);
              updatedReminder.notificationIds = notificationIds;
            }
          }
        } else {
          // Disable: cancel notifications
          if (reminder.notificationIds) {
            await cancelNotifications(reminder.notificationIds);
          }
          updatedReminder.notificationIds = [];
        }

        const updatedReminders = reminders.map(r =>
          r.id === reminder.id ? updatedReminder : r
        );

        await saveReminders(updatedReminders);
      }
    } catch (error) {
      console.error('Error toggling reminder:', error);
      Alert.alert('Error', 'Failed to update reminder');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      dateTime: new Date(),
      type: 'once',
      isEnabled: true,
    });
  };

  const openAddModal = () => {
    resetForm();
    setEditingReminder(null);
    setShowAddModal(true);
  };

  const openEditModal = (reminder) => {
    setFormData({
      title: reminder.title,
      description: reminder.description || '',
      dateTime: new Date(reminder.dateTime),
      type: reminder.type,
      isEnabled: reminder.isEnabled,
    });
    setEditingReminder(reminder);
    setShowAddModal(true);
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadReminders();
    setRefreshing(false);
  }, []);

  const getTypeIcon = (type) => {
    switch (type) {
      case 'daily': return 'today';
      case 'weekly': return 'calendar';
      case 'monthly': return 'calendar-outline';
      default: return 'time';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'daily': return 'Daily';
      case 'weekly': return 'Weekly';
      case 'monthly': return 'Monthly';
      default: return 'One-time';
    }
  };

  const isReminderPast = (dateTime) => {
    return new Date(dateTime) < new Date();
  };

  const renderReminderItem = ({ item: reminder }) => {
    const isPast = isReminderPast(reminder.dateTime);
    const reminderDate = new Date(reminder.dateTime);

    return (
      <Card style={[styles.reminderCard, { backgroundColor: colors.surface }]}>
        <View style={styles.reminderHeader}>
          <View style={styles.reminderInfo}>
            <View style={styles.reminderTitleRow}>
              <Text style={[styles.reminderTitle, { color: colors.text }]} numberOfLines={1}>
                {reminder.title}
              </Text>
              <View style={[styles.typeChip, { backgroundColor: colors.primary + '15' }]}>
                <Ionicons
                  name={getTypeIcon(reminder.type)}
                  size={12}
                  color={colors.primary}
                />
                <Text style={[styles.typeText, { color: colors.primary }]}>
                  {getTypeLabel(reminder.type)}
                </Text>
              </View>
            </View>

            {reminder.description && (
              <Text style={[styles.reminderDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                {reminder.description}
              </Text>
            )}

            <View style={styles.reminderMeta}>
              <View style={styles.dateTimeContainer}>
                <Ionicons name="calendar" size={14} color={colors.textSecondary} />
                <Text style={[styles.dateTimeText, { color: colors.textSecondary }]}>
                  {formatDate(reminderDate, 'datetime')}
                </Text>
              </View>

              {isPast && (
                <View style={[styles.pastIndicator, { backgroundColor: colors.error + '15' }]}>
                  <Ionicons name="time" size={12} color={colors.error} />
                  <Text style={[styles.pastText, { color: colors.error }]}>Past</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.reminderActions}>
            <Switch
              value={reminder.isEnabled}
              onValueChange={() => handleToggleReminder(reminder)}
              trackColor={{ false: colors.border, true: colors.primary + '30' }}
              thumbColor={reminder.isEnabled ? colors.primary : colors.textSecondary}
              style={styles.reminderSwitch}
            />

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary + '15' }]}
              onPress={() => openEditModal(reminder)}
            >
              <Ionicons name="pencil" size={16} color={colors.primary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.error + '15' }]}
              onPress={() => handleDeleteReminder(reminder)}
            >
              <Ionicons name="trash" size={16} color={colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="notifications-outline" size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>No Reminders Yet</Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Create your first reminder to get started
      </Text>
      <Button
        title="Add Reminder"
        onPress={openAddModal}
        style={styles.emptyButton}
      />
    </View>
  );

  const renderTypeSelector = () => (
    <View style={styles.formGroup}>
      <Text style={[styles.formLabel, { color: colors.text }]}>Repeat</Text>
      <View style={styles.typeSelector}>
        {[
          { key: 'once', label: 'One-time', icon: 'time' },
          { key: 'daily', label: 'Daily', icon: 'today' },
          { key: 'weekly', label: 'Weekly', icon: 'calendar' },
          { key: 'monthly', label: 'Monthly', icon: 'calendar-outline' },
        ].map((type) => (
          <TouchableOpacity
            key={type.key}
            style={[
              styles.typeOption,
              {
                backgroundColor: formData.type === type.key ? colors.primary + '15' : colors.background,
                borderColor: formData.type === type.key ? colors.primary : colors.border,
              }
            ]}
            onPress={() => setFormData(prev => ({ ...prev, type: type.key }))}
          >
            <Ionicons
              name={type.icon}
              size={16}
              color={formData.type === type.key ? colors.primary : colors.textSecondary}
            />
            <Text style={[
              styles.typeOptionText,
              { color: formData.type === type.key ? colors.primary : colors.textSecondary }
            ]}>
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderAddEditModal = () => (
    <Modal
      visible={showAddModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => {
        setShowAddModal(false);
        setEditingReminder(null);
        resetForm();
      }}
    >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
          <TouchableOpacity
            onPress={() => {
              setShowAddModal(false);
              setEditingReminder(null);
              resetForm();
            }}
          >
            <Text style={[styles.modalCancelText, { color: colors.primary }]}>Cancel</Text>
          </TouchableOpacity>

          <Text style={[styles.modalTitle, { color: colors.text }]}>
            {editingReminder ? 'Edit Reminder' : 'Add Reminder'}
          </Text>

          <TouchableOpacity
            onPress={editingReminder ? handleEditReminder : handleAddReminder}
          >
            <Text style={[styles.modalSaveText, { color: colors.primary }]}>
              {editingReminder ? 'Update' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.modalContent}>
          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: colors.text }]}>Title *</Text>
            <TextInput
              style={[styles.formInput, {
                backgroundColor: colors.surface,
                borderColor: colors.border,
                color: colors.text
              }]}
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              placeholder="Enter reminder title"
              placeholderTextColor={colors.textSecondary}
              maxLength={100}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: colors.text }]}>Description</Text>
            <TextInput
              style={[styles.formTextArea, {
                backgroundColor: colors.surface,
                borderColor: colors.border,
                color: colors.text
              }]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Enter description (optional)"
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={3}
              maxLength={500}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: colors.text }]}>Date & Time *</Text>
            <CustomDateTimePicker
              value={formData.dateTime}
              onDateTimeChange={(date) => setFormData(prev => ({ ...prev, dateTime: date }))}
              minimumDate={new Date()}
              placeholder="Select date and time"
              icon={<Ionicons name="calendar" size={20} color={colors.primary} />}
            />
          </View>

          {renderTypeSelector()}

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <View>
                <Text style={[styles.formLabel, { color: colors.text }]}>Enable Notifications</Text>
                <Text style={[styles.switchDescription, { color: colors.textSecondary }]}>
                  Receive notifications for this reminder
                </Text>
              </View>
              <Switch
                value={formData.isEnabled}
                onValueChange={(value) => setFormData(prev => ({ ...prev, isEnabled: value }))}
                trackColor={{ false: colors.border, true: colors.primary + '30' }}
                thumbColor={formData.isEnabled ? colors.primary : colors.textSecondary}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Reminders</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={openAddModal}
        >
          <Ionicons name="add" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading reminders...</Text>
        </View>
      ) : (
        <FlatList
          data={reminders}
          renderItem={renderReminderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}

      {renderAddEditModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  listContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  reminderCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  reminderInfo: {
    flex: 1,
    marginRight: 12,
  },
  reminderTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  reminderTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  typeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  reminderDescription: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  reminderMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateTimeText: {
    fontSize: 14,
    marginLeft: 6,
  },
  pastIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  pastText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  reminderActions: {
    alignItems: 'center',
    gap: 8,
  },
  reminderSwitch: {
    transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 40,
  },
  emptyButton: {
    paddingHorizontal: 32,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalCancelText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  formTextArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  typeOptionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchDescription: {
    fontSize: 14,
    marginTop: 2,
  },
});

export default ReminderScreen;