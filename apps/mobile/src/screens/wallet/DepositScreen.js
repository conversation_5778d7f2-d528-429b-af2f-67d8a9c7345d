import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { useOptimisticUpdate } from '../../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import ApiService from '../../services/api';

export default function DepositScreen() {
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('mpesa');
  const [loading, setLoading] = useState(false);



  const paymentMethods = [
    { id: 'mpesa', name: 'M-<PERSON><PERSON><PERSON>', icon: 'phone-portrait' },
    { id: 'bank', name: 'Bank Transfer', icon: 'card' },
    { id: 'card', name: 'Credit/Debit Card', icon: 'card-outline' },
  ];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Compact Header */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View style={[styles.header, { borderBottomColor: 'transparent' }]}>
          <View style={styles.headerContent}>
            <Ionicons name="wallet" size={24} color={colors.success} />
            <Text style={[styles.title, { color: colors.text }]}>Deposit</Text>
          </View>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView style={styles.scrollableContainer} showsVerticalScrollIndicator={false}>

      <View style={styles.form}>
        <Text style={[styles.label, { color: colors.text }]}>Amount (KES)</Text>
        <TextInput
          style={[styles.amountInput, {
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          keyboardType="numeric"
          placeholderTextColor={colors.textTertiary}
        />

        <Text style={[styles.label, { color: colors.text }]}>Payment Method</Text>
        {paymentMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentMethod,
              {
                backgroundColor: colors.surface,
                borderColor: paymentMethod === method.id ? colors.primary : colors.border
              },
              paymentMethod === method.id && { backgroundColor: colors.primary + '20' },
            ]}
            onPress={() => setPaymentMethod(method.id)}
          >
            <Ionicons
              name={method.icon}
              size={24}
              color={paymentMethod === method.id ? colors.primary : colors.textSecondary}
            />
            <Text style={[
              styles.methodText,
              { color: paymentMethod === method.id ? colors.text : colors.textSecondary },
              paymentMethod === method.id && { fontWeight: '600' },
            ]}>
              {method.name}
            </Text>
            <Ionicons
              name={paymentMethod === method.id ? 'radio-button-on' : 'radio-button-off'}
              size={20}
              color={paymentMethod === method.id ? colors.primary : colors.textSecondary}
            />
          </TouchableOpacity>
        ))}

        {/* Main Deposit Button */}
        <TouchableOpacity
          style={[styles.depositButton, { backgroundColor: colors.primary }]}
          onPress={async () => {
            console.log('🔄 Deposit button clicked!');
            console.log('� Amount entered:', parseFloat(amount) || 0);
            console.log('💳 Payment method:', paymentMethod);

            const depositAmount = parseFloat(amount) || 0;

            if (!depositAmount || depositAmount <= 0) {
              console.log('❌ Invalid amount');
              Alert.alert('Invalid Amount', 'Please enter a valid amount');
              return;
            }

            console.log('✅ Amount validation passed');
            console.log('� Making direct API call...');

            try {
              setLoading(true);
              console.log('�📡 Calling ApiService.initiateDeposit directly...');
              const response = await ApiService.initiateDeposit(depositAmount, paymentMethod);
              console.log('✅ Direct API response:', response);

              Alert.alert(
                'Deposit Initiated',
                `M-Pesa STK push sent to your phone for KES ${depositAmount.toLocaleString()}`,
                [{ text: 'OK' }]
              );

              // Clear the amount after successful deposit
              setAmount('');
            } catch (error) {
              console.error('❌ Deposit failed:', error);
              Alert.alert('Deposit Failed', error.message);
            } finally {
              setLoading(false);
            }
          }}
          disabled={loading}
        >
          <View style={styles.buttonContent}>
            {loading && (
              <Ionicons
                name="refresh"
                size={20}
                color={colors.white}
                style={[styles.loadingIcon, { marginRight: spacing.sm }]}
              />
            )}
            <Text style={[styles.depositButtonText, { color: colors.white }]}>
              {loading ? 'Processing...' : `Deposit KES ${amount || '0.00'}`}
            </Text>
          </View>
        </TouchableOpacity>


      </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  scrollableContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  form: {
    padding: spacing.xl,
  },
  label: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
    marginTop: spacing.md,
  },
  amountInput: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
    borderWidth: 2,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 2,
  },
  methodText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    marginLeft: spacing.sm,
  },
  depositButton: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    alignItems: 'center',
    marginTop: spacing.xl,
    ...shadows.md,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIcon: {
    // Animation would be added here in a real implementation
  },
  depositButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
});
