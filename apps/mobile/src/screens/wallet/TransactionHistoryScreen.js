import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import ApiService from '../../services/api';
import DatabaseService from '../../services/database';
import ReceiptService from '../../services/receiptService';

export default function TransactionHistoryScreen() {
  const { theme, user } = useApp();
  const colors = getThemeColors(theme);

  const [filter, setFilter] = useState('all');
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  // Removed modal states - instant download only
  const [receiptLoading, setReceiptLoading] = useState(false);
  const [showTransactionMenu, setShowTransactionMenu] = useState(null);
  const [showHeaderMenu, setShowHeaderMenu] = useState(false);

  // Load transactions from API and local database
  const loadTransactions = useCallback(async () => {
    try {
      console.log('📊 Loading transactions from API...');

      // Try to load from API first
      let apiTransactions = [];
      try {
        const response = await ApiService.getTransactions(50, 0);
        if (response.success && response.data) {
          apiTransactions = response.data;
          console.log('✅ Loaded transactions from API:', apiTransactions.length);

          // Save to local database
          try {
            for (const transaction of apiTransactions) {
              transaction.synced = true;
              const existing = await DatabaseService.findById('transactions', transaction.id);
              if (existing) {
                await DatabaseService.update('transactions', transaction.id, transaction);
              } else {
                await DatabaseService.insert('transactions', transaction);
              }
            }
          } catch (dbError) {
            console.warn('⚠️ Failed to save to local database:', dbError.message);
            // Continue without local storage - API data is still available
          }
        }
      } catch (error) {
        console.warn('⚠️ Failed to load from API, using local data:', error.message);
      }

      // Load from local database as fallback
      let localTransactions = [];
      try {
        localTransactions = await DatabaseService.findAll(
          'transactions',
          '',
          [],
          'created_at DESC',
          50
        );
      } catch (dbError) {
        console.warn('⚠️ Failed to load from local database:', dbError.message);
        // Continue with empty array - will show empty state
      }

      // Use API data if available, otherwise use local data, or fallback to empty
      let transactionsData = [];
      if (apiTransactions.length > 0) {
        transactionsData = apiTransactions;
      } else if (localTransactions.length > 0) {
        transactionsData = localTransactions;
      } else {
        // Show empty state - no transactions available
        console.log('📊 No transactions available from API or local database');

        // Add sample transactions for demonstration (only in development)
        
      }

      // Transform data to match expected format
      const formattedTransactions = transactionsData.map(tx => ({
        ...tx,
        date: tx.createdAt || tx.created_at,
        // Ensure amount is properly signed based on transaction type
        amount: tx.type === 'deposit' ? Math.abs(tx.amount) : -Math.abs(tx.amount),
      }));

      setTransactions(formattedTransactions);

    } catch (error) {
      console.error('❌ Failed to load transactions:', error);
      Alert.alert('Error', 'Failed to load transaction history');
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadTransactions();
    setRefreshing(false);
  }, [loadTransactions]);

  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      await loadTransactions();
      setLoading(false);
    };

    initializeData();
  }, [loadTransactions]);

  const filterTypes = [
    { id: 'all', name: 'All', icon: 'list' },
    { id: 'deposit', name: 'Deposits', icon: 'arrow-down-circle' },
    { id: 'withdraw', name: 'Withdrawals', icon: 'arrow-up-circle' },
    { id: 'transfer', name: 'Transfers', icon: 'swap-horizontal' },
  ];

  const filteredTransactions = filter === 'all'
    ? transactions
    : transactions.filter(t => t.type === filter);



  const getTransactionColor = (type, amount) => {
    if (amount > 0) return colors.success; // Green for incoming
    if (type === 'withdraw') return colors.error; // Red for withdrawals
    return colors.primary; // Primary color for transfers
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Receipt functionality - INSTANT DOWNLOAD (NO PREVIEW)
  const handleReceiptAction = (transaction, action) => {
    console.log('🔍 handleReceiptAction called with:', { transactionId: transaction?.id, action });

    switch (action) {
      case 'download':
        console.log('🔍 Calling handleInstantDownloadReceipt for transaction:', transaction?.id);
        // INSTANT PDF DOWNLOAD - NO MODAL, NO PREVIEW
        handleInstantDownloadReceipt(transaction);
        break;
      case 'print':
        handlePrintReceipt(transaction);
        break;
      case 'share':
        handleShareReceipt(transaction);
        break;
    }
  };

  // INSTANT PDF DOWNLOAD - NO MODAL, NO PREVIEW, NO BULLSHIT
  const handleInstantDownloadReceipt = async (transaction) => {
    if (!transaction) return;

    console.log('📄 INSTANT PDF DOWNLOAD for transaction:', transaction.id);
    console.log('🔍 handleInstantDownloadReceipt called - user data:', user);

    // Show loading indicator briefly
    setReceiptLoading(true);

    try {
      // Build user info for receipt - THIS IS THE USER'S PERSONAL TRANSACTION
      const firstName = user?.firstName || user?.first_name || '';
      const lastName = user?.lastName || user?.last_name || '';
      const fullName = `${firstName} ${lastName}`.trim() || 'User';

      const userInfo = {
        name: fullName,
        firstName: firstName,
        lastName: lastName,
        email: user?.email || '',
        phone: user?.phone || '',
        // For personal transactions, the user is always the one who made the transaction
        isPersonalTransaction: true
      };

      console.log('👤 User data for receipt:', { firstName, lastName, fullName, user });

      console.log('👤 User info for personal receipt:', userInfo);

      // DIRECT PDF DOWNLOAD - NO FORMAT SELECTION
      const result = await ReceiptService.downloadReceipt(transaction, 'pdf', userInfo);

      if (result.success) {
        console.log('✅ PDF downloaded successfully:', result.fileName);
        // Show brief success message
        Alert.alert(
          'Downloaded',
          `Receipt saved as ${result.fileName}`,
          [{ text: 'OK', style: 'default' }],
          { cancelable: true }
        );
      } else {
        throw new Error(result.error || 'Download failed');
      }
    } catch (error) {
      console.error('❌ Instant download failed:', error);
      Alert.alert(
        'Download Failed',
        'Unable to download receipt. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setReceiptLoading(false);
    }
  };

  // Legacy function - kept for compatibility but not used
  const handleDownloadReceipt = async (format) => {
    if (!selectedTransaction) return;

    console.log('📄 Downloading receipt for transaction:', selectedTransaction);

    setReceiptLoading(true);
    try {
      // Build user info for legacy function
      const userInfo = {
        name: user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() : 'User',
        firstName: user?.first_name || '',
        lastName: user?.last_name || '',
        email: user?.email || '',
        phone: user?.phone || '',
        isPersonalTransaction: true
      };

      const result = await ReceiptService.downloadReceipt(selectedTransaction, format, userInfo);
      if (result.success) {
        setShowReceiptModal(false);
      }
    } catch (error) {
      console.error('Error downloading receipt:', error);
      Alert.alert('Error', 'Failed to download receipt');
    } finally {
      setReceiptLoading(false);
    }
  };

  const handlePrintReceipt = async (transaction) => {
    try {
      // Build user info for print - THIS IS THE USER'S PERSONAL TRANSACTION
      const firstName = user?.firstName || user?.first_name || '';
      const lastName = user?.lastName || user?.last_name || '';
      const fullName = `${firstName} ${lastName}`.trim() || 'User';

      const userInfo = {
        name: fullName,
        firstName: firstName,
        lastName: lastName,
        email: user?.email || '',
        phone: user?.phone || '',
        // For personal transactions, the user is always the one who made the transaction
        isPersonalTransaction: true
      };

      console.log('🖨️ Print receipt - user data:', { firstName, lastName, fullName, user });
      console.log('🖨️ Print receipt - userInfo:', userInfo);

      await ReceiptService.printReceipt(transaction, userInfo);
    } catch (error) {
      console.error('Error printing receipt:', error);
      Alert.alert('Error', 'Failed to print receipt');
    }
  };

  const handleShareReceipt = async (transaction) => {
    try {
      // Build user info for share - THIS IS THE USER'S PERSONAL TRANSACTION
      const firstName = user?.firstName || user?.first_name || '';
      const lastName = user?.lastName || user?.last_name || '';
      const fullName = `${firstName} ${lastName}`.trim() || 'User';

      const userInfo = {
        name: fullName,
        firstName: firstName,
        lastName: lastName,
        email: user?.email || '',
        phone: user?.phone || '',
        // For personal transactions, the user is always the one who made the transaction
        isPersonalTransaction: true
      };

      console.log('📤 Share receipt - user data:', { firstName, lastName, fullName, user });
      console.log('📤 Share receipt - userInfo:', userInfo);

      const result = await ReceiptService.generatePDFReceipt(transaction, userInfo);
      if (result.success) {
        await ReceiptService.shareReceipt(result.uri, result.fileName);
      }
    } catch (error) {
      console.error('Error sharing receipt:', error);
      Alert.alert('Error', 'Failed to share receipt');
    }
  };

  // INSTANT BULK PDF DOWNLOAD - NO MODAL, NO PREVIEW
  const handleInstantBulkDownload = async () => {
    if (transactions.length === 0) {
      Alert.alert('No Data', 'No transactions available to download');
      return;
    }

    console.log('📄 INSTANT BULK PDF DOWNLOAD for', transactions.length, 'transactions');

    setReceiptLoading(true);
    try {
      // Build user info for bulk receipt - THESE ARE THE USER'S PERSONAL TRANSACTIONS
      const firstName = user?.firstName || user?.first_name || '';
      const lastName = user?.lastName || user?.last_name || '';
      const fullName = `${firstName} ${lastName}`.trim() || 'User';

      const userInfo = {
        name: fullName,
        firstName: firstName,
        lastName: lastName,
        email: user?.email || '',
        phone: user?.phone || '',
        // For personal transaction history, the user is always the one who made all transactions
        isPersonalTransaction: true,
        reportTitle: 'Personal Transaction History'
      };

      console.log('👤 User data for bulk receipt:', { firstName, lastName, fullName, user });

      console.log('👤 User info for personal bulk receipt:', userInfo);

      // DIRECT PDF DOWNLOAD - NO FORMAT SELECTION
      const result = await ReceiptService.downloadBulkReceipts(transactions, 'pdf', userInfo);

      if (result.success) {
        console.log('✅ Bulk PDF downloaded successfully:', result.fileName);
        Alert.alert(
          'Downloaded',
          `Transaction history saved as ${result.fileName}`,
          [{ text: 'OK', style: 'default' }]
        );
      } else {
        throw new Error(result.error || 'Bulk download failed');
      }
    } catch (error) {
      console.error('❌ Instant bulk download failed:', error);
      Alert.alert(
        'Download Failed',
        'Unable to download transaction history. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setReceiptLoading(false);
    }
  };

  // Legacy function - kept for compatibility but not used
  const handleBulkDownload = async (format) => {
    if (transactions.length === 0) {
      Alert.alert('No Data', 'No transactions available to download');
      return;
    }

    setReceiptLoading(true);
    try {
      // Build user info for legacy function
      const userInfo = {
        name: user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() : 'User',
        firstName: user?.first_name || '',
        lastName: user?.last_name || '',
        email: user?.email || '',
        phone: user?.phone || '',
        isPersonalTransaction: true,
        reportTitle: 'Personal Transaction History'
      };

      const result = await ReceiptService.downloadBulkReceipts(transactions, format, userInfo);
      if (result.success) {
        Alert.alert(
          'Success',
          `Transaction history downloaded successfully as ${result.fileName}`,
          [{ text: 'OK' }]
        );
        // Modal removed - instant download only
      }
    } catch (error) {
      console.error('Error downloading bulk receipts:', error);
      Alert.alert('Error', 'Failed to download transaction history');
    } finally {
      setReceiptLoading(false);
    }
  };

  // Transaction menu actions
  const handleTransactionMenuAction = (transaction, action) => {
    setShowTransactionMenu(null);
    handleReceiptAction(transaction, action);
  };



  const renderTransaction = ({ item, index }) => (
    <View style={[styles.transactionItem, { backgroundColor: colors.surface }]}>
      <View style={styles.transactionDetails}>
        <Text style={[styles.transactionDescription, { color: colors.text }]}>{item.description}</Text>
        <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>{formatDate(item.date)}</Text>
        <View style={styles.statusContainer}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: item.status === 'completed' ? colors.success : colors.warning }
          ]}>
            <Text style={[styles.statusText, { color: colors.white }]}>{item.status}</Text>
          </View>
        </View>
      </View>

      <View style={styles.transactionAmount}>
        <Text style={[
          styles.amountText,
          { color: getTransactionColor(item.type, item.amount) }
        ]}>
          {item.amount > 0 ? '+' : ''}KES {Math.abs(item.amount).toLocaleString()}
        </Text>

        {/* Three-dot menu */}
        <TouchableOpacity
          style={[styles.menuButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setShowTransactionMenu(showTransactionMenu === item.id ? null : item.id)}
        >
          <Ionicons name="ellipsis-vertical" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Compact Header with Filters */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View style={[styles.header, { borderBottomColor: 'transparent' }]}>
          <View style={styles.headerContent}>
            {/* Filters moved to header row - now taking full width */}
            <View style={styles.headerFilters}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.filterScrollContainer}
              >
                {filterTypes.map((type) => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.compactFilterButton,
                      { backgroundColor: colors.backgroundSecondary },
                      filter === type.id && { backgroundColor: colors.primary },
                    ]}
                    onPress={() => setFilter(type.id)}
                  >
                    <Ionicons
                      name={type.icon}
                      size={14}
                      color={filter === type.id ? colors.white : colors.textSecondary}
                    />
                    <Text style={[
                      styles.compactFilterText,
                      { color: filter === type.id ? colors.white : colors.textSecondary },
                      filter === type.id && { fontWeight: '600' },
                    ]}>
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Three-dot menu for actions */}
            {transactions.length > 0 && (
              <TouchableOpacity
                style={[styles.headerMenuButton, { backgroundColor: colors.backgroundSecondary }]}
                onPress={() => setShowHeaderMenu(!showHeaderMenu)}
              >
                <Ionicons name="ellipsis-vertical" size={20} color={colors.text} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      {/* Header Menu Dropdown */}
      {showHeaderMenu && (
        <View style={[styles.headerMenuDropdown, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowHeaderMenu(false);
              handleInstantBulkDownload();
            }}
          >
            <Ionicons name="download" size={18} color={colors.text} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>Download All</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowHeaderMenu(false);
              // Add export functionality here if needed
            }}
          >
            <Ionicons name="share" size={18} color={colors.text} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>Export</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Overlay to close menu */}
      {showHeaderMenu && (
        <TouchableOpacity
          style={styles.menuOverlay}
          onPress={() => setShowHeaderMenu(false)}
          activeOpacity={1}
        />
      )}

      {/* Scrollable Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary, marginTop: spacing.md }]}>
            Loading transactions...
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          style={styles.transactionsList}
          contentContainerStyle={styles.scrollableContent}
          showsVerticalScrollIndicator={false}
          onScroll={() => setShowTransactionMenu(null)}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Ionicons name="receipt-outline" size={64} color={colors.textTertiary} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                {transactions.length === 0 ? 'No transactions yet' : 'No transactions found'}
              </Text>
              <Text style={[styles.emptySubtext, { color: colors.textTertiary }]}>
                {transactions.length === 0
                  ? 'Start by making a deposit or transfer'
                  : 'Try changing the filter above'
                }
              </Text>
            </View>
          }
        />
      )}

      {/* Transaction Menu Modal */}
      <Modal
        visible={!!showTransactionMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowTransactionMenu(null)}
      >
        <TouchableOpacity
          style={styles.menuModalBackdrop}
          onPress={() => setShowTransactionMenu(null)}
          activeOpacity={1}
        >
          <View style={[
            styles.transactionMenuModal,
            {
              backgroundColor: colors.surface,
              borderColor: colors.border,
            }
          ]}>
            {/* Menu Header */}
            <View style={[styles.menuHeader, { borderBottomColor: colors.border }]}>
              <Text style={[styles.menuHeaderText, { color: colors.text }]}>Receipt Actions</Text>
            </View>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => handleTransactionMenuAction(
                filteredTransactions.find(t => t.id === showTransactionMenu),
                'download'
              )}
              activeOpacity={0.7}
            >
              <View style={[styles.menuIconContainer, { backgroundColor: colors.primary + '20' }]}>
                <Ionicons name="download-outline" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.menuItemText, { color: colors.text }]}>Download Receipt</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => handleTransactionMenuAction(
                filteredTransactions.find(t => t.id === showTransactionMenu),
                'print'
              )}
              activeOpacity={0.7}
            >
              <View style={[styles.menuIconContainer, { backgroundColor: colors.success + '20' }]}>
                <Ionicons name="print-outline" size={18} color={colors.success} />
              </View>
              <Text style={[styles.menuItemText, { color: colors.text }]}>Print Receipt</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: 'transparent' }]}
              onPress={() => handleTransactionMenuAction(
                filteredTransactions.find(t => t.id === showTransactionMenu),
                'share'
              )}
              activeOpacity={0.7}
            >
              <View style={[styles.menuIconContainer, { backgroundColor: colors.accent + '20' }]}>
                <Ionicons name="share-outline" size={18} color={colors.accent} />
              </View>
              <Text style={[styles.menuItemText, { color: colors.text }]}>Share Receipt</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Receipt Format Selection Modal - REMOVED (instant download only) */}

      {/* Bulk Download Modal - REMOVED (instant download only) */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: spacing.sm,
  },
  headerFilters: {
    flex: 1,
  },
  filterScrollContainer: {
    paddingHorizontal: spacing.xs,
  },
  compactFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    gap: spacing.xs,
    marginRight: spacing.xs,
  },
  compactFilterText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  headerMenuButton: {
    width: 36,
    height: 36,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerMenuDropdown: {
    position: 'absolute',
    top: 60,
    right: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    paddingVertical: spacing.xs,
    minWidth: 150,
    zIndex: 1001,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  menuItemText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  menuOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  scrollableContent: {
    flexGrow: 1,
    paddingBottom: spacing.xl,
  },

  bulkDownloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  bulkDownloadText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
  },

  transactionsList: {
    flex: 1,
    paddingHorizontal: spacing.md,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.sm,
    position: 'relative',
    zIndex: 1,
  },
  transactionIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  transactionDate: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
  },
  statusContainer: {
    marginTop: spacing.xs,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.semibold,
    textTransform: 'capitalize',
  },
  transactionAmount: {
    alignItems: 'flex-end',
    position: 'relative',
  },
  amountText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  menuButton: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  transactionMenuModal: {
    width: 220,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    ...shadows.xl,
    elevation: 20,
    overflow: 'hidden',
  },
  menuHeader: {
    padding: spacing.md,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  menuHeaderText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  transactionMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    gap: spacing.md,
    borderBottomWidth: 1,
  },
  menuIconContainer: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  transactionMenuItemText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginTop: spacing.md,
  },
  emptySubtext: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    padding: spacing.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
  },
  modalCloseButton: {
    padding: spacing.xs,
  },
  modalSubtitle: {
    fontSize: typography.fontSize.base,
    marginBottom: spacing.xl,
  },
  formatOptions: {
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  formatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    gap: spacing.md,
  },
  formatButtonText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  formatButtonSubtext: {
    fontSize: typography.fontSize.sm,
    opacity: 0.8,
  },
  modalLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    paddingVertical: spacing.md,
  },
  loadingText: {
    fontSize: typography.fontSize.sm,
  },
});
