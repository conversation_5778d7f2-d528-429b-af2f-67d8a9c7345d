import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  FlatList,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useApp } from '../../context/AppContext';
import { useLightningData, useOptimisticUpdate } from '../../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import ApiService from '../../services/api';

const WalletScreen = ({ navigation }) => {
  const { theme, user, wallets, transactions, loadLocalData, getCachedData, prefetchForPage } = useApp();
  const colors = getThemeColors(theme);

  // Lightning data hooks for instant wallet data
  const {
    data: walletData,
    loading: walletLoading,
    refresh: refreshWallet,
    isInstant: walletInstant,
  } = useLightningData('wallet');

  const {
    data: transactionData,
    loading: transactionsLoading,
    refresh: refreshTransactions,
  } = useLightningData('transactions');

  // Optimistic updates for wallet operations
  const { transferMoney } = useOptimisticUpdate();

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceLoading, setBalanceLoading] = useState(false);
  const [realTimeBalance, setRealTimeBalance] = useState(null);
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [recentTransactions, setRecentTransactions] = useState([]);

  useEffect(() => {
    if (wallets.length > 0) {
      setSelectedWallet(wallets.find(w => w.type === 'personal') || wallets[0]);
    }
    setRecentTransactions(transactions.slice(0, 5));

    // Load real-time balance on component mount
    loadRealTimeBalance();

    // Set up auto-refresh for balance every 30 seconds
    const balanceInterval = setInterval(() => {
      loadRealTimeBalance();
    }, 30000);

    return () => {
      clearInterval(balanceInterval);
    };
  }, [wallets, transactions]);

  // Refresh balance when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadRealTimeBalance();
    }, [])
  );

  // Load real-time balance from API
  const loadRealTimeBalance = async () => {
    try {
      setBalanceLoading(true);
      const response = await ApiService.getWalletBalance();

      if (response.success) {
        const balance = response.data.balance || 0;
        console.log('💰 Real-time balance loaded:', balance);
        setRealTimeBalance(balance);
      } else {
        console.warn('Failed to load wallet balance:', response.error);
        // Fallback to static balance calculation
        const fallbackBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
        console.log('💰 Using fallback balance:', fallbackBalance);
        setRealTimeBalance(fallbackBalance);
      }
    } catch (error) {
      console.error('Error loading wallet balance:', error);
      // Fallback to static balance calculation
      const fallbackBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      console.log('💰 Using error fallback balance:', fallbackBalance);
      setRealTimeBalance(fallbackBalance);
    } finally {
      setBalanceLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadLocalData(),
      loadRealTimeBalance()
    ]);
    setRefreshing(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getTotalBalance = () => {
    // Always prioritize real-time balance over static wallet data
    if (realTimeBalance !== null && realTimeBalance !== undefined) {
      console.log('💰 Using real-time balance:', realTimeBalance);
      return realTimeBalance;
    }
    // Fallback to static calculation only if real-time balance is not available
    const staticBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
    console.log('💰 Using static balance:', staticBalance, 'from wallets:', wallets.length);
    return staticBalance;
  };

  const getWalletIcon = (type) => {
    switch (type) {
      case 'personal': return 'wallet';
      case 'chama': return 'people';
      case 'business': return 'briefcase';
      case 'savings': return 'piggy-bank';
      default: return 'card';
    }
  };

  const getWalletColor = (type) => {
    switch (type) {
      case 'personal': return colors.primary;
      case 'chama': return colors.secondary;
      case 'business': return colors.info;
      case 'savings': return colors.success;
      default: return colors.textSecondary;
    }
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'deposit': return 'arrow-down';
      case 'withdrawal': return 'arrow-up';
      case 'transfer': return 'swap-horizontal';
      case 'payment': return 'card';
      default: return 'cash';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'deposit': return colors.success;
      case 'withdrawal': return colors.error;
      case 'transfer': return colors.info;
      case 'payment': return colors.warning;
      default: return colors.textSecondary;
    }
  };

  const renderWalletHeader = () => (
    
    <Card style={styles.section}>
      <View style={styles.totalBalanceContainer}>
        <Text style={[styles.totalBalanceLabel, { color: colors.textSecondary }]}>
          Total Balance
        </Text>
        <View style={styles.balanceRow}>
          {balanceLoading ? (
            <View style={styles.balanceLoadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={[styles.balanceLoadingText, { color: colors.textSecondary }]}>
                Updating...
              </Text>
            </View>
          ) : (
            <Text style={[styles.totalBalanceAmount, { color: colors.text }]}>
              {formatCurrency(getTotalBalance())}
            </Text>
          )}
          <TouchableOpacity
            onPress={loadRealTimeBalance}
            style={[styles.refreshButton, { backgroundColor: colors.primary + '20' }]}
          >
            <Ionicons name="refresh" size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => navigation.navigate('Transfer')}
        >
          <Ionicons name="send" size={24} color={colors.primary} />
          <Text style={[styles.actionText, { color: colors.primary }]}>
            Send
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.success + '20' }]}
          onPress={() => navigation.navigate('RequestMoney')}
        >
          <Ionicons name="download" size={24} color={colors.success} />
          <Text style={[styles.actionText, { color: colors.success }]}>
            Receive
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.info + '20' }]}
          onPress={() => navigation.navigate('Deposit')}
        >
          <Ionicons name="add" size={24} color={colors.info} />
          <Text style={[styles.actionText, { color: colors.info }]}>
            Top Up
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.error + '20' }]}
          onPress={() => navigation.navigate('Withdraw')}
        >
          <Ionicons name="arrow-up-circle" size={24} color={colors.error} />
          <Text style={[styles.actionText, { color: colors.error }]}>
            Withdraw
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderWalletHeader()}

        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Quick Actions
          </Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: colors.primary + '20' }]}
              onPress={() => navigation.navigate('TransactionHistory')}
            >
              <Ionicons name="list" size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.primary }]}>
                History
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: colors.success + '20' }]}
              onPress={() => navigation.navigate('AIAssistant')}
            >
              <Ionicons name="sparkles" size={24} color={colors.success} />
              <Text style={[styles.quickActionText, { color: colors.success }]}>
                AI Advisor
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  totalBalanceContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  totalBalanceLabel: {
    fontSize: typography.fontSize.base,
    marginBottom: spacing.sm,
  },
  totalBalanceAmount: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginTop: spacing.sm,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
  },
  quickActionText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    marginTop: spacing.sm,
  },
  // Real-time balance styles
  balanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.md,
  },
  balanceLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  balanceLoadingText: {
    fontSize: typography.fontSize.sm,
    fontStyle: 'italic',
  },
  refreshButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.full,
  },
});

export default WalletScreen;
