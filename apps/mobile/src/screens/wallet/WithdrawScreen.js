import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import ApiService from '../../services/api';
import { formatCurrency } from '../../utils/formatters';

export default function WithdrawScreen({ navigation }) {
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [amount, setAmount] = useState('');
  const [withdrawMethod, setWithdrawMethod] = useState('mpesa');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [loading, setLoading] = useState(false);

  const availableBalance = 15750.50; // Mock balance - should be replaced with real balance

  const handleWithdraw = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (parseFloat(amount) > availableBalance) {
      Alert.alert('Error', 'Insufficient balance');
      return;
    }

    if (withdrawMethod === 'mpesa' && !phoneNumber) {
      Alert.alert('Error', 'Please enter your M-Pesa phone number');
      return;
    }

    if (withdrawMethod === 'bank' && !accountNumber) {
      Alert.alert('Error', 'Please enter your bank account number');
      return;
    }

    const methodName = withdrawMethod === 'mpesa' ? 'M-Pesa' : 'Bank Account';
    const destination = withdrawMethod === 'mpesa' ? phoneNumber : accountNumber;

    Alert.alert(
      'Withdraw Confirmation',
      `Withdraw ${formatCurrency(parseFloat(amount))} to ${methodName} (${destination})?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: async () => {
            try {
              setLoading(true);

              // Call the actual withdrawal API with all required parameters
              const response = await ApiService.initiateWithdrawal(
                parseFloat(amount),
                withdrawMethod,
                phoneNumber, // For M-Pesa
                accountNumber, // For bank (account number)
                'KCB', // For bank (bank code) - should be dynamic based on user selection
                `Withdrawal via ${withdrawMethod === 'mpesa' ? 'M-Pesa' : 'Bank'}`
              );

              if (response.success) {
                // Show enhanced success message
                const successMessage = withdrawMethod === 'mpesa'
                  ? `Withdrawal of ${formatCurrency(parseFloat(amount))} has been initiated to M-Pesa number ${phoneNumber}. You will receive the money shortly.`
                  : `Withdrawal of ${formatCurrency(parseFloat(amount))} has been initiated to your bank account. Processing may take 1-3 business days.`;

                Alert.alert(
                  'Withdrawal Initiated!',
                  successMessage,
                  [
                    {
                      text: 'View Transactions',
                      onPress: () => {
                        // Reset form and navigate to transaction history
                        setAmount('');
                        setPhoneNumber('');
                        setAccountNumber('');
                        navigation.navigate('TransactionHistory');
                      },
                    },
                    {
                      text: 'OK',
                      style: 'default',
                      onPress: () => {
                        // Reset form and navigate back
                        setAmount('');
                        setPhoneNumber('');
                        setAccountNumber('');
                        navigation.goBack();
                      },
                    },
                  ]
                );
              } else {
                Alert.alert('Withdrawal Failed', response.error || 'Failed to initiate withdrawal');
              }
            } catch (error) {
              console.error('Withdrawal error:', error);
              Alert.alert('Withdrawal Failed', error.message || 'Failed to initiate withdrawal');
            } finally {
              setLoading(false);
            }
          }
        },
      ]
    );
  };

  const withdrawMethods = [
    { id: 'mpesa', name: 'M-Pesa', icon: 'phone-portrait' },
    { id: 'bank', name: 'Bank Account', icon: 'card' },
  ];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Compact Header */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View style={[styles.header, { borderBottomColor: 'transparent' }]}>
          <View style={styles.headerContent}>
            <Ionicons name="arrow-up-circle" size={24} color={colors.error} />
            <View>
              <Text style={[styles.title, { color: colors.text }]}>Withdraw</Text>
              <Text style={[styles.balanceText, { color: colors.success }]}>KES {availableBalance.toLocaleString()}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView style={styles.scrollableContainer} showsVerticalScrollIndicator={false}>

      <View style={styles.form}>
        <Text style={[styles.label, { color: colors.text }]}>Amount (KES)</Text>
        <TextInput
          style={[styles.amountInput, {
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          keyboardType="numeric"
          placeholderTextColor={colors.textTertiary}
        />

        <Text style={[styles.label, { color: colors.text }]}>Withdrawal Method</Text>
        {withdrawMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.withdrawMethod,
              {
                backgroundColor: colors.surface,
                borderColor: withdrawMethod === method.id ? colors.primary : colors.border
              },
              withdrawMethod === method.id && { backgroundColor: colors.primary + '20' },
            ]}
            onPress={() => setWithdrawMethod(method.id)}
          >
            <Ionicons
              name={method.icon}
              size={24}
              color={withdrawMethod === method.id ? colors.primary : colors.textSecondary}
            />
            <Text style={[
              styles.methodText,
              { color: withdrawMethod === method.id ? colors.text : colors.textSecondary },
              withdrawMethod === method.id && { fontWeight: '600' },
            ]}>
              {method.name}
            </Text>
            <Ionicons
              name={withdrawMethod === method.id ? 'radio-button-on' : 'radio-button-off'}
              size={20}
              color={withdrawMethod === method.id ? colors.primary : colors.textSecondary}
            />
          </TouchableOpacity>
        ))}

        {withdrawMethod === 'mpesa' && (
          <View>
            <Text style={[styles.label, { color: colors.text }]}>M-Pesa Phone Number</Text>
            <TextInput
              style={[styles.input, {
                backgroundColor: colors.surface,
                borderColor: colors.border,
                color: colors.text
              }]}
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder="************"
              keyboardType="phone-pad"
              placeholderTextColor={colors.textTertiary}
            />
          </View>
        )}

        {withdrawMethod === 'bank' && (
          <View>
            <Text style={[styles.label, { color: colors.text }]}>Bank Account Number</Text>
            <TextInput
              style={[styles.input, {
                backgroundColor: colors.surface,
                borderColor: colors.border,
                color: colors.text
              }]}
              value={accountNumber}
              onChangeText={setAccountNumber}
              placeholder="Enter account number"
              keyboardType="numeric"
              placeholderTextColor={colors.textTertiary}
            />
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.withdrawButton,
            {
              backgroundColor: loading ? colors.textSecondary : colors.error,
              opacity: loading ? 0.7 : 1
            }
          ]}
          onPress={handleWithdraw}
          disabled={loading}
        >
          <Text style={[styles.withdrawButtonText, { color: colors.white }]}>
            {loading ? 'Processing...' : `Withdraw ${formatCurrency(parseFloat(amount) || 0)}`}
          </Text>
        </TouchableOpacity>
      </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  scrollableContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  balanceText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  balanceCard: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginTop: spacing.md,
    alignItems: 'center',
    borderWidth: 1,
    ...shadows.sm,
  },
  balanceLabel: {
    fontSize: typography.fontSize.sm,
  },
  balanceAmount: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginTop: spacing.xs,
  },
  form: {
    padding: spacing.xl,
  },
  label: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
    marginTop: spacing.md,
  },
  amountInput: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
    borderWidth: 2,
  },
  input: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
    borderWidth: 2,
  },
  withdrawMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 2,
  },
  methodText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    marginLeft: spacing.sm,
  },
  withdrawButton: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    alignItems: 'center',
    marginTop: spacing.xl,
    ...shadows.md,
  },
  withdrawButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
});
