import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { useOptimisticUpdate } from '../../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import ApiService from '../../services/api';
import { formatCurrency } from '../../utils/formatters';

export default function TransferScreen({ navigation }) {
  const { theme } = useApp();
  const colors = getThemeColors(theme);

  const [amount, setAmount] = useState('');
  const [recipient, setRecipient] = useState('');
  const [transferType, setTransferType] = useState('user');
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);

  const availableBalance = 15750.50; // Mock balance - should be replaced with real balance

  const handleTransfer = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (parseFloat(amount) > availableBalance) {
      Alert.alert('Error', 'Insufficient balance');
      return;
    }

    if (!recipient) {
      Alert.alert('Error', 'Please enter recipient details');
      return;
    }

    Alert.alert(
      'Transfer Confirmation',
      `Transfer ${formatCurrency(parseFloat(amount))} to ${recipient}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: async () => {
            try {
              setLoading(true);

              // Call the actual transfer API with recipient type
              const response = await ApiService.transferMoney(
                recipient,
                parseFloat(amount),
                note || `Transfer to ${recipient}`,
                transferType // 'phone', 'email', or 'user'
              );

              if (response.success) {
                // Show enhanced success message
                Alert.alert(
                  'Transfer Successful!',
                  `${formatCurrency(parseFloat(amount))} has been successfully transferred to ${recipient}. The recipient will be notified of the transfer.`,
                  [
                    {
                      text: 'View Transactions',
                      onPress: () => {
                        // Reset form and navigate to transaction history
                        setAmount('');
                        setRecipient('');
                        setNote('');
                        navigation.navigate('TransactionHistory');
                      },
                    },
                    {
                      text: 'OK',
                      style: 'default',
                      onPress: () => {
                        // Reset form and navigate back
                        setAmount('');
                        setRecipient('');
                        setNote('');
                        navigation.goBack();
                      },
                    },
                  ]
                );
              } else {
                Alert.alert('Transfer Failed', response.error || 'Failed to complete transfer');
              }
            } catch (error) {
              console.error('Transfer error:', error);
              Alert.alert('Transfer Failed', error.message || 'Failed to complete transfer');
            } finally {
              setLoading(false);
            }
          }
        },
      ]
    );
  };

  const transferTypes = [
    { id: 'user', name: 'VaultKe User', icon: 'person', placeholder: 'Enter username or phone' },
    { id: 'chama', name: 'Chama Wallet', icon: 'people', placeholder: 'Enter chama name or ID' },
  ];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Compact Header */}
      <View style={[styles.staticHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View style={[styles.header, { borderBottomColor: 'transparent' }]}>
          <View style={styles.headerContent}>
            <Ionicons name="swap-horizontal" size={24} color={colors.primary} />
            <View>
              <Text style={[styles.title, { color: colors.text }]}>Transfer</Text>
              <Text style={[styles.balanceText, { color: colors.success }]}>KES {availableBalance.toLocaleString()}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView style={styles.scrollableContainer} showsVerticalScrollIndicator={false}>

      <View style={styles.form}>
        <Text style={[styles.label, { color: colors.text }]}>Transfer To</Text>
        {transferTypes.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.transferType,
              {
                backgroundColor: colors.surface,
                borderColor: transferType === type.id ? colors.primary : colors.border
              },
              transferType === type.id && { backgroundColor: colors.primary + '20' },
            ]}
            onPress={() => setTransferType(type.id)}
          >
            <Ionicons
              name={type.icon}
              size={24}
              color={transferType === type.id ? colors.primary : colors.textSecondary}
            />
            <Text style={[
              styles.typeText,
              { color: transferType === type.id ? colors.text : colors.textSecondary },
              transferType === type.id && { fontWeight: '600' },
            ]}>
              {type.name}
            </Text>
            <Ionicons
              name={transferType === type.id ? 'radio-button-on' : 'radio-button-off'}
              size={20}
              color={transferType === type.id ? colors.primary : colors.textSecondary}
            />
          </TouchableOpacity>
        ))}

        <Text style={[styles.label, { color: colors.text }]}>Recipient</Text>
        <TextInput
          style={[styles.input, {
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }]}
          value={recipient}
          onChangeText={setRecipient}
          placeholder={transferTypes.find(t => t.id === transferType)?.placeholder}
          placeholderTextColor={colors.textTertiary}
        />

        <Text style={[styles.label, { color: colors.text }]}>Amount (KES)</Text>
        <TextInput
          style={[styles.amountInput, {
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          keyboardType="numeric"
          placeholderTextColor={colors.textTertiary}
        />

        <Text style={[styles.label, { color: colors.text }]}>Note (Optional)</Text>
        <TextInput
          style={[styles.noteInput, {
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }]}
          value={note}
          onChangeText={setNote}
          placeholder="Add a note for this transfer"
          placeholderTextColor={colors.textTertiary}
          multiline
          numberOfLines={3}
        />

        <View style={[styles.feeInfo, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.feeRow}>
            <Text style={[styles.feeLabel, { color: colors.textSecondary }]}>Transfer Amount</Text>
            <Text style={[styles.feeValue, { color: colors.text }]}>KES {amount || '0.00'}</Text>
          </View>
          <View style={styles.feeRow}>
            <Text style={[styles.feeLabel, { color: colors.textSecondary }]}>Transfer Fee</Text>
            <Text style={[styles.feeValue, { color: colors.text }]}>KES 0.00</Text>
          </View>
          <View style={[styles.feeRow, styles.totalRow, { borderTopColor: colors.border }]}>
            <Text style={[styles.totalLabel, { color: colors.text }]}>Total</Text>
            <Text style={[styles.totalValue, { color: colors.success }]}>KES {amount || '0.00'}</Text>
          </View>
        </View>

        <TouchableOpacity
          style={[
            styles.transferButton,
            {
              backgroundColor: loading ? colors.textSecondary : colors.primary,
              opacity: loading ? 0.7 : 1
            }
          ]}
          onPress={handleTransfer}
          disabled={loading}
        >
          <Text style={[styles.transferButtonText, { color: colors.white }]}>
            {loading ? 'Processing...' : `Transfer ${formatCurrency(parseFloat(amount) || 0)}`}
          </Text>
        </TouchableOpacity>
      </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  staticHeader: {
    borderBottomWidth: 1,
    ...shadows.sm,
    zIndex: 1000,
    elevation: 5,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  scrollableContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  balanceText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  balanceCard: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginTop: spacing.md,
    alignItems: 'center',
    borderWidth: 1,
    ...shadows.sm,
  },
  balanceLabel: {
    fontSize: typography.fontSize.sm,
  },
  balanceAmount: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginTop: spacing.xs,
  },
  form: {
    padding: spacing.xl,
  },
  label: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.sm,
    marginTop: spacing.md,
  },
  transferType: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 2,
  },
  typeText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    marginLeft: spacing.sm,
  },
  input: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
    borderWidth: 2,
  },
  amountInput: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
    borderWidth: 2,
  },
  noteInput: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
    borderWidth: 2,
    textAlignVertical: 'top',
  },
  feeInfo: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginTop: spacing.md,
    borderWidth: 1,
  },
  feeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  totalRow: {
    borderTopWidth: 1,
    paddingTop: spacing.sm,
    marginTop: spacing.sm,
  },
  feeLabel: {
    fontSize: typography.fontSize.base,
  },
  feeValue: {
    fontSize: typography.fontSize.base,
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  transferButton: {
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    alignItems: 'center',
    marginTop: spacing.xl,
    ...shadows.md,
  },
  transferButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
});
