import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  Dimensions,
  Animated,
  PanGestureHandler,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../context/AppContext';
import { useLightningData, useOptimisticUpdate } from '../hooks/useLightningData';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../utils/theme';
import { getTimeAgo } from '../utils/dateUtils';
import Card from '../components/common/Card';
import Button from '../components/common/Button';
import ApiService from '../services/api';
import Toast from 'react-native-toast-message';
import DatabaseService from '../services/database';

const { width: screenWidth } = Dimensions.get('window');

const NotificationsScreen = ({ navigation }) => {
  const { theme, notifications: contextNotifications, loadLocalData, getCachedData } = useApp();
  const colors = getThemeColors(theme);

  // Lightning data hooks for instant notifications
  const {
    data: notifications,
    loading,
    refresh: refreshNotifications,
    isInstant: notificationsInstant,
  } = useLightningData('notifications');

  const {
    data: unreadCount,
    refresh: refreshUnreadCount,
  } = useLightningData('unread-count');

  // Optimistic updates for notification operations
  const { markNotificationAsRead, deleteNotification, markAllNotificationsAsRead } = useOptimisticUpdate();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [forceUpdate, setForceUpdate] = useState(0);
  const [invitationsCount, setInvitationsCount] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const [refreshTimeout, setRefreshTimeout] = useState(null);

  // Use lightning data directly instead of local state for real-time updates
  const displayNotifications = notifications || contextNotifications || [];

  // Force re-render when notifications change to ensure filter counts update
  const [filterUpdateTrigger, setFilterUpdateTrigger] = useState(0);

  // Debounced refresh function to prevent too many API calls
  const debouncedRefresh = React.useCallback(async () => {
    // Clear any existing timeout
    if (refreshTimeout) {
      clearTimeout(refreshTimeout);
    }

    // Set a new timeout for debounced refresh
    const timeoutId = setTimeout(async () => {
      console.log('🔄 DEBOUNCED REFRESH: Executing debounced refresh...');
      try {
        await Promise.all([
          refreshNotifications(),
          refreshUnreadCount()
        ]);
        setFilterUpdateTrigger(prev => prev + 1);
        console.log('🔄 DEBOUNCED REFRESH: Completed');
      } catch (error) {
        console.error('❌ DEBOUNCED REFRESH: Failed:', error);
      }
    }, 500); // 500ms debounce

    setRefreshTimeout(timeoutId);
  }, [refreshTimeout, refreshNotifications, refreshUnreadCount]);

  useEffect(() => {
    setFilterUpdateTrigger(prev => prev + 1);
  }, [displayNotifications.length, displayNotifications.filter(n => !(n.isRead || n.is_read)).length]);

  // Log when notifications change for debugging
  useEffect(() => {
    console.log('🔄 Notifications data changed:', {
      lightningCount: notifications?.length || 0,
      contextCount: contextNotifications?.length || 0,
      unreadCount: unreadCount || 0
    });
  }, [notifications, contextNotifications, unreadCount]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      console.log('🎯 NotificationsScreen focused - refreshing data...');

      // Refresh both notifications and unread count when screen comes into focus
      const refreshData = async () => {
        try {
          await Promise.all([
            refreshNotifications(),
            refreshUnreadCount()
          ]);
          console.log('🎯 Focus refresh completed');
        } catch (error) {
          console.error('❌ Focus refresh failed:', error);
        }
      };

      refreshData();
    }, [refreshNotifications, refreshUnreadCount])
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
      }
    };
  }, [refreshTimeout]);

  // Calculate filter counts - recalculates on every render for real-time updates
  const allCount = displayNotifications.length;
  const unreadFilterCount = displayNotifications.filter(n => !(n.isRead || n.is_read)).length;
  const chamaCount = displayNotifications.filter(n => n.type === 'chama' || n.type === 'chama_invitation' || n.type === 'member_joined').length;
  const financialCount = displayNotifications.filter(n => n.type === 'financial' || n.type.includes('contribution') || n.type.includes('loan') || n.type.includes('welfare') || n.type === 'guarantor_request').length;
  const supportCount = displayNotifications.filter(n => n.type === 'support_update' || n.type === 'new_support_request').length;
  const systemCount = displayNotifications.filter(n => n.type === 'system').length;

  const filters = [
    { id: 'all', name: 'All', count: allCount },
    { id: 'unread', name: 'Unread', count: unreadFilterCount },
    { id: 'chama', name: 'Chama', count: chamaCount },
    { id: 'financial', name: 'Financial', count: financialCount },
    { id: 'support', name: 'Support', count: supportCount },
    { id: 'system', name: 'System', count: systemCount },
  ];

  // Debug filter counts when they change
  useEffect(() => {
    console.log('🔢 Filter counts recalculated:', {
      all: allCount,
      unread: unreadFilterCount,
      chama: chamaCount,
      financial: financialCount,
      support: supportCount,
      system: systemCount,
      trigger: filterUpdateTrigger,
      totalNotifications: displayNotifications.length
    });
  }, [allCount, unreadFilterCount, chamaCount, financialCount, supportCount, systemCount, filterUpdateTrigger, displayNotifications.length]);

  // Debug filter counts
  useEffect(() => {
    console.log('🔢 Filter counts updated:', {
      all: filters[0].count,
      unread: filters[1].count,
      chama: filters[2].count,
      financial: filters[3].count,
      support: filters[4].count,
      system: filters[5].count,
    });
  }, [displayNotifications.length, displayNotifications.filter(n => !(n.isRead || n.is_read)).length]);

  useEffect(() => {
    loadNotifications();
    loadInvitationsCount();
  }, []);

  const loadInvitationsCount = async () => {
    try {
      const response = await ApiService.getUserInvitations();
      if (response.success) {
        setInvitationsCount(response.count || 0);
      }
    } catch (error) {
      console.log('Failed to load invitations count:', error);
      setInvitationsCount(0);
    }
  };

  const loadNotifications = async () => {
    try {
      const response = await ApiService.getNotifications();
      if (response.success) {
        await loadLocalData();
      }
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  };

  const onRefresh = async () => {
    console.log('🔄 PULL REFRESH: Starting pull-to-refresh...');
    setRefreshing(true);

    try {
      // Use lightning data refresh for better performance and consistency
      await Promise.all([
        refreshNotifications(),
        refreshUnreadCount(),
        loadInvitationsCount()
      ]);

      // Force a re-render to update filter counts
      setFilterUpdateTrigger(prev => prev + 1);

      console.log('🔄 PULL REFRESH: Pull-to-refresh completed');
    } catch (error) {
      console.error('❌ PULL REFRESH: Pull-to-refresh failed:', error);
    } finally {
      setRefreshing(false);
    }
  };



  const markAllAsRead = async () => {
    try {
      console.log('📖 MARK ALL READ: Starting mark all notifications as read...');

      // Count unread notifications before marking as read
      const unreadNotifications = displayNotifications.filter(n => !(n.isRead || n.is_read));
      console.log('📊 MARK ALL READ: Marking', unreadNotifications.length, 'notifications as read');

      // Use optimistic update for better UX
      const markAllResult = await markAllNotificationsAsRead();
      console.log('📖 MARK ALL READ: Mark all result:', markAllResult);

      // Show success toast
      Toast.show({
        type: 'success',
        text1: 'All Marked as Read',
        text2: `${unreadNotifications.length} notifications marked as read`,
        position: 'bottom',
        visibilityTime: 2000,
      });

      // Force a re-render to update filter counts (optimistic update handles data refresh)
      setFilterUpdateTrigger(prev => prev + 1);

      console.log('✅ All notifications marked as read successfully');
    } catch (error) {
      console.error('❌ Mark all as read error:', error);
      Toast.show({
        type: 'error',
        text1: 'Update Failed',
        text2: 'Could not mark all notifications as read. Please try again.',
        position: 'bottom',
        visibilityTime: 3000,
      });
    }
  };



  const handleNotificationPress = async (notification) => {
    // Handle both old and new notification formats
    const isRead = notification.isRead || notification.is_read;
    if (!isRead) {
      await markNotificationAsRead(notification.id);
    }

    // Navigate based on notification type
    switch (notification.type) {
      case 'chama_invitation':
        // Show invitation response dialog
        showInvitationDialog(notification);
        break;
      case 'guarantor_request':
        // Show guarantor response dialog
        showGuarantorDialog(notification);
        break;
      case 'chama':
        if (notification.data?.chamaId || notification.metadata?.chama_id) {
          navigation.navigate('ChamaDetails', {
            chamaId: notification.data?.chamaId || notification.metadata?.chama_id
          });
        }
        break;
      case 'meeting_scheduled':
      case 'meeting_started':
      case 'meeting_created':
        if (notification.data?.chamaId) {
          navigation.navigate('ChamaMeetings', {
            chamaId: notification.data.chamaId,
            meetingId: notification.data.meetingId
          });
        }
        break;
      case 'loan_application_submitted':
      case 'loan_approved':
      case 'loan_disbursed':
      case 'loan_rejected':
      case 'loan_application_new':
      case 'loan_approved_member':
        if (notification.data?.chamaId) {
          navigation.navigate('ChamaLoans', {
            chamaId: notification.data.chamaId,
            loanId: notification.data.loanId
          });
        }
        break;
      case 'welfare_request_created':
      case 'welfare_approved':
      case 'welfare_rejected':
      case 'welfare_request_new':
      case 'welfare_approved_member':
        if (notification.data?.chamaId) {
          navigation.navigate('ChamaWelfare', {
            chamaId: notification.data.chamaId,
            welfareId: notification.data.welfareId
          });
        }
        break;
      case 'contribution_recorded':
      case 'welfare_contribution_recorded':
      case 'loan_payment_recorded':
      case 'member_contribution':
      case 'member_welfare_contribution':
        if (notification.data?.chamaId) {
          navigation.navigate('ChamaContributions', {
            chamaId: notification.data.chamaId,
            transactionId: notification.data.transactionId
          });
        }
        break;
      case 'member_joined':
        if (notification.data?.chamaId) {
          navigation.navigate('ChamaMembers', {
            chamaId: notification.data.chamaId
          });
        }
        break;
      case 'financial':
        if (notification.metadata?.transaction_id) {
          navigation.navigate('TransactionDetails', { transactionId: notification.metadata.transaction_id });
        } else {
          navigation.navigate('Wallet');
        }
        break;
      case 'loan':
        if (notification.metadata?.loan_id) {
          navigation.navigate('LoanDetails', { loanId: notification.metadata.loan_id });
        }
        break;
      case 'marketplace':
        if (notification.metadata?.order_id) {
          navigation.navigate('OrderTracking', { orderId: notification.metadata.order_id });
        } else {
          navigation.navigate('MainTabs', { screen: 'Marketplace' });
        }
        break;
      case 'support_update':
      case 'new_support_request':
        // Navigate to support screen or show support details
        if (notification.data) {
          try {
            const data = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data;
            if (data.supportRequestId) {
              // For users, show their support request details
              // For admins, navigate to admin support management
              navigation.navigate('ContactSupport', {
                requestId: data.supportRequestId,
                highlightRequest: true
              });
            }
          } catch (error) {
            console.warn('Failed to parse support notification data:', error);
            navigation.navigate('ContactSupport');
          }
        } else {
          navigation.navigate('ContactSupport');
        }
        break;
      default:
        break;
    }
  };

  const getFilteredNotifications = () => {
    let filtered = displayNotifications;

    switch (selectedFilter) {
      case 'unread':
        filtered = displayNotifications.filter(n => !(n.isRead || n.is_read));
        break;
      case 'chama':
        filtered = displayNotifications.filter(n =>
          n.type === 'chama' ||
          n.type === 'chama_invitation' ||
          n.type === 'member_joined'
        );
        break;
      case 'financial':
        filtered = displayNotifications.filter(n =>
          n.type === 'financial' ||
          n.type.includes('contribution') ||
          n.type.includes('loan') ||
          n.type.includes('welfare') ||
          n.type === 'guarantor_request'
        );
        break;
      case 'system':
        filtered = displayNotifications.filter(n => n.type === 'system');
        break;
      case 'support':
        filtered = displayNotifications.filter(n =>
          n.type === 'support_update' ||
          n.type === 'new_support_request'
        );
        break;
      default:
        break;
    }

    return filtered.sort((a, b) =>
      new Date(b.createdAt || b.created_at) - new Date(a.createdAt || a.created_at)
    );
  };

  const showInvitationDialog = (notification) => {
    const invitationData = notification.data;
    Alert.alert(
      'Chama Invitation',
      `${invitationData.inviterName} invited you to join ${invitationData.chamaName}\n\nContribution: KES ${invitationData.contributionAmount} ${invitationData.contributionFrequency}\n\n${invitationData.chamaDescription || ''}`,
      [
        {
          text: 'Reject',
          style: 'destructive',
          onPress: () => respondToInvitation(invitationData.invitationId, 'reject'),
        },
        {
          text: 'Accept',
          style: 'default',
          onPress: () => respondToInvitation(invitationData.invitationId, 'accept'),
        },
      ]
    );
  };

  const respondToInvitation = async (invitationId, action) => {
    try {
      const endpoint = action === 'accept'
        ? `/notifications/invitations/${invitationId}/accept`
        : `/notifications/invitations/${invitationId}/reject`;

      const response = await ApiService.makeRequest(endpoint, 'POST');

      if (response.success) {
        Alert.alert(
          'Success',
          action === 'accept'
            ? 'You have successfully joined the chama!'
            : 'Invitation rejected successfully',
          [{ text: 'OK', onPress: () => loadLocalData() }]
        );
      } else {
        throw new Error(response.error || 'Failed to respond to invitation');
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to respond to invitation');
    }
  };

  const showGuarantorDialog = (notification) => {
    const guarantorData = notification.data ? JSON.parse(notification.data) : {};
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-KE', {
        style: 'currency',
        currency: 'KES',
        minimumFractionDigits: 0,
      }).format(amount);
    };

    Alert.alert(
      'Guarantor Request',
      `You have been requested to guarantee a loan of ${formatCurrency(guarantorData.amount)}\n\nPurpose: ${guarantorData.purpose || 'Not specified'}\n\nDo you accept to be a guarantor for this loan?`,
      [
        {
          text: 'Decline',
          style: 'destructive',
          onPress: () => showGuarantorReasonDialog(guarantorData.guarantor_id, 'decline'),
        },
        {
          text: 'Accept',
          style: 'default',
          onPress: () => respondToGuarantorRequest(guarantorData.guarantor_id, 'accept'),
        },
      ]
    );
  };

  const showGuarantorReasonDialog = (guarantorId, action) => {
    Alert.prompt(
      'Decline Reason',
      'Please provide a reason for declining (optional):',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Submit',
          onPress: (reason) => respondToGuarantorRequest(guarantorId, action, reason || ''),
        },
      ],
      'plain-text'
    );
  };

  const respondToGuarantorRequest = async (guarantorId, action, reason = '') => {
    try {
      const response = await ApiService.respondToGuarantorRequest(guarantorId, action, reason);

      if (response.success) {
        Alert.alert(
          'Success',
          action === 'accept'
            ? 'You have accepted to be a guarantor for this loan!'
            : 'You have declined the guarantor request',
          [{ text: 'OK', onPress: () => loadLocalData() }]
        );
      } else {
        throw new Error(response.error || 'Failed to respond to guarantor request');
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to respond to guarantor request');
    }
  };

  const getNotificationIcon = (type, priority) => {
    switch (type) {
      case 'chama_invitation':
        return 'mail';
      case 'guarantor_request':
        return 'shield-checkmark';
      case 'chama':
      case 'member_joined':
        return 'people';
      case 'meeting_scheduled':
      case 'meeting_started':
      case 'meeting_created':
        return 'calendar';
      case 'loan_application_submitted':
      case 'loan_approved':
      case 'loan_disbursed':
      case 'loan_rejected':
      case 'loan_application_new':
      case 'loan_approved_member':
        return 'cash';
      case 'welfare_request_created':
      case 'welfare_approved':
      case 'welfare_rejected':
      case 'welfare_request_new':
      case 'welfare_approved_member':
        return 'heart';
      case 'contribution_recorded':
      case 'welfare_contribution_recorded':
      case 'loan_payment_recorded':
      case 'member_contribution':
      case 'member_welfare_contribution':
        return 'wallet';
      case 'financial':
        return 'card';
      case 'loan':
        return 'cash';
      case 'marketplace':
        return 'storefront';
      case 'system':
        return priority === 'high' ? 'warning' : 'information-circle';
      case 'support_update':
      case 'new_support_request':
        return 'help-circle';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = (type, priority) => {
    switch (type) {
      case 'chama_invitation':
        return colors.primary;
      case 'guarantor_request':
        return colors.warning;
      case 'chama':
      case 'member_joined':
        return colors.primary;
      case 'meeting_scheduled':
      case 'meeting_started':
      case 'meeting_created':
        return colors.info;
      case 'loan_application_submitted':
      case 'loan_approved':
      case 'loan_disbursed':
      case 'loan_rejected':
      case 'loan_application_new':
      case 'loan_approved_member':
        return colors.warning;
      case 'welfare_request_created':
      case 'welfare_approved':
      case 'welfare_rejected':
      case 'welfare_request_new':
      case 'welfare_approved_member':
        return colors.error;
      case 'contribution_recorded':
      case 'welfare_contribution_recorded':
      case 'loan_payment_recorded':
      case 'member_contribution':
      case 'member_welfare_contribution':
        return colors.success;
      case 'financial':
        return colors.success;
      case 'loan':
        return colors.warning;
      case 'marketplace':
        return colors.info;
      case 'system':
        return priority === 'high' ? colors.error : colors.secondary;
      case 'support_update':
      case 'new_support_request':
        return colors.info;
      default:
        return colors.textSecondary;
    }
  };

  // Time formatting function is now imported from utils

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <View style={styles.headerTop}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Notifications
        </Text>

        <View style={styles.headerActions}>
          {displayNotifications.filter(n => !(n.isRead || n.is_read)).length > 0 && (
            <TouchableOpacity onPress={markAllAsRead} style={styles.markAllButton}>
              <Text style={[styles.markAllText, { color: colors.primary }]}>
                Mark all read
              </Text>
            </TouchableOpacity>
          )}

          {/* 3-dots menu with invitation indicator */}
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => setShowMenu(!showMenu)}
          >
            <Ionicons name="ellipsis-vertical" size={20} color={colors.text} />
            {invitationsCount > 0 && (
              <View style={[styles.invitationIndicator, { backgroundColor: colors.error }]}>
                <Text style={[styles.invitationCount, { color: colors.white }]}>
                  {invitationsCount > 9 ? '9+' : invitationsCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Dropdown Menu */}
      {showMenu && (
        <View style={[styles.dropdownMenu, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowMenu(false);
              navigation.navigate('Invitations');
            }}
          >
            <Ionicons name="mail" size={18} color={colors.primary} />
            <Text style={[styles.menuItemText, { color: colors.text }]}>
              Chama Invitations
            </Text>
            {invitationsCount > 0 && (
              <View style={[styles.menuBadge, { backgroundColor: colors.error }]}>
                <Text style={[styles.menuBadgeText, { color: colors.white }]}>
                  {invitationsCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      )}
      
      <FlatList
        horizontal
        data={filters}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterChip,
              {
                backgroundColor: selectedFilter === item.id ? colors.primary : colors.backgroundSecondary,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedFilter(item.id)}
          >
            <Text style={[
              styles.filterText,
              { color: selectedFilter === item.id ? colors.white : colors.textSecondary }
            ]}>
              {item.name} ({item.count})
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}
      />
    </View>
  );

  const renderNotification = ({ item }) => {
    const isRead = item.isRead || item.is_read;
    const createdAt = item.createdAt || item.created_at;

    return (
      <Card style={[
        styles.notificationCard,
        {
          backgroundColor: isRead ? colors.surface : colors.primary + '10',
          borderColor: isRead ? colors.border : colors.primary + '30',
          borderWidth: isRead ? 1 : 2,
        }
      ]}>
        <TouchableOpacity
          style={styles.notificationContent}
          onPress={() => handleNotificationPress(item)}
          activeOpacity={0.7}
        >
          <View style={styles.notificationHeader}>
            <View style={[
              styles.notificationIcon,
              { backgroundColor: getNotificationColor(item.type, item.priority) + '20' }
            ]}>
              <Ionicons
                name={getNotificationIcon(item.type, item.priority)}
                size={24}
                color={getNotificationColor(item.type, item.priority)}
              />
            </View>

            <View style={styles.notificationInfo}>
              <View style={styles.titleRow}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>
                  {item.title}
                </Text>
                {!isRead && (
                  <View style={[styles.unreadDot, { backgroundColor: colors.primary }]} />
                )}
              </View>
              <Text style={[styles.notificationTime, { color: colors.textTertiary }]}>
                {getTimeAgo(createdAt)}
              </Text>
            </View>
          </View>

          <Text style={[styles.notificationMessage, { color: colors.text }]}>
            {item.message}
          </Text>

          {item.action_url && (
            <View style={styles.notificationAction}>
              <Text style={[styles.actionText, { color: colors.primary }]}>
                Tap to view details →
              </Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Action Buttons Row */}
        <View style={styles.actionButtonsRow}>
          {!isRead && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary + '15' }]}
              onPress={async () => {
                try {
                  console.log('📖 MARK READ: Starting mark as read for notification:', item.id);
                  const markResult = await markNotificationAsRead(item.id);
                  console.log('📖 MARK READ: Mark result:', markResult);

                  // Show success toast
                  Toast.show({
                    type: 'success',
                    text1: 'Marked as Read',
                    text2: 'Notification has been marked as read',
                    position: 'bottom',
                    visibilityTime: 1500,
                  });

                  // Force a re-render to update filter counts (optimistic update handles data refresh)
                  setFilterUpdateTrigger(prev => prev + 1);

                  console.log('✅ Notification marked as read:', item.id);
                } catch (error) {
                  console.error('❌ Failed to mark notification as read:', error);
                  Toast.show({
                    type: 'error',
                    text1: 'Update Failed',
                    text2: 'Could not mark notification as read. Please try again.',
                    position: 'bottom',
                    visibilityTime: 3000,
                  });
                }
              }}
            >
              <Ionicons name="checkmark" size={16} color={colors.primary} />
              <Text style={[styles.actionButtonText, { color: colors.primary }]}>
                Mark Read
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.error + '15' }]}
            onPress={async () => {
              console.log('🗑️ BUTTON PRESSED - Delete button pressed for notification:', item.id);
              console.log('🗑️ BUTTON PRESSED - Deleting instantly without confirmation');

              try {
                // Store notification state before deletion for count calculation
                const wasUnread = !(item.isRead || item.is_read);
                console.log('🗑️ DELETION: Starting deletion for notification:', item.id, 'wasUnread:', wasUnread);

                // Delete notification with optimistic update (handles cache invalidation automatically)
                const deleteResult = await deleteNotification(item.id);
                console.log('🗑️ DELETION: Delete result:', deleteResult);

                // Show success toast
                Toast.show({
                  type: 'success',
                  text1: 'Notification Deleted',
                  text2: 'The notification has been removed successfully',
                  position: 'bottom',
                  visibilityTime: 2000,
                });

                // Force a re-render to update filter counts (optimistic update handles data refresh)
                setFilterUpdateTrigger(prev => prev + 1);

                console.log('✅ Notification deleted successfully:', item.id, 'wasUnread:', wasUnread);
              } catch (error) {
                console.error('❌ Failed to delete notification:', error);
                Toast.show({
                  type: 'error',
                  text1: 'Delete Failed',
                  text2: 'Could not delete the notification. Please try again.',
                  position: 'bottom',
                  visibilityTime: 3000,
                });
              }
            }}
          >
            <Ionicons name="trash-outline" size={16} color={colors.error} />
            <Text style={[styles.actionButtonText, { color: colors.error }]}>
              Delete
            </Text>
          </TouchableOpacity>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name={selectedFilter === 'unread' ? 'checkmark-circle-outline' : 'notifications-outline'} 
        size={64} 
        color={colors.textTertiary} 
      />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {selectedFilter === 'unread' ? 'All caught up!' : 'No notifications'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        {selectedFilter === 'unread' 
          ? 'You have no unread notifications'
          : 'You\'ll see notifications here when they arrive'
        }
      </Text>
    </View>
  );

  const filteredNotifications = getFilteredNotifications();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={() => setShowMenu(false)}
        disabled={!showMenu}
      >
        {renderHeader()}

        <FlatList
          data={filteredNotifications}
          renderItem={renderNotification}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.notificationsList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          ListEmptyComponent={!loading && renderEmptyState()}
          showsVerticalScrollIndicator={false}
        />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...shadows.sm,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  headerTitle: {
    fontSize: screenWidth < 350 ? typography.fontSize.lg : typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  markAllButton: {
    paddingHorizontal: spacing.xs,
    paddingVertical: spacing.xs,
  },
  markAllText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
  },
  menuButton: {
    position: 'relative',
    padding: spacing.xs,
    borderRadius: borderRadius.md,
  },
  invitationIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  invitationCount: {
    fontSize: 10,
    fontWeight: typography.fontWeight.bold,
    lineHeight: 12,
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    right: spacing.md,
    minWidth: 200,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    ...shadows.md,
    zIndex: 1000,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  menuItemText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
  },
  menuBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  menuBadgeText: {
    fontSize: 10,
    fontWeight: typography.fontWeight.bold,
    lineHeight: 12,
  },

  filtersContainer: {
    paddingRight: spacing.md,
  },
  filterChip: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.lg,
    marginRight: spacing.xs,
    borderWidth: 1,
    minHeight: 32,
    justifyContent: 'center',
  },
  filterText: {
    fontSize: screenWidth < 350 ? typography.fontSize.xs : typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
  },
  notificationsList: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  notificationCard: {
    marginBottom: spacing.lg,
    overflow: 'hidden',
    minHeight: 120, // Ensure minimum height for readability
  },
  notificationContent: {
    padding: spacing.lg,
    paddingBottom: spacing.md, // Less bottom padding since we have action buttons
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    minHeight: 48, // Ensure minimum height
  },
  notificationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.lg,
    flexShrink: 0,
    marginTop: 2, // Slight adjustment for better alignment
  },
  notificationInfo: {
    flex: 1,
    minWidth: 0, // Allows text to wrap properly
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
    minHeight: 24, // Ensure minimum height for title
  },
  notificationTitle: {
    fontSize: screenWidth < 350 ? typography.fontSize.base : typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    flex: 1,
    marginRight: spacing.md,
    lineHeight: screenWidth < 350 ? 20 : 24,
    color: 'inherit', // Will inherit from parent
  },
  notificationTime: {
    fontSize: screenWidth < 350 ? typography.fontSize.xs : typography.fontSize.sm,
    marginTop: spacing.xs,
    fontWeight: typography.fontWeight.medium,
  },
  unreadDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    flexShrink: 0,
    marginTop: 2,
  },
  notificationMessage: {
    fontSize: screenWidth < 350 ? typography.fontSize.sm : typography.fontSize.base,
    lineHeight: screenWidth < 350 ? 20 : 22,
    marginBottom: spacing.md,
    fontWeight: typography.fontWeight.normal,
    color: 'inherit', // Will inherit from parent
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
    gap: spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    gap: spacing.xs,
  },
  actionButtonText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  notificationAction: {
    marginTop: spacing.md,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  actionText: {
    fontSize: screenWidth < 350 ? typography.fontSize.sm : typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
    paddingHorizontal: spacing.xl,
    minHeight: 300,
  },
  emptyTitle: {
    fontSize: screenWidth < 350 ? typography.fontSize.lg : typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginTop: spacing.lg,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: screenWidth < 350 ? typography.fontSize.sm : typography.fontSize.base,
    textAlign: 'center',
    lineHeight: typography.lineHeight.relaxed,
    maxWidth: 280,
  },
});

export default NotificationsScreen;
