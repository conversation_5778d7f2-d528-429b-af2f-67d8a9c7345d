import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

const PayBillsScreen = ({ navigation }) => {
  const { theme, user, wallets } = useApp();
  const colors = getThemeColors(theme);

  const [selectedBiller, setSelectedBiller] = useState(null);
  const [accountNumber, setAccountNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [selectedWallet, setSelectedWallet] = useState(null);

  const billers = [
    {
      id: 'kplc',
      name: 'Kenya Power (KPLC)',
      icon: 'flash',
      color: colors.warning,
      accountLabel: 'Account Number',
      accountPlaceholder: 'Enter your KPLC account number',
    },
    {
      id: 'nairobi_water',
      name: 'Nairobi Water',
      icon: 'water',
      color: colors.info,
      accountLabel: 'Account Number',
      accountPlaceholder: 'Enter your water account number',
    },
    {
      id: 'dstv',
      name: 'DSTV',
      icon: 'tv',
      color: colors.error,
      accountLabel: 'Smart Card Number',
      accountPlaceholder: 'Enter your DSTV smart card number',
    },
    {
      id: 'gotv',
      name: 'GOTV',
      icon: 'tv',
      color: colors.success,
      accountLabel: 'IUC Number',
      accountPlaceholder: 'Enter your GOTV IUC number',
    },
    {
      id: 'zuku',
      name: 'Zuku',
      icon: 'wifi',
      color: colors.primary,
      accountLabel: 'Account Number',
      accountPlaceholder: 'Enter your Zuku account number',
    },
    {
      id: 'startimes',
      name: 'StarTimes',
      icon: 'tv',
      color: colors.secondary,
      accountLabel: 'Smart Card Number',
      accountPlaceholder: 'Enter your StarTimes smart card number',
    },
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleBillerSelect = (biller) => {
    setSelectedBiller(biller);
    setAccountNumber('');
    setAmount('');
  };

  const handleAmountChange = (text) => {
    const numericText = text.replace(/\D/g, '');
    setAmount(numericText);
  };

  const handlePayBill = () => {
    if (!selectedBiller) {
      Alert.alert('Select Biller', 'Please select a biller to pay.');
      return;
    }
    
    if (!accountNumber.trim()) {
      Alert.alert('Account Number Required', `Please enter your ${selectedBiller.accountLabel.toLowerCase()}.`);
      return;
    }
    
    if (!amount || parseInt(amount) < 1) {
      Alert.alert('Invalid Amount', 'Please enter a valid amount to pay.');
      return;
    }

    Alert.alert(
      'Confirm Payment',
      `Pay ${formatCurrency(parseInt(amount))} to ${selectedBiller.name} for account ${accountNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Confirm', 
          onPress: () => {
            Alert.alert(
              'Coming Soon',
              'Bill payment feature will be available soon! We\'re working on integrating with various service providers.',
              [{ text: 'OK', onPress: () => navigation.goBack() }]
            );
          }
        },
      ]
    );
  };

  const renderBillerGrid = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Select Biller
      </Text>
      <View style={styles.billersGrid}>
        {billers.map((biller) => (
          <TouchableOpacity
            key={biller.id}
            style={[
              styles.billerCard,
              {
                backgroundColor: selectedBiller?.id === biller.id ? biller.color + '20' : colors.surface,
                borderColor: selectedBiller?.id === biller.id ? biller.color : colors.border,
              },
            ]}
            onPress={() => handleBillerSelect(biller)}
          >
            <View style={[styles.billerIcon, { backgroundColor: biller.color + '20' }]}>
              <Ionicons name={biller.icon} size={24} color={biller.color} />
            </View>
            <Text
              style={[
                styles.billerName,
                {
                  color: selectedBiller?.id === biller.id ? biller.color : colors.text,
                },
              ]}
            >
              {biller.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  const renderAccountInput = () => {
    if (!selectedBiller) return null;

    return (
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {selectedBiller.accountLabel}
        </Text>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: colors.backgroundSecondary,
              color: colors.text,
              borderColor: colors.border,
            },
          ]}
          placeholder={selectedBiller.accountPlaceholder}
          placeholderTextColor={colors.textTertiary}
          value={accountNumber}
          onChangeText={setAccountNumber}
          autoCapitalize="none"
        />
      </Card>
    );
  };

  const renderAmountInput = () => {
    if (!selectedBiller) return null;

    return (
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Amount to Pay
        </Text>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: colors.backgroundSecondary,
              color: colors.text,
              borderColor: colors.border,
            },
          ]}
          placeholder="Enter amount (KES)"
          placeholderTextColor={colors.textTertiary}
          value={amount}
          onChangeText={handleAmountChange}
          keyboardType="numeric"
        />
      </Card>
    );
  };

  const renderWalletSelector = () => {
    if (!selectedBiller) return null;

    return (
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Payment Method
        </Text>
        <View style={styles.walletContainer}>
          <Ionicons name="wallet" size={24} color={colors.primary} />
          <View style={styles.walletInfo}>
            <Text style={[styles.walletName, { color: colors.text }]}>
              Personal Wallet
            </Text>
            <Text style={[styles.walletBalance, { color: colors.textSecondary }]}>
              Balance: {formatCurrency(wallets.find(w => w.type === 'personal')?.balance || 0)}
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderSummary = () => {
    if (!selectedBiller || !accountNumber || !amount) return null;

    return (
      <Card style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Payment Summary
        </Text>
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            Biller:
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {selectedBiller.name}
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            {selectedBiller.accountLabel}:
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {accountNumber}
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
            Amount:
          </Text>
          <Text style={[styles.summaryValue, { color: colors.text, fontWeight: typography.fontWeight.bold }]}>
            {formatCurrency(parseInt(amount))}
          </Text>
        </View>
      </Card>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderBillerGrid()}
        {renderAccountInput()}
        {renderAmountInput()}
        {renderWalletSelector()}
        {renderSummary()}

        {selectedBiller && accountNumber && amount && (
          <Button
            title="Pay Bill"
            onPress={handlePayBill}
            style={styles.payButton}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing.xl,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  billersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  billerCard: {
    flex: 1,
    minWidth: '45%',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  billerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  billerName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
  },
  input: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
  },
  walletContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: borderRadius.md,
  },
  walletInfo: {
    marginLeft: spacing.md,
    flex: 1,
  },
  walletName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  walletBalance: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  payButton: {
    margin: spacing.md,
  },
});

export default PayBillsScreen;
