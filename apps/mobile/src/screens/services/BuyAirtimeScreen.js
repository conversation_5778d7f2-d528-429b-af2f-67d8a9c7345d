import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import { getThemeColors, spacing, typography, borderRadius, shadows } from '../../utils/theme';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

const BuyAirtimeScreen = ({ navigation }) => {
  const { theme, user, wallets } = useApp();
  const colors = getThemeColors(theme);

  const [phoneNumber, setPhoneNumber] = useState('');
  const [selectedAmount, setSelectedAmount] = useState(null);
  const [customAmount, setCustomAmount] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState(null);
  const [selectedWallet, setSelectedWallet] = useState(null);

  const networks = [
    { id: 'safaricom', name: 'Safaricom', color: '#00A651', prefix: ['070', '071', '072', '074', '075', '076', '077', '078', '079'] },
    { id: 'airtel', name: 'Airtel', color: '#FF0000', prefix: ['073', '078', '079'] },
    { id: 'telkom', name: 'Telkom', color: '#0066CC', prefix: ['077'] },
  ];

  const quickAmounts = [50, 100, 200, 500, 1000, 2000];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const detectNetwork = (number) => {
    const cleanNumber = number.replace(/\D/g, '');
    if (cleanNumber.length >= 3) {
      const prefix = cleanNumber.substring(0, 3);
      return networks.find(network => 
        network.prefix.some(p => prefix.startsWith(p))
      );
    }
    return null;
  };

  const handlePhoneNumberChange = (text) => {
    const cleanText = text.replace(/\D/g, '');
    let formattedText = cleanText;
    
    if (cleanText.length > 3 && cleanText.length <= 6) {
      formattedText = `${cleanText.substring(0, 3)} ${cleanText.substring(3)}`;
    } else if (cleanText.length > 6) {
      formattedText = `${cleanText.substring(0, 3)} ${cleanText.substring(3, 6)} ${cleanText.substring(6, 10)}`;
    }
    
    setPhoneNumber(formattedText);
    
    const detectedNetwork = detectNetwork(cleanText);
    if (detectedNetwork) {
      setSelectedNetwork(detectedNetwork);
    }
  };

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (text) => {
    const numericText = text.replace(/\D/g, '');
    setCustomAmount(numericText);
    setSelectedAmount(null);
  };

  const getSelectedAmount = () => {
    return selectedAmount || (customAmount ? parseInt(customAmount) : 0);
  };

  const handlePurchase = () => {
    const amount = getSelectedAmount();
    const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');
    
    if (!cleanPhoneNumber || cleanPhoneNumber.length !== 10) {
      Alert.alert('Invalid Phone Number', 'Please enter a valid 10-digit phone number.');
      return;
    }
    
    if (!amount || amount < 10) {
      Alert.alert('Invalid Amount', 'Please select or enter an amount of at least KES 10.');
      return;
    }
    
    if (!selectedNetwork) {
      Alert.alert('Network Not Detected', 'Unable to detect network. Please check the phone number.');
      return;
    }

    Alert.alert(
      'Confirm Purchase',
      `Buy ${formatCurrency(amount)} airtime for ${phoneNumber} (${selectedNetwork.name})?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Confirm', 
          onPress: () => {
            Alert.alert(
              'Coming Soon',
              'Airtime purchase feature will be available soon! We\'re working on integrating with mobile network operators.',
              [{ text: 'OK', onPress: () => navigation.goBack() }]
            );
          }
        },
      ]
    );
  };

  const renderNetworkIndicator = () => {
    if (!selectedNetwork) return null;
    
    return (
      <View style={[styles.networkIndicator, { backgroundColor: selectedNetwork.color + '20' }]}>
        <View style={[styles.networkDot, { backgroundColor: selectedNetwork.color }]} />
        <Text style={[styles.networkText, { color: selectedNetwork.color }]}>
          {selectedNetwork.name}
        </Text>
      </View>
    );
  };

  const renderQuickAmounts = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Quick Amounts
      </Text>
      <View style={styles.amountsGrid}>
        {quickAmounts.map((amount) => (
          <TouchableOpacity
            key={amount}
            style={[
              styles.amountButton,
              {
                backgroundColor: selectedAmount === amount ? colors.primary : colors.surface,
                borderColor: selectedAmount === amount ? colors.primary : colors.border,
              },
            ]}
            onPress={() => handleAmountSelect(amount)}
          >
            <Text
              style={[
                styles.amountText,
                {
                  color: selectedAmount === amount ? colors.white : colors.text,
                },
              ]}
            >
              {formatCurrency(amount)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  const renderWalletSelector = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Payment Method
      </Text>
      <View style={styles.walletContainer}>
        <Ionicons name="wallet" size={24} color={colors.primary} />
        <View style={styles.walletInfo}>
          <Text style={[styles.walletName, { color: colors.text }]}>
            Personal Wallet
          </Text>
          <Text style={[styles.walletBalance, { color: colors.textSecondary }]}>
            Balance: {formatCurrency(wallets.find(w => w.type === 'personal')?.balance || 0)}
          </Text>
        </View>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Phone Number
          </Text>
          <View style={styles.phoneInputContainer}>
            <TextInput
              style={[
                styles.phoneInput,
                {
                  backgroundColor: colors.backgroundSecondary,
                  color: colors.text,
                  borderColor: colors.border,
                },
              ]}
              placeholder="0712 345 678"
              placeholderTextColor={colors.textTertiary}
              value={phoneNumber}
              onChangeText={handlePhoneNumberChange}
              keyboardType="phone-pad"
              maxLength={13}
            />
            {renderNetworkIndicator()}
          </View>
        </Card>

        {renderQuickAmounts()}

        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Custom Amount
          </Text>
          <TextInput
            style={[
              styles.customAmountInput,
              {
                backgroundColor: colors.backgroundSecondary,
                color: colors.text,
                borderColor: colors.border,
              },
            ]}
            placeholder="Enter amount (KES)"
            placeholderTextColor={colors.textTertiary}
            value={customAmount}
            onChangeText={handleCustomAmountChange}
            keyboardType="numeric"
          />
        </Card>

        {renderWalletSelector()}

        <View style={styles.summaryContainer}>
          <Card style={styles.summaryCard}>
            <Text style={[styles.summaryTitle, { color: colors.text }]}>
              Purchase Summary
            </Text>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Phone Number:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {phoneNumber || 'Not entered'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Network:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {selectedNetwork?.name || 'Auto-detect'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Amount:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {getSelectedAmount() > 0 ? formatCurrency(getSelectedAmount()) : 'Not selected'}
              </Text>
            </View>
          </Card>
        </View>

        <Button
          title="Buy Airtime"
          onPress={handlePurchase}
          disabled={!phoneNumber || getSelectedAmount() === 0}
          style={styles.purchaseButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing.xl,
  },
  section: {
    margin: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  phoneInputContainer: {
    position: 'relative',
  },
  phoneInput: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
  },
  networkIndicator: {
    position: 'absolute',
    right: spacing.md,
    top: '50%',
    transform: [{ translateY: -12 }],
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  networkDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  networkText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  amountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  amountButton: {
    flex: 1,
    minWidth: '30%',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    alignItems: 'center',
  },
  amountText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  customAmountInput: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
  },
  walletContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: borderRadius.md,
  },
  walletInfo: {
    marginLeft: spacing.md,
    flex: 1,
  },
  walletName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  walletBalance: {
    fontSize: typography.fontSize.sm,
    marginTop: spacing.xs,
  },
  summaryContainer: {
    margin: spacing.md,
  },
  summaryCard: {
    padding: spacing.lg,
  },
  summaryTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  purchaseButton: {
    margin: spacing.md,
  },
});

export default BuyAirtimeScreen;
