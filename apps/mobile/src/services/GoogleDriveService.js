/**
 * Google Drive Backup Service
 * Handles OAuth authentication and backup/restore operations with Google Drive
 */

// Note: expo-auth-session might need to be installed first
// Run: npx expo install expo-auth-session expo-web-browser
// import * as AuthSession from 'expo-auth-session';
// import * as WebBrowser from 'expo-web-browser';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Linking } from 'react-native';
import ApiService from './api';

// Complete the auth session (commented out until expo-auth-session is properly installed)
// WebBrowser.maybeCompleteAuthSession();

class GoogleDriveService {
  constructor() {
    this.clientId = process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID || '700521271518-apj801tf38k25daiisnqt70f8m7j2o43.apps.googleusercontent.com';
    this.baseUrl = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8080';
  }

  /**
   * Debug authentication status
   */
  async debugAuthStatus() {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');
      const userRole = await AsyncStorage.getItem('userRole');

      console.log('🔍 Auth Debug Info:');
      console.log('  Token exists:', !!token);
      console.log('  Token preview:', token ? token.substring(0, 20) + '...' : 'none');
      console.log('  User data exists:', !!userData);
      console.log('  User role:', userRole);

      if (token) {
        // Test token with backend
        try {
          const response = await fetch(`${this.baseUrl}/api/v1/debug/auth-status-protected`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
          const result = await response.json();
          console.log('  Backend validation:', response.ok ? '✅ Valid' : '❌ Invalid');
          console.log('  Backend response:', result);

          return {
            hasToken: !!token,
            tokenValid: response.ok,
            backendResponse: result
          };
        } catch (error) {
          console.log('  Backend test failed:', error.message);
          return {
            hasToken: !!token,
            tokenValid: false,
            error: error.message
          };
        }
      }

      return {
        hasToken: false,
        tokenValid: false
      };
    } catch (error) {
      console.error('Debug auth status error:', error);
      return { error: error.message };
    }
  }

  /**
   * Get Google Drive OAuth URL for authentication
   */
  async getAuthURL() {
    try {
      // First check if we have a valid auth token
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('User not authenticated. Please login first.');
      }

      console.log('🔐 Making authenticated request for OAuth URL...');
      const response = await ApiService.makeRequest('/users/google-drive/auth-url');

      if (response.success) {
        console.log('✅ OAuth URL generated successfully');
      }

      return response;
    } catch (error) {
      console.error('Failed to get Google Drive auth URL:', error);

      // If authentication failed, provide helpful error message
      if (error.message.includes('not authenticated') || error.message.includes('Authorization header')) {
        return {
          success: false,
          error: 'Authentication required. Please logout and login again.',
          requiresLogin: true
        };
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Authenticate user with Google Drive
   * Opens browser for OAuth flow
   */
  async authenticate() {
    try {
      console.log('🔐 Starting Google Drive authentication...');

      // Get auth URL from backend (authenticated endpoint)
      const authResult = await this.getAuthURL();

      if (authResult.success && authResult.auth_url) {
        console.log('🔗 Auth URL generated successfully');

        // In a real mobile app, this would open WebBrowser
        // For now, provide the URL for manual completion
        return {
          success: true,
          requiresBrowserAuth: true,
          authUrl: authResult.auth_url,
          secureToken: authResult.secure_token,
          expiresIn: authResult.expires_in,
          message: 'Complete Google Drive authentication in browser',
          instructions: [
            '1. Click the "Open Browser" button below',
            '2. Sign in with your Google account',
            '3. Grant permissions to VaultKe',
            '4. Return to the app - connection will be automatic'
          ]
        };
      } else {
        // Handle authentication errors specifically
        if (authResult.requiresLogin) {
          return {
            success: false,
            error: authResult.error,
            requiresLogin: true,
            message: 'Please logout and login again to refresh your authentication.'
          };
        }

        return { success: false, error: authResult.error || 'Failed to get auth URL' };
      }

    } catch (error) {
      console.error('Google Drive authentication error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Open browser for OAuth authentication
   */
  async openAuthBrowser() {
    try {
      const authResult = await this.getAuthURL();
      if (authResult.success && authResult.auth_url) {
        // Use React Native Linking to open the URL
        const supported = await Linking.canOpenURL(authResult.auth_url);
        if (supported) {
          await Linking.openURL(authResult.auth_url);
          return { success: true, message: 'Browser opened for authentication' };
        } else {
          // Fallback for web
          if (typeof window !== 'undefined') {
            window.open(authResult.auth_url, '_blank');
            return { success: true, message: 'Browser opened for authentication' };
          }
          return { success: false, error: 'Cannot open authentication URL' };
        }
      } else {
        return { success: false, error: 'Failed to get auth URL' };
      }
    } catch (error) {
      console.error('Failed to open auth browser:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code) {
    try {
      // This would normally exchange the code for tokens
      // For now, we'll simulate storing tokens
      const tokens = {
        access_token: 'simulated_access_token',
        refresh_token: 'simulated_refresh_token',
        expires_in: 3600,
        token_type: 'Bearer'
      };

      // Store tokens on backend
      const response = await ApiService.makeRequest('/users/google-drive/store-tokens', {
        method: 'POST',
        body: tokens,
      });

      return response;
    } catch (error) {
      console.error('Token exchange error:', error);
      return { success: false, error: error.message };
    }
  }

  // OAuth token exchange methods removed for now
  // Will be implemented once expo-auth-session is properly configured

  /**
   * Disconnect Google Drive (revoke tokens)
   */
  async disconnect() {
    try {
      const response = await ApiService.makeRequest('/users/google-drive/disconnect', {
        method: 'POST',
      });

      return response;
    } catch (error) {
      console.error('Failed to disconnect Google Drive:', error);
      throw error;
    }
  }

  /**
   * Create backup of user data
   */
  async createBackup() {
    try {
      console.log('📦 Creating Google Drive backup...');

      const response = await ApiService.makeRequest('/users/google-drive/backup', {
        method: 'POST',
      });

      if (response.success) {
        console.log('✅ Backup created successfully');
        
        // Store last backup date locally
        await AsyncStorage.setItem('last_backup_date', new Date().toISOString());
      }

      return response;
    } catch (error) {
      console.error('Backup creation error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Restore data from Google Drive backup
   */
  async restoreBackup() {
    try {
      console.log('📥 Restoring from Google Drive backup...');

      const response = await ApiService.makeRequest('/users/google-drive/restore', {
        method: 'POST',
      });

      if (response.success) {
        console.log('✅ Restore completed successfully');
      }

      return response;
    } catch (error) {
      console.error('Restore error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get backup information
   */
  async getBackupInfo() {
    try {
      const response = await ApiService.makeRequest('/users/google-drive/backup-info');
      
      if (response.success) {
        return response;
      }

      // Fallback to local storage
      const localBackupDate = await AsyncStorage.getItem('last_backup_date');
      return {
        success: true,
        lastBackup: localBackupDate
      };
    } catch (error) {
      console.error('Failed to get backup info:', error);
      
      // Fallback to local storage
      try {
        const localBackupDate = await AsyncStorage.getItem('last_backup_date');
        return {
          success: true,
          lastBackup: localBackupDate
        };
      } catch (localError) {
        return { success: false, error: error.message };
      }
    }
  }

  /**
   * Check if Google Drive is connected
   */
  async isConnected() {
    try {
      const response = await ApiService.makeRequest('/users/google-drive/status');
      return response.success && response.connected;
    } catch (error) {
      console.error('Failed to check Google Drive status:', error);
      return false;
    }
  }
}

export default new GoogleDriveService();
