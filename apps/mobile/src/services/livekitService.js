import { Platform, PermissionsAndroid } from 'react-native';
import ApiService from './api';

// Conditionally import LiveKit based on platform
let Room, RoomEvent, Track, RemoteTrack, RemoteParticipant, VideoPresets, registerGlobals;

console.log('🔍 Platform check:', Platform.OS);
console.log('🔍 Is mobile platform:', Platform.OS === 'ios' || Platform.OS === 'android');
console.log('🔍 Available platforms:', Object.keys(Platform));

if (Platform.OS === 'web') {
  // Use LiveKit Web SDK for web platform
  try {
    console.log('🔍 Loading LiveKit Web SDK...');
    const LiveKitWeb = require('livekit-client');

    Room = LiveKitWeb.Room;
    RoomEvent = LiveKitWeb.RoomEvent;
    Track = LiveKitWeb.Track;
    RemoteTrack = LiveKitWeb.RemoteTrack;
    RemoteParticipant = LiveKitWeb.RemoteParticipant;
    VideoPresets = LiveKitWeb.VideoPresets;

    console.log('🔍 Web Room class:', !!Room);
    console.log('🔍 Web RoomEvent:', !!RoomEvent);
    console.log('✅ LiveKit Web SDK loaded successfully');
  } catch (error) {
    console.log('⚠️ LiveKit Web SDK not available:', error.message);
    console.log('⚠️ Error details:', error);
  }
} else if (Platform.OS === 'ios' || Platform.OS === 'android') {
  // Use LiveKit React Native SDK for mobile platforms
  try {
    console.log('🔍 Loading LiveKit React Native SDK...');
    const LiveKit = require('@livekit/react-native');
    const WebRTC = require('@livekit/react-native-webrtc');

    Room = LiveKit.Room;
    RoomEvent = LiveKit.RoomEvent;
    Track = LiveKit.Track;
    RemoteTrack = LiveKit.RemoteTrack;
    RemoteParticipant = LiveKit.RemoteParticipant;
    VideoPresets = LiveKit.VideoPresets;
    registerGlobals = WebRTC.registerGlobals;

    // Register WebRTC globals for LiveKit
    registerGlobals();
    console.log('✅ LiveKit React Native SDK loaded successfully');
  } catch (error) {
    console.log('⚠️ LiveKit React Native SDK not available:', error.message);
    console.log('⚠️ Error details:', error);
  }
} else {
  console.log('🔍 Unknown platform, using fallback');
}

class LiveKitService {
  constructor() {
    this.room = null;
    this.isConnected = false;
    this.participants = new Map();
    this.eventListeners = new Map();
    this.isRealLiveKit = !!(Room && RoomEvent); // Check if real LiveKit is available
    this.localVideoTrack = null;
    this.localAudioTrack = null;

    console.log(`🎬 LiveKit Service initialized - Real SDK: ${this.isRealLiveKit}`);
  }

  // Initialize and connect to a LiveKit room
  async connectToRoom(connectionData) {
    try {
      console.log('🎬 LiveKit: Connecting to room with data:', connectionData);
      console.log('🔍 isRealLiveKit:', this.isRealLiveKit);
      console.log('🔍 hasToken:', !!connectionData.token);
      console.log('🔍 hasWsUrl:', !!connectionData.wsUrl);
      console.log('🔍 forceReal:', !!connectionData.forceReal);
      console.log('🔍 Room class available:', !!Room);

      // Allow forcing real LiveKit mode (for previews)
      if (connectionData.forceReal) {
        this.isRealLiveKit = true;
        console.log('🎬 LiveKit: Forced to use real LiveKit mode');
      }

      if (this.isRealLiveKit && connectionData.token && connectionData.wsUrl) {
        console.log('✅ Using real LiveKit SDK');
        // Use real LiveKit SDK
        return await this.connectToRealLiveKit(connectionData);
      } else {
        console.log('⚠️ Falling back to mock implementation');
        console.log('⚠️ Reasons: isRealLiveKit:', this.isRealLiveKit, 'hasToken:', !!connectionData.token, 'hasWsUrl:', !!connectionData.wsUrl);
        // Fallback to mock implementation
        return await this.connectToMockRoom(connectionData);
      }
    } catch (error) {
      console.error('🎬 LiveKit: Failed to connect to room:', error);
      throw error;
    }
  }

  // Real LiveKit connection
  async connectToRealLiveKit(connectionData) {
    console.log('🎬 Real LiveKit: Connecting with token and WebSocket URL');
    console.log('🎬 Real LiveKit: wsUrl:', connectionData.wsUrl);
    console.log('🎬 Real LiveKit: token length:', connectionData.token?.length);
    console.log('🎬 Real LiveKit: token preview:', connectionData.token?.substring(0, 50) + '...');

    try {
      // Validate connection data
      if (!connectionData.token) {
        throw new Error('No access token provided');
      }
      if (!connectionData.wsUrl) {
        throw new Error('No WebSocket URL provided');
      }
      if (!connectionData.token.includes('.')) {
        throw new Error('Invalid token format - not a JWT');
      }

      // Create room with video presets fallback
      const roomOptions = {
        adaptiveStream: true,
        dynacast: true,
      };

      // Add video capture defaults if VideoPresets is available
      if (VideoPresets && VideoPresets.h720) {
        roomOptions.videoCaptureDefaults = {
          resolution: VideoPresets.h720.resolution,
        };
      } else {
        // Fallback resolution if VideoPresets is not available
        roomOptions.videoCaptureDefaults = {
          resolution: {
            width: 1280,
            height: 720,
          },
        };
      }

      this.room = new Room(roomOptions);
      console.log('🎬 Real LiveKit: Room instance created with options');

      // Set up event listeners for real LiveKit
      this.setupRealLiveKitEventListeners();

      // Add connection timeout
      console.log('🎬 Real LiveKit: Attempting to connect...');
      const connectionTimeout = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Connection timeout after 30 seconds')), 30000);
      });

      const connectionPromise = this.room.connect(connectionData.wsUrl, connectionData.token);
      await Promise.race([connectionPromise, connectionTimeout]);

      this.isConnected = true;
      console.log('🎬 Real LiveKit: Connected to room:', connectionData.roomName);

      // Enable camera and microphone by default
      console.log('🎬 Real LiveKit: Enabling camera and microphone...');
      await this.enableCameraAndMicrophone();

      return this.room;
    } catch (error) {
      console.error('🎬 Real LiveKit: Connection failed:', error);
      console.error('🎬 Real LiveKit: Error details:', error.message);
      console.error('🎬 Real LiveKit: Error stack:', error.stack);

      // Provide specific error messages based on error type
      let errorMessage = error.message;
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        errorMessage = 'Authentication failed - Invalid LiveKit credentials. Please check your API key and secret.';
      } else if (error.message.includes('CONNECTION_REFUSED') || error.message.includes('could not establish signal connection')) {
        errorMessage = 'Connection refused - LiveKit server is unavailable. Using local test mode.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - Please check your internet connection.';
      } else if (error.message.includes('invalid API key')) {
        errorMessage = 'Invalid API key - Please check your LiveKit configuration.';
      }

      console.log('🎬 Error analysis:', errorMessage);

      // For testing: create a local-only room with real camera access
      if (error.message.includes('invalid API key') ||
          error.message.includes('could not establish signal connection') ||
          error.message.includes('401') ||
          error.message.includes('CONNECTION_REFUSED')) {
        console.log('🎬 Creating local test room with real camera access...');
        console.log('🎬 This allows you to test camera/microphone without LiveKit server');
        return await this.createLocalTestRoom(connectionData);
      }

      // Re-throw with more descriptive error
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      throw enhancedError;
    }
  }

  // Mock LiveKit connection (fallback)
  async connectToMockRoom(connectionData) {
    console.log('🎬 Mock LiveKit: Using fallback implementation');

    // Request permissions for mock room too
    const permissions = await this.requestPermissions();
    console.log('🎬 Mock LiveKit: Permissions:', permissions);

    // Simulate connection delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock room instance with proper methods
    this.room = {
      name: connectionData.roomName || 'mock-room',
      localParticipant: {
        identity: 'local-user',
        name: 'You',
        isCameraEnabled: false,
        isMicrophoneEnabled: false,
        setCameraEnabled: async (enabled) => {
          console.log('🎬 Mock LiveKit: Setting camera to:', enabled);
          if (enabled && !permissions.camera) {
            console.warn('🎬 Mock LiveKit: Camera permission not granted');
            return false;
          }
          this.room.localParticipant.isCameraEnabled = enabled;
          return enabled;
        },
        setMicrophoneEnabled: async (enabled) => {
          console.log('🎬 Mock LiveKit: Setting microphone to:', enabled);
          if (enabled && !permissions.audio) {
            console.warn('🎬 Mock LiveKit: Microphone permission not granted');
            return false;
          }
          this.room.localParticipant.isMicrophoneEnabled = enabled;
          return enabled;
        },
        getTrackByKind: (kind) => {
          if (kind === 'video' && this.room.localParticipant.isCameraEnabled) {
            return { kind: 'video', enabled: true, mock: true };
          }
          if (kind === 'audio' && this.room.localParticipant.isMicrophoneEnabled) {
            return { kind: 'audio', enabled: true, mock: true };
          }
          return null;
        },
        switchCamera: async () => {
          console.log('🎬 Mock LiveKit: Camera switched (simulated)');
        }
      },
      remoteParticipants: new Map(),
      disconnect: async () => {
        console.log('🎬 Mock LiveKit: Room disconnected');
        this.isConnected = false;
      },
    };

    this.isConnected = true;
    console.log('🎬 Mock LiveKit: Connected to room:', this.room.name);

    // Enable camera and microphone if permissions are available
    if (permissions.camera) {
      await this.room.localParticipant.setCameraEnabled(true);
    }
    if (permissions.audio) {
      await this.room.localParticipant.setMicrophoneEnabled(true);
    }

    // Simulate some participants joining
    setTimeout(() => {
      const mockParticipant = {
        identity: 'user-1',
        name: 'John Doe',
        isCameraEnabled: true,
        isMicrophoneEnabled: false,
        getTrackByKind: () => null,
      };

      this.room.remoteParticipants.set('user-1', mockParticipant);
      this.emit('participantConnected', mockParticipant);
    }, 2000);

    return this.room;
  }

  // Create a local test room with mock media (for testing when LiveKit server is unavailable)
  async createLocalTestRoom(connectionData) {
    console.log('🎬 Creating local test room with mock media...');

    try {
      // Request permissions first for React Native
      const permissions = await this.requestPermissions();
      console.log('🎬 Local Test: Permissions granted:', permissions);

      // Create a mock room object that simulates real media
      this.room = {
        name: connectionData.roomName,
        localParticipant: {
          identity: 'local-user',
          name: 'You (Local Test)',
          isCameraEnabled: false,
          isMicrophoneEnabled: false,
          setCameraEnabled: async (enabled) => {
            console.log('🎬 Local Test: Setting camera to:', enabled);

            if (enabled && permissions.camera) {
              // Simulate camera access for React Native
              console.log('🎬 Local Test: Camera access simulated (permissions granted)');
              this.room.localParticipant.isCameraEnabled = true;
              return true;
            } else if (enabled && !permissions.camera) {
              console.warn('🎬 Local Test: Camera access denied - no permissions');
              this.room.localParticipant.isCameraEnabled = false;
              return false;
            } else {
              // Disable camera
              this.room.localParticipant.isCameraEnabled = false;
              return false;
            }
          },
          setMicrophoneEnabled: async (enabled) => {
            console.log('🎬 Local Test: Setting microphone to:', enabled);

            if (enabled && permissions.audio) {
              // Simulate microphone access for React Native
              console.log('🎬 Local Test: Microphone access simulated (permissions granted)');
              this.room.localParticipant.isMicrophoneEnabled = true;
              return true;
            } else if (enabled && !permissions.audio) {
              console.warn('🎬 Local Test: Microphone access denied - no permissions');
              this.room.localParticipant.isMicrophoneEnabled = false;
              return false;
            } else {
              // Disable microphone
              this.room.localParticipant.isMicrophoneEnabled = false;
              return false;
            }
          },
          getTrackByKind: (kind) => {
            // Return mock track objects for React Native
            if (kind === 'video' && this.room.localParticipant.isCameraEnabled) {
              return { kind: 'video', enabled: true, mock: true };
            }
            if (kind === 'audio' && this.room.localParticipant.isMicrophoneEnabled) {
              return { kind: 'audio', enabled: true, mock: true };
            }
            return null;
          },
          switchCamera: async () => {
            console.log('🎬 Local Test: Camera switched (simulated)');
          }
        },
        remoteParticipants: new Map(),
        disconnect: async () => {
          console.log('🎬 Local Test: Disconnecting...');
          this.isConnected = false;
        }
      };

      this.isConnected = true;
      console.log('🎬 Local Test: Test room created with mock media support');

      // Enable camera and microphone for testing if permissions are available
      console.log('🎬 Local Test: Enabling camera and microphone...');
      if (permissions.camera) {
        const cameraEnabled = await this.room.localParticipant.setCameraEnabled(true);
        console.log('🎬 Local Test: Camera enabled:', cameraEnabled);
      }

      if (permissions.audio) {
        const micEnabled = await this.room.localParticipant.setMicrophoneEnabled(true);
        console.log('🎬 Local Test: Microphone enabled:', micEnabled);
      }

      // Simulate a realistic meeting environment
      console.log('🎬 Local Test: Creating realistic meeting environment...');

      // Add a notification that this is a test environment
      setTimeout(() => {
        this.emit('connectionStateChanged', 'connected');
        console.log('🎬 Local Test: Test environment ready - mock camera/microphone active');
      }, 1000);

      return this.room;
    } catch (error) {
      console.error('🎬 Local Test: Failed to create test room:', error);
      throw error;
    }
  }

  // Set up event listeners for real LiveKit
  setupRealLiveKitEventListeners() {
    if (!this.room || !this.isRealLiveKit) return;

    console.log('🎬 Real LiveKit: Setting up event listeners');

    this.room.on(RoomEvent.ParticipantConnected, (participant) => {
      console.log('🎬 Real LiveKit: Participant connected:', participant.identity);
      this.emit('participantConnected', participant);
    });

    this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      console.log('🎬 Real LiveKit: Participant disconnected:', participant.identity);
      this.emit('participantDisconnected', participant);
    });

    this.room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
      console.log('🎬 Real LiveKit: Track subscribed:', track.kind, participant.identity);
      this.emit('trackSubscribed', { track, publication, participant });
    });

    this.room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
      console.log('🎬 Real LiveKit: Track unsubscribed:', track.kind, participant.identity);
      this.emit('trackUnsubscribed', { track, publication, participant });
    });

    this.room.on(RoomEvent.ConnectionStateChanged, (state) => {
      console.log('🎬 Real LiveKit: Connection state changed:', state);
      this.emit('connectionStateChanged', state);
    });

    this.room.on(RoomEvent.Disconnected, (reason) => {
      console.log('🎬 Real LiveKit: Disconnected:', reason);
      this.isConnected = false;
      this.emit('disconnected', reason);
    });
  }

  // Request permissions for camera and microphone
  async requestPermissions() {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        const cameraGranted = granted[PermissionsAndroid.PERMISSIONS.CAMERA] === PermissionsAndroid.RESULTS.GRANTED;
        const audioGranted = granted[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] === PermissionsAndroid.RESULTS.GRANTED;

        console.log('🎬 Permissions - Camera:', cameraGranted, 'Audio:', audioGranted);
        return { camera: cameraGranted, audio: audioGranted };
      } catch (error) {
        console.error('🎬 Failed to request permissions:', error);
        return { camera: false, audio: false };
      }
    }
    // iOS permissions are handled automatically by the system
    return { camera: true, audio: true };
  }

  // Enable camera and microphone for real LiveKit
  async enableCameraAndMicrophone() {
    if (!this.room) return;

    try {
      console.log('🎬 LiveKit: Enabling camera and microphone...');

      if (this.isRealLiveKit) {
        if (Platform.OS === 'web') {
          // Web SDK approach
          console.log('🎬 Web LiveKit: Enabling camera and microphone...');
          await this.room.localParticipant.enableCameraAndMicrophone();
        } else {
          // Mobile SDK approach - request permissions first
          console.log('🎬 Mobile LiveKit: Requesting permissions...');
          const permissions = await this.requestPermissions();

          if (!permissions.camera || !permissions.audio) {
            console.warn('🎬 Mobile LiveKit: Some permissions not granted, continuing with available permissions');
          }

          // Enable camera and microphone separately to handle partial permissions
          try {
            if (permissions.camera) {
              await this.room.localParticipant.setCameraEnabled(true);
              console.log('🎬 Mobile LiveKit: Camera enabled');
            }
          } catch (error) {
            console.warn('🎬 Mobile LiveKit: Failed to enable camera:', error.message);
          }

          try {
            if (permissions.audio) {
              await this.room.localParticipant.setMicrophoneEnabled(true);
              console.log('🎬 Mobile LiveKit: Microphone enabled');
            }
          } catch (error) {
            console.warn('🎬 Mobile LiveKit: Failed to enable microphone:', error.message);
          }
        }
      } else {
        // Mock/Local test implementation
        console.log('🎬 Mock LiveKit: Enabling camera and microphone...');
        if (this.room.localParticipant.setCameraEnabled) {
          await this.room.localParticipant.setCameraEnabled(true);
        } else {
          this.room.localParticipant.isCameraEnabled = true;
        }

        if (this.room.localParticipant.setMicrophoneEnabled) {
          await this.room.localParticipant.setMicrophoneEnabled(true);
        } else {
          this.room.localParticipant.isMicrophoneEnabled = true;
        }
      }

      console.log('🎬 LiveKit: Camera and microphone initialization completed');
    } catch (error) {
      console.error('🎬 LiveKit: Failed to enable camera/microphone:', error);
      console.error('🎬 LiveKit: Error details:', error.message);
    }
  }

  // Enable/disable camera
  async toggleCamera() {
    if (!this.room) return false;

    try {
      const currentState = this.room.localParticipant.isCameraEnabled;
      const newState = !currentState;

      if (this.isRealLiveKit) {
        // Real LiveKit implementation
        if (Platform.OS === 'web') {
          // Web SDK approach
          await this.room.localParticipant.setCameraEnabled(newState);
        } else {
          // Mobile SDK approach
          await this.room.localParticipant.setCameraEnabled(newState);
        }
        const actualState = this.room.localParticipant.isCameraEnabled;
        console.log('🎬 Real LiveKit: Camera toggled to:', actualState);
        return actualState;
      } else {
        // Mock implementation with proper method call
        if (this.room.localParticipant.setCameraEnabled) {
          const result = await this.room.localParticipant.setCameraEnabled(newState);
          console.log('🎬 Mock LiveKit: Camera toggled to:', result);
          return result;
        } else {
          // Fallback for basic mock
          this.room.localParticipant.isCameraEnabled = newState;
          console.log('🎬 Mock LiveKit: Camera toggled to:', newState);
          return newState;
        }
      }
    } catch (error) {
      console.error('🎬 LiveKit: Failed to toggle camera:', error);
      throw error;
    }
  }

  // Enable/disable microphone
  async toggleMicrophone() {
    if (!this.room) return false;

    try {
      const currentState = this.room.localParticipant.isMicrophoneEnabled;
      const newState = !currentState;

      if (this.isRealLiveKit) {
        // Real LiveKit implementation
        if (Platform.OS === 'web') {
          // Web SDK approach
          await this.room.localParticipant.setMicrophoneEnabled(newState);
        } else {
          // Mobile SDK approach
          await this.room.localParticipant.setMicrophoneEnabled(newState);
        }
        const actualState = this.room.localParticipant.isMicrophoneEnabled;
        console.log('🎬 Real LiveKit: Microphone toggled to:', actualState);
        return actualState;
      } else {
        // Mock implementation with proper method call
        if (this.room.localParticipant.setMicrophoneEnabled) {
          const result = await this.room.localParticipant.setMicrophoneEnabled(newState);
          console.log('🎬 Mock LiveKit: Microphone toggled to:', result);
          return result;
        } else {
          // Fallback for basic mock
          this.room.localParticipant.isMicrophoneEnabled = newState;
          console.log('🎬 Mock LiveKit: Microphone toggled to:', newState);
          return newState;
        }
      }
    } catch (error) {
      console.error('🎬 LiveKit: Failed to toggle microphone:', error);
      throw error;
    }
  }

  // Switch camera (front/back)
  async switchCamera() {
    if (!this.room) return;

    try {
      if (this.isRealLiveKit) {
        // Real LiveKit implementation
        if (Platform.OS === 'web') {
          // Web doesn't support camera switching in the same way
          console.log('🎬 Web LiveKit: Camera switching not supported on web');
        } else {
          await this.room.localParticipant.switchCamera();
          console.log('🎬 Real LiveKit: Camera switched');
        }
      } else {
        // Mock implementation with proper method call
        if (this.room.localParticipant.switchCamera) {
          await this.room.localParticipant.switchCamera();
        }
        console.log('🎬 Mock LiveKit: Camera switched (simulated)');
      }
    } catch (error) {
      console.error('🎬 LiveKit: Failed to switch camera:', error);
      throw error;
    }
  }

  // Send data to other participants
  async sendData(data, reliable = true) {
    if (!this.room) return;

    try {
      const encoder = new TextEncoder();
      const payload = encoder.encode(JSON.stringify(data));
      await this.room.localParticipant.publishData(payload, reliable);
    } catch (error) {
      console.error('Failed to send data:', error);
      throw error;
    }
  }

  // Get all participants
  getParticipants() {
    if (!this.room) return [];

    const participants = [];

    if (this.isRealLiveKit) {
      // Real LiveKit implementation

      // Add local participant
      if (this.room.localParticipant) {
        participants.push({
          identity: this.room.localParticipant.identity,
          name: this.room.localParticipant.name || this.room.localParticipant.identity,
          isLocal: true,
          isCameraEnabled: this.room.localParticipant.isCameraEnabled,
          isMicrophoneEnabled: this.room.localParticipant.isMicrophoneEnabled,
          participant: this.room.localParticipant
        });
      }

      // Add remote participants
      this.room.remoteParticipants.forEach((participant) => {
        participants.push({
          identity: participant.identity,
          name: participant.name || participant.identity,
          isLocal: false,
          isCameraEnabled: participant.isCameraEnabled,
          isMicrophoneEnabled: participant.isMicrophoneEnabled,
          participant: participant
        });
      });
    } else {
      // Mock implementation

      // Add local participant
      if (this.room.localParticipant) {
        participants.push({
          identity: this.room.localParticipant.identity,
          name: this.room.localParticipant.name || this.room.localParticipant.identity,
          isLocal: true,
          isCameraEnabled: this.room.localParticipant.isCameraEnabled,
          isMicrophoneEnabled: this.room.localParticipant.isMicrophoneEnabled,
          participant: {
            ...this.room.localParticipant,
            getTrackByKind: () => null,
          }
        });
      }

      // Add remote participants
      this.room.remoteParticipants.forEach((participant) => {
        participants.push({
          identity: participant.identity,
          name: participant.name || participant.identity,
          isLocal: false,
          isCameraEnabled: participant.isCameraEnabled,
          isMicrophoneEnabled: participant.isMicrophoneEnabled,
          participant: {
            ...participant,
            getTrackByKind: () => null,
          }
        });
      });
    }

    return participants;
  }

  // Get video track for a participant
  getVideoTrack(participantIdentity) {
    if (!this.room) return null;

    if (participantIdentity === this.room.localParticipant?.identity) {
      return this.room.localParticipant.getTrackByKind(Track.Kind.Video);
    }

    const participant = this.room.remoteParticipants.get(participantIdentity);
    return participant?.getTrackByKind(Track.Kind.Video);
  }

  // Get audio track for a participant
  getAudioTrack(participantIdentity) {
    if (!this.room) return null;

    if (participantIdentity === this.room.localParticipant?.identity) {
      return this.room.localParticipant.getTrackByKind(Track.Kind.Audio);
    }

    const participant = this.room.remoteParticipants.get(participantIdentity);
    return participant?.getTrackByKind(Track.Kind.Audio);
  }

  // Disconnect from room
  async disconnect() {
    if (this.room && this.isConnected) {
      try {
        if (this.isRealLiveKit) {
          // Real LiveKit implementation
          await this.room.disconnect();
          console.log('🎬 Real LiveKit: Disconnected from room');
        } else {
          // Mock implementation
          if (this.room.disconnect) {
            await this.room.disconnect();
          } else {
            console.log('🎬 Mock LiveKit: Manual disconnect');
          }
          console.log('🎬 Mock LiveKit: Disconnected from room');
        }

        this.isConnected = false;
        this.participants.clear();
        this.room = null;
        this.localVideoTrack = null;
        this.localAudioTrack = null;
      } catch (error) {
        console.error('🎬 LiveKit: Failed to disconnect:', error);
        // Force cleanup even if disconnect fails
        this.isConnected = false;
        this.room = null;
        this.participants.clear();
        this.localVideoTrack = null;
        this.localAudioTrack = null;
      }
    }
  }

  // Event listener management
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  // Get room state
  getRoomState() {
    if (!this.room) return null;

    return {
      isConnected: this.isConnected,
      roomName: this.room.name,
      participantCount: this.room.remoteParticipants.size + 1, // +1 for local participant
      localParticipant: this.room.localParticipant,
      remoteParticipants: Array.from(this.room.remoteParticipants.values())
    };
  }
}

// Export singleton instance
export default new LiveKitService();
