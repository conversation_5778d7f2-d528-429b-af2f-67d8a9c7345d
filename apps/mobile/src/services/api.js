import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import mockSettingsService from './mockSettingsService';
import { TUNNEL_CONFIG } from '../../tunnel-config.js';

// Simple API Configuration - automatically detects environment
const getApiBaseUrl = () => {
  // 1. Environment variable override (highest priority)
  if (process.env.REACT_APP_API_URL) {
    console.log('🌐 Using environment variable API URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }

  // 2. Auto-detect based on current URL
  if (typeof window !== 'undefined' && window.location) {
    const hostname = window.location.hostname;

    // Development: localhost/127.0.0.1
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      console.log('🏠 Development mode: Using localhost backend');
      return 'http://localhost:8080/api/v1';
    }

    // Tunnel development
    if (hostname.includes('tunnelmole.net')) {
      console.log('🚇 Tunnel mode: Using tunnel backend');
      return `${TUNNEL_CONFIG.BACKEND_TUNNEL_URL}/api/v1`;
    }
  }

  // 3. Production fallback
  console.log('🚀 Production mode: Using production backend');
  return 'https://api.vaultke.com/api/v1'; // Replace with your production URL
};


const API_BASE_URL = getApiBaseUrl();

// Configuration
const CONFIG = {
  REQUEST_TIMEOUT: 10000, // 10 seconds
  RETRY_ATTEMPTS: 3,
};

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;

    // Log the API URL being used (development only)
    if (__DEV__) {
      console.log('🌐 VaultKe API URL:', this.baseURL);
    }
  }

  // Get auth token from storage
  async getAuthToken() {
    try {
      return await AsyncStorage.getItem('authToken');
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Set auth token in storage
  async setAuthToken(token) {
    try {
      await AsyncStorage.setItem('authToken', token);
    } catch (error) {
      console.error('Error setting auth token:', error);
    }
  }

  // Remove auth token from storage
  async removeAuthToken() {
    try {
      await AsyncStorage.removeItem('authToken');
    } catch (error) {
      console.error('Error removing auth token:', error);
    }
  }

  // Enhanced device info for better login session tracking
  getDeviceInfo() {
    // React Native / Mobile App Environment
    if (typeof navigator === 'undefined') {
      return {
        userAgent: 'VaultKe-Mobile-App/1.0',
        platform: 'mobile',
        language: 'en',
        timezone: 'UTC',
        deviceType: 'mobile',
        deviceName: 'Mobile Device - VaultKe App',
        browserName: 'VaultKe App',
        osName: 'Mobile OS',
      };
    }

    const userAgent = navigator.userAgent || 'VaultKe-App/1.0';

    let deviceInfo = {
      userAgent: userAgent,
      language: navigator.language || 'en',
      timezone: 'UTC',
      deviceType: 'unknown',
      deviceName: 'Unknown Device',
      browserName: 'Unknown Browser',
      osName: 'Unknown OS',
    };

    // Enhanced device type detection
    const ua = userAgent.toLowerCase();

    // Mobile detection
    if (/mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua)) {
      deviceInfo.deviceType = 'mobile';

      if (/iphone/i.test(ua)) {
        deviceInfo.deviceName = 'iPhone';
        deviceInfo.osName = 'iOS';
      } else if (/ipad/i.test(ua)) {
        deviceInfo.deviceName = 'iPad';
        deviceInfo.osName = 'iPadOS';
      } else if (/android/i.test(ua)) {
        deviceInfo.deviceName = 'Android Device';
        deviceInfo.osName = 'Android';

        // Try to detect specific Android devices
        if (/samsung/i.test(ua)) {
          deviceInfo.deviceName = 'Samsung Phone';
        } else if (/pixel/i.test(ua)) {
          deviceInfo.deviceName = 'Google Pixel';
        } else if (/huawei/i.test(ua)) {
          deviceInfo.deviceName = 'Huawei Phone';
        }
      }
    } else {
      // Desktop detection
      deviceInfo.deviceType = 'desktop';

      if (/windows/i.test(ua)) {
        deviceInfo.deviceName = 'Windows PC';
        deviceInfo.osName = 'Windows';
      } else if (/macintosh|mac os x/i.test(ua)) {
        deviceInfo.deviceName = 'Mac';
        deviceInfo.osName = 'macOS';
      } else if (/linux/i.test(ua)) {
        deviceInfo.deviceName = 'Linux PC';
        deviceInfo.osName = 'Linux';
      }
    }

    // Browser detection
    if (/edg\//i.test(ua)) {
      deviceInfo.browserName = 'Microsoft Edge';
    } else if (/chrome/i.test(ua) && !/edg/i.test(ua)) {
      deviceInfo.browserName = 'Google Chrome';
    } else if (/firefox/i.test(ua)) {
      deviceInfo.browserName = 'Mozilla Firefox';
    } else if (/safari/i.test(ua) && !/chrome/i.test(ua)) {
      deviceInfo.browserName = 'Safari';
    } else if (/opera/i.test(ua)) {
      deviceInfo.browserName = 'Opera';
    }

    // Combine device name with browser for better identification (using safe separator)
    deviceInfo.deviceName = `${deviceInfo.deviceName} - ${deviceInfo.browserName}`;

    // Try to get timezone
    try {
      deviceInfo.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
    } catch (e) {
      deviceInfo.timezone = 'UTC';
    }

    // Try to get screen info
    try {
      if (typeof screen !== 'undefined') {
        deviceInfo.screenResolution = `${screen.width}x${screen.height}`;
        // Add screen info to device name for better identification (using safe separator)
        deviceInfo.deviceName = `${deviceInfo.deviceName} - ${deviceInfo.screenResolution}`;
      }
    } catch (e) {
      // Ignore screen info errors
    }

    // Try to get connection info
    try {
      if (navigator.connection) {
        deviceInfo.connectionType = navigator.connection.effectiveType || 'unknown';
      }
    } catch (e) {
      // Ignore connection info errors
    }

    return deviceInfo;
  }

  // Helper function to sanitize header values
  sanitizeHeaderValue(value) {
    if (!value || typeof value !== 'string') {
      return '';
    }

    // Remove or replace characters that are not valid in HTTP headers
    // HTTP headers should only contain ASCII characters (0-127)
    return value
      .replace(/[^\x00-\x7F]/g, '') // Remove non-ASCII characters
      .replace(/[\r\n\t]/g, ' ') // Replace line breaks and tabs with spaces
      .trim()
      .substring(0, 200); // Limit length to prevent overly long headers
  }

  // Make authenticated request
  async makeRequest(endpoint, options = {}) {
    // console.log('🌐 API: makeRequest called with:', { endpoint, method: options.method || 'GET' });
    const token = await this.getAuthToken();

    // Check if body is FormData to handle file uploads properly
    const isFormData = options.body instanceof FormData;

    // Get enhanced device info for better session tracking
    const deviceInfo = this.getDeviceInfo();

    const config = {
      method: 'GET',
      headers: {
        // Don't set Content-Type for FormData - let browser set it with boundary
        ...(!isFormData && { 'Content-Type': 'application/json' }),
        ...(token && { Authorization: `Bearer ${token}` }),
        // Enhanced device headers for better login session tracking (User-Agent is set automatically by browser)
        // Sanitize all header values to prevent encoding issues
        'X-Timezone': this.sanitizeHeaderValue(deviceInfo.timezone),
        'X-Language': this.sanitizeHeaderValue(deviceInfo.language),
        'X-Device-Type': this.sanitizeHeaderValue(deviceInfo.deviceType),
        'X-Device-Name': this.sanitizeHeaderValue(deviceInfo.deviceName),
        'X-Browser-Name': this.sanitizeHeaderValue(deviceInfo.browserName),
        'X-OS-Name': this.sanitizeHeaderValue(deviceInfo.osName),
        ...(deviceInfo.screenResolution && { 'X-Screen-Resolution': this.sanitizeHeaderValue(deviceInfo.screenResolution) }),
        ...(deviceInfo.connectionType && { 'X-Connection-Type': this.sanitizeHeaderValue(deviceInfo.connectionType) }),
        ...options.headers,
      },
      ...options,
    };

    // Only stringify body if it's not FormData
    if (config.body && typeof config.body === 'object' && !isFormData) {
      config.body = JSON.stringify(config.body);
    }

    try {
      if (__DEV__) {
        // console.log(`🌐 API Request: ${config.method || 'GET'} ${this.baseURL}${endpoint}`);
        // console.log('📤 Request headers:', config.headers);
        if (config.body instanceof FormData) {
          // console.log('📤 Request body: FormData with entries:');
          for (let [key, value] of config.body.entries()) {
            // console.log(`  ${key}:`, value);
          }
        } else if (config.body) {
          // console.log('📤 Request body:', config.body);
        }
      }

      const response = await fetch(`${this.baseURL}${endpoint}`, config);

      if (__DEV__) {
        // console.log(`📡 Response status: ${response.status} ${response.statusText}`);
      }

      // Handle non-JSON responses
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error('Non-JSON response:', textResponse);
        throw new Error(`Server returned non-JSON response: ${textResponse}`);
      }

      const data = await response.json();

      // Debug logging for chat rooms specifically
      if (endpoint === '/chat/rooms') {
        // console.log('🔍 makeRequest: Raw response for /chat/rooms:', {
        //   ok: response.ok,
        //   status: response.status,
        //   dataType: typeof data,
        //   dataKeys: Object.keys(data || {}),
        //   success: data.success,
        //   dataLength: Array.isArray(data.data) ? data.data.length : 'not array',
        //   rawData: JSON.stringify(data).substring(0, 500)
        // });
      }

      if (__DEV__) {
        // console.log('📥 Response data:', data);
      }

      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
          console.log('🔐 Authentication failed, clearing invalid token');
          await this.removeAuthToken();
          await AsyncStorage.removeItem('userRole');
          await AsyncStorage.removeItem('userData');

          // Provide specific error message for authentication failures
          const errorMessage = data.error || 'Authentication required. Please login again.';
          throw new Error(errorMessage);
        }

        // Handle rate limiting
        if (response.status === 429) {
          console.warn('⏳ Rate limited by server');
          throw new Error('Too many requests. Please wait a moment and try again.');
        }

        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('🚨 API Request failed:', error);
      console.error('🔗 Request URL:', `${this.baseURL}${endpoint}`);
      console.error('⚙️ Request config:', config);

      // Provide helpful error messages for common issues
      if (error.message.includes('NetworkError') || error.message.includes('CORS')) {
        throw new Error('Unable to connect to server. Please check your internet connection and ensure the backend server is running.');
      }

      if (error.message.includes('Failed to fetch')) {
        throw new Error('Network error. Please check if the backend server is running on the correct port.');
      }

      if (error.message.includes('Content-Length header')) {
        console.warn('⚠️ Content-Length mismatch - this is usually a backend issue');
        throw new Error('Server response error. The backend may be sending incomplete data.');
      }

      throw error;
    }
  }



  // Health check method
  async checkHealth() {
    try {
      // Debug current environment
      // console.log('🌐 Current window.location:', typeof window !== 'undefined' ? window.location.href : 'No window object');
      // console.log('🌐 API Base URL:', this.baseURL);

      const healthUrl = `${this.baseURL.replace('/api/v1', '')}/health`;
      // console.log('🏥 Health check URL:', healthUrl);

      // Proper timeout implementation using AbortController
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        // console.log('⏰ Health check timeout after 5 seconds');
        controller.abort();
      }, 5000);

      // console.log('🚀 Starting health check request...');
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      // console.log('🏥 Health check response status:', response.status);
      // console.log('🏥 Health check response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }

      const result = await response.json();
      // console.log('✅ Health check successful:', result);
      return result;
    } catch (error) {
      console.error('❌ Health check failed:', error.name, error.message);
      if (error.name === 'AbortError') {
        console.error('❌ Health check was aborted (timeout)');
      }
      throw error;
    }
  }

  // Authentication endpoints
  async login(credentials) {
    const response = await this.makeRequest('/auth/login', {
      method: 'POST',
      body: credentials,
    });

    if (response.success && response.data?.token) {
      await this.setAuthToken(response.data.token);
      await AsyncStorage.setItem('userRole', response.data.user?.role || 'user');
      await AsyncStorage.setItem('userData', JSON.stringify(response.data.user));
    }

    return response;
  }

  async register(userData) {
    const response = await this.makeRequest('/auth/register', {
      method: 'POST',
      body: userData,
    });

    if (response.success && response.data?.token) {
      await this.setAuthToken(response.data.token);
      await AsyncStorage.setItem('userRole', response.data.user?.role || 'user');
      await AsyncStorage.setItem('userData', JSON.stringify(response.data.user));
    }

    return response;
  }

  async logout() {
    // console.log('🔴 API Service: Starting logout...');
    try {
      // console.log('🔴 API Service: Making logout request to backend...');
      const response = await this.makeRequest('/auth/logout', { method: 'POST' });
      // console.log('🔴 API Service: Logout response:', response);
      return response;
    } catch (error) {
      console.error('🔴 API Service: Logout API call failed:', error);
      // Don't throw error - continue with local cleanup even if API fails
      return { success: false, error: error.message };
    } finally {
      // console.log('🔴 API Service: Cleaning up local auth data...');
      await this.removeAuthToken();
      await AsyncStorage.removeItem('userRole');
      await AsyncStorage.removeItem('userData');
      // console.log('🔴 API Service: Local cleanup completed');
    }
  }

  async refreshToken() {
    return await this.makeRequest('/auth/refresh', { method: 'POST' });
  }

  async getProfile() {
    return await this.makeRequest('/users/profile');
  }

  async updateProfile(profileData) {
    // console.log('🔄 updateProfile called with:', {
    //   hasProfileImage: !!profileData.profile_image,
    //   hasAvatar: !!profileData.avatar,
    //   profileImageValue: profileData.profile_image,
    //   avatarValue: profileData.avatar
    // });

    // Check if we have a profile image to upload (check both field names)
    const imageField = profileData.profile_image || profileData.avatar;
    if (imageField && (imageField.startsWith('file://') || imageField.startsWith('blob:') || imageField.includes('ImagePicker'))) {
      // console.log('🖼️ Detected image upload, using FormData method');
      return await this.updateProfileWithImage(profileData);
    }

    // console.log('📝 No image detected, using regular JSON update');
    // Regular profile update without image
    return await this.makeRequest('/users/profile', {
      method: 'PUT',
      body: profileData,
    });
  }

  async getUsers(limit = 50, offset = 0, query = '') {
    let url = `/users/?limit=${limit}&offset=${offset}`;
    if (query) {
      url += `&q=${encodeURIComponent(query)}`;
    }
    return await this.makeRequest(url);
  }

  async searchUsers(query) {
    return await this.makeRequest(`/users/?q=${encodeURIComponent(query)}`);
  }

  async getAllUsersForAdmin(limit = 15, offset = 0, query = '') {
    let url = `/users/admin/all?limit=${limit}&offset=${offset}`;
    if (query) {
      url += `&q=${encodeURIComponent(query)}`;
    }
    return await this.makeRequest(url);
  }

  // FAST: Get ALL users without pagination for complete user management
  async getAllUsersComplete(query = '') {
    // console.log('🚀 FAST: Fetching ALL users for complete management...');
    try {
      let url = `/users/admin/all?limit=1000&offset=0`; // Get up to 1000 users
      if (query) {
        url += `&q=${encodeURIComponent(query)}`;
      }

      // console.log(`🔍 FAST: Making request to ${url}`);

      // Try the endpoint, if it fails, try alternative endpoints
      let response = await this.makeRequest(url);

      // If the admin endpoint fails, try regular users endpoint
      if (!response.success && response.status === 404) {
        // console.log('⚠️ FAST: Admin endpoint not found, trying regular users endpoint...');
        const fallbackUrl = `/users?limit=1000&offset=0${query ? `&q=${encodeURIComponent(query)}` : ''}`;
        // console.log(`🔍 FAST: Trying fallback URL: ${fallbackUrl}`);
        response = await this.makeRequest(fallbackUrl);
      }

      // If still failing, try another common endpoint
      if (!response.success && response.status === 404) {
        // console.log('⚠️ FAST: Regular users endpoint not found, trying /api/users...');
        const apiUrl = `/api/users?limit=1000&offset=0${query ? `&q=${encodeURIComponent(query)}` : ''}`;
        // console.log(`🔍 FAST: Trying API URL: ${apiUrl}`);
        response = await this.makeRequest(apiUrl);
      }

      // If all endpoints fail, try to get users from any available endpoint
      if (!response.success) {
        // console.log('⚠️ FAST: All user endpoints failed, trying basic /users endpoint...');
        try {
          const basicResponse = await this.makeRequest('/users');
          if (basicResponse.success && basicResponse.data) {
            // console.log(`✅ FAST: Basic users endpoint worked, got ${basicResponse.data.length} users`);
            response = basicResponse;
          }
        } catch (basicError) {
          console.error('❌ FAST: Basic users endpoint also failed:', basicError.message);
        }
      }

      // console.log('🔍 FAST: Raw API response:', {
      //   success: response.success,
      //   dataType: typeof response.data,
      //   dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
      //   error: response.error || 'none',
      //   status: response.status
      // });

      if (response.success && response.data) {
        // console.log(`✅ FAST: Got ${response.data.length} total users from API`);

        // Remove duplicates by ID (ensure unique users)
        const uniqueUsers = response.data.reduce((acc, user) => {
          if (user && user.id && !acc.find(existing => existing.id === user.id)) {
            acc.push(user);
          }
          return acc;
        }, []);

        // console.log(`🔍 FAST: Removed ${response.data.length - uniqueUsers.length} duplicates`);
        // console.log(`📊 FAST: Final unique user count: ${uniqueUsers.length}`);

        return {
          success: true,
          data: uniqueUsers,
          totalCount: uniqueUsers.length,
          hasDuplicates: response.data.length !== uniqueUsers.length
        };
      } else {
        console.warn('⚠️ FAST: API response not successful:', response.error || 'Unknown error');
      }

      return response;
    } catch (error) {
      console.error('❌ FAST: Failed to get all users:', error);
      return { success: false, error: error.message, data: [] };
    }
  }

  async updateUserRole(userId, role) {
    return await this.makeRequest(`/users/${userId}/role`, {
      method: 'PUT',
      body: { role },
    });
  }

  async updateUserStatus(userId, status) {
    return await this.makeRequest(`/users/${userId}/status`, {
      method: 'PUT',
      body: { status },
    });
  }

  async deleteUser(userId) {
    return await this.makeRequest(`/users/${userId}`, {
      method: 'DELETE',
    });
  }

  async updateProfileWithImage(profileData) {
    // console.log('🖼️ updateProfileWithImage called with:', profileData);
    const token = await this.getAuthToken();

    // Create FormData for file upload
    const formData = new FormData();

    // Get image from either field name
    const imageUri = profileData.profile_image || profileData.avatar;

    // Add profile image if exists
    if (imageUri) {
      // console.log('📁 Processing image URI:', imageUri);

      // Handle web File objects
      if (imageUri instanceof File) {
        // console.log('📁 Web File object detected');
        formData.append('avatar', imageUri, imageUri.name);
      } else {
        // Handle mobile URIs
        const filename = imageUri.split('/').pop() || 'profile.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';

        // console.log('📁 Mobile URI detected:', { filename, type });
        formData.append('avatar', {
          uri: imageUri,
          name: filename,
          type: type,
        });
      }
    }

    // Add other profile fields
    Object.keys(profileData).forEach(key => {
      if (key !== 'profile_image' && profileData[key] !== null && profileData[key] !== undefined) {
        formData.append(key, profileData[key]);
      }
    });

    try {
      const response = await fetch(`${this.baseURL}/users/profile`, {
        method: 'PUT',
        headers: {
          // Don't set Content-Type for FormData - let browser set it with boundary
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: formData,
      });

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      // console.error('Profile update with image failed:', error);
      throw error;
    }
  }

  // Wallet endpoints
  async getWalletBalance() {
    return await this.makeRequest('/wallets/balance');
  }

  async getTransactions(limit = 20, offset = 0) {
    return await this.makeRequest(`/wallets/transactions?limit=${limit}&offset=${offset}`);
  }

  async initiateDeposit(amount, paymentMethod = 'mpesa', description = '', reference = '') {
    return await this.makeRequest('/wallets/deposit', {
      method: 'POST',
      body: {
        amount,
        paymentMethod,
        description: description || `Deposit via ${paymentMethod}`,
        reference
      },
    });
  }

  async initiateWithdrawal(amount, withdrawMethod, phoneNumber = '', bankAccountNumber = '', bankCode = '', description = '') {
    const body = {
      amount,
      withdrawMethod,
      description: description || `Withdrawal via ${withdrawMethod}`,
    };

    // Add method-specific fields
    if (withdrawMethod === 'mpesa') {
      body.phoneNumber = phoneNumber;
    } else if (withdrawMethod === 'bank') {
      body.bankAccountNumber = bankAccountNumber;
      body.bankCode = bankCode;
    }

    return await this.makeRequest('/wallets/withdraw', {
      method: 'POST',
      body,
    });
  }

  async transferMoney(recipientId, amount, description = '', recipientType = 'user') {
    return await this.makeRequest('/wallets/transfer', {
      method: 'POST',
      body: {
        recipientId,
        recipientType, // 'user', 'phone', 'email'
        amount,
        description: description || 'Money transfer',
      },
    });
  }

  // Money request endpoints
  async createMoneyRequest(amount, reason, requestType = 'qr_code') {
    return await this.makeRequest('/wallet/create-money-request', {
      method: 'POST',
      body: { amount, reason, requestType },
    });
  }

  async sendMoneyRequest(amount, reason, targetUserId, targetPhone, requestType = 'direct') {
    return await this.makeRequest('/wallet/send-money-request', {
      method: 'POST',
      body: { amount, reason, targetUserId, targetPhone, requestType },
    });
  }

  async getRecentContacts() {
    return await this.makeRequest('/wallet/recent-contacts');
  }

  // Chama endpoints
  async getChamas(limit = 20, offset = 0) {
    return await this.makeRequest(`/chamas/?limit=${limit}&offset=${offset}`);
  }

  async getAllChamasForAdmin(limit = 100, offset = 0) {
    return await this.makeRequest(`/chamas/admin/all?limit=${limit}&offset=${offset}`);
  }

  // REAL: System Analytics API methods
  async getSystemAnalytics(period = '7d') {
    // console.log(`📊 REAL: Fetching system analytics for period: ${period}`);
    try {
      // Try the dedicated analytics endpoint first
      let response = await this.makeRequest(`/admin/analytics?period=${period}`);

      // If dedicated endpoint doesn't exist, build analytics from multiple endpoints
      if (!response.success && response.status === 404) {
        // console.log('📊 REAL: Building analytics from multiple endpoints...');
        response = await this.buildSystemAnalytics(period);
      }

      // console.log('📊 REAL: System analytics response:', {
      //   success: response.success,
      //   hasData: !!response.data,
      //   period: period
      // });

      return response;
    } catch (error) {
      console.error('❌ REAL: System analytics failed:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  // REAL: Build system analytics from existing endpoints
  async buildSystemAnalytics(period = '7d') {
    // console.log('📊 REAL: Building comprehensive system analytics...');
    try {
      // Fetch data from multiple endpoints in parallel
      const [usersResponse, chamasResponse, transactionsResponse] = await Promise.all([
        this.getAllUsersComplete(),
        this.getAllChamasForAdmin(1000, 0),
        this.getTransactions(1000, 0) // Get wallet transactions
      ]);

      // console.log('📊 REAL: Raw analytics data:', {
      //   users: usersResponse.success ? usersResponse.data?.length : 'failed',
      //   chamas: chamasResponse.success ? chamasResponse.data?.length : 'failed',
      //   transactions: transactionsResponse.success ? transactionsResponse.data?.length : 'failed'
      // });

      // Calculate analytics from real data
      const analytics = this.calculateSystemAnalytics(
        usersResponse.data || [],
        chamasResponse.data || [],
        transactionsResponse.data || [],
        period
      );

      return {
        success: true,
        data: analytics,
        source: 'calculated',
        period: period
      };
    } catch (error) {
      console.error('❌ REAL: Failed to build system analytics:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  // REAL: Calculate system analytics from real data
  calculateSystemAnalytics(users, chamas, transactions, period) {
    // console.log('📊 REAL: Calculating analytics from real data...');

    const now = new Date();
    const periodDays = parseInt(period.replace('d', '')) || 7;
    const periodStart = new Date(now.getTime() - (periodDays * 24 * 60 * 60 * 1000));

    // Filter data by period
    const recentTransactions = transactions.filter(t =>
      new Date(t.createdAt || t.created_at) >= periodStart
    );
    const recentUsers = users.filter(u =>
      new Date(u.createdAt || u.created_at) >= periodStart
    );
    const recentChamas = chamas.filter(c =>
      new Date(c.createdAt || c.created_at) >= periodStart
    );

    // Calculate user metrics
    const userMetrics = {
      total: users.length,
      active: users.filter(u => u.status === 'active').length,
      inactive: users.filter(u => u.status !== 'active').length,
      newUsers: recentUsers.length,
      adminUsers: users.filter(u => u.role === 'admin').length
    };

    // Calculate chama metrics
    const chamaMetrics = {
      total: chamas.length,
      active: chamas.filter(c => c.isActive !== false).length,
      newChamas: recentChamas.length,
      totalMembers: chamas.reduce((sum, c) => sum + (c.memberCount || 0), 0)
    };

    // Calculate transaction metrics
    const transactionMetrics = {
      total: transactions.length,
      recentCount: recentTransactions.length,
      totalVolume: transactions.reduce((sum, t) => sum + (parseFloat(t.amount) || 0), 0),
      recentVolume: recentTransactions.reduce((sum, t) => sum + (parseFloat(t.amount) || 0), 0),
      avgTransaction: transactions.length > 0 ?
        transactions.reduce((sum, t) => sum + (parseFloat(t.amount) || 0), 0) / transactions.length : 0
    };

    // Calculate growth rates
    const growthRates = {
      userGrowth: users.length > 0 ? (recentUsers.length / users.length) * 100 : 0,
      chamaGrowth: chamas.length > 0 ? (recentChamas.length / chamas.length) * 100 : 0,
      transactionGrowth: transactions.length > 0 ? (recentTransactions.length / transactions.length) * 100 : 0
    };

    // System health metrics
    const systemHealth = {
      activeUserRate: users.length > 0 ? (userMetrics.active / users.length) * 100 : 0,
      activeChamaRate: chamas.length > 0 ? (chamaMetrics.active / chamas.length) * 100 : 0,
      avgMembersPerChama: chamas.length > 0 ? chamaMetrics.totalMembers / chamas.length : 0
    };

    const analytics = {
      period: period,
      generatedAt: now.toISOString(),
      users: userMetrics,
      chamas: chamaMetrics,
      transactions: transactionMetrics,
      growth: growthRates,
      health: systemHealth,
      summary: {
        totalUsers: userMetrics.total,
        totalChamas: chamaMetrics.total,
        totalTransactions: transactionMetrics.total,
        totalVolume: transactionMetrics.totalVolume,
        healthScore: Math.round((systemHealth.activeUserRate + systemHealth.activeChamaRate) / 2)
      }
    };

    // console.log('📊 REAL: Analytics calculated:', {
    //   users: userMetrics.total,
    //   chamas: chamaMetrics.total,
    //   transactions: transactionMetrics.total,
    //   volume: transactionMetrics.totalVolume,
    //   healthScore: analytics.summary.healthScore
    // });

    return analytics;
  }

  async getUserChamas(limit = 20, offset = 0) {
    return await this.makeRequest(`/chamas/my?limit=${limit}&offset=${offset}`);
  }

  async getChamaById(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}`);
  }

  async getChamaStatistics(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/statistics`);
  }

  async getChamaWalletBalance(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/wallet/balance`);
  }

  async getUserStatistics() {
    return await this.makeRequest('/users/statistics');
  }

  async getAdminStatistics() {
    return await this.makeRequest('/users/admin/statistics');
  }

  async getSystemAnalytics(period = '7d') {
    return await this.makeRequest(`/users/admin/analytics?period=${period}`);
  }

  async createChama(chamaData) {
    return await this.makeRequest('/chamas/', {
      method: 'POST',
      body: chamaData,
    });
  }

  async updateChama(chamaId, updateData) {
    return await this.makeRequest(`/chamas/${chamaId}`, {
      method: 'PUT',
      body: updateData,
    });
  }

  async joinChama(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/join`, {
      method: 'POST',
    });
  }

  async leaveChama(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/leave`, {
      method: 'POST',
    });
  }

  async getChamaMembers(chamaId) {
    // console.log('🔍 🎯 REGULAR getChamaMembers called for chamaId:', chamaId);
    // console.log('🔍 🎯 REGULAR Calling endpoint: /chamas/' + chamaId + '/members');
    const result = await this.makeRequest(`/chamas/${chamaId}/members`);
    // console.log('🔍 🎯 REGULAR getChamaMembers response structure:', {
    //   success: result.success,
    //   dataLength: result.data?.length,
    //   firstMemberKeys: result.data?.[0] ? Object.keys(result.data[0]) : [],
    //   firstMemberUserKeys: result.data?.[0]?.user ? Object.keys(result.data[0].user) : []
    // });
    return result;
  }

  async getChamaTransactions(chamaId, limit = 20, offset = 0) {
    return await this.makeRequest(`/chamas/${chamaId}/transactions?limit=${limit}&offset=${offset}`);
  }

  // Invitation endpoints
  async sendChamaInvitation(chamaId, invitationData) {
    return await this.makeRequest(`/chamas/${chamaId}/invite`, {
      method: 'POST',
      body: invitationData,
    });
  }

  async getUserInvitations() {
    return await this.makeRequest('/chamas/invitations');
  }

  async respondToInvitation(invitationId, response) {
    return await this.makeRequest(`/chamas/invitations/${invitationId}/respond`, {
      method: 'POST',
      body: { response },
    });
  }

  async getChamaSentInvitations(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/invitations/sent`);
  }

  async cancelInvitation(invitationId) {
    return await this.makeRequest(`/chamas/invitations/${invitationId}/cancel`, {
      method: 'POST',
    });
  }

  async resendInvitation(invitationId) {
    return await this.makeRequest(`/chamas/invitations/${invitationId}/resend`, {
      method: 'POST',
    });
  }

  // Guarantor methods
  async respondToGuarantorRequest(guarantorId, action, reason = '') {
    return await this.makeRequest(`/loans/guarantors/${guarantorId}/respond`, {
      method: 'POST',
      body: { action, reason },
    });
  }

  // Contribution endpoints
  async makeContribution(contributionData) {
    return await this.makeRequest('/contributions', {
      method: 'POST',
      body: contributionData,
    });
  }

  async getContributions(chamaId, limit = 20, offset = 0) {
    return await this.makeRequest(`/contributions?chamaId=${chamaId}&limit=${limit}&offset=${offset}`);
  }

  // Marketplace endpoints
  async getProducts(filters = {}, limit = 20, offset = 0) {
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
      ...filters,
    });
    return await this.makeRequest(`/marketplace/products/?${queryParams}`);
  }

  // REAL: Get ALL products for complete marketplace management
  async getAllProductsComplete(query = '') {
    // console.log('🛒 REAL: Fetching ALL products for complete marketplace...');
    try {
      // Start with smaller limit to avoid timeouts, then try larger if successful
      let url = `/marketplace/products/all?limit=100&offset=0`; // Start with 100 products
      if (query) {
        url += `&q=${encodeURIComponent(query)}`;
      }

      // console.log(`🔍 REAL: Making request to ${url} (starting with smaller limit)`);

      // Try the dedicated all products endpoint first with smaller limit
      let response = await this.makeRequest(url);

      // If dedicated endpoint doesn't exist or fails, try multiple fallback strategies
      if (!response.success) {
        // console.log('⚠️ REAL: All products endpoint failed, trying fallback strategies...');
        // console.log('⚠️ REAL: Error:', response.error);

        // Strategy 1: Try regular products endpoint with high limit
        const fallbackUrl = `/marketplace/products/?limit=500&offset=0${query ? `&q=${encodeURIComponent(query)}` : ''}`;
        // console.log(`🔍 REAL: Strategy 1 - Regular endpoint: ${fallbackUrl}`);
        response = await this.makeRequest(fallbackUrl);

        // Strategy 2: If regular endpoint also fails, try with smaller limit
        if (!response.success) {
          // console.log('⚠️ REAL: Strategy 1 failed, trying Strategy 2 with smaller limit...');
          const smallerUrl = `/marketplace/products/?limit=50&offset=0${query ? `&q=${encodeURIComponent(query)}` : ''}`;
          // console.log(`🔍 REAL: Strategy 2 - Smaller limit: ${smallerUrl}`);
          response = await this.makeRequest(smallerUrl);
        }

        // Strategy 3: If still failing, try with very small limit
        if (!response.success) {
          // console.log('⚠️ REAL: Strategy 2 failed, trying Strategy 3 with very small limit...');
          const verySmallUrl = `/marketplace/products/?limit=10&offset=0${query ? `&q=${encodeURIComponent(query)}` : ''}`;
          // console.log(`🔍 REAL: Strategy 3 - Very small limit: ${verySmallUrl}`);
          response = await this.makeRequest(verySmallUrl);
        }
      }

      // console.log('🔍 REAL: Raw products API response:', {
      //   success: response.success,
      //   dataType: typeof response.data,
      //   dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
      //   error: response.error || 'none',
      //   status: response.status
      // });

      if (response.success && response.data) {
        // console.log(`✅ REAL: Got ${response.data.length} products from API (limit: 100)`);

        // If we got 100 products successfully, try to get more (up to 500 max to avoid timeout)
        if (response.data.length === 100) {
          // console.log('🔄 REAL: Got full batch, trying to load more products...');
          try {
            const moreUrl = `/marketplace/products/all?limit=400&offset=0${query ? `&q=${encodeURIComponent(query)}` : ''}`;
            // console.log(`🔍 REAL: Trying larger batch: ${moreUrl}`);

            const moreResponse = await this.makeRequest(moreUrl);
            if (moreResponse.success && moreResponse.data && moreResponse.data.length > response.data.length) {
              // console.log(`✅ REAL: Got ${moreResponse.data.length} products with larger batch`);
              response = moreResponse; // Use the larger response
            } else {
              console.log('⚠️ REAL: Larger batch failed or returned same data, using smaller batch');
            }
          } catch (error) {
            // console.log('⚠️ REAL: Larger batch failed, using smaller batch:', error.message);
          }
        }

        // Remove duplicates by ID (ensure unique products)
        const uniqueProducts = response.data.reduce((acc, product) => {
          if (product && product.id && !acc.find(existing => existing.id === product.id)) {
            acc.push(product);
          }
          return acc;
        }, []);

        // console.log(`🔍 REAL: Removed ${response.data.length - uniqueProducts.length} duplicates`);
        // console.log(`📊 REAL: Final unique product count: ${uniqueProducts.length}`);

        return {
          success: true,
          data: uniqueProducts,
          totalCount: uniqueProducts.length,
          hasDuplicates: response.data.length !== uniqueProducts.length,
          loadStrategy: response.data.length > 100 ? 'progressive-large' : 'progressive-small'
        };
      } else {
        console.warn('⚠️ REAL: Products API response not successful:', response.error || 'Unknown error');
      }

      return response;
    } catch (error) {
      console.error('❌ REAL: Failed to get all products:', error);

      // DON'T USE FALLBACK - Return the actual error so we can debug
      // console.log('❌ REAL: Returning actual error instead of fallback data');
      return {
        success: false,
        error: error.message,
        data: [],
        source: 'api-error'
      };
    }
  }



  // REAL: Get marketplace categories with robust fallback
  async getMarketplaceCategories() {
    // console.log('🏷️ REAL: Fetching marketplace categories...');
    try {
      // Try the dedicated categories endpoint first
      let response = await this.makeRequest('/marketplace/categories');

      // If dedicated endpoint doesn't exist or fails, build categories from products
      if (!response.success) {
        // console.log('🏷️ REAL: Categories endpoint failed, building from products...');
        // console.log('🏷️ REAL: Categories endpoint error:', response.error);

        // Always fallback to building categories from products
        response = await this.buildMarketplaceCategoriesFromProducts();
      }

      // console.log('🏷️ REAL: Final categories response:', {
      //   success: response.success,
      //   categoriesCount: Array.isArray(response.data) ? response.data.length : 'not array',
      //   source: response.source || 'api',
      //   error: response.error || 'none'
      // });

      return response;
    } catch (error) {
      console.error('❌ REAL: Failed to get marketplace categories:', error);

      // Final fallback: return default categories
      // console.log('🏷️ REAL: Using default fallback categories...');
      return {
        success: true,
        data: this.getDefaultMarketplaceCategories(),
        source: 'default-fallback',
        error: null
      };
    }
  }

  // REAL: Build categories from products if no dedicated endpoint
  async buildMarketplaceCategoriesFromProducts() {
    // console.log('🏷️ REAL: Building categories from existing products...');
    try {
      // Try to get products, but with a fallback approach
      let productsResponse;

      try {
        productsResponse = await this.getAllProductsComplete();
      } catch (error) {
        // console.log('🏷️ REAL: getAllProductsComplete failed, trying regular products endpoint...');
        // Fallback to regular products endpoint
        productsResponse = await this.getProducts({}, 100, 0);
      }

      if (productsResponse.success && productsResponse.data && productsResponse.data.length > 0) {
        const products = productsResponse.data;

        // Extract unique categories from products
        const categoryMap = new Map();

        products.forEach(product => {
          if (product.category) {
            const categoryId = product.category;
            if (!categoryMap.has(categoryId)) {
              categoryMap.set(categoryId, {
                id: categoryId,
                name: this.formatCategoryName(categoryId),
                icon: this.getCategoryIcon(categoryId),
                productCount: 0
              });
            }
            categoryMap.get(categoryId).productCount++;
          }
        });

        const categories = Array.from(categoryMap.values());

        // If no categories found from products, use defaults
        if (categories.length === 0) {
          // console.log('🏷️ REAL: No categories found in products, using defaults');
          return {
            success: true,
            data: this.getDefaultMarketplaceCategories(),
            source: 'default-no-product-categories'
          };
        }

        // console.log('🏷️ REAL: Built categories from products:', {
        //   totalCategories: categories.length,
        //   totalProducts: products.length,
        //   categories: categories.map(c => `${c.name} (${c.productCount})`)
        // });

        return {
          success: true,
          data: categories,
          source: 'built-from-products'
        };
      }

      // No products found, return default categories
      // console.log('🏷️ REAL: No products found, using default categories');
      return {
        success: true,
        data: this.getDefaultMarketplaceCategories(),
        source: 'default-no-products'
      };
    } catch (error) {
      // console.error('❌ REAL: Failed to build categories from products:', error);

      // Final fallback to default categories
      // console.log('🏷️ REAL: Error building categories, using defaults');
      return {
        success: true,
        data: this.getDefaultMarketplaceCategories(),
        source: 'default-error-fallback'
      };
    }
  }

  // Helper method to format category names
  formatCategoryName(categoryId) {
    const categoryNames = {
      'agriculture': 'Agriculture',
      'food_beverage': 'Food & Beverage',
      'clothing': 'Clothing',
      'electronics': 'Electronics',
      'home_garden': 'Home & Garden',
      'health_beauty': 'Health & Beauty',
      'sports_outdoors': 'Sports & Outdoors',
      'books_media': 'Books & Media',
      'automotive': 'Automotive',
      'services': 'Services'
    };

    return categoryNames[categoryId] || categoryId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  // Helper method to get category icons
  getCategoryIcon(categoryId) {
    const categoryIcons = {
      'agriculture': 'leaf',
      'food_beverage': 'restaurant',
      'clothing': 'shirt',
      'electronics': 'phone-portrait',
      'home_garden': 'home',
      'health_beauty': 'heart',
      'sports_outdoors': 'football',
      'books_media': 'book',
      'automotive': 'car',
      'services': 'construct'
    };

    return categoryIcons[categoryId] || 'pricetag';
  }

  // Get default marketplace categories as final fallback
  getDefaultMarketplaceCategories() {
    // console.log('🏷️ REAL: Returning default marketplace categories');
    return [
      { id: 'agriculture', name: 'Agriculture', icon: 'leaf', productCount: 0 },
      { id: 'food_beverage', name: 'Food & Beverage', icon: 'restaurant', productCount: 0 },
      { id: 'clothing', name: 'Clothing', icon: 'shirt', productCount: 0 },
      { id: 'electronics', name: 'Electronics', icon: 'phone-portrait', productCount: 0 },
      { id: 'home_garden', name: 'Home & Garden', icon: 'home', productCount: 0 },
      { id: 'health_beauty', name: 'Health & Beauty', icon: 'heart', productCount: 0 },
      { id: 'sports_outdoors', name: 'Sports & Outdoors', icon: 'football', productCount: 0 },
      { id: 'automotive', name: 'Automotive', icon: 'car', productCount: 0 },
      { id: 'services', name: 'Services', icon: 'construct', productCount: 0 },
      { id: 'other', name: 'Other', icon: 'ellipsis-horizontal', productCount: 0 }
    ];
  }

  // REAL: Marketplace Analytics API method
  async getMarketplaceAnalytics(period = '7d') {
    // console.log(`🛒 REAL: Fetching marketplace analytics for period: ${period}`);
    try {
      // Try the dedicated analytics endpoint first
      let response = await this.makeRequest(`/marketplace/analytics?period=${period}`);

      // If dedicated endpoint doesn't exist, build analytics from multiple endpoints
      if (!response.success && response.status === 404) {
        // console.log('🛒 REAL: Building marketplace analytics from multiple endpoints...');
        response = await this.buildMarketplaceAnalytics(period);
      }

      // console.log('🛒 REAL: Marketplace analytics response:', {
      //   success: response.success,
      //   hasData: !!response.data,
      //   period: period
      // });

      return response;
    } catch (error) {
      console.error('❌ REAL: Marketplace analytics failed:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  // REAL: Build marketplace analytics from existing endpoints
  async buildMarketplaceAnalytics(period = '7d') {
    // console.log('🛒 REAL: Building comprehensive marketplace analytics...');
    try {
      // Fetch data from multiple endpoints in parallel
      const [productsResponse, categoriesResponse, ordersResponse] = await Promise.all([
        this.getAllProductsComplete(),
        this.getMarketplaceCategories(),
        this.getOrders({ limit: 1000 })
      ]);

      // // console.log('🛒 REAL: Raw marketplace data:', {
      //   products: productsResponse.success ? productsResponse.data?.length : 'failed',
      //   categories: categoriesResponse.success ? categoriesResponse.data?.length : 'failed',
      //   orders: ordersResponse.success ? ordersResponse.data?.length : 'failed'
      // });

      // Calculate analytics from real data
      const analytics = this.calculateMarketplaceAnalytics(
        productsResponse.data || [],
        categoriesResponse.data || [],
        ordersResponse.data || [],
        period
      );

      return {
        success: true,
        data: analytics,
        source: 'calculated',
        period: period
      };
    } catch (error) {
      console.error('❌ REAL: Failed to build marketplace analytics:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  // REAL: Calculate marketplace analytics from real data
  calculateMarketplaceAnalytics(products, categories, orders, period) {
    // console.log('🛒 REAL: Calculating marketplace analytics from real data...');

    const now = new Date();
    const periodDays = parseInt(period.replace('d', '')) || 7;
    const periodStart = new Date(now.getTime() - (periodDays * 24 * 60 * 60 * 1000));

    // Filter data by period
    const recentOrders = orders.filter(o =>
      new Date(o.createdAt || o.created_at) >= periodStart
    );
    const recentProducts = products.filter(p =>
      new Date(p.createdAt || p.created_at) >= periodStart
    );

    // Calculate product metrics
    const productMetrics = {
      total: products.length,
      active: products.filter(p => p.status === 'active').length,
      inactive: products.filter(p => p.status !== 'active').length,
      newProducts: recentProducts.length
    };

    // Calculate category metrics
    const categoryMetrics = {
      total: categories.length,
      withProducts: categories.filter(c => c.productCount > 0).length,
      empty: categories.filter(c => c.productCount === 0).length
    };

    // Calculate order metrics
    const orderMetrics = {
      total: orders.length,
      recentCount: recentOrders.length,
      totalRevenue: orders.reduce((sum, o) => sum + (parseFloat(o.total_amount || o.amount) || 0), 0),
      recentRevenue: recentOrders.reduce((sum, o) => sum + (parseFloat(o.total_amount || o.amount) || 0), 0),
      avgOrderValue: orders.length > 0 ?
        orders.reduce((sum, o) => sum + (parseFloat(o.total_amount || o.amount) || 0), 0) / orders.length : 0
    };

    // Calculate growth rates
    const growthRates = {
      productGrowth: products.length > 0 ? (recentProducts.length / products.length) * 100 : 0,
      orderGrowth: orders.length > 0 ? (recentOrders.length / orders.length) * 100 : 0,
      revenueGrowth: orderMetrics.totalRevenue > 0 ? (orderMetrics.recentRevenue / orderMetrics.totalRevenue) * 100 : 0
    };

    // Product distribution by category
    const productsByCategory = categories.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      productCount: products.filter(p => p.category === category.id).length,
      recentProducts: recentProducts.filter(p => p.category === category.id).length
    }));

    const analytics = {
      period: period,
      generatedAt: now.toISOString(),
      products: productMetrics,
      categories: categoryMetrics,
      orders: orderMetrics,
      growth: growthRates,
      distribution: productsByCategory,
      summary: {
        totalProducts: productMetrics.total,
        totalCategories: categoryMetrics.total,
        totalOrders: orderMetrics.total,
        totalRevenue: orderMetrics.totalRevenue,
        avgOrderValue: orderMetrics.avgOrderValue,
        marketplaceHealth: Math.round((productMetrics.active / Math.max(productMetrics.total, 1)) * 100)
      }
    };

    // console.log('🛒 REAL: Marketplace analytics calculated:', {
    //   products: productMetrics.total,
    //   categories: categoryMetrics.total,
    //   orders: orderMetrics.total,
    //   revenue: orderMetrics.totalRevenue,
    //   health: analytics.summary.marketplaceHealth
    // });

    return analytics;
  }

  async getProductById(productId) {
    return await this.makeRequest(`/marketplace/products/${productId}`);
  }

  async createProduct(productData) {
    return await this.makeRequest('/marketplace/products/', {
      method: 'POST',
      body: productData,
    });
  }

  async updateProduct(productId, productData) {
    return await this.makeRequest(`/marketplace/products/${productId}`, {
      method: 'PUT',
      body: productData,
    });
  }

  async deleteProduct(productId) {
    return await this.makeRequest(`/marketplace/products/${productId}`, {
      method: 'DELETE',
    });
  }

  async addToCart(productId, quantity) {
    return await this.makeRequest('/marketplace/cart', {
      method: 'POST',
      body: { productId, quantity },
    });
  }

  async getCart() {
    return await this.makeRequest('/marketplace/cart');
  }

  async removeFromCart(cartItemId) {
    return await this.makeRequest(`/marketplace/cart/${cartItemId}`, {
      method: 'DELETE',
    });
  }

  async createOrder(orderData) {
    return await this.makeRequest('/marketplace/orders/', {
      method: 'POST',
      body: orderData,
    });
  }

  async getOrders(filters = {}) {
    const queryParams = new URLSearchParams(filters);
    return await this.makeRequest(`/marketplace/orders/?${queryParams}`);
  }

  async getOrder(orderId) {
    return await this.makeRequest(`/marketplace/orders/${orderId}`);
  }

  async updateOrder(orderId, updateData) {
    return await this.makeRequest(`/marketplace/orders/${orderId}`, {
      method: 'PUT',
      body: updateData,
    });
  }

  // Seller Analytics
  async getSellerAnalytics(period = '30d') {
    return await this.makeRequest(`/marketplace/analytics/seller?period=${period}`);
  }

  async getBuyerStats() {
    return await this.makeRequest('/marketplace/analytics/buyer');
  }

  // Order Management
  async updateOrderStatus(orderId, status, notes = '') {
    return await this.makeRequest(`/marketplace/orders/${orderId}/status`, {
      method: 'PUT',
      body: { status, notes },
    });
  }

  async assignDeliveryPerson(orderId) {
    return await this.makeRequest(`/marketplace/orders/${orderId}/assign-delivery`, {
      method: 'POST',
    });
  }

  // Delivery Management
  async getDeliveries(filters = {}) {
    const queryParams = new URLSearchParams(filters);
    return await this.makeRequest(`/marketplace/deliveries/?${queryParams}`);
  }

  async acceptDelivery(deliveryId) {
    return await this.makeRequest(`/marketplace/deliveries/${deliveryId}/accept`, {
      method: 'POST',
    });
  }

  async updateDeliveryStatus(deliveryId, updateData) {
    return await this.makeRequest(`/marketplace/deliveries/${deliveryId}/status`, {
      method: 'PUT',
      body: updateData,
    });
  }

  // Order Details
  async getOrderById(orderId) {
    return await this.makeRequest(`/marketplace/orders/${orderId}`);
  }

  // Wishlist endpoints
  async getWishlist() {
    return await this.makeRequest('/marketplace/wishlist');
  }

  async addToWishlist(productId) {
    return await this.makeRequest('/marketplace/wishlist', {
      method: 'POST',
      body: { productId },
    });
  }

  async removeFromWishlist(productId) {
    return await this.makeRequest(`/marketplace/wishlist/${productId}`, {
      method: 'DELETE',
    });
  }

  // Reviews endpoints
  async getProductReviews(productId) {
    return await this.makeRequest(`/marketplace/products/${productId}/reviews`);
  }

  async createReview(reviewData) {
    return await this.makeRequest('/marketplace/reviews', {
      method: 'POST',
      body: reviewData,
    });
  }

  // Delivery/Carrier endpoints (duplicate methods removed - using the ones above)

  async getDeliveryTracking(orderId) {
    return await this.makeRequest(`/marketplace/orders/${orderId}/tracking`);
  }

  // Enhanced Delivery Management APIs
  async getDeliveryContacts() {
    return await this.makeRequest('/marketplace/delivery-contacts');
  }

  async createDeliveryContact(contactData) {
    return await this.makeRequest('/marketplace/delivery-contacts', {
      method: 'POST',
      body: contactData,
    });
  }

  async updateDeliveryContact(contactId, contactData) {
    return await this.makeRequest(`/marketplace/delivery-contacts/${contactId}`, {
      method: 'PUT',
      body: contactData,
    });
  }

  async deleteDeliveryContact(contactId) {
    return await this.makeRequest(`/marketplace/delivery-contacts/${contactId}`, {
      method: 'DELETE',
    });
  }

  async getMyDeliveries(status = 'all') {
    return await this.makeRequest(`/marketplace/deliveries/my-deliveries?status=${status}`);
  }

  async confirmDeliveryWithOtp(deliveryId, otpData) {
    return await this.makeRequest(`/marketplace/deliveries/${deliveryId}/confirm-otp`, {
      method: 'POST',
      body: otpData,
    });
  }

  async notifyBuyerDeliveryArrival(deliveryId) {
    return await this.makeRequest(`/marketplace/deliveries/${deliveryId}/notify-arrival`, {
      method: 'POST',
    });
  }

  async assignProductsToDelivery(orderId, assignmentData) {
    return await this.makeRequest(`/marketplace/orders/${orderId}/assign-products`, {
      method: 'POST',
      body: assignmentData,
    });
  }

  async generateDeliveryOtp(orderId) {
    return await this.makeRequest(`/marketplace/orders/${orderId}/generate-otp`, {
      method: 'POST',
    });
  }

  async submitProductReview(orderId, reviewData) {
    return await this.makeRequest(`/marketplace/orders/${orderId}/review`, {
      method: 'POST',
      body: reviewData,
    });
  }

  async uploadReviewPhoto(reviewId, photoData) {
    const formData = new FormData();
    formData.append('photo', {
      uri: photoData.uri,
      type: photoData.type || 'image/jpeg',
      name: photoData.name || 'review_photo.jpg',
    });

    return await this.makeRequest(`/marketplace/reviews/${reviewId}/photo`, {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Unified Delivery Tracking
  async getUnifiedDeliveryTracking(orderId, userRole) {
    return await this.makeRequest(`/marketplace/orders/${orderId}/tracking?role=${userRole}`);
  }

  // Marketplace Role Registration
  async registerMarketplaceRole(registrationData) {
    return await this.makeRequest('/marketplace/register-role', {
      method: 'POST',
      body: registrationData,
    });
  }

  async checkMarketplaceRoles(userId) {
    return await this.makeRequest(`/marketplace/user-roles/${userId}`);
  }

  async updateMarketplaceRole(roleId, updateData) {
    return await this.makeRequest(`/marketplace/roles/${roleId}`, {
      method: 'PUT',
      body: updateData,
    });
  }

  // Enhanced Delivery Management
  async getDeliveryPersonProfile(userId) {
    return await this.makeRequest(`/marketplace/delivery-persons/${userId}/profile`);
  }

  async updateDeliveryPersonStatus(userId, statusData) {
    return await this.makeRequest(`/marketplace/delivery-persons/${userId}/status`, {
      method: 'PUT',
      body: statusData,
    });
  }

  async getDeliveryEarnings(userId, period = 'month') {
    return await this.makeRequest(`/marketplace/delivery-persons/${userId}/earnings?period=${period}`);
  }

  // User Search for Delivery Contacts (using advanced search endpoint)
  async searchUsersAdvanced(searchParams) {
    const queryParams = new URLSearchParams(searchParams);
    return await this.makeRequest(`/users/search/advanced?${queryParams}`);
  }

  // Auto-seller detection and registration
  async getUserProducts(userId) {
    return await this.makeRequest(`/marketplace/products/user/${userId}`);
  }

  async autoRegisterAsSeller(userId) {
    return await this.makeRequest('/marketplace/auto-register-seller', {
      method: 'POST',
      body: { userId },
    });
  }

  async autoRegisterAsBuyer(userId) {
    return await this.makeRequest('/marketplace/auto-register-buyer', {
      method: 'POST',
      body: { userId },
    });
  }

  // Enhanced product details with ownership and buyer eligibility
  async getProductWithOwnership(productId) {
    return await this.makeRequest(`/marketplace/products/${productId}/details`);
  }

  // Check marketplace roles for a user
  async checkMarketplaceRoles(userId) {
    return await this.makeRequest(`/marketplace/user-roles/${userId}`);
  }

  // Product Reviews
  async getProductReviews(productId) {
    return await this.makeRequest(`/marketplace/reviews/product/${productId}`);
  }

  async getProductReviewStats(productId) {
    return await this.makeRequest(`/marketplace/reviews/product/${productId}/stats`);
  }

  async createReview(formData) {
    return await this.makeRequest('/marketplace/reviews', {
      method: 'POST',
      body: formData,
      isFormData: true,
    });
  }

  async getUserReviews() {
    return await this.makeRequest('/marketplace/reviews/my');
  }

  // Loan endpoints
  async getLoans(chamaId, limit = 20, offset = 0) {
    // 🔐 Security: Include user context for proper filtering on backend
    return await this.makeRequest(`/loans/?chamaId=${chamaId}&limit=${limit}&offset=${offset}&includeUserContext=true`);
  }

  async applyForLoan(loanData) {
    return await this.makeRequest('/loans/apply', {
      method: 'POST',
      body: loanData,
    });
  }

  async respondToGuaranteeRequest(loanId, response) {
    return await this.makeRequest(`/loans/${loanId}/guarantor-response`, {
      method: 'POST',
      body: response,
    });
  }

  async approveLoan(loanId, approvalData) {
    return await this.makeRequest(`/loans/${loanId}/approve`, {
      method: 'POST',
      body: approvalData,
    });
  }

  async makeLoanPayment(loanId, amount, paymentMethod) {
    return await this.makeRequest(`/loans/${loanId}/payments`, {
      method: 'POST',
      body: { amount, paymentMethod },
    });
  }

  // Chat endpoints
  async getChatRooms() {
    try {
      // console.log('🔄 ApiService.getChatRooms: Making request to /chat/rooms...');
      const result = await this.makeRequest('/chat/rooms');

      // console.log('🔍 ApiService.getChatRooms: Raw makeRequest result:', {
      //   success: result.success,
      //   dataType: typeof result.data,
      //   dataLength: Array.isArray(result.data) ? result.data.length : 'not array',
      //   hasData: !!result.data,
      //   firstRoom: result.data?.[0]?.name || 'no first room',
      //   responseKeys: Object.keys(result || {}),
      //   rawData: JSON.stringify(result).substring(0, 400)
      // });

      // Removed auto-join logic - users should join rooms explicitly when needed
      // This was causing 404 errors because the join endpoint was missing

      // console.log('🔍 ApiService.getChatRooms: Final result before return:', {
      //   success: result.success,
      //   dataType: typeof result.data,
      //   dataLength: Array.isArray(result.data) ? result.data.length : 'not array',
      //   finalData: JSON.stringify(result).substring(0, 400)
      // });

      return result;
    } catch (error) {
      // Handle CORS and network errors gracefully with fallback data (production-like scenario)
      const isCorsOrNetworkError = error.message.includes('CORS') ||
                                   error.message.includes('NetworkError') ||
                                   error.message.includes('Unable to connect to server') ||
                                   error.message.includes('fetch resource');

      if (isCorsOrNetworkError) {
        console.error('❌ Chat rooms API failed due to network/CORS issues');
        return { success: false, error: error.message, data: [] };
      }

      // Handle Content-Length header issues by trying to create default rooms
      if (error.message.includes('Content-Length') || error.message.includes('Server response error')) {
        console.warn('⚠️ Chat rooms API has backend Content-Length issues, trying to create default rooms...');

        // Try to create default rooms first
        try {
          await this.createDefaultChatRooms();
          // console.log('✅ Default rooms created, retrying chat rooms fetch...');

          // Retry getting rooms after creating defaults
          return await this.makeRequest('/chat/rooms');
        } catch (createError) {
          console.warn('⚠️ Could not create default rooms, using fallback data:', createError.message);
        }

        // PRIVACY: No fallback data - return empty result
        console.error('🚨 PRIVACY: Refusing to return fallback chat data - only real database data allowed');
        return {
          success: false,
          error: 'Unable to load chat rooms from server',
          data: [],
          serverError: true
        };
      }

      throw error;
    }
  }

  async createChatRoom(roomData) {
    return await this.makeRequest('/chat/rooms', {
      method: 'POST',
      body: roomData,
    });
  }

  async getChatMessages(roomId, limit = 50, offset = 0) {
    try {
      return await this.makeRequest(`/chat/rooms/${roomId}/messages?limit=${limit}&offset=${offset}`);
    } catch (error) {
      // Handle CORS and network issues with fallback messages
      const isCorsOrNetworkError = error.message.includes('CORS') ||
                                   error.message.includes('NetworkError') ||
                                   error.message.includes('Unable to connect to server') ||
                                   error.message.includes('fetch resource');

      if (isCorsOrNetworkError) {
        console.error(`❌ Network/CORS issue getting messages for ${roomId}`);
        return { success: false, error: error.message, data: [] };
      }

      // If user is not a member, try to join the room first
      if (error.message.includes('not a member of this chat room')) {
        // console.log(`🔗 User not member of ${roomId}, attempting to join...`);

        try {
          // Try to join the room
          await this.joinChatRoom(roomId);
          // console.log(`✅ Successfully joined room ${roomId}, retrying message fetch...`);

          // Retry getting messages after joining
          return await this.makeRequest(`/chat/rooms/${roomId}/messages?limit=${limit}&offset=${offset}`);
        } catch (joinError) {
          console.error(`❌ Failed to join room ${roomId}:`, joinError);
          throw error; // Throw original error if join fails
        }
      }

      throw error;
    }
  }

  async joinChatRoom(roomId) {
    return await this.makeRequest(`/chat/rooms/${roomId}/join`, {
      method: 'POST'
    });
  }

  async createDefaultChatRooms() {
    const defaultRooms = [
      {
        id: 'general-chat',
        name: 'General Discussion',
        type: 'group',
        description: 'General discussion for all VaultKe users'
      },
      {
        id: 'support-chat',
        name: 'Customer Support',
        type: 'support',
        description: 'Get help from our support team'
      }
    ];

    const createdRooms = [];
    for (const room of defaultRooms) {
      try {
        const result = await this.createChatRoom(room);
        if (result.success) {
          createdRooms.push(result.data);
          // console.log(`✅ Created default room: ${room.name}`);
        }
      } catch (error) {
        // Room might already exist, try to join it
        try {
          await this.joinChatRoom(room.id);
          // console.log(`🔗 Joined existing room: ${room.name}`);
        } catch (joinError) {
          console.warn(`⚠️ Could not create or join room ${room.name}:`, joinError.message);
        }
      }
    }

    return createdRooms;
  }

  async getChatRoomMembers(roomId) {
    return await this.makeRequest(`/chat/rooms/${roomId}/members`);
  }

  async getChatRoomDetails(roomId) {
    return await this.makeRequest(`/chat/rooms/${roomId}`);
  }

  async deleteChatRoom(roomId) {
    return await this.makeRequest(`/chat/rooms/${roomId}`, {
      method: 'DELETE',
    });
  }

  async clearChatRoom(roomId) {
    return await this.makeRequest(`/chat/rooms/${roomId}/clear`, {
      method: 'POST',
    });
  }

  async sendMessage(roomId, messageData) {
    // Check if we have an image to upload
    if (messageData.type === 'image' && messageData.metadata?.imageUri) {
      return await this.sendMessageWithImage(roomId, messageData);
    }

    // Regular text message
    return await this.makeRequest(`/chat/rooms/${roomId}/messages`, {
      method: 'POST',
      body: messageData,
    });
  }

  async sendMessageWithImage(roomId, messageData) {
    const token = await this.getAuthToken();

    // Create FormData for file upload
    const formData = new FormData();

    // Add the image file
    if (messageData.metadata?.imageUri) {
      const imageUri = messageData.metadata.imageUri;

      // Check if it's a base64 data URL
      if (imageUri.startsWith('data:')) {
        // Handle base64 data URL
        const [header, base64Data] = imageUri.split(',');
        const mimeMatch = header.match(/data:([^;]+)/);
        const mimeType = mimeMatch ? mimeMatch[1] : 'image/jpeg';

        // Convert base64 to blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mimeType });

        const filename = `image_${Date.now()}.${mimeType.split('/')[1]}`;

        formData.append('image', blob, filename);
      } else {
        // Handle file URI (for actual file uploads)
        const filename = imageUri.split('/').pop() || 'image.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';

        const fileObject = {
          uri: imageUri,
          name: filename,
          type: type,
        };

        formData.append('image', fileObject);
      }
    }

    // Add message data
    formData.append('type', messageData.type);
    formData.append('content', messageData.content || '');

    // Add metadata without imageUri (since we're uploading the file separately)
    const cleanMetadata = { ...messageData.metadata };
    delete cleanMetadata.imageUri;
    formData.append('metadata', JSON.stringify(cleanMetadata));



    try {
      const response = await fetch(`${this.baseURL}/chat/rooms/${roomId}/messages`, {
        method: 'POST',
        headers: {
          // Don't set Content-Type for FormData - let browser set it with boundary
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: formData,
      });

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('Send message with image failed:', error);
      throw error;
    }
  }

  async createPrivateChat(recipientId, context = null) {
    const body = {
      type: 'private',
      recipientId: recipientId
    };

    // Add context information if provided (for product inquiries, etc.)
    if (context) {
      body.context = context;
    }

    return await this.makeRequest('/chat/rooms', {
      method: 'POST',
      body,
    });
  }

  async createSupportChat(userId, context = null) {
    const body = {
      type: 'support',
      recipientId: userId
    };

    // Add context information for support requests
    if (context) {
      body.context = context;
    }

    return await this.makeRequest('/chat/rooms', {
      method: 'POST',
      body: JSON.stringify(body)
    });
  }

  // Notification endpoints
  async getNotifications(limit = 20, offset = 0) {
    return await this.makeRequest(`/notifications/?limit=${limit}&offset=${offset}`);
  }

  async getUnreadNotificationCount() {
    return await this.makeRequest('/notifications/unread-count');
  }

  // Comprehensive data preloading endpoint
  async preloadAllUserData() {
    try {
      // console.log('🚀 Starting comprehensive data preload...');
      const startTime = Date.now();

      // Execute all data requests in parallel for maximum speed
      const [
        profileResponse,
        walletResponse,
        chamasResponse,
        transactionsResponse,
        notificationsResponse,
        unreadCountResponse,
        cartResponse,
        ordersResponse,
        chatRoomsResponse,
        productsResponse,
        learningCategoriesResponse,
        learningCoursesResponse,
        meetingsResponse,
        invitationsResponse,
        supportRequestsResponse,
      ] = await Promise.allSettled([
        this.getProfile(),
        this.getWalletBalance(),
        this.getUserChamas(50, 0), // Load more chamas
        this.getTransactions(100, 0), // Load more transactions
        this.getNotifications(100, 0), // Load more notifications
        this.getUnreadNotificationCount(),
        this.getCart(),
        this.getOrders({ limit: 50 }), // Load more orders
        this.getChatRooms(),
        this.getProducts({}, 100, 0), // Load more products
        this.getLearningCategories(),
        this.getLearningCourses({ limit: 50 }),
        this.getUserMeetings(50, 0),
        this.getUserInvitations(),
        this.getSupportRequests({ limit: 20 }),
      ]);

      const endTime = Date.now();
      // console.log(`✅ Data preload completed in ${endTime - startTime}ms`);

      // Process results and handle failures gracefully
      const results = {
        profile: this.processSettledResult(profileResponse, 'profile'),
        wallet: this.processSettledResult(walletResponse, 'wallet'),
        chamas: this.processSettledResult(chamasResponse, 'chamas'),
        transactions: this.processSettledResult(transactionsResponse, 'transactions'),
        notifications: this.processSettledResult(notificationsResponse, 'notifications'),
        unreadCount: this.processSettledResult(unreadCountResponse, 'unreadCount'),
        cart: this.processSettledResult(cartResponse, 'cart'),
        orders: this.processSettledResult(ordersResponse, 'orders'),
        chatRooms: this.processSettledResult(chatRoomsResponse, 'chatRooms'),
        products: this.processSettledResult(productsResponse, 'products'),
        learningCategories: this.processSettledResult(learningCategoriesResponse, 'learningCategories'),
        learningCourses: this.processSettledResult(learningCoursesResponse, 'learningCourses'),
        meetings: this.processSettledResult(meetingsResponse, 'meetings'),
        invitations: this.processSettledResult(invitationsResponse, 'invitations'),
        supportRequests: this.processSettledResult(supportRequestsResponse, 'supportRequests'),
      };

      // Calculate success rate
      const totalRequests = 15;
      const successfulRequests = Object.values(results).filter(r => r.success).length;
      const successRate = (successfulRequests / totalRequests) * 100;

      // console.log(`📊 Preload success rate: ${successRate.toFixed(1)}% (${successfulRequests}/${totalRequests})`);

      return {
        success: true,
        data: results,
        meta: {
          totalRequests,
          successfulRequests,
          successRate,
          loadTime: endTime - startTime,
        }
      };

    } catch (error) {
      console.error('❌ Comprehensive data preload failed:', error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }
  }

  // Helper method to process Promise.allSettled results
  processSettledResult(settledResult, dataType) {
    if (settledResult.status === 'fulfilled') {
      const result = settledResult.value;
      if (result.success) {
        // console.log(`✅ ${dataType} loaded successfully`);
        return result;
      } else {
        console.warn(`⚠️ ${dataType} API returned unsuccessful result:`, result);
        return { success: false, error: result.error || 'API returned unsuccessful result', data: null };
      }
    } else {
      console.error(`❌ ${dataType} failed to load:`, settledResult.reason);
      return { success: false, error: settledResult.reason.message, data: null };
    }
  }

  // Support endpoints
  async createSupportRequest(supportRequest) {
    return await this.makeRequest('/support/requests', {
      method: 'POST',
      body: JSON.stringify(supportRequest)
    });
  }

  async getSupportRequests(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.makeRequest(`/support/requests?${queryString}`);
  }

  async updateSupportRequest(requestId, updateData) {
    return await this.makeRequest(`/support/requests/${requestId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }

  async createTestSupportRequest() {
    return await this.makeRequest('/support/test-request', {
      method: 'POST',
      body: JSON.stringify({})
    });
  }

  // Security endpoints
  async changePassword(passwordData) {
    return await this.makeRequest('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(passwordData)
    });
  }

  async getLoginHistory(limit = 50, offset = 0) {
    return await this.makeRequest(`/auth/login-history?limit=${limit}&offset=${offset}`);
  }

  async logoutAllDevices() {
    return await this.makeRequest('/auth/logout-all-devices', {
      method: 'POST'
    });
  }

  async logoutSpecificDevice(sessionId) {
    return await this.makeRequest(`/auth/logout-device/${sessionId}`, {
      method: 'POST'
    });
  }

  async markNotificationAsRead(notificationId) {
    return await this.makeRequest(`/notifications/${notificationId}/read`, {
      method: 'PUT',  // Fixed: backend expects PUT, not POST
    });
  }

  async markAllNotificationsAsRead() {
    return await this.makeRequest('/notifications/read-all', {
      method: 'POST',
    });
  }

  // Notification preferences endpoints
  async getNotificationPreferences() {
    return await this.makeRequest('/notifications/preferences');
  }

  async updateNotificationPreferences(preferences) {
    return await this.makeRequest('/notifications/preferences', {
      method: 'PUT',
      body: preferences,
    });
  }

  async getNotificationSounds() {
    return await this.makeRequest('/notifications/sounds');
  }

  async deleteNotification(notificationId) {
    console.log('🗑️ API: deleteNotification called with ID:', notificationId);
    console.log('🗑️ API: Making DELETE request to:', `/notifications/${notificationId}`);

    try {
      const result = await this.makeRequest(`/notifications/${notificationId}`, {
        method: 'DELETE',
      });

      console.log('🗑️ API: Delete request result:', result);
      return result;
    } catch (error) {
      console.error('🗑️ API: Delete request failed:', error);
      throw error;
    }
  }

  // Learning API methods
  async getLearningCategories() {
    return await this.makeRequest('/learning/categories');
  }

  async getLearningCourses(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/learning/courses?${queryString}` : '/learning/courses';
    return await this.makeRequest(url);
  }

  async getLearningCourse(courseId) {
    return await this.makeRequest(`/learning/courses/${courseId}`);
  }

  async getLearningCategory(categoryId) {
    return await this.makeRequest(`/learning/categories/${categoryId}`);
  }

  async startCourse(courseId) {
    return await this.makeRequest(`/learning/courses/${courseId}/start`, {
      method: 'POST',
    });
  }

  // Admin Learning API methods
  async createLearningCategory(categoryData) {
    return await this.makeRequest('/learning/admin/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData),
    });
  }

  async updateLearningCategory(categoryId, categoryData) {
    return await this.makeRequest(`/learning/admin/categories/${categoryId}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData),
    });
  }

  async createLearningCourse(courseData) {
    return await this.makeRequest('/learning/admin/courses', {
      method: 'POST',
      body: JSON.stringify(courseData),
    });
  }

  async updateLearningCourse(courseId, courseData) {
    return await this.makeRequest(`/learning/admin/courses/${courseId}`, {
      method: 'PUT',
      body: JSON.stringify(courseData),
    });
  }

  async deleteLearningCourse(courseId) {
    return await this.makeRequest(`/learning/admin/courses/${courseId}`, {
      method: 'DELETE',
    });
  }

  async submitQuizResults(courseId, results) {
    return await this.makeRequest(`/learning/courses/${courseId}/submit-quiz`, {
      method: 'POST',
      body: JSON.stringify(results),
    });
  }

  // Learning content upload methods
  async uploadLearningImage(imageData) {
    // console.log('🖼️ Uploading image:', imageData);

    const formData = new FormData();

    if (imageData.file) {
      // Handle File object (web) - this is the actual File from input
      // console.log('📁 Web file upload:', imageData.file);
      formData.append('image', imageData.file, imageData.file.name);
      // console.log('📤 FormData created with web file object');
    } else if (imageData.uri) {
      // Handle file URI (React Native)
      const filename = imageData.uri.split('/').pop() || 'image.jpg';
      const match = /\.(\w+)$/.exec(filename);
      const type = match ? `image/${match[1]}` : 'image/jpeg';

      // console.log('📁 Mobile file details:', { filename, type, uri: imageData.uri });

      // For React Native, create proper file object
      const fileObject = {
        uri: imageData.uri,
        name: filename,
        type: type,
      };

      formData.append('image', fileObject);
      // console.log('📤 FormData created with mobile file object');
    } else {
      console.error('❌ Invalid image data:', imageData);
      throw new Error('No valid image data provided');
    }

    // For file uploads, let the browser set Content-Type with boundary
    return await this.makeRequest('/learning/upload/image', {
      method: 'POST',
      body: formData,
      // Don't set headers - let browser handle multipart/form-data boundary
    });
  }

  async uploadLearningVideo(videoData) {
    const formData = new FormData();

    if (videoData.uri) {
      // Handle file URI (React Native)
      const filename = videoData.uri.split('/').pop() || 'video.mp4';
      const match = /\.(\w+)$/.exec(filename);
      const type = match ? `video/${match[1]}` : 'video/mp4';

      formData.append('video', {
        uri: videoData.uri,
        name: filename,
        type: type,
      });
    } else if (videoData.file) {
      // Handle File object (web)
      formData.append('video', videoData.file);
    }

    return await this.makeRequest('/learning/upload/video', {
      method: 'POST',
      body: formData,
    });
  }

  async uploadLearningDocument(documentData) {
    const formData = new FormData();

    if (documentData.uri) {
      // Handle file URI (React Native)
      const filename = documentData.uri.split('/').pop() || 'document.pdf';
      const match = /\.(\w+)$/.exec(filename);
      const type = documentData.mimeType || 'application/pdf';

      formData.append('document', {
        uri: documentData.uri,
        name: filename,
        type: type,
      });
    } else if (documentData.file) {
      // Handle File object (web)
      formData.append('document', documentData.file);
    }

    return await this.makeRequest('/learning/upload/document', {
      method: 'POST',
      body: formData,
    });
  }

  async updateLearningCategory(categoryId, categoryData) {
    return await this.makeRequest(`/learning/admin/categories/${categoryId}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData),
    });
  }

  async deleteLearningCategory(categoryId) {
    return await this.makeRequest(`/learning/admin/categories/${categoryId}`, {
      method: 'DELETE',
    });
  }

  // REAL: Learning Analytics API method
  async getLearningAnalytics() {
    // console.log('📚 REAL: Fetching learning analytics...');
    try {
      // Try the dedicated learning analytics endpoint first
      let response = await this.makeRequest('/learning/admin/analytics');

      // If dedicated endpoint doesn't exist, build analytics from multiple endpoints
      if (!response.success && response.status === 404) {
        // console.log('📚 REAL: Building learning analytics from multiple endpoints...');
        response = await this.buildLearningAnalytics();
      }

      // console.log('📚 REAL: Learning analytics response:', {
      //   success: response.success,
      //   hasData: !!response.data,
      // });

      return response;
    } catch (error) {
      console.error('❌ REAL: Learning analytics failed:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  // REAL: Build learning analytics from existing endpoints
  async buildLearningAnalytics() {
    console.log('📚 REAL: Building comprehensive learning analytics...');
    try {
      // Fetch data from multiple endpoints in parallel
      const [categoriesResponse, coursesResponse] = await Promise.all([
        this.getLearningCategories(),
        this.getLearningCourses({ limit: 1000 })
      ]);

      // console.log('📚 REAL: Raw learning data:', {
      //   categories: categoriesResponse.success ? categoriesResponse.data?.length : 'failed',
      //   courses: coursesResponse.success ? coursesResponse.data?.length : 'failed'
      // });

      // Calculate analytics from real data
      const analytics = this.calculateLearningAnalytics(
        categoriesResponse.data || [],
        coursesResponse.data || []
      );

      return {
        success: true,
        data: analytics,
        source: 'calculated'
      };
    } catch (error) {
      console.error('❌ REAL: Failed to build learning analytics:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  // REAL: Calculate learning analytics from real data
  calculateLearningAnalytics(categories, courses) {
    // console.log('📚 REAL: Calculating learning analytics from real data...');

    // Calculate category metrics
    const categoryMetrics = {
      total: categories.length,
      active: categories.filter(c => c.is_active !== false).length,
      inactive: categories.filter(c => c.is_active === false).length
    };

    // Calculate course metrics
    const courseMetrics = {
      total: courses.length,
      published: courses.filter(c => c.status === 'published').length,
      draft: courses.filter(c => c.status === 'draft').length,
      archived: courses.filter(c => c.status === 'archived').length
    };

    // Calculate course distribution by category
    const coursesByCategory = categories.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      courseCount: courses.filter(c => c.category_id === category.id).length
    }));

    const analytics = {
      generatedAt: new Date().toISOString(),
      categories: categoryMetrics,
      courses: courseMetrics,
      distribution: coursesByCategory,
      summary: {
        totalCategories: categoryMetrics.total,
        totalCourses: courseMetrics.total,
        publishedCourses: courseMetrics.published,
        activeCategories: categoryMetrics.active,
        completionRate: courseMetrics.total > 0 ? (courseMetrics.published / courseMetrics.total * 100).toFixed(1) : 0
      }
    };

    // console.log('📚 REAL: Learning analytics calculated:', {
    //   categories: categoryMetrics.total,
    //   courses: courseMetrics.total,
    //   published: courseMetrics.published,
    //   completionRate: analytics.summary.completionRate
    // });

    return analytics;
  }



  // M-Pesa endpoints
  async initiateMpesaPayment(phoneNumber, amount, accountReference, description) {
    return await this.makeRequest('/payments/mpesa/stk', {
      method: 'POST',
      body: {
        phoneNumber,
        amount,
        accountReference,
        transactionDesc: description,
      },
    });
  }

  async checkMpesaStatus(checkoutRequestId) {
    return await this.makeRequest(`/payments/mpesa/status/${checkoutRequestId}`);
  }



  // Meeting endpoints
  async getMeetings(chamaId, limit = 20, offset = 0) {
    return await this.makeRequest(`/meetings/?chamaId=${chamaId}&limit=${limit}&offset=${offset}`);
  }

  // Get all meetings for the current user across all chamas they belong to
  async getUserMeetings(limit = 50, offset = 0) {
    try {
      // Try the dedicated user meetings endpoint first
      const response = await this.makeRequest(`/meetings/user?limit=${limit}&offset=${offset}`);

      // Check if the response actually has data (not just a placeholder message)
      if (response.success && response.data && Array.isArray(response.data)) {
        return response;
      } else {
        // If no data or just a placeholder message, use fallback
        // console.log('📅 User meetings endpoint returned no data, using chama-based fallback');
        throw new Error('No data in user meetings response');
      }
    } catch (error) {
      // console.log('📅 User meetings endpoint not available, using chama-based fallback');

      // Fallback: Get user's chamas first, then get meetings for each chama
      try {
        // console.log('📅 Getting user chamas for meetings fallback...');
        const chamasResponse = await this.getUserChamas();
        if (!chamasResponse.success || !chamasResponse.data) {
          // console.log('📅 No chamas found for user');
          return {
            success: true,
            data: [],
            message: 'No chamas found for user',
            meta: { total: 0, limit, offset }
          };
        }

        // Get meetings from all user's chamas
        const allMeetings = [];
        const chamas = chamasResponse.data;
        // console.log(`📅 Found ${chamas.length} chamas, getting meetings for each...`);

        for (const chama of chamas) {
          try {
            // console.log(`📅 Getting meetings for chama: ${chama.name} (${chama.id})`);
            const meetingsResponse = await this.getMeetings(chama.id);
            if (meetingsResponse.success && meetingsResponse.data) {
              // Add chama name to each meeting for identification
              const meetingsWithChama = meetingsResponse.data.map(meeting => ({
                ...meeting,
                chamaName: chama.name,
                chamaId: chama.id
              }));
              allMeetings.push(...meetingsWithChama);
              // console.log(`📅 Added ${meetingsWithChama.length} meetings from ${chama.name}`);
            }
          } catch (chamaError) {
            // console.log(`📅 Failed to get meetings for chama ${chama.name}:`, chamaError);
            // Continue with other chamas even if one fails
          }
        }

        // Sort meetings by date (newest first)
        allMeetings.sort((a, b) => {
          const dateA = new Date(a.scheduledAt || a.date);
          const dateB = new Date(b.scheduledAt || b.date);
          return dateB - dateA;
        });

        // console.log(`📅 Total meetings found: ${allMeetings.length}`);

        // Apply pagination
        const paginatedMeetings = allMeetings.slice(offset, offset + limit);

        return {
          success: true,
          data: paginatedMeetings,
          message: `Found ${allMeetings.length} meetings from ${chamas.length} chamas`,
          meta: {
            total: allMeetings.length,
            limit,
            offset,
            chamasCount: chamas.length
          }
        };
      } catch (fallbackError) {
        console.error('📅 Chama-based fallback also failed:', fallbackError);
      }

      // If all else fails, return empty result
      return {
        success: true,
        data: [],
        message: 'No meetings found',
        meta: { total: 0, limit, offset }
      };
    }
  }

  async createMeeting(meetingData) {
    return await this.makeRequest('/meetings/', {
      method: 'POST',
      body: meetingData,
    });
  }

  // LiveKit-enabled meeting endpoints
  async createLiveKitMeeting(meetingData) {
    return await this.makeRequest('/meetings/livekit', {
      method: 'POST',
      body: meetingData,
    });
  }

  async joinLiveKitMeeting(meetingId, userRole = 'member') {
    return await this.makeRequest(`/meetings/${meetingId}/join-livekit`, {
      method: 'POST',
      body: { userRole },
    });
  }

  async joinJitsiMeeting(meetingId, userRole = 'member') {
    return await this.makeRequest(`/meetings/${meetingId}/join-jitsi`, {
      method: 'POST',
      body: { userRole },
    });
  }

  async startMeeting(meetingId) {
    return await this.makeRequest(`/meetings/${meetingId}/start`, {
      method: 'POST',
    });
  }

  async endMeeting(meetingId) {
    return await this.makeRequest(`/meetings/${meetingId}/end`, {
      method: 'POST',
    });
  }

  async markMeetingAttendance(meetingId, attendanceData) {
    return await this.makeRequest(`/meetings/${meetingId}/attendance`, {
      method: 'POST',
      body: attendanceData,
    });
  }

  async getMeetingAttendance(meetingId) {
    return await this.makeRequest(`/meetings/${meetingId}/attendance`);
  }

  async updateMeeting(meetingId, meetingData) {
    return await this.makeRequest(`/meetings/${meetingId}`, {
      method: 'PUT',
      body: meetingData,
    });
  }

  async joinMeeting(meetingId) {
    return await this.makeRequest(`/meetings/${meetingId}/join`, {
      method: 'POST',
    });
  }

  // Merry-Go-Round endpoints
  async getMerryGoRounds(chamaId, limit = 20, offset = 0) {
    return await this.makeRequest(`/merry-go-rounds/?chamaId=${chamaId}&limit=${limit}&offset=${offset}`);
  }

  async createMerryGoRound(merryGoRoundData) {
    return await this.makeRequest('/merry-go-rounds/', {
      method: 'POST',
      body: merryGoRoundData,
    });
  }

  async joinMerryGoRound(merryGoRoundId) {
    return await this.makeRequest(`/merry-go-rounds/${merryGoRoundId}/join`, {
      method: 'POST',
    });
  }

  // Welfare endpoints
  async getWelfareRequests(chamaId, limit = 20, offset = 0) {
    // console.log('🔍 API: Getting welfare requests for chama:', chamaId);
    // const url = `/welfare/?chamaId=${chamaId}&limit=${limit}&offset=${offset}`;
    // console.log('🔍 API: Request URL:', url);

    const result = await this.makeRequest(url);
    // console.log('🔍 API: Welfare requests response:', result);

    return result;
  }

  async createWelfareRequest(welfareData) {
    return await this.makeRequest('/welfare/', {
      method: 'POST',
      body: welfareData,
    });
  }

  async voteOnWelfareRequest(requestId, voteData) {
    // console.log('🗳️ API: Voting on welfare request:', requestId, 'with data:', voteData);
    // console.log('🗳️ API: Full URL will be:', `${this.baseURL}/welfare/${requestId}/vote`);
    // console.log('🗳️ API: Request body:', JSON.stringify(voteData, null, 2));

    try {
      // console.log('🗳️ API: About to call makeRequest...');
      const result = await this.makeRequest(`/welfare/${requestId}/vote`, {
        method: 'POST',
        body: voteData,
      });

      // console.log('🗳️ API: Vote response (success):', result);
      return result;
    } catch (error) {
      console.error('🗳️ API: Vote request failed with error:', error);
      console.error('🗳️ API: Error message:', error.message);
      console.error('🗳️ API: Error type:', typeof error);
      console.error('🗳️ API: Error stack:', error.stack);

      // Extract status code from error message
      let status = 500;
      if (error.message.includes('400')) status = 400;
      else if (error.message.includes('401')) status = 401;
      else if (error.message.includes('403')) status = 403;
      else if (error.message.includes('404')) status = 404;

      // console.log('🗳️ API: Extracted status:', status);

      // Return a proper error response instead of throwing
      const errorResponse = {
        success: false,
        error: error.message,
        status: status
      };

      // console.log('🗳️ API: Returning error response:', errorResponse);
      return errorResponse;
    }
  }

  async updateWelfareRequest(requestId, status) {
    return await this.makeRequest(`/welfare/${requestId}`, {
      method: 'PUT',
      body: { status },
    });
  }

  async contributeToWelfare(contributionData) {
    return await this.makeRequest('/welfare/contribute', {
      method: 'POST',
      body: contributionData,
    });
  }

  async getWelfareContributions(requestId) {
    return await this.makeRequest(`/welfare/${requestId}/contributions`);
  }

  // Loan application endpoints
  async createLoanApplication(loanData) {
    return await this.makeRequest('/loans/apply', {
      method: 'POST',
      body: loanData,
    });
  }

  async getLoanApplications(chamaId, status = null) {
    const params = new URLSearchParams({ chamaId });
    if (status) params.append('status', status);
    return await this.makeRequest(`/loans/?${params.toString()}`);
  }

  async getLoanApplication(loanId) {
    return await this.makeRequest(`/loans/${loanId}`);
  }

  async respondToGuarantorRequest(loanId, response) {
    return await this.makeRequest(`/loans/${loanId}/guarantor-response`, {
      method: 'POST',
      body: { response }, // 'accept' or 'reject'
    });
  }

  async approveLoan(loanId, approvalData) {
    return await this.makeRequest(`/loans/${loanId}/approve`, {
      method: 'POST',
      body: approvalData,
    });
  }

  async rejectLoan(loanId, reason) {
    return await this.makeRequest(`/loans/${loanId}/reject`, {
      method: 'POST',
      body: { reason },
    });
  }

  async disburseLoan(loanId) {
    return await this.makeRequest(`/loans/${loanId}/disburse`, {
      method: 'POST',
    });
  }

  async getGuarantorRequests(userId) {
    return await this.makeRequest(`/loans/guarantor-requests?userId=${userId}`);
  }

  // ===== SHARES & DIVIDENDS API METHODS =====

  // Shares endpoints
  async createShares(chamaId, shareData) {
    return await this.makeRequest(`/chamas/${chamaId}/shares`, {
      method: 'POST',
      body: shareData,
    });
  }

  async getChamaShares(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/shares?${params.toString()}`);
  }

  async getMemberShares(chamaId, memberId) {
    return await this.makeRequest(`/chamas/${chamaId}/shares/members/${memberId}`);
  }

  async getChamaSharesSummary(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/shares/summary`);
  }

  async updateShares(chamaId, shareId, updateData) {
    return await this.makeRequest(`/chamas/${chamaId}/shares/${shareId}`, {
      method: 'PUT',
      body: updateData,
    });
  }

  async getShareTransactions(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/shares/transactions?${params.toString()}`);
  }

  // Dividends endpoints
  async declareDividend(chamaId, dividendData) {
    return await this.makeRequest(`/chamas/${chamaId}/dividends`, {
      method: 'POST',
      body: dividendData,
    });
  }

  async getChamaDividendDeclarations(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/dividends?${params.toString()}`);
  }

  async getDividendDeclarationDetails(chamaId, declarationId) {
    return await this.makeRequest(`/chamas/${chamaId}/dividends/${declarationId}`);
  }

  async approveDividend(chamaId, declarationId) {
    return await this.makeRequest(`/chamas/${chamaId}/dividends/${declarationId}/approve`, {
      method: 'POST',
    });
  }

  async processDividendPayments(chamaId, declarationId, paymentData) {
    return await this.makeRequest(`/chamas/${chamaId}/dividends/${declarationId}/process`, {
      method: 'POST',
      body: paymentData,
    });
  }

  async getMemberDividendHistory(chamaId, memberId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/dividends/members/${memberId}/history?${params.toString()}`);
  }

  async getMyDividendHistory(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/dividends/my-history?${params.toString()}`);
  }

  // ===== VOTING API METHODS (Using Old Vote System) =====

  // Vote endpoints using existing vote tables
  async createVote(chamaId, voteData) {
    return await this.makeRequest(`/chamas/${chamaId}/votes`, {
      method: 'POST',
      body: voteData,
    });
  }

  async getChamaVotes(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/votes?${params.toString()}`);
  }

  async getActiveVotes(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/votes/active`);
  }

  async getVoteResults(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/votes/results`);
  }

  async getVoteDetails(chamaId, voteId) {
    return await this.makeRequest(`/chamas/${chamaId}/votes/${voteId}`);
  }

  async castVoteOnItem(chamaId, voteId, voteData) {
    return await this.makeRequest(`/chamas/${chamaId}/votes/${voteId}/vote`, {
      method: 'POST',
      body: voteData,
    });
  }

  async createRoleEscalationVote(chamaId, escalationData) {
    return await this.makeRequest(`/chamas/${chamaId}/votes/role-escalation`, {
      method: 'POST',
      body: escalationData,
    });
  }

  // Legacy methods for backward compatibility (redirect to new methods)
  async createPoll(chamaId, pollData) {
    return await this.createVote(chamaId, pollData);
  }

  async getChamaPolls(chamaId, limit = 50, offset = 0) {
    return await this.getChamaVotes(chamaId, limit, offset);
  }

  async getActivePolls(chamaId) {
    return await this.getActiveVotes(chamaId);
  }

  async getPollResults(chamaId) {
    return await this.getVoteResults(chamaId);
  }

  async getPollDetails(chamaId, pollId) {
    return await this.getVoteDetails(chamaId, pollId);
  }

  async castVote(chamaId, pollId, voteData) {
    return await this.castVoteOnItem(chamaId, pollId, voteData);
  }

  async createRoleEscalationPoll(chamaId, escalationData) {
    return await this.createRoleEscalationVote(chamaId, escalationData);
  }

  // Note: getChamaMembers is already defined above at line 440
  // This duplicate method was causing issues by overriding the correct one
  async getChamaMembersForPolls(chamaId) {
    return await this.makeRequest(`/chamas/${chamaId}/polls/members`);
  }

  // ===== ACCOUNT MANAGEMENT API METHODS =====

  // Disbursements endpoints
  async getDisbursementBatches(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/disbursements?${params.toString()}`);
  }

  async createDisbursementBatch(chamaId, batchData) {
    return await this.makeRequest(`/chamas/${chamaId}/disbursements`, {
      method: 'POST',
      body: batchData,
    });
  }

  async approveDisbursementBatch(chamaId, batchId) {
    return await this.makeRequest(`/chamas/${chamaId}/disbursements/${batchId}/approve`, {
      method: 'POST',
    });
  }

  async processDisbursementBatch(chamaId, batchId, processData) {
    return await this.makeRequest(`/chamas/${chamaId}/disbursements/${batchId}/process`, {
      method: 'POST',
      body: processData,
    });
  }

  // Transparency endpoints
  async getTransparencyLog(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/transparency?${params.toString()}`);
  }

  // Reports endpoints
  async getFinancialReports(chamaId, limit = 50, offset = 0) {
    const params = new URLSearchParams({ limit: limit.toString(), offset: offset.toString() });
    return await this.makeRequest(`/chamas/${chamaId}/reports?${params.toString()}`);
  }

  async generateFinancialReport(chamaId, reportData) {
    return await this.makeRequest(`/chamas/${chamaId}/reports/generate`, {
      method: 'POST',
      body: reportData,
    });
  }

  async downloadFinancialReport(chamaId, reportId) {
    return await this.makeRequest(`/chamas/${chamaId}/reports/${reportId}/download`, {
      method: 'GET',
    });
  }

  // Member role management
  async getMemberRole(chamaId, memberId) {
    return await this.makeRequest(`/chamas/${chamaId}/members/${memberId}/role`);
  }

  async updateMemberRole(chamaId, memberId, roleData) {
    return await this.makeRequest(`/chamas/${chamaId}/members/${memberId}/role`, {
      method: 'PUT',
      body: roleData,
    });
  }

  // ==================== RECEIPT ENDPOINTS ====================

  async getTransactionReceipt(transactionId, format = 'json') {
    return await this.makeRequest(`/receipts/transactions/${transactionId}?format=${format}`, {
      method: 'GET',
    });
  }

  async downloadTransactionReceipt(transactionId, format = 'pdf') {
    return await this.makeRequest(`/receipts/transactions/${transactionId}/download?format=${format}`, {
      method: 'GET',
    });
  }

  // ==================== NOTIFICATION PREFERENCES ENDPOINTS ====================

  // Notification Preferences API methods
  async getNotificationPreferences() {
    try {
      return await this.makeRequest('/notifications/preferences', {
        method: 'GET',
      });
    } catch (error) {
      console.log('Using mock notification preferences service');
      return await mockSettingsService.getNotificationPreferences();
    }
  }

  async updateNotificationPreferences(preferences) {
    try {
      return await this.makeRequest('/notifications/preferences', {
        method: 'PUT',
        body: preferences,
      });
    } catch (error) {
      console.log('Using mock notification preferences service');
      return await mockSettingsService.updateNotificationPreferences(preferences);
    }
  }

  async getAvailableNotificationSounds() {
    return await this.makeRequest('/notifications/sounds', {
      method: 'GET',
    });
  }

  async testNotificationSound(soundId) {
    return await this.makeRequest('/notifications/test-sound', {
      method: 'POST',
      body: { sound_id: soundId },
    });
  }

  // Legacy notification settings methods (for backward compatibility)
  async getNotificationSettings() {
    return await this.makeRequest('/notifications/settings', {
      method: 'GET',
    });
  }

  async updateNotificationSettings(settings) {
    return await this.makeRequest('/notifications/settings', {
      method: 'PUT',
      body: settings,
    });
  }

  async updateUserSettings(settings) {
    try {
      // Handle different types of settings
      const results = {};

      // Update notification settings
      if (settings.notifications) {
        const notificationResult = await this.updateNotificationPreferences(settings.notifications);
        results.notifications = notificationResult;
      }

      // Update privacy settings
      if (settings.privacy) {
        const privacyResult = await this.updatePrivacySettings(settings.privacy);
        results.privacy = privacyResult;
      }

      // Update security settings
      if (settings.security) {
        const securityResult = await this.updateSecuritySettings(settings.security);
        results.security = securityResult;
      }

      // Update preferences
      if (settings.preferences) {
        const preferencesResult = await this.updateUserPreferences(settings.preferences);
        results.preferences = preferencesResult;
      }

      return {
        success: true,
        message: 'Settings updated successfully',
        data: results
      };
    } catch (error) {
      console.error('Failed to update user settings:', error);
      return {
        success: false,
        error: 'Failed to update settings'
      };
    }
  }

  // Privacy settings
  async updatePrivacySettings(privacySettings) {
    try {
      return await this.makeRequest('/users/privacy-settings', {
        method: 'PUT',
        body: privacySettings,
      });
    } catch (error) {
      console.log('Using mock privacy settings service');
      return await mockSettingsService.updatePrivacySettings(privacySettings);
    }
  }

  async getPrivacySettings() {
    try {
      return await this.makeRequest('/users/privacy-settings');
    } catch (error) {
      console.log('Using mock privacy settings service');
      return await mockSettingsService.getPrivacySettings();
    }
  }

  // Security settings
  async updateSecuritySettings(securitySettings) {
    try {
      return await this.makeRequest('/users/security-settings', {
        method: 'PUT',
        body: securitySettings,
      });
    } catch (error) {
      console.log('Using mock security settings service');
      return await mockSettingsService.updateSecuritySettings(securitySettings);
    }
  }

  async getSecuritySettings() {
    try {
      return await this.makeRequest('/users/security-settings');
    } catch (error) {
      console.log('Using mock security settings service');
      return await mockSettingsService.getSecuritySettings();
    }
  }

  // User preferences
  async updateUserPreferences(preferences) {
    try {
      return await this.makeRequest('/users/preferences', {
        method: 'PUT',
        body: preferences,
      });
    } catch (error) {
      console.log('Using mock user preferences service');
      return await mockSettingsService.updateUserPreferences(preferences);
    }
  }

  async getUserPreferences() {
    try {
      return await this.makeRequest('/users/preferences');
    } catch (error) {
      console.log('Using mock user preferences service');
      return await mockSettingsService.getUserPreferences();
    }
  }

  // Account management
  async deleteAccount() {
    try {
      return await this.makeRequest('/users/delete-account', {
        method: 'DELETE',
      });
    } catch (error) {
      console.log('Using mock account management service');
      return await mockSettingsService.deleteAccount();
    }
  }

  async exportUserData() {
    try {
      return await this.makeRequest('/users/export-data');
    } catch (error) {
      console.log('Using mock data export service');
      return await mockSettingsService.exportUserData();
    }
  }
}

export default new ApiService();
