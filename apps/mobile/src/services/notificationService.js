import * as Notifications from 'expo-notifications';
import { Audio } from 'expo-av';
import { Platform, Vibration, AppState } from 'react-native';
import ApiService from './api';

class NotificationService {
  constructor() {
    this.currentSound = null;
    this.userPreferences = null;
    this.isInitialized = false;
    this.appState = AppState.currentState;
    this.isWeb = Platform.OS === 'web';

    console.log(`🌐 NotificationService: Platform detected as ${Platform.OS}`);

    if (this.isWeb) {
      console.log('🌐 Web platform detected - using web-compatible notification methods');
      this.setupWebNotificationHandler();
    } else {
      this.setupNotificationHandler();
    }

    this.setupAppStateListener();
  }

  // Setup app state listener to handle background/foreground transitions
  setupAppStateListener() {
    this.appStateListener = AppState.addEventListener('change', (nextAppState) => {
      console.log('🔄 App state changed from', this.appState, 'to', nextAppState);
      this.appState = nextAppState;
    });
  }

  // Web-compatible notification handler (no native APIs)
  setupWebNotificationHandler() {
    console.log('🌐 Setting up web-compatible notification handler');
    // Web notifications will be handled through browser APIs or polling
    // No native notification setup required
  }

  // Configure how notifications are handled when the app is in the foreground (mobile only)
  setupNotificationHandler() {
    Notifications.setNotificationHandler({
      handleNotification: async (notification) => {
        console.log('🔔 Handling notification:', notification.request.content.title);

        // Always load fresh user preferences for each notification
        await this.loadUserPreferences();

        const shouldPlaySound = this.shouldPlayCustomSound(notification);
        console.log('🔊 Should play custom sound:', shouldPlaySound);

        return {
          shouldShowAlert: true,
          shouldPlaySound: !shouldPlaySound, // Let us handle custom sounds if enabled
          shouldSetBadge: true,
        };
      },
    });

    // Listen for received notifications
    this.notificationListener = Notifications.addNotificationReceivedListener(
      this.handleNotificationReceived.bind(this)
    );

    // Listen for notification responses (when user taps notification)
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      this.handleNotificationResponse.bind(this)
    );

    // Set up background notification handling
    this.setupBackgroundNotificationHandling();

    console.log('✅ Notification handlers setup complete');
  }

  // Load user notification preferences
  async loadUserPreferences() {
    try {
      const response = await ApiService.getNotificationPreferences();
      if (response.success) {
        this.userPreferences = response.data.preferences;
        return this.userPreferences;
      }
    } catch (error) {
      console.warn('Failed to load notification preferences:', error);
    }
    return null;
  }

  // Check if we should play a custom sound for this notification
  shouldPlayCustomSound(notification) {
    if (!this.userPreferences) return false;
    
    // Check if sound is enabled
    if (!this.userPreferences.sound_enabled) return false;
    
    // Check if we have a custom sound selected
    if (!this.userPreferences.notification_sound_id) return false;
    
    return true;
  }

  // Handle notification received (when app is in foreground)
  async handleNotificationReceived(notification) {
    console.log('🔔 Notification received:', notification.request.content.title);

    // Always ensure we have fresh preferences
    await this.loadUserPreferences();

    // Handle sound and vibration
    await this.handleNotificationAlert(notification);
  }

  // Comprehensive notification alert handling
  async handleNotificationAlert(notification) {
    try {
      console.log('🚨 Processing notification alert...');

      // Always try to play sound first (most important)
      const soundPlayed = await this.handleNotificationSound(notification);

      // Handle vibration if enabled
      await this.handleNotificationVibration();

      console.log(`✅ Notification alert processed - Sound: ${soundPlayed ? 'Yes' : 'No'}, Vibration: ${this.userPreferences?.vibration_enabled ? 'Yes' : 'No'}`);

    } catch (error) {
      console.error('❌ Failed to process notification alert:', error);
      // Fallback to system defaults
      await this.playSystemNotificationSound();
      if (this.userPreferences?.vibration_enabled !== false) {
        Vibration.vibrate(400); // Default vibration
      }
    }
  }

  // Handle notification sound with multiple fallbacks
  async handleNotificationSound(notification) {
    try {
      // Check if sound is disabled
      if (this.userPreferences?.sound_enabled === false) {
        console.log('🔇 Sound disabled by user preferences');
        return false;
      }

      // Try custom sound first
      if (this.shouldPlayCustomSound(notification)) {
        console.log('🎵 Attempting to play custom sound...');
        const customSoundPlayed = await this.playCustomNotificationSound();
        if (customSoundPlayed) {
          return true;
        }
      }

      // Fallback to system sound
      console.log('🔊 Playing system notification sound...');
      await this.playSystemNotificationSound();
      return true;

    } catch (error) {
      console.error('❌ Failed to play notification sound:', error);
      return false;
    }
  }

  // Handle notification vibration
  async handleNotificationVibration() {
    try {
      if (this.userPreferences?.vibration_enabled !== false) {
        console.log('📳 Triggering vibration...');
        // Use a notification-appropriate vibration pattern
        Vibration.vibrate([0, 250, 100, 250]); // Short-pause-short-pause pattern
      } else {
        console.log('📳 Vibration disabled by user preferences');
      }
    } catch (error) {
      console.error('❌ Failed to trigger vibration:', error);
    }
  }

  // Handle notification response (when user taps notification)
  handleNotificationResponse(response) {
    console.log('🔔 Notification response:', response.notification.request.content.title);
    // Handle navigation or other actions based on notification data
    // This is where you could navigate to specific screens based on notification type
  }

  // Setup background notification handling
  setupBackgroundNotificationHandling() {
    if (this.isWeb) {
      console.log('🌐 Skipping background notification setup for web platform');
      return;
    }

    try {
      // Configure notification categories for better handling (mobile only)
      Notifications.setNotificationCategoryAsync('default', [
        {
          identifier: 'view',
          buttonTitle: 'View',
          options: { opensAppToForeground: true },
        },
        {
          identifier: 'dismiss',
          buttonTitle: 'Dismiss',
          options: { isDestructive: true },
        },
      ]);

      console.log('✅ Background notification handling configured');
    } catch (error) {
      console.error('❌ Failed to setup background notification handling:', error);
    }
  }

  // Play the user's selected notification sound
  async playCustomNotificationSound() {
    try {
      console.log('🎵 Starting custom notification sound playback...');

      // Stop any currently playing sound
      if (this.currentSound) {
        try {
          await this.currentSound.stopAsync();
          await this.currentSound.unloadAsync();
        } catch (stopError) {
          console.warn('Warning stopping previous sound:', stopError);
        }
        this.currentSound = null;
      }

      // Get the selected sound
      const soundId = this.userPreferences?.notification_sound_id;
      if (!soundId) {
        console.log('❌ No custom sound ID found');
        return false;
      }

      // Get available sounds to find the file path
      console.log('📡 Fetching available sounds...');
      const soundsResponse = await ApiService.getAvailableNotificationSounds();
      if (!soundsResponse.success) {
        console.log('❌ Failed to fetch available sounds');
        return false;
      }

      const selectedSound = soundsResponse.data.sounds?.find(s => s.id === soundId);
      if (!selectedSound || !selectedSound.file_path) {
        console.log('❌ Selected sound not found or has no file path');
        return false;
      }

      console.log('🎵 Playing custom sound:', selectedSound.name);

      // Configure audio mode for notification playback
      const audioConfig = {
        allowsRecordingIOS: false,
        staysActiveInBackground: true, // Allow background playback
        playsInSilentModeIOS: true, // CRUCIAL: Play even in silent mode
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      };

      // Add platform-specific interruption modes with valid values
      if (Platform.OS === 'ios') {
        audioConfig.interruptionModeIOS = Audio.INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS;
      } else {
        audioConfig.interruptionModeAndroid = Audio.INTERRUPTION_MODE_ANDROID_DUCK_OTHERS;
      }

      await Audio.setAudioModeAsync(audioConfig);

      // Create and play the sound
      const soundObject = new Audio.Sound();
      this.currentSound = soundObject;

      const soundUri = `http://localhost:8080${selectedSound.file_path}`;
      console.log('🔗 Loading sound from:', soundUri);

      await soundObject.loadAsync({
        uri: soundUri,
        shouldPlay: false,
        isLooping: false,
      });

      // Set volume based on user preference
      const volume = Math.max(0.1, Math.min(1.0, (this.userPreferences.volume_level || 80) / 100));
      await soundObject.setVolumeAsync(volume);
      console.log('🔊 Volume set to:', volume);

      // Play the sound
      await soundObject.playAsync();
      console.log('✅ Custom sound playback started');

      // Clean up when sound finishes
      soundObject.setOnPlaybackStatusUpdate((status) => {
        if (status.didJustFinish) {
          console.log('🎵 Custom sound finished playing');
          soundObject.unloadAsync().catch(console.error);
          if (this.currentSound === soundObject) {
            this.currentSound = null;
          }
        } else if (status.error) {
          console.error('🎵 Sound playback error:', status.error);
          soundObject.unloadAsync().catch(console.error);
          if (this.currentSound === soundObject) {
            this.currentSound = null;
          }
        }
      });

      return true;

    } catch (error) {
      console.error('❌ Failed to play custom notification sound:', error);
      return false;
    }
  }

  // Fallback to system notification sound
  async playSystemNotificationSound() {
    try {
      console.log('🔊 Playing system notification sound...');

      // Configure audio mode for system sound
      const audioConfig = {
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true, // CRUCIAL: Play even in silent mode
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      };

      // Add platform-specific interruption modes with valid values
      if (Platform.OS === 'ios') {
        audioConfig.interruptionModeIOS = Audio.INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS;
      } else {
        audioConfig.interruptionModeAndroid = Audio.INTERRUPTION_MODE_ANDROID_DUCK_OTHERS;
      }

      await Audio.setAudioModeAsync(audioConfig);

      if (Platform.OS === 'ios') {
        // Use iOS system notification sound
        const { sound } = await Audio.Sound.createAsync(
          { uri: 'system://notification' },
          { shouldPlay: true, volume: 0.8 }
        );

        // Clean up after playing
        setTimeout(() => {
          sound.unloadAsync().catch(console.error);
        }, 3000);

      } else {
        // For Android, let the system handle notification sound
        console.log('🔊 Using Android system notification handling');
        // The system will play the default notification sound
        // when shouldPlaySound is true in the notification handler
      }

      console.log('✅ System notification sound triggered');

    } catch (error) {
      console.error('❌ Failed to play system notification sound:', error);
      // Last resort: try to trigger any available sound
      try {
        Vibration.vibrate(200); // At least provide haptic feedback
      } catch (vibError) {
        console.error('❌ Even vibration failed:', vibError);
      }
    }
  }

  // Request notification permissions with comprehensive settings
  async requestPermissions(userInitiated = false) {
    if (this.isWeb) {
      console.log('🌐 Web platform: Using browser notification API');
      try {
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          console.log('🌐 Browser notification permission:', permission);
          return permission === 'granted';
        } else {
          console.warn('🌐 Browser does not support notifications');
          return false;
        }
      } catch (error) {
        console.warn('🌐 Browser notification request failed:', error);
        return false;
      }
    }

    try {
      console.log('🔐 Requesting notification permissions...', userInitiated ? '(user-initiated)' : '(automatic)');

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      console.log('📋 Current permission status:', existingStatus);

      let finalStatus = existingStatus;

      // Only request permissions if they're not already granted
      if (existingStatus !== 'granted') {
        // If this is not user-initiated, don't request permissions to avoid the error
        if (!userInitiated) {
          console.log('⚠️ Permissions not granted and not user-initiated - skipping request');
          return false;
        }

        console.log('🔐 Requesting new permissions (user-initiated)...');

        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowDisplayInCarPlay: true,
            allowCriticalAlerts: false, // Don't request critical alerts by default
            allowProvisional: false,
            allowAnnouncements: true,
          },
          android: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
          },
        });

        finalStatus = status;
        console.log('📋 New permission status:', finalStatus);
      }

      if (finalStatus !== 'granted') {
        console.warn('❌ Notification permissions not granted:', finalStatus);
        return false;
      }

      console.log('✅ Notification permissions granted');

      // Also request audio permissions for custom sounds (only if notifications are granted)
      try {
        await Audio.requestPermissionsAsync();
        console.log('✅ Audio permissions requested');
      } catch (audioError) {
        console.warn('⚠️ Audio permissions failed:', audioError);
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to request notification permissions:', error);
      return false;
    }
  }

  // Initialize the service (call this after app startup)
  async initialize() {
    if (this.isInitialized) return true;

    try {
      console.log('🚀 Initializing notification service...');

      // Check existing permissions but don't request them automatically
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      console.log('📋 Existing notification permissions:', existingStatus);

      if (existingStatus !== 'granted') {
        console.log('⚠️ Notification service initialized without permissions - will request when needed');
      }

      // Load user preferences
      await this.loadUserPreferences();

      // Set up audio session
      await this.setupAudioSession();

      // Set up notification channels (Android)
      await this.setupNotificationChannels();

      this.isInitialized = true;
      console.log('✅ Notification service initialized successfully');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
      return false;
    }
  }

  // Request permissions when user explicitly wants to enable notifications
  async requestPermissionsFromUser() {
    console.log('👤 User requesting notification permissions...');
    return await this.requestPermissions(true); // Mark as user-initiated
  }

  // Check if notifications are enabled
  async areNotificationsEnabled() {
    if (this.isWeb) {
      if ('Notification' in window) {
        return Notification.permission === 'granted';
      }
      return false;
    }

    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('❌ Failed to check notification permissions:', error);
      return false;
    }
  }

  // Get notification permission status with user-friendly message
  async getPermissionStatus() {
    try {
      const { status } = await Notifications.getPermissionsAsync();

      const statusMessages = {
        'granted': 'Notifications are enabled',
        'denied': 'Notifications are disabled. Please enable them in device settings.',
        'undetermined': 'Notification permissions not yet requested'
      };

      return {
        status,
        enabled: status === 'granted',
        message: statusMessages[status] || 'Unknown notification status'
      };
    } catch (error) {
      console.error('❌ Failed to get permission status:', error);
      return {
        status: 'error',
        enabled: false,
        message: 'Unable to check notification permissions'
      };
    }
  }

  // Setup audio session for reliable sound playbook
  async setupAudioSession() {
    if (this.isWeb) {
      console.log('🌐 Skipping audio session setup for web platform');
      return;
    }

    try {
      // Use valid iOS interruption mode constants (mobile only)
      const audioConfig = {
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true, // CRUCIAL for notifications
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      };

      // Add platform-specific interruption modes with valid values
      if (Platform.OS === 'ios') {
        // Use valid iOS interruption mode
        audioConfig.interruptionModeIOS = Audio.INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS;
      } else {
        // Use valid Android interruption mode
        audioConfig.interruptionModeAndroid = Audio.INTERRUPTION_MODE_ANDROID_DUCK_OTHERS;
      }

      await Audio.setAudioModeAsync(audioConfig);
      console.log('✅ Audio session configured for notifications');
    } catch (error) {
      console.error('❌ Failed to setup audio session:', error);
      // Try with minimal config as fallback
      try {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
        });
        console.log('✅ Audio session configured with fallback settings');
      } catch (fallbackError) {
        console.error('❌ Even fallback audio session failed:', fallbackError);
      }
    }
  }

  // Setup notification channels for Android
  async setupNotificationChannels() {
    if (this.isWeb) {
      console.log('🌐 Skipping notification channels setup for web platform');
      return;
    }

    try {
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Default notifications',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'default',
          enableVibrate: true,
          showBadge: true,
        });

        await Notifications.setNotificationChannelAsync('financial', {
          name: 'Financial alerts',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'default',
          enableVibrate: true,
          showBadge: true,
        });

        await Notifications.setNotificationChannelAsync('chama', {
          name: 'Chama notifications',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'default',
          enableVibrate: true,
          showBadge: true,
        });

        console.log('✅ Android notification channels configured');
      }
    } catch (error) {
      console.error('❌ Failed to setup notification channels:', error);
    }
  }

  // Schedule a local notification (for testing)
  async scheduleTestNotification(title, body, soundId = null) {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return;

      // If soundId is provided, temporarily update preferences for this test
      if (soundId) {
        const originalPrefs = this.userPreferences;
        this.userPreferences = {
          ...this.userPreferences,
          sound_enabled: true,
          notification_sound_id: soundId,
        };

        // Schedule the notification
        await Notifications.scheduleNotificationAsync({
          content: {
            title: title || 'Test Notification',
            body: body || 'This is a test notification with your selected sound.',
            sound: false, // We'll handle sound ourselves
          },
          trigger: { seconds: 1 },
        });

        // Restore original preferences after a delay
        setTimeout(() => {
          this.userPreferences = originalPrefs;
        }, 5000);
      } else {
        await Notifications.scheduleNotificationAsync({
          content: {
            title: title || 'Test Notification',
            body: body || 'This is a test notification.',
            sound: true, // Use system sound
          },
          trigger: { seconds: 1 },
        });
      }
    } catch (error) {
      console.error('Failed to schedule test notification:', error);
    }
  }

  // Enhanced test notification with better feedback
  async testNotificationWithSound(soundId) {
    try {
      console.log('🧪 Testing notification with sound ID:', soundId);

      // Ensure we have permissions first (user-initiated)
      const hasPermissions = await this.requestPermissionsFromUser();
      if (!hasPermissions) {
        console.warn('❌ No notification permissions for test');
        return false;
      }

      // Temporarily update preferences for testing
      const originalPrefs = this.userPreferences;
      this.userPreferences = {
        ...this.userPreferences,
        sound_enabled: true,
        vibration_enabled: true,
        notification_sound_id: soundId,
        volume_level: 80,
      };

      // Schedule test notification with comprehensive settings
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'VaultKe Test Notification',
          body: 'Testing your selected notification sound and vibration. This should ring and notify you!',
          sound: false, // We handle sound ourselves for custom sounds
          badge: 1,
          categoryIdentifier: 'default',
          data: {
            type: 'test',
            soundId: soundId,
            timestamp: Date.now()
          },
        },
        trigger: {
          seconds: 1,
          channelId: 'default', // For Android
        },
      });

      // Also trigger immediate sound and vibration for testing
      setTimeout(async () => {
        try {
          // Force play the sound immediately as backup
          await this.forcePlayNotificationSound(soundId);

          // Force vibration
          if (this.userPreferences?.vibration_enabled !== false) {
            Vibration.vibrate([0, 250, 100, 250]);
          }
        } catch (immediateError) {
          console.warn('⚠️ Immediate test feedback failed:', immediateError);
        }
      }, 500);

      // Restore original preferences after test
      setTimeout(() => {
        this.userPreferences = originalPrefs;
      }, 15000); // Give more time for the test

      console.log('✅ Test notification scheduled with immediate feedback');
      return true;

    } catch (error) {
      console.error('❌ Failed to test notification:', error);
      return false;
    }
  }

  // Force play notification sound (for testing)
  async forcePlayNotificationSound(soundId = null) {
    try {
      if (soundId) {
        // Temporarily set the sound ID
        const originalSoundId = this.userPreferences?.notification_sound_id;
        if (this.userPreferences) {
          this.userPreferences.notification_sound_id = soundId;
          this.userPreferences.sound_enabled = true;
        }

        const played = await this.playCustomNotificationSound();

        // Restore original sound ID
        if (this.userPreferences && originalSoundId) {
          this.userPreferences.notification_sound_id = originalSoundId;
        }

        return played;
      } else {
        return await this.playSystemNotificationSound();
      }
    } catch (error) {
      console.error('❌ Failed to force play notification sound:', error);
      return false;
    }
  }

  // Verify notification system is working properly
  async verifyNotificationSystem() {
    try {
      console.log('🔍 Verifying notification system...');

      const results = {
        permissions: false,
        audioSession: false,
        preferences: false,
        channels: false,
        overall: false
      };

      // Check permissions
      const { status } = await Notifications.getPermissionsAsync();
      results.permissions = status === 'granted';
      console.log('📋 Permissions:', results.permissions ? '✅' : '❌');

      // Check audio session
      try {
        await this.setupAudioSession();
        results.audioSession = true;
        console.log('🔊 Audio session:', '✅');
      } catch (audioError) {
        console.log('🔊 Audio session:', '❌', audioError.message);
      }

      // Check preferences
      await this.loadUserPreferences();
      results.preferences = this.userPreferences !== null;
      console.log('⚙️ Preferences:', results.preferences ? '✅' : '❌');

      // Check channels (Android)
      if (Platform.OS === 'android') {
        try {
          await this.setupNotificationChannels();
          results.channels = true;
          console.log('📢 Channels:', '✅');
        } catch (channelError) {
          console.log('📢 Channels:', '❌', channelError.message);
        }
      } else {
        results.channels = true; // Not needed on iOS
      }

      // Overall status
      results.overall = results.permissions && results.audioSession && results.preferences && results.channels;

      console.log('🎯 Notification system status:', results.overall ? '✅ READY' : '❌ ISSUES DETECTED');
      console.log('📊 Detailed results:', results);

      return results;

    } catch (error) {
      console.error('❌ Failed to verify notification system:', error);
      return { overall: false, error: error.message };
    }
  }

  // Clean up resources
  cleanup() {
    console.log('🧹 Cleaning up notification service...');

    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
      this.notificationListener = null;
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
      this.responseListener = null;
    }
    if (this.appStateListener) {
      this.appStateListener.remove();
      this.appStateListener = null;
    }
    if (this.currentSound) {
      this.currentSound.unloadAsync().catch(console.error);
      this.currentSound = null;
    }

    this.isInitialized = false;
    console.log('✅ Notification service cleanup complete');
  }
}

// Create and export a singleton instance
const notificationService = new NotificationService();
export default notificationService;
