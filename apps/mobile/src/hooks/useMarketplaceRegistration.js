import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useApp } from '../context/AppContext';
import ApiService from '../services/api';

const useMarketplaceRegistration = () => {
  const { user } = useApp();
  const [userRoles, setUserRoles] = useState({
    buyer: false,
    seller: false,
    delivery_person: false,
  });
  const [loading, setLoading] = useState(true);
  const [registrationModalVisible, setRegistrationModalVisible] = useState(false);
  const [requiredRole, setRequiredRole] = useState(null);
  const [triggerAction, setTriggerAction] = useState(null);

  // Load user's marketplace roles
  const loadUserRoles = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Check explicit marketplace roles
      const rolesResponse = await ApiService.checkMarketplaceRoles(user.id);
      let roles = rolesResponse.success ? rolesResponse.data : {
        buyer: false,
        seller: false,
        delivery_person: false,
      };

      // Auto-detect seller role based on product listings
      try {
        const productsResponse = await ApiService.getUserProducts(user.id);
        if (productsResponse.success && productsResponse.data && productsResponse.data.length > 0) {
          // User has products listed, automatically make them a seller
          roles.seller = true;

          // If they weren't explicitly registered as a seller, register them automatically
          if (!rolesResponse.success || !rolesResponse.data?.seller) {
            await ApiService.autoRegisterAsSeller(user.id);
          }
        }
      } catch (error) {
        console.log('Could not check user products for auto-seller detection:', error);
      }

      setUserRoles(roles);
    } catch (error) {
      console.error('Failed to load user roles:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    loadUserRoles();
  }, [loadUserRoles]);

  // Check if user has a specific role
  const hasRole = useCallback((role) => {
    return userRoles[role] === true;
  }, [userRoles]);

  // Check if user can perform an action (has required role)
  const canPerformAction = useCallback((requiredRole) => {
    return hasRole(requiredRole);
  }, [hasRole]);

  // Trigger registration modal for a specific role
  const requireRole = useCallback((role, action = null) => {
    if (hasRole(role)) {
      return true; // User already has the role
    }

    // Show registration modal
    setRequiredRole(role);
    setTriggerAction(action);
    setRegistrationModalVisible(true);
    return false; // User needs to register
  }, [hasRole]);

  // Handle specific marketplace actions
  const handleBuyProduct = useCallback((productId, onSuccess) => {
    if (requireRole('buyer', 'buy_product')) {
      // User has buyer role, proceed with purchase
      onSuccess && onSuccess();
      return true;
    }
    // Registration modal will be shown
    return false;
  }, [requireRole]);

  const handleSellProduct = useCallback((onSuccess) => {
    if (requireRole('seller', 'sell_product')) {
      // User has seller role, proceed to add product
      onSuccess && onSuccess();
      return true;
    }
    // Registration modal will be shown
    return false;
  }, [requireRole]);

  const handleDeliveryAction = useCallback((onSuccess) => {
    if (requireRole('delivery_person', 'delivery_action')) {
      // User has delivery person role, proceed with delivery action
      onSuccess && onSuccess();
      return true;
    }
    // Registration modal will be shown
    return false;
  }, [requireRole]);

  // Handle registration completion
  const handleRegistrationComplete = useCallback(async (role, registrationData) => {
    try {
      // Update local state
      setUserRoles(prev => ({
        ...prev,
        [role]: true,
      }));

      // Show success message
      const roleNames = {
        buyer: 'Buyer',
        seller: 'Seller',
        delivery_person: 'Delivery Person'
      };

      Alert.alert(
        'Registration Successful!',
        `You are now registered as a ${roleNames[role]}. You can now access all ${roleNames[role].toLowerCase()} features.`,
        [{ text: 'OK' }]
      );

      // Execute the original action if there was one
      if (triggerAction) {
        switch (triggerAction) {
          case 'buy_product':
            // Navigate to product purchase or cart
            break;
          case 'sell_product':
            // Navigate to add product screen
            break;
          case 'delivery_action':
            // Navigate to delivery dashboard
            break;
          default:
            break;
        }
      }

      // Reset modal state
      setRequiredRole(null);
      setTriggerAction(null);
    } catch (error) {
      console.error('Failed to handle registration completion:', error);
      Alert.alert('Error', 'Failed to complete registration process');
    }
  }, [triggerAction]);

  // Close registration modal
  const closeRegistrationModal = useCallback(() => {
    setRegistrationModalVisible(false);
    setRequiredRole(null);
    setTriggerAction(null);
  }, []);

  // Get user's role status for display
  const getRoleStatus = useCallback(() => {
    const roles = [];
    if (userRoles.buyer) roles.push('Buyer');
    if (userRoles.seller) roles.push('Seller');
    if (userRoles.delivery_person) roles.push('Delivery Person');
    
    return {
      roles,
      hasAnyRole: roles.length > 0,
      roleCount: roles.length,
      displayText: roles.length > 0 ? roles.join(', ') : 'No marketplace roles'
    };
  }, [userRoles]);

  // Check if user needs to register for marketplace access
  const needsMarketplaceRegistration = useCallback(() => {
    return !userRoles.buyer && !userRoles.seller && !userRoles.delivery_person;
  }, [userRoles]);

  // Get recommended role based on user activity or preferences
  const getRecommendedRole = useCallback(() => {
    // This could be enhanced with user behavior analysis
    // For now, return buyer as the most common starting role
    if (needsMarketplaceRegistration()) {
      return 'buyer';
    }
    return null;
  }, [needsMarketplaceRegistration]);

  // Refresh user roles (useful after registration or role updates)
  const refreshRoles = useCallback(async () => {
    await loadUserRoles();
  }, [loadUserRoles]);

  return {
    // State
    userRoles,
    loading,
    registrationModalVisible,
    requiredRole,
    triggerAction,

    // Role checking functions
    hasRole,
    canPerformAction,
    needsMarketplaceRegistration,
    getRoleStatus,
    getRecommendedRole,

    // Action functions
    requireRole,
    handleBuyProduct,
    handleSellProduct,
    handleDeliveryAction,

    // Modal management
    handleRegistrationComplete,
    closeRegistrationModal,

    // Utility functions
    refreshRoles,
  };
};

export default useMarketplaceRegistration;
