{"name": "mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client --clear", "dev:tunnel": "expo start --tunnel --dev-client", "dev:lan": "expo start --lan --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear", "reset": "expo r --clear"}, "dependencies": {"18": "^0.0.0", "@expo/metro-config": "0.18.11", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@livekit/react-native": "^2.7.4", "@livekit/react-native-webrtc": "^125.0.9", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/netinfo": "^11.3.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/drawer": "^7.3.3", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@signalapp/libsignal-client": "^0.76.3", "base-64": "^1.0.0", "crypto-js": "^4.2.0", "expo": "^53.0.9", "expo-auth-session": "~5.5.2", "expo-av": "^15.0.1", "expo-camera": "^16.0.6", "expo-clipboard": "^7.1.5", "expo-document-picker": "^12.0.2", "expo-file-system": "^18.0.4", "expo-image-picker": "^16.0.2", "expo-location": "^18.0.2", "expo-notifications": "^0.30.1", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "livekit-client": "^2.13.3", "lodash": "^4.17.21", "metro": "0.82.4", "moment": "^2.30.1", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.0", "react-native": "0.74.5", "react-native-crypto-js": "^1.0.0", "react-native-gesture-handler": "^2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-jitsi-meet": "^2.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "^10.0.0", "react-native-modal": "^13.0.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.16.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-securerandom": "^1.0.1", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.6", "react-native-webview": "^13.15.0", "socket.io-client": "^4.8.1", "uuid": "^11.0.3", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}