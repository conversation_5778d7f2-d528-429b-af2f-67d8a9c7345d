package models

import (
	"time"
)

// ShareType represents the type of shares
type ShareType string

const (
	ShareTypeOrdinary  ShareType = "ordinary"
	ShareTypePreferred ShareType = "preferred"
)

// ShareStatus represents the status of shares
type ShareStatus string

const (
	ShareStatusActive      ShareStatus = "active"
	ShareStatusTransferred ShareStatus = "transferred"
	ShareStatusRedeemed    ShareStatus = "redeemed"
)

// Share represents a member's share ownership in a chama
type Share struct {
	ID                string      `json:"id" db:"id"`
	ChamaID           string      `json:"chamaId" db:"chama_id"`
	MemberID          string      `json:"memberId" db:"member_id"`
	ShareType         ShareType   `json:"shareType" db:"share_type"`
	SharesOwned       int         `json:"sharesOwned" db:"shares_owned"`
	ShareValue        float64     `json:"shareValue" db:"share_value"`
	TotalValue        float64     `json:"totalValue" db:"total_value"`
	PurchaseDate      time.Time   `json:"purchaseDate" db:"purchase_date"`
	CertificateNumber *string     `json:"certificateNumber,omitempty" db:"certificate_number"`
	Status            ShareStatus `json:"status" db:"status"`
	CreatedAt         time.Time   `json:"createdAt" db:"created_at"`
	UpdatedAt         time.Time   `json:"updatedAt" db:"updated_at"`
}

// ShareWithMemberInfo represents share information with member details
type ShareWithMemberInfo struct {
	Share
	MemberName  string `json:"memberName"`
	MemberEmail string `json:"memberEmail"`
}

// ShareSummary represents aggregated share information for a member
type ShareSummary struct {
	MemberID       string  `json:"memberId"`
	MemberName     string  `json:"memberName"`
	TotalShares    int     `json:"totalShares"`
	TotalValue     float64 `json:"totalValue"`
	ShareTypes     []Share `json:"shareTypes"`
	LastPurchase   *time.Time `json:"lastPurchase,omitempty"`
}

// CreateShareRequest represents the request to create new shares
type CreateShareRequest struct {
	MemberID          string    `json:"memberId" binding:"required"`
	ShareType         ShareType `json:"shareType" binding:"required,oneof=ordinary preferred"`
	SharesCount       int       `json:"sharesCount" binding:"required,min=1"`
	ShareValue        float64   `json:"shareValue" binding:"required,min=0"`
	PurchaseDate      time.Time `json:"purchaseDate" binding:"required"`
	CertificateNumber *string   `json:"certificateNumber,omitempty"`
}

// UpdateShareRequest represents the request to update shares
type UpdateShareRequest struct {
	SharesOwned       *int        `json:"sharesOwned,omitempty" binding:"omitempty,min=0"`
	ShareValue        *float64    `json:"shareValue,omitempty" binding:"omitempty,min=0"`
	CertificateNumber *string     `json:"certificateNumber,omitempty"`
	Status            *ShareStatus `json:"status,omitempty" binding:"omitempty,oneof=active transferred redeemed"`
}

// ShareTransactionType represents the type of share transaction
type ShareTransactionType string

const (
	ShareTransactionPurchase   ShareTransactionType = "purchase"
	ShareTransactionTransfer   ShareTransactionType = "transfer"
	ShareTransactionRedemption ShareTransactionType = "redemption"
	ShareTransactionSplit      ShareTransactionType = "split"
)

// ShareTransactionStatus represents the status of a share transaction
type ShareTransactionStatus string

const (
	ShareTransactionPending   ShareTransactionStatus = "pending"
	ShareTransactionCompleted ShareTransactionStatus = "completed"
	ShareTransactionCancelled ShareTransactionStatus = "cancelled"
)

// ShareTransaction represents a share transaction
type ShareTransaction struct {
	ID             string                  `json:"id" db:"id"`
	ChamaID        string                  `json:"chamaId" db:"chama_id"`
	FromMemberID   *string                 `json:"fromMemberId,omitempty" db:"from_member_id"`
	ToMemberID     *string                 `json:"toMemberId,omitempty" db:"to_member_id"`
	TransactionType ShareTransactionType   `json:"transactionType" db:"transaction_type"`
	SharesCount    int                     `json:"sharesCount" db:"shares_count"`
	ShareValue     float64                 `json:"shareValue" db:"share_value"`
	TotalAmount    float64                 `json:"totalAmount" db:"total_amount"`
	TransactionDate time.Time              `json:"transactionDate" db:"transaction_date"`
	Status         ShareTransactionStatus  `json:"status" db:"status"`
	ApprovedBy     *string                 `json:"approvedBy,omitempty" db:"approved_by"`
	Description    *string                 `json:"description,omitempty" db:"description"`
	CreatedAt      time.Time               `json:"createdAt" db:"created_at"`
	UpdatedAt      time.Time               `json:"updatedAt" db:"updated_at"`
}

// CreateShareTransactionRequest represents the request to create a share transaction
type CreateShareTransactionRequest struct {
	FromMemberID    *string              `json:"fromMemberId,omitempty"`
	ToMemberID      *string              `json:"toMemberId,omitempty"`
	TransactionType ShareTransactionType `json:"transactionType" binding:"required,oneof=purchase transfer redemption split"`
	SharesCount     int                  `json:"sharesCount" binding:"required,min=1"`
	ShareValue      float64              `json:"shareValue" binding:"required,min=0"`
	TransactionDate time.Time            `json:"transactionDate" binding:"required"`
	Description     *string              `json:"description,omitempty"`
}

// SharesResponse represents the response structure for share operations
type SharesResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// SharesListResponse represents the response for listing shares
type SharesListResponse struct {
	Success bool    `json:"success"`
	Data    []Share `json:"data"`
	Count   int     `json:"count"`
	Error   string  `json:"error,omitempty"`
}

// ChamaSharesSummaryResponse represents the response for chama shares summary
type ChamaSharesSummaryResponse struct {
	Success      bool           `json:"success"`
	Data         []ShareSummary `json:"data"`
	TotalShares  int            `json:"totalShares"`
	TotalValue   float64        `json:"totalValue"`
	TotalMembers int            `json:"totalMembers"`
	Error        string         `json:"error,omitempty"`
}

// IsValidShareType checks if the share type is valid
func IsValidShareType(shareType string) bool {
	switch ShareType(shareType) {
	case ShareTypeOrdinary, ShareTypePreferred:
		return true
	default:
		return false
	}
}

// IsValidShareStatus checks if the share status is valid
func IsValidShareStatus(status string) bool {
	switch ShareStatus(status) {
	case ShareStatusActive, ShareStatusTransferred, ShareStatusRedeemed:
		return true
	default:
		return false
	}
}

// IsValidShareTransactionType checks if the transaction type is valid
func IsValidShareTransactionType(transactionType string) bool {
	switch ShareTransactionType(transactionType) {
	case ShareTransactionPurchase, ShareTransactionTransfer, ShareTransactionRedemption, ShareTransactionSplit:
		return true
	default:
		return false
	}
}

// CalculateTotalValue calculates the total value of shares
func (s *Share) CalculateTotalValue() {
	s.TotalValue = float64(s.SharesOwned) * s.ShareValue
}

// CanTransfer checks if shares can be transferred
func (s *Share) CanTransfer() bool {
	return s.Status == ShareStatusActive && s.SharesOwned > 0
}

// CanRedeem checks if shares can be redeemed
func (s *Share) CanRedeem() bool {
	return s.Status == ShareStatusActive && s.SharesOwned > 0
}
