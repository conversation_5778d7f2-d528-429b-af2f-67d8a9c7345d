package database

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

// Initialize creates and returns a database connection
func Initialize(databaseURL string) (*sql.DB, error) {
	// Add SQLite-specific parameters for better concurrent access
	if databaseURL == "vaultke.db" {
		databaseURL = "vaultke.db?_busy_timeout=30000&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000&_foreign_keys=1"
	}

	db, err := sql.Open("sqlite3", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool for better performance
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(0) // No limit

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Set SQLite pragmas for better concurrent access
	pragmas := []string{
		"PRAGMA busy_timeout = 30000",
		"PRAGMA journal_mode = WAL",
		"PRAGMA synchronous = NORMAL",
		"PRAGMA cache_size = 1000",
		"PRAGMA foreign_keys = ON",
		"PRAGMA temp_store = memory",
	}

	for _, pragma := range pragmas {
		if _, err := db.Exec(pragma); err != nil {
			log.Printf("Warning: failed to set pragma %s: %v", pragma, err)
		}
	}

	log.Println("Database connection established successfully")
	return db, nil
}

// Migrate runs database migrations
func Migrate(db *sql.DB) error {
	migrations := []string{
		createUsersTable,
		createChamasTable,
		createChamaMembersTable,
		createWalletsTable,
		createTransactionsTable,
		createProductsTable,
		createCartItemsTable,
		createOrdersTable,
		createOrderItemsTable,
		createProductReviewsTable,
		createWishlistTable,
		createNotificationsTable,
		createChatRoomsTable,
		createChatMessagesTable,
		createChatRoomMembersTable,
		createLoansTable,
		createGuarantorsTable,
		createLoanPaymentsTable,
		createMeetingsTable,
		createMeetingAttendanceTable,
		createMeetingDocumentsTable,
		createMeetingMinutesTable,
		createVotesTable,
		createVoteOptionsTable,
		createUserVotesTable,
		createMerryGoRoundTable,
		createMerryGoRoundParticipantsTable,
		createWelfareTable,
		createWelfareContributionsTable,
		createWelfareRequestsTable,
		addWelfareRequestBeneficiaryField,
		addUserProfileFields,
		addChatMessageFields,
		addWelfareContributionFields,
		addMeetingDocumentFileUrl,
		createChamaInvitationsTable,
		addChamaPermissionsColumn,
		addInvitationRoleColumns,
		createLearningTables,
		createRemindersTable,
		createSharesAndDividendsTables,
		// createPollsAndVotingTables, // DISABLED: Conflicts with existing vote system
		createDisbursementTables,
		createDeliveryContactsTable,
		addOrderItemDeliveryPersonField,
		createMarketplaceRolesTable,
		populateExistingSellers,
		addEnhancedLearningContentFields,
		createQuizResultsTable,
		addChamaCategoryColumn,
	}

	for i, migration := range migrations {
		// Handle special migrations that need custom logic
		if i == len(migrations)-10 { // Tenth to last migration is addOrderItemDeliveryPersonField
			if err := addMissingOrderItemDeliveryPersonField(db); err != nil {
				return fmt.Errorf("failed to add order item delivery person field: %w", err)
			}
		} else if i == len(migrations)-9 { // Ninth to last migration is addWelfareRequestBeneficiaryField
			if err := addMissingWelfareRequestBeneficiaryField(db); err != nil {
				return fmt.Errorf("failed to add welfare request beneficiary field: %w", err)
			}
		} else if i == len(migrations)-8 { // Eighth to last migration is addUserProfileFields
			if err := addMissingUserProfileFields(db); err != nil {
				return fmt.Errorf("failed to add user profile fields: %w", err)
			}
		} else if i == len(migrations)-7 { // Seventh to last migration is addChatMessageFields
			if err := addMissingChatMessageFields(db); err != nil {
				return fmt.Errorf("failed to add chat message fields: %w", err)
			}
		} else if i == len(migrations)-6 { // Sixth to last migration is addWelfareContributionFields
			if err := addMissingWelfareContributionFields(db); err != nil {
				return fmt.Errorf("failed to add welfare contribution fields: %w", err)
			}
		} else if i == len(migrations)-5 { // Fifth to last migration is addMeetingDocumentFileUrl
			if err := addMissingMeetingDocumentFileUrl(db); err != nil {
				return fmt.Errorf("failed to add meeting document file_url field: %w", err)
			}
		} else if i == len(migrations)-4 { // Fourth to last migration is addChamaPermissionsColumn
			if err := addMissingChamaPermissionsColumn(db); err != nil {
				return fmt.Errorf("failed to add chama permissions column: %w", err)
			}
		} else if i == len(migrations)-3 { // Third to last migration is addInvitationRoleColumns
			if err := addMissingInvitationRoleColumns(db); err != nil {
				return fmt.Errorf("failed to add invitation role columns: %w", err)
			}
		} else if i == len(migrations)-2 { // Second to last migration is addEnhancedLearningContentFields
			if err := addMissingEnhancedLearningContentFields(db); err != nil {
				return fmt.Errorf("failed to add enhanced learning content fields: %w", err)
			}
		} else if i == len(migrations)-2 { // Second to last migration is createQuizResultsTable
			if _, err := db.Exec(migration); err != nil {
				return fmt.Errorf("failed to create quiz results table: %w", err)
			}
		} else if i == len(migrations)-1 { // Last migration is addChamaCategoryColumn
			if err := addCategoryColumnToChamasTable(db); err != nil {
				return fmt.Errorf("failed to add chama category column: %w", err)
			}
		} else {
			// Regular migrations
			if _, err := db.Exec(migration); err != nil {
				return fmt.Errorf("failed to run migration %d: %w", i+1, err)
			}
		}
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// SQL migration statements
const createUsersTable = `
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    avatar TEXT,
    role TEXT NOT NULL DEFAULT 'user',
    status TEXT NOT NULL DEFAULT 'pending',
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_phone_verified BOOLEAN DEFAULT FALSE,
    language TEXT DEFAULT 'en',
    theme TEXT DEFAULT 'dark',
    county TEXT,
    town TEXT,
    latitude REAL,
    longitude REAL,
    business_type TEXT,
    business_description TEXT,
    rating REAL DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);`

const createChamasTable = `
CREATE TABLE IF NOT EXISTS chamas (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL DEFAULT 'chama', -- 'chama' or 'contribution'
    type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'active',
    avatar TEXT,
    county TEXT NOT NULL,
    town TEXT NOT NULL,
    latitude REAL,
    longitude REAL,
    contribution_amount REAL NOT NULL,
    contribution_frequency TEXT NOT NULL,
    target_amount REAL, -- For contribution groups
    target_deadline DATETIME, -- For contribution groups
    payment_method TEXT, -- 'till' or 'paybill'
    till_number TEXT, -- For TILL payments
    paybill_business_number TEXT, -- For PAYBILL payments
    paybill_account_number TEXT, -- For PAYBILL payments
    payment_recipient_name TEXT, -- Name user should expect on successful payment
    max_members INTEGER,
    current_members INTEGER DEFAULT 0,
    total_funds REAL DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    requires_approval BOOLEAN DEFAULT TRUE,
    rules TEXT, -- JSON array of rules
    meeting_frequency TEXT,
    meeting_day_of_week INTEGER,
    meeting_day_of_month INTEGER,
    meeting_time TEXT,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);`

const createChamaMembersTable = `
CREATE TABLE IF NOT EXISTS chama_members (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'member',
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    total_contributions REAL DEFAULT 0,
    last_contribution DATETIME,
    rating REAL DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(chama_id, user_id)
);`

const createWalletsTable = `
CREATE TABLE IF NOT EXISTS wallets (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL,
    owner_id TEXT NOT NULL,
    balance REAL DEFAULT 0,
    currency TEXT DEFAULT 'KES',
    is_active BOOLEAN DEFAULT TRUE,
    is_locked BOOLEAN DEFAULT FALSE,
    daily_limit REAL,
    monthly_limit REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);`

const createTransactionsTable = `
CREATE TABLE IF NOT EXISTS transactions (
    id TEXT PRIMARY KEY,
    from_wallet_id TEXT,
    to_wallet_id TEXT,
    type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    amount REAL NOT NULL,
    currency TEXT DEFAULT 'KES',
    description TEXT,
    reference TEXT,
    payment_method TEXT NOT NULL,
    metadata TEXT, -- JSON metadata
    fees REAL DEFAULT 0,
    initiated_by TEXT NOT NULL,
    approved_by TEXT,
    requires_approval BOOLEAN DEFAULT FALSE,
    approval_deadline DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_wallet_id) REFERENCES wallets(id),
    FOREIGN KEY (to_wallet_id) REFERENCES wallets(id),
    FOREIGN KEY (initiated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);`

const createProductsTable = `
CREATE TABLE IF NOT EXISTS products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    price REAL NOT NULL,
    currency TEXT DEFAULT 'KES',
    images TEXT, -- JSON array of image URLs
    status TEXT NOT NULL DEFAULT 'active',
    stock INTEGER DEFAULT 0,
    min_order INTEGER DEFAULT 1,
    max_order INTEGER,
    seller_id TEXT NOT NULL,
    chama_id TEXT,
    county TEXT NOT NULL,
    town TEXT NOT NULL,
    address TEXT,
    tags TEXT, -- JSON array of tags
    rating REAL DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    total_sales INTEGER DEFAULT 0,
    is_promoted BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (chama_id) REFERENCES chamas(id)
);`

const createCartItemsTable = `
CREATE TABLE IF NOT EXISTS cart_items (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    price REAL NOT NULL,
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE(user_id, product_id)
);`

const createOrdersTable = `
CREATE TABLE IF NOT EXISTS orders (
    id TEXT PRIMARY KEY,
    buyer_id TEXT NOT NULL,
    seller_id TEXT NOT NULL,
    chama_id TEXT,
    total_amount REAL NOT NULL,
    currency TEXT DEFAULT 'KES',
    status TEXT NOT NULL DEFAULT 'pending',
    payment_method TEXT NOT NULL,
    payment_status TEXT NOT NULL DEFAULT 'pending',
    delivery_county TEXT NOT NULL,
    delivery_town TEXT NOT NULL,
    delivery_address TEXT NOT NULL,
    delivery_phone TEXT NOT NULL,
    delivery_fee REAL DEFAULT 0,
    delivery_person_id TEXT,
    delivery_status TEXT NOT NULL DEFAULT 'pending',
    estimated_delivery DATETIME,
    actual_delivery DATETIME,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (buyer_id) REFERENCES users(id),
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (delivery_person_id) REFERENCES users(id)
);`

const createOrderItemsTable = `
CREATE TABLE IF NOT EXISTS order_items (
    id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    price REAL NOT NULL,
    name TEXT NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);`

const createProductReviewsTable = `
CREATE TABLE IF NOT EXISTS product_reviews (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    order_id TEXT NOT NULL,
    reviewer_id TEXT NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    images TEXT, -- JSON array of image URLs
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (reviewer_id) REFERENCES users(id),
    UNIQUE(order_id, product_id, reviewer_id)
);`

const createWishlistTable = `
CREATE TABLE IF NOT EXISTS wishlist (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE(user_id, product_id)
);`

const createNotificationsTable = `
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL,
    data TEXT, -- JSON data
    is_read BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);`

const createChatRoomsTable = `
CREATE TABLE IF NOT EXISTS chat_rooms (
    id TEXT PRIMARY KEY,
    name TEXT,
    type TEXT NOT NULL, -- 'private', 'group', 'chama'
    chama_id TEXT,
    created_by TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_message TEXT,
    last_message_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);`

const createChatMessagesTable = `
CREATE TABLE IF NOT EXISTS chat_messages (
    id TEXT PRIMARY KEY,
    room_id TEXT NOT NULL,
    sender_id TEXT NOT NULL,
    message TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT DEFAULT 'text',
    message_type TEXT DEFAULT 'text', -- 'text', 'image', 'file', 'voice'
    metadata TEXT DEFAULT '{}',
    file_url TEXT,
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    reply_to TEXT,
    reply_to_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES chat_rooms(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (reply_to) REFERENCES chat_messages(id),
    FOREIGN KEY (reply_to_id) REFERENCES chat_messages(id)
);`

const createMeetingsTable = `
CREATE TABLE IF NOT EXISTS meetings (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    scheduled_at DATETIME NOT NULL,
    duration INTEGER, -- in minutes
    location TEXT,
    meeting_url TEXT,
    meeting_type TEXT NOT NULL DEFAULT 'physical', -- 'physical', 'virtual', 'hybrid'
    livekit_room_name TEXT, -- LiveKit room identifier
    livekit_room_id TEXT, -- LiveKit room ID
    status TEXT NOT NULL DEFAULT 'scheduled', -- 'scheduled', 'active', 'ended', 'cancelled'
    started_at DATETIME,
    ended_at DATETIME,
    recording_enabled BOOLEAN DEFAULT FALSE,
    recording_url TEXT,
    transcript_url TEXT,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);`

const createMeetingAttendanceTable = `
CREATE TABLE IF NOT EXISTS meeting_attendance (
    id TEXT PRIMARY KEY,
    meeting_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    attendance_type TEXT NOT NULL, -- 'physical', 'virtual'
    joined_at DATETIME,
    left_at DATETIME,
    duration_minutes INTEGER DEFAULT 0,
    is_present BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(meeting_id, user_id)
);`

const createMeetingDocumentsTable = `
CREATE TABLE IF NOT EXISTS meeting_documents (
    id TEXT PRIMARY KEY,
    meeting_id TEXT NOT NULL,
    uploaded_by TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_size INTEGER,
    file_type TEXT,
    document_type TEXT, -- 'agenda', 'minutes', 'attachment', 'recording'
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);`

const createMeetingMinutesTable = `
CREATE TABLE IF NOT EXISTS meeting_minutes (
    id TEXT PRIMARY KEY,
    meeting_id TEXT NOT NULL,
    content TEXT NOT NULL,
    taken_by TEXT NOT NULL, -- Secretary or authorized user
    status TEXT NOT NULL DEFAULT 'draft', -- 'draft', 'approved', 'published'
    approved_by TEXT,
    approved_at DATETIME,
    version INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id),
    FOREIGN KEY (taken_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);`

const createVotesTable = `
CREATE TABLE IF NOT EXISTS votes (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL DEFAULT 'single', -- 'single', 'multiple'
    status TEXT NOT NULL DEFAULT 'active',
    starts_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ends_at DATETIME NOT NULL,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);`

const createVoteOptionsTable = `
CREATE TABLE IF NOT EXISTS vote_options (
    id TEXT PRIMARY KEY,
    vote_id TEXT NOT NULL,
    option_text TEXT NOT NULL,
    vote_count INTEGER DEFAULT 0,
    FOREIGN KEY (vote_id) REFERENCES votes(id)
);`

const createUserVotesTable = `
CREATE TABLE IF NOT EXISTS user_votes (
    id TEXT PRIMARY KEY,
    vote_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    option_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vote_id) REFERENCES votes(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (option_id) REFERENCES vote_options(id),
    UNIQUE(vote_id, user_id, option_id)
);`

const createChatRoomMembersTable = `
CREATE TABLE IF NOT EXISTS chat_room_members (
    id TEXT PRIMARY KEY,
    room_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'member',
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_read_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    is_muted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (room_id) REFERENCES chat_rooms(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(room_id, user_id)
);`

const createLoansTable = `
CREATE TABLE IF NOT EXISTS loans (
    id TEXT PRIMARY KEY,
    borrower_id TEXT NOT NULL,
    chama_id TEXT NOT NULL,
    type TEXT NOT NULL,
    amount REAL NOT NULL,
    interest_rate REAL DEFAULT 0,
    duration INTEGER NOT NULL,
    purpose TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    approved_by TEXT,
    approved_at DATETIME,
    disbursed_at DATETIME,
    due_date DATETIME,
    total_amount REAL DEFAULT 0,
    paid_amount REAL DEFAULT 0,
    remaining_amount REAL DEFAULT 0,
    required_guarantors INTEGER NOT NULL,
    approved_guarantors INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (borrower_id) REFERENCES users(id),
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);`

const createGuarantorsTable = `
CREATE TABLE IF NOT EXISTS guarantors (
    id TEXT PRIMARY KEY,
    loan_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    amount REAL NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    message TEXT,
    responded_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (loan_id) REFERENCES loans(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(loan_id, user_id)
);`

const createLoanPaymentsTable = `
CREATE TABLE IF NOT EXISTS loan_payments (
    id TEXT PRIMARY KEY,
    loan_id TEXT NOT NULL,
    amount REAL NOT NULL,
    principal_amount REAL NOT NULL,
    interest_amount REAL NOT NULL,
    payment_method TEXT NOT NULL,
    reference TEXT,
    paid_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (loan_id) REFERENCES loans(id)
);`

const createMerryGoRoundTable = `
CREATE TABLE IF NOT EXISTS merry_go_rounds (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    amount_per_round REAL NOT NULL,
    frequency TEXT NOT NULL, -- 'weekly', 'monthly'
    total_participants INTEGER NOT NULL,
    current_round INTEGER DEFAULT 1,
    status TEXT NOT NULL DEFAULT 'active',
    start_date DATE NOT NULL,
    next_payout_date DATE,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);`

const createMerryGoRoundParticipantsTable = `
CREATE TABLE IF NOT EXISTS merry_go_round_participants (
    id TEXT PRIMARY KEY,
    merry_go_round_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    position INTEGER NOT NULL,
    has_received BOOLEAN DEFAULT FALSE,
    received_at DATETIME,
    total_contributed REAL DEFAULT 0,
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (merry_go_round_id) REFERENCES merry_go_rounds(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(merry_go_round_id, user_id),
    UNIQUE(merry_go_round_id, position)
);`

const createWelfareTable = `
CREATE TABLE IF NOT EXISTS welfare_funds (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    target_amount REAL,
    current_amount REAL DEFAULT 0,
    contribution_per_member REAL,
    purpose TEXT NOT NULL, -- 'emergency', 'medical', 'funeral', 'education'
    status TEXT NOT NULL DEFAULT 'active',
    beneficiary_id TEXT,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (beneficiary_id) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);`

const createWelfareContributionsTable = `
CREATE TABLE IF NOT EXISTS welfare_contributions (
    id TEXT PRIMARY KEY,
    welfare_fund_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    amount REAL NOT NULL,
    payment_method TEXT NOT NULL,
    reference TEXT,
    contributed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (welfare_fund_id) REFERENCES welfare_funds(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);`

const createWelfareRequestsTable = `
CREATE TABLE IF NOT EXISTS welfare_requests (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    requester_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    amount REAL NOT NULL,
    category TEXT NOT NULL,
    urgency TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    votes_for INTEGER DEFAULT 0,
    votes_against INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id),
    FOREIGN KEY (requester_id) REFERENCES users(id)
);`

// addWelfareRequestBeneficiaryField adds beneficiary_id field to welfare_requests table
const addWelfareRequestBeneficiaryField = "SELECT 1" // Placeholder - actual logic in migration function

// addUserProfileFields adds missing profile fields if they don't exist
const addUserProfileFields = "SELECT 1" // Placeholder - actual logic in migration function

// addChatMessageFields adds missing chat message fields if they don't exist
const addChatMessageFields = "SELECT 1" // Placeholder - actual logic in migration function

// addChatRoomFields adds missing chat room fields if they don't exist
const addChatRoomFields = "SELECT 1" // Placeholder - actual logic in migration function

// addMissingUserProfileFields safely adds missing profile fields to users table
func addMissingUserProfileFields(db *sql.DB) error {
	// Check if columns exist and add them if they don't
	columns := []struct {
		name     string
		dataType string
	}{
		{"bio", "TEXT"},
		{"occupation", "TEXT"},
		{"date_of_birth", "DATE"},
	}

	for _, col := range columns {
		// Check if column exists
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('users') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE users ADD COLUMN %s %s", col.name, col.dataType)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to users table", col.name)
		} else {
			log.Printf("Column %s already exists in users table", col.name)
		}
	}

	return nil
}

// addMissingWelfareRequestBeneficiaryField safely adds beneficiary_id field to welfare_requests table
func addMissingWelfareRequestBeneficiaryField(db *sql.DB) error {
	// Check if beneficiary_id column exists
	var exists bool
	query := `SELECT COUNT(*) FROM pragma_table_info('welfare_requests') WHERE name = 'beneficiary_id'`
	err := db.QueryRow(query).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if beneficiary_id column exists: %w", err)
	}

	// Add column if it doesn't exist
	if !exists {
		alterQuery := `ALTER TABLE welfare_requests ADD COLUMN beneficiary_id TEXT`
		if _, err := db.Exec(alterQuery); err != nil {
			return fmt.Errorf("failed to add beneficiary_id column: %w", err)
		}
		log.Printf("Added beneficiary_id column to welfare_requests table")

		// Set beneficiary_id to requester_id for existing records (self-requests)
		updateQuery := `UPDATE welfare_requests SET beneficiary_id = requester_id WHERE beneficiary_id IS NULL`
		if _, err := db.Exec(updateQuery); err != nil {
			log.Printf("Warning: failed to update existing records with beneficiary_id: %v", err)
		} else {
			log.Printf("Updated existing welfare requests to set beneficiary_id = requester_id")
		}
	} else {
		log.Printf("Column beneficiary_id already exists in welfare_requests table")
	}

	return nil
}

// addMissingChatMessageFields safely adds missing chat message fields to chat_messages table
func addMissingChatMessageFields(db *sql.DB) error {
	// Check if columns exist and add them if they don't
	columns := []struct {
		name         string
		dataType     string
		defaultValue string
	}{
		{"content", "TEXT", "''"},
		{"type", "TEXT", "'text'"},
		{"metadata", "TEXT", "'{}'"},
		{"is_deleted", "BOOLEAN", "FALSE"},
		{"reply_to_id", "TEXT", "NULL"},
	}

	for _, col := range columns {
		// Check if column exists
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('chat_messages') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE chat_messages ADD COLUMN %s %s DEFAULT %s", col.name, col.dataType, col.defaultValue)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to chat_messages table", col.name)
		} else {
			log.Printf("Column %s already exists in chat_messages table", col.name)
		}
	}

	// Update existing records to populate content from message if content is empty
	updateQuery := `UPDATE chat_messages SET content = message WHERE content = '' OR content IS NULL`
	if _, err := db.Exec(updateQuery); err != nil {
		log.Printf("Warning: failed to update content from message: %v", err)
	} else {
		log.Printf("Updated content field from message field for existing records")
	}

	// Update new records to populate message from content to maintain compatibility
	updateQuery2 := `UPDATE chat_messages SET message = content WHERE message = '' OR message IS NULL`
	if _, err := db.Exec(updateQuery2); err != nil {
		log.Printf("Warning: failed to update message from content: %v", err)
	} else {
		log.Printf("Updated message field from content field for compatibility")
	}

	return nil
}

// addMissingChatRoomFields safely adds missing chat room fields to chat_rooms table
func addMissingChatRoomFields(db *sql.DB) error {
	// Check if columns exist and add them if they don't
	columns := []struct {
		name         string
		dataType     string
		defaultValue string
	}{
		{"is_active", "BOOLEAN", "TRUE"},
		{"last_message", "TEXT", "NULL"},
		{"last_message_at", "DATETIME", "NULL"},
		{"updated_at", "DATETIME", "CURRENT_TIMESTAMP"},
	}

	for _, col := range columns {
		// Check if column exists
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('chat_rooms') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE chat_rooms ADD COLUMN %s %s DEFAULT %s", col.name, col.dataType, col.defaultValue)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to chat_rooms table", col.name)
		} else {
			log.Printf("Column %s already exists in chat_rooms table", col.name)
		}
	}

	return nil
}

// addMissingTransactionFields adds missing columns to the transactions table
func addMissingTransactionFields(db *sql.DB) error {
	columns := []struct {
		name         string
		dataType     string
		defaultValue string
	}{
		{"checkout_request_id", "TEXT", "NULL"},
	}

	for _, col := range columns {
		// Check if column exists
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('transactions') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE transactions ADD COLUMN %s %s DEFAULT %s", col.name, col.dataType, col.defaultValue)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to transactions table", col.name)
		} else {
			log.Printf("Column %s already exists in transactions table", col.name)
		}
	}

	return nil
}

// addTransactionFields is a placeholder for the migration system
const addTransactionFields = ""

// addWelfareContributionFields is a placeholder for the migration system
const addWelfareContributionFields = ""

// addMissingWelfareContributionFields adds missing columns to the welfare_contributions table
func addMissingWelfareContributionFields(db *sql.DB) error {
	columns := []struct {
		name         string
		dataType     string
		defaultValue string
	}{
		{"welfare_request_id", "TEXT", "NULL"},
		{"contributor_id", "TEXT", "NULL"},
		{"message", "TEXT", "NULL"},
		{"status", "TEXT", "'completed'"},
		{"created_at", "DATETIME", "CURRENT_TIMESTAMP"},
		{"updated_at", "DATETIME", "CURRENT_TIMESTAMP"},
	}

	for _, col := range columns {
		// Check if column exists
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('welfare_contributions') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE welfare_contributions ADD COLUMN %s %s DEFAULT %s", col.name, col.dataType, col.defaultValue)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to welfare_contributions table", col.name)
		} else {
			log.Printf("Column %s already exists in welfare_contributions table", col.name)
		}
	}

	return nil
}

// Migration constant for adding file_url to meeting_documents
const addMeetingDocumentFileUrl = `-- This is handled by addMissingMeetingDocumentFileUrl function`

// addMissingMeetingDocumentFileUrl adds the file_url column to meeting_documents table if it doesn't exist
func addMissingMeetingDocumentFileUrl(db *sql.DB) error {
	// Check if file_url column exists
	rows, err := db.Query("PRAGMA table_info(meeting_documents)")
	if err != nil {
		return fmt.Errorf("failed to get table info: %w", err)
	}
	defer rows.Close()

	hasFileUrl := false
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue sql.NullString

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			continue
		}

		if name == "file_url" {
			hasFileUrl = true
			break
		}
	}

	if !hasFileUrl {
		log.Println("Adding file_url column to meeting_documents table...")
		_, err = db.Exec("ALTER TABLE meeting_documents ADD COLUMN file_url TEXT")
		if err != nil {
			return fmt.Errorf("failed to add file_url column: %w", err)
		}
		log.Println("Successfully added file_url column to meeting_documents table")
	} else {
		log.Println("file_url column already exists in meeting_documents table")
	}

	return nil
}

// createChamaInvitationsTable creates the chama invitations table
const createChamaInvitationsTable = `
CREATE TABLE IF NOT EXISTS chama_invitations (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    inviter_id TEXT NOT NULL,
    email TEXT NOT NULL,
    phone_number TEXT,
    message TEXT,
    invitation_token TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'expired')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    responded_at DATETIME,
    responded_by TEXT,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (responded_by) REFERENCES users(id) ON DELETE SET NULL
);

CREATE INDEX IF NOT EXISTS idx_chama_invitations_email ON chama_invitations(email);
CREATE INDEX IF NOT EXISTS idx_chama_invitations_chama_id ON chama_invitations(chama_id);
CREATE INDEX IF NOT EXISTS idx_chama_invitations_status ON chama_invitations(status);
CREATE INDEX IF NOT EXISTS idx_chama_invitations_token ON chama_invitations(invitation_token);
`

// addChamaPermissionsColumn adds permissions column to chamas table
const addChamaPermissionsColumn = `
-- This will be handled by addMissingChamaPermissionsColumn function
SELECT 1;
`

// addInvitationRoleColumns adds role-related columns to chama_invitations table
const addInvitationRoleColumns = `
-- This will be handled by addMissingInvitationRoleColumns function
SELECT 1;
`

// addMissingChamaPermissionsColumn adds permissions column to chamas table if it doesn't exist
func addMissingChamaPermissionsColumn(db *sql.DB) error {
	// Check if permissions column exists
	var columnExists bool
	checkQuery := `
		SELECT COUNT(*) > 0
		FROM pragma_table_info('chamas')
		WHERE name = 'permissions'
	`

	err := db.QueryRow(checkQuery).Scan(&columnExists)
	if err != nil {
		return fmt.Errorf("failed to check if permissions column exists: %w", err)
	}

	if !columnExists {
		log.Println("Adding permissions column to chamas table")
		addColumnQuery := `
			ALTER TABLE chamas ADD COLUMN permissions TEXT DEFAULT '{"allowMerryGoRound": true, "allowWelfare": true, "allowMarketplace": true}'
		`
		_, err = db.Exec(addColumnQuery)
		if err != nil {
			return fmt.Errorf("failed to add permissions column: %w", err)
		}
		log.Println("Successfully added permissions column to chamas table")
	} else {
		log.Println("Column permissions already exists in chamas table")
	}

	return nil
}

// addCategoryColumnToChamasTable adds the category column to the chamas table if it doesn't exist
func addCategoryColumnToChamasTable(db *sql.DB) error {
	// Check if category column exists
	var columnExists bool
	checkColumnQuery := `
		SELECT COUNT(*) > 0
		FROM pragma_table_info('chamas')
		WHERE name = 'category'
	`
	err := db.QueryRow(checkColumnQuery).Scan(&columnExists)
	if err != nil {
		return fmt.Errorf("failed to check if category column exists: %w", err)
	}

	if !columnExists {
		log.Println("Adding category column to chamas table")
		addColumnQuery := `
			ALTER TABLE chamas ADD COLUMN category TEXT NOT NULL DEFAULT 'chama'
		`
		_, err = db.Exec(addColumnQuery)
		if err != nil {
			return fmt.Errorf("failed to add category column: %w", err)
		}
		log.Println("Successfully added category column to chamas table")
	} else {
		log.Println("Column category already exists in chamas table")
	}

	// Check if target_amount column exists
	var targetAmountExists bool
	checkTargetAmountQuery := `
		SELECT COUNT(*) > 0
		FROM pragma_table_info('chamas')
		WHERE name = 'target_amount'
	`
	err = db.QueryRow(checkTargetAmountQuery).Scan(&targetAmountExists)
	if err != nil {
		return fmt.Errorf("failed to check if target_amount column exists: %w", err)
	}

	if !targetAmountExists {
		log.Println("Adding target_amount column to chamas table")
		addTargetAmountQuery := `
			ALTER TABLE chamas ADD COLUMN target_amount REAL
		`
		_, err = db.Exec(addTargetAmountQuery)
		if err != nil {
			return fmt.Errorf("failed to add target_amount column: %w", err)
		}
		log.Println("Successfully added target_amount column to chamas table")
	}

	// Check if target_deadline column exists
	var targetDeadlineExists bool
	checkTargetDeadlineQuery := `
		SELECT COUNT(*) > 0
		FROM pragma_table_info('chamas')
		WHERE name = 'target_deadline'
	`
	err = db.QueryRow(checkTargetDeadlineQuery).Scan(&targetDeadlineExists)
	if err != nil {
		return fmt.Errorf("failed to check if target_deadline column exists: %w", err)
	}

	if !targetDeadlineExists {
		log.Println("Adding target_deadline column to chamas table")
		addTargetDeadlineQuery := `
			ALTER TABLE chamas ADD COLUMN target_deadline DATETIME
		`
		_, err = db.Exec(addTargetDeadlineQuery)
		if err != nil {
			return fmt.Errorf("failed to add target_deadline column: %w", err)
		}
		log.Println("Successfully added target_deadline column to chamas table")
	}

	// Add payment method columns
	paymentColumns := []struct {
		name     string
		dataType string
	}{
		{"payment_method", "TEXT"},
		{"till_number", "TEXT"},
		{"paybill_business_number", "TEXT"},
		{"paybill_account_number", "TEXT"},
		{"payment_recipient_name", "TEXT"},
	}

	for _, col := range paymentColumns {
		var exists bool
		checkQuery := `
			SELECT COUNT(*) > 0
			FROM pragma_table_info('chamas')
			WHERE name = ?
		`
		err = db.QueryRow(checkQuery, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if %s column exists: %w", col.name, err)
		}

		if !exists {
			log.Printf("Adding %s column to chamas table", col.name)
			addColumnQuery := fmt.Sprintf("ALTER TABLE chamas ADD COLUMN %s %s", col.name, col.dataType)
			_, err = db.Exec(addColumnQuery)
			if err != nil {
				return fmt.Errorf("failed to add %s column: %w", col.name, err)
			}
			log.Printf("Successfully added %s column to chamas table", col.name)
		}
	}

	return nil
}

// addMissingInvitationRoleColumns safely adds role-related columns to chama_invitations table
func addMissingInvitationRoleColumns(db *sql.DB) error {
	columns := []struct {
		name         string
		dataType     string
		defaultValue string
	}{
		{"role", "TEXT", ""},
		{"role_name", "TEXT", ""},
		{"role_description", "TEXT", ""},
	}

	for _, col := range columns {
		// Check if column exists
		var count int
		err := db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('chama_invitations')
			WHERE name = ?
		`, col.name).Scan(&count)
		if err != nil {
			log.Printf("Error checking for column %s: %v", col.name, err)
			continue
		}

		// Add column if it doesn't exist
		if count == 0 {
			query := fmt.Sprintf("ALTER TABLE chama_invitations ADD COLUMN %s %s", col.name, col.dataType)
			if col.defaultValue != "" {
				query += fmt.Sprintf(" DEFAULT '%s'", col.defaultValue)
			}

			_, err = db.Exec(query)
			if err != nil {
				log.Printf("Error adding column %s: %v", col.name, err)
				continue
			}
			log.Printf("Added column %s to chama_invitations table", col.name)
		}
	}

	return nil
}

// createLearningTables creates all learning management system tables
const createLearningTables = `
-- Learning categories table
CREATE TABLE IF NOT EXISTS learning_categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    color TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Learning courses table
CREATE TABLE IF NOT EXISTS learning_courses (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category_id TEXT NOT NULL,
    level TEXT NOT NULL CHECK (level IN ('beginner', 'intermediate', 'advanced')),
    type TEXT NOT NULL CHECK (type IN ('article', 'video', 'course', 'quiz')),
    content TEXT, -- Main content (markdown for articles, video URL for videos)
    thumbnail_url TEXT,
    duration_minutes INTEGER,
    estimated_read_time TEXT,
    tags TEXT, -- JSON array of tags
    prerequisites TEXT, -- JSON array of prerequisite course IDs
    learning_objectives TEXT, -- JSON array of learning objectives
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    is_featured BOOLEAN DEFAULT false,
    -- Enhanced content fields for new system
    video_url TEXT, -- Direct video URL for video courses
    quiz_questions TEXT, -- JSON array of quiz questions with answers
    article_content TEXT, -- JSON object with headline_image and sections
    course_structure TEXT, -- JSON object with topics, subtopics, and outline
    view_count INTEGER DEFAULT 0,
    rating REAL DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES learning_categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Learning course lessons table (for multi-lesson courses)
CREATE TABLE IF NOT EXISTS learning_lessons (
    id TEXT PRIMARY KEY,
    course_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    lesson_order INTEGER NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('text', 'video', 'quiz', 'assignment')),
    duration_minutes INTEGER,
    video_url TEXT,
    attachments TEXT, -- JSON array of attachment URLs
    is_required BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES learning_courses(id) ON DELETE CASCADE,
    UNIQUE(course_id, lesson_order)
);

-- User course progress table
CREATE TABLE IF NOT EXISTS user_course_progress (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    course_id TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    progress_percentage REAL DEFAULT 0,
    current_lesson_id TEXT,
    started_at DATETIME,
    completed_at DATETIME,
    last_accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    time_spent_minutes INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES learning_courses(id) ON DELETE CASCADE,
    FOREIGN KEY (current_lesson_id) REFERENCES learning_lessons(id),
    UNIQUE(user_id, course_id)
);

-- User lesson progress table
CREATE TABLE IF NOT EXISTS user_lesson_progress (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    lesson_id TEXT NOT NULL,
    course_id TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    started_at DATETIME,
    completed_at DATETIME,
    time_spent_minutes INTEGER DEFAULT 0,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES learning_lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES learning_courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, lesson_id)
);

-- Course ratings and reviews table
CREATE TABLE IF NOT EXISTS learning_course_reviews (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    course_id TEXT NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    is_verified BOOLEAN DEFAULT false, -- true if user completed the course
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES learning_courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, course_id)
);

-- Learning achievements/certificates table
CREATE TABLE IF NOT EXISTS learning_achievements (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    course_id TEXT NOT NULL,
    achievement_type TEXT NOT NULL CHECK (achievement_type IN ('completion', 'excellence', 'speed', 'consistency')),
    title TEXT NOT NULL,
    description TEXT,
    badge_url TEXT,
    certificate_url TEXT,
    earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES learning_courses(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_learning_courses_category ON learning_courses(category_id);
CREATE INDEX IF NOT EXISTS idx_learning_courses_status ON learning_courses(status);
CREATE INDEX IF NOT EXISTS idx_learning_courses_level ON learning_courses(level);
CREATE INDEX IF NOT EXISTS idx_learning_courses_type ON learning_courses(type);
CREATE INDEX IF NOT EXISTS idx_learning_courses_featured ON learning_courses(is_featured);
CREATE INDEX IF NOT EXISTS idx_learning_lessons_course ON learning_lessons(course_id);
CREATE INDEX IF NOT EXISTS idx_user_course_progress_user ON user_course_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_course_progress_course ON user_course_progress(course_id);
CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_user ON user_lesson_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_lesson ON user_lesson_progress(lesson_id);
CREATE INDEX IF NOT EXISTS idx_learning_course_reviews_course ON learning_course_reviews(course_id);
CREATE INDEX IF NOT EXISTS idx_learning_achievements_user ON learning_achievements(user_id);
`

const createRemindersTable = `
CREATE TABLE IF NOT EXISTS reminders (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    reminder_type TEXT NOT NULL DEFAULT 'once', -- 'once', 'daily', 'weekly', 'monthly'
    scheduled_at DATETIME NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    is_completed BOOLEAN DEFAULT FALSE,
    notification_sent BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_reminders_user_id ON reminders(user_id);
CREATE INDEX IF NOT EXISTS idx_reminders_scheduled_at ON reminders(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_reminders_type ON reminders(reminder_type);
CREATE INDEX IF NOT EXISTS idx_reminders_enabled ON reminders(is_enabled);
CREATE INDEX IF NOT EXISTS idx_reminders_completed ON reminders(is_completed);
`

const createSharesAndDividendsTables = `
-- Shares ownership table
CREATE TABLE IF NOT EXISTS shares (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    member_id TEXT NOT NULL,
    share_type TEXT NOT NULL DEFAULT 'ordinary', -- 'ordinary', 'preferred'
    shares_owned INTEGER NOT NULL DEFAULT 0,
    share_value REAL NOT NULL DEFAULT 0,
    total_value REAL NOT NULL DEFAULT 0,
    purchase_date DATETIME NOT NULL,
    certificate_number TEXT,
    status TEXT NOT NULL DEFAULT 'active', -- 'active', 'transferred', 'redeemed'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Dividend declarations table
CREATE TABLE IF NOT EXISTS dividend_declarations (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    declaration_date DATETIME NOT NULL,
    dividend_per_share REAL NOT NULL,
    total_dividend_amount REAL NOT NULL,
    payment_date DATETIME,
    status TEXT NOT NULL DEFAULT 'declared', -- 'declared', 'approved', 'paid', 'cancelled'
    declared_by TEXT NOT NULL,
    approved_by TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (declared_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- Individual dividend payments table
CREATE TABLE IF NOT EXISTS dividend_payments (
    id TEXT PRIMARY KEY,
    dividend_declaration_id TEXT NOT NULL,
    member_id TEXT NOT NULL,
    shares_eligible INTEGER NOT NULL,
    dividend_amount REAL NOT NULL,
    payment_status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'paid', 'failed'
    payment_date DATETIME,
    payment_method TEXT,
    transaction_reference TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dividend_declaration_id) REFERENCES dividend_declarations(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Share transactions table (for transfers, purchases, redemptions)
CREATE TABLE IF NOT EXISTS share_transactions (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    from_member_id TEXT,
    to_member_id TEXT,
    transaction_type TEXT NOT NULL, -- 'purchase', 'transfer', 'redemption', 'split'
    shares_count INTEGER NOT NULL,
    share_value REAL NOT NULL,
    total_amount REAL NOT NULL,
    transaction_date DATETIME NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'cancelled'
    approved_by TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (from_member_id) REFERENCES users(id),
    FOREIGN KEY (to_member_id) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shares_chama_member ON shares(chama_id, member_id);
CREATE INDEX IF NOT EXISTS idx_shares_status ON shares(status);
CREATE INDEX IF NOT EXISTS idx_dividend_declarations_chama ON dividend_declarations(chama_id);
CREATE INDEX IF NOT EXISTS idx_dividend_declarations_status ON dividend_declarations(status);
CREATE INDEX IF NOT EXISTS idx_dividend_payments_declaration ON dividend_payments(dividend_declaration_id);
CREATE INDEX IF NOT EXISTS idx_dividend_payments_member ON dividend_payments(member_id);
CREATE INDEX IF NOT EXISTS idx_dividend_payments_status ON dividend_payments(payment_status);
CREATE INDEX IF NOT EXISTS idx_share_transactions_chama ON share_transactions(chama_id);
CREATE INDEX IF NOT EXISTS idx_share_transactions_type ON share_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_share_transactions_status ON share_transactions(status);
`

const createPollsAndVotingTables = `
-- Polls table for voting and role escalation
CREATE TABLE IF NOT EXISTS polls (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    poll_type TEXT NOT NULL, -- 'general', 'role_escalation', 'financial_decision'
    created_by TEXT NOT NULL,
    start_date DATETIME NOT NULL,
    end_date DATETIME NOT NULL,
    status TEXT NOT NULL DEFAULT 'active', -- 'active', 'completed', 'cancelled'
    is_anonymous BOOLEAN DEFAULT TRUE,
    requires_majority BOOLEAN DEFAULT TRUE,
    majority_percentage REAL DEFAULT 50.0,
    total_eligible_voters INTEGER DEFAULT 0,
    total_votes_cast INTEGER DEFAULT 0,
    result TEXT, -- 'passed', 'failed', 'pending'
    result_declared_at DATETIME,
    metadata TEXT, -- JSON for additional poll-specific data
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Poll options table
CREATE TABLE IF NOT EXISTS poll_options (
    id TEXT PRIMARY KEY,
    poll_id TEXT NOT NULL,
    option_text TEXT NOT NULL,
    option_order INTEGER NOT NULL DEFAULT 0,
    vote_count INTEGER DEFAULT 0,
    metadata TEXT, -- JSON for option-specific data (e.g., candidate info for role escalation)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE
);

-- Votes table (anonymous voting)
CREATE TABLE IF NOT EXISTS votes (
    id TEXT PRIMARY KEY,
    poll_id TEXT NOT NULL,
    option_id TEXT NOT NULL,
    voter_hash TEXT NOT NULL, -- Hashed voter ID for anonymity
    vote_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_valid BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE,
    FOREIGN KEY (option_id) REFERENCES poll_options(id) ON DELETE CASCADE,
    UNIQUE(poll_id, voter_hash) -- One vote per voter per poll
);

-- Role escalation requests table
CREATE TABLE IF NOT EXISTS role_escalation_requests (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    candidate_id TEXT NOT NULL,
    current_role TEXT NOT NULL,
    requested_role TEXT NOT NULL,
    requested_by TEXT NOT NULL,
    poll_id TEXT,
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'voting'
    justification TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (candidate_id) REFERENCES users(id),
    FOREIGN KEY (requested_by) REFERENCES users(id),
    FOREIGN KEY (poll_id) REFERENCES polls(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_polls_chama ON polls(chama_id);
CREATE INDEX IF NOT EXISTS idx_polls_status ON polls(status);
CREATE INDEX IF NOT EXISTS idx_polls_type ON polls(poll_type);
CREATE INDEX IF NOT EXISTS idx_polls_dates ON polls(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_poll_options_poll ON poll_options(poll_id);
CREATE INDEX IF NOT EXISTS idx_votes_poll ON votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_votes_option ON votes(option_id);
CREATE INDEX IF NOT EXISTS idx_votes_hash ON votes(voter_hash);
CREATE INDEX IF NOT EXISTS idx_role_escalation_chama ON role_escalation_requests(chama_id);
CREATE INDEX IF NOT EXISTS idx_role_escalation_candidate ON role_escalation_requests(candidate_id);
CREATE INDEX IF NOT EXISTS idx_role_escalation_status ON role_escalation_requests(status);
`

const createDisbursementTables = `
-- Disbursement batches table (for mass distributions)
CREATE TABLE IF NOT EXISTS disbursement_batches (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    batch_type TEXT NOT NULL, -- 'dividend', 'shares', 'savings', 'loan'
    title TEXT NOT NULL,
    description TEXT,
    total_amount REAL NOT NULL,
    total_recipients INTEGER NOT NULL,
    initiated_by TEXT NOT NULL,
    approved_by TEXT,
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'processing', 'completed', 'failed'
    approval_required BOOLEAN DEFAULT TRUE,
    scheduled_date DATETIME,
    processed_date DATETIME,
    metadata TEXT, -- JSON for batch-specific data
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (initiated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- Individual disbursements table
CREATE TABLE IF NOT EXISTS disbursements (
    id TEXT PRIMARY KEY,
    batch_id TEXT NOT NULL,
    recipient_id TEXT NOT NULL,
    disbursement_type TEXT NOT NULL, -- 'dividend', 'share_redemption', 'savings_withdrawal', 'loan_disbursement'
    amount REAL NOT NULL,
    currency TEXT DEFAULT 'KES',
    payment_method TEXT NOT NULL, -- 'bank_transfer', 'mobile_money', 'cash'
    account_details TEXT, -- JSON with payment details
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    transaction_reference TEXT,
    processed_date DATETIME,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata TEXT, -- JSON for disbursement-specific data
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES disbursement_batches(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id)
);

-- Financial transparency log table
CREATE TABLE IF NOT EXISTS financial_transparency_log (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    activity_type TEXT NOT NULL, -- 'disbursement', 'revenue', 'expense', 'contribution'
    title TEXT NOT NULL,
    description TEXT,
    amount REAL NOT NULL,
    currency TEXT DEFAULT 'KES',
    transaction_type TEXT NOT NULL, -- 'debit', 'credit'
    reference_id TEXT, -- Reference to related transaction/disbursement
    reference_type TEXT, -- 'disbursement_batch', 'transaction', 'contribution'
    performed_by TEXT NOT NULL,
    affected_members TEXT, -- JSON array of affected member IDs
    visibility TEXT NOT NULL DEFAULT 'all_members', -- 'all_members', 'officials_only'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES users(id)
);

-- Reports and invoices table
CREATE TABLE IF NOT EXISTS financial_reports (
    id TEXT PRIMARY KEY,
    chama_id TEXT NOT NULL,
    report_type TEXT NOT NULL, -- 'monthly_statement', 'dividend_report', 'disbursement_report', 'transparency_report'
    title TEXT NOT NULL,
    description TEXT,
    report_period_start DATETIME,
    report_period_end DATETIME,
    generated_by TEXT NOT NULL,
    file_path TEXT, -- Path to generated PDF/document
    file_size INTEGER,
    status TEXT NOT NULL DEFAULT 'generating', -- 'generating', 'ready', 'failed'
    download_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    metadata TEXT, -- JSON for report-specific data
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chama_id) REFERENCES chamas(id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(id)
);`

const createDeliveryContactsTable = `
-- Delivery contacts table (for marketplace delivery management)
CREATE TABLE IF NOT EXISTS delivery_contacts (
    id TEXT PRIMARY KEY,
    seller_id TEXT NOT NULL,
    user_id TEXT,
    name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    address TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for delivery contacts
CREATE INDEX IF NOT EXISTS idx_delivery_contacts_seller ON delivery_contacts(seller_id);
CREATE INDEX IF NOT EXISTS idx_delivery_contacts_user ON delivery_contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_delivery_contacts_active ON delivery_contacts(is_active);
`

const createMarketplaceRolesTable = `
-- Marketplace roles table (for tracking user roles in marketplace)
CREATE TABLE IF NOT EXISTS marketplace_roles (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('buyer', 'seller', 'delivery_person')),
    auto_detected BOOLEAN DEFAULT FALSE,
    registration_data TEXT, -- JSON data from registration
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, role)
);

-- Create indexes for marketplace roles
CREATE INDEX IF NOT EXISTS idx_marketplace_roles_user ON marketplace_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_roles_role ON marketplace_roles(role);
CREATE INDEX IF NOT EXISTS idx_marketplace_roles_active ON marketplace_roles(is_active);
`

const populateExistingSellers = `
-- Populate existing sellers from products table
INSERT OR IGNORE INTO marketplace_roles (user_id, role, auto_detected, registration_data, is_active, created_at, updated_at)
SELECT DISTINCT
    p.seller_id,
    'seller',
    1,
    json_object(
        'auto_detected', 1,
        'product_count', (SELECT COUNT(*) FROM products p2 WHERE p2.seller_id = p.seller_id),
        'first_product_date', (SELECT MIN(created_at) FROM products p3 WHERE p3.seller_id = p.seller_id),
        'migration_date', datetime('now')
    ),
    1,
    datetime('now'),
    datetime('now')
FROM products p
WHERE p.seller_id IS NOT NULL
AND p.seller_id != ''
AND NOT EXISTS (
    SELECT 1 FROM marketplace_roles mr
    WHERE mr.user_id = p.seller_id AND mr.role = 'seller'
);
`

// addOrderItemDeliveryPersonField adds delivery_person_id field to order_items table
const addOrderItemDeliveryPersonField = "SELECT 1" // Placeholder - actual logic in migration function

// addMissingOrderItemDeliveryPersonField adds delivery_person_id field to order_items table if it doesn't exist
func addMissingOrderItemDeliveryPersonField(db *sql.DB) error {
	// Check if delivery_person_id column exists
	var exists bool
	query := `SELECT COUNT(*) FROM pragma_table_info('order_items') WHERE name = 'delivery_person_id'`
	err := db.QueryRow(query).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if delivery_person_id column exists: %w", err)
	}

	// Add column if it doesn't exist
	if !exists {
		alterQuery := "ALTER TABLE order_items ADD COLUMN delivery_person_id TEXT"
		if _, err := db.Exec(alterQuery); err != nil {
			return fmt.Errorf("failed to add delivery_person_id column: %w", err)
		}
		log.Printf("Added delivery_person_id column to order_items table")
	} else {
		log.Printf("Column delivery_person_id already exists in order_items table")
	}

	return nil
}

// addOrderItemDeliveryFields adds delivery-related fields to order_items table
const addOrderItemDeliveryFields = "SELECT 1" // Placeholder - actual logic in migration function

// addMissingOrderItemDeliveryFields adds delivery fields to order_items table if they don't exist
func addMissingOrderItemDeliveryFields(db *sql.DB) error {
	columns := []struct {
		name     string
		dataType string
	}{
		{"delivery_person_id", "TEXT"},
		{"delivery_status", "TEXT DEFAULT 'pending'"},
		{"delivery_fee", "REAL DEFAULT 0"},
		{"buyer_notes", "TEXT"},
		{"delivery_notes", "TEXT"},
		{"assigned_at", "DATETIME"},
		{"delivered_at", "DATETIME"},
	}

	for _, col := range columns {
		// Check if column exists
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('order_items') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE order_items ADD COLUMN %s %s", col.name, col.dataType)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to order_items table", col.name)
		} else {
			log.Printf("Column %s already exists in order_items table", col.name)
		}
	}

	// Add foreign key constraint for delivery_person_id if it doesn't exist
	// Note: SQLite doesn't support adding foreign key constraints to existing tables,
	// so we'll handle this at the application level

	return nil
}

// addEnhancedLearningContentFields adds enhanced content fields to learning_courses table
const addEnhancedLearningContentFields = "SELECT 1" // Placeholder - actual logic in migration function

// createQuizResultsTable creates the quiz_results table for storing quiz results
const createQuizResultsTable = `
CREATE TABLE IF NOT EXISTS quiz_results (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    course_id TEXT NOT NULL,
    score INTEGER NOT NULL,
    correct_answers INTEGER NOT NULL,
    total_questions INTEGER NOT NULL,
    passed BOOLEAN NOT NULL DEFAULT 0,
    time_taken INTEGER, -- in seconds
    detailed_results TEXT, -- JSON string with detailed results
    created_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES learning_courses(id) ON DELETE CASCADE
);`

// addMissingEnhancedLearningContentFields adds enhanced learning content fields if they don't exist
func addMissingEnhancedLearningContentFields(db *sql.DB) error {
	// First check if the learning_courses table exists
	var tableExists bool
	query := `SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='learning_courses'`
	err := db.QueryRow(query).Scan(&tableExists)
	if err != nil {
		return fmt.Errorf("failed to check if learning_courses table exists: %w", err)
	}

	if !tableExists {
		log.Printf("learning_courses table does not exist, skipping enhanced learning content fields migration")
		return nil
	}

	// Define the columns to add
	columns := []struct {
		name     string
		dataType string
	}{
		{"video_url", "TEXT"},
		{"quiz_questions", "TEXT"},
		{"article_content", "TEXT"},
		{"course_structure", "TEXT"},
	}

	// Check and add each column
	for _, col := range columns {
		var exists bool
		query := `SELECT COUNT(*) FROM pragma_table_info('learning_courses') WHERE name = ?`
		err := db.QueryRow(query, col.name).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if column %s exists: %w", col.name, err)
		}

		// Add column if it doesn't exist
		if !exists {
			alterQuery := fmt.Sprintf("ALTER TABLE learning_courses ADD COLUMN %s %s", col.name, col.dataType)
			if _, err := db.Exec(alterQuery); err != nil {
				return fmt.Errorf("failed to add column %s: %w", col.name, err)
			}
			log.Printf("Added column %s to learning_courses table", col.name)
		} else {
			log.Printf("Column %s already exists in learning_courses table", col.name)
		}
	}

	return nil
}

// addChamaCategoryColumn adds category column to chamas table
const addChamaCategoryColumn = `
-- This will be handled by addCategoryColumnToChamasTable function
SELECT 1;
`
