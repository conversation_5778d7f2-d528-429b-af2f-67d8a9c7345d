package api

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/oauth2"
	"vaultke-backend/internal/services"
)

// GoogleDriveTokens represents the OAuth tokens for Google Drive
type GoogleDriveTokens struct {
	AccessToken  string    `json:"access_token" db:"access_token"`
	RefreshToken string    `json:"refresh_token" db:"refresh_token"`
	ExpiresIn    int       `json:"expires_in" db:"expires_in"`
	TokenType    string    `json:"token_type" db:"token_type"`
	Timestamp    time.Time `json:"timestamp"`
}

// OAuthState represents the OAuth state with security measures
type OAuthState struct {
	UserID    string    `json:"user_id"`
	State     string    `json:"state"`
	Nonce     string    `json:"nonce"`
	Challenge string    `json:"challenge"`
	Verifier  string    `json:"verifier"`
	Method    string    `json:"method"`
	ExpiresAt time.Time `json:"expires_at"`
}

// Global state storage for OAuth states (in production, use Redis or database)
var (
	globalOAuthStates = make(map[string]*OAuthState)
	globalStatesMutex sync.RWMutex
)

// SecureOAuthService handles secure OAuth operations
type SecureOAuthService struct {
	clientID     string
	clientSecret string
	redirectURI  string
	jwtSecret    []byte
}

// NewSecureOAuthService creates a new secure OAuth service
func NewSecureOAuthService(clientID, clientSecret, redirectURI string, jwtSecret []byte) *SecureOAuthService {
	return &SecureOAuthService{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURI:  redirectURI,
		jwtSecret:    jwtSecret,
	}
}

// generateSecureState creates a cryptographically secure state parameter
func (s *SecureOAuthService) generateSecureState() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// generateNonce creates a unique nonce for the request
func (s *SecureOAuthService) generateNonce() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generatePKCEChallenge creates PKCE challenge and verifier
func (s *SecureOAuthService) generatePKCEChallenge() (challenge, verifier string, err error) {
	// Generate code verifier (43-128 characters)
	verifierBytes := make([]byte, 32)
	if _, err := rand.Read(verifierBytes); err != nil {
		return "", "", err
	}
	verifier = base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(verifierBytes)

	// Generate code challenge (SHA256 hash of verifier)
	hash := sha256.Sum256([]byte(verifier))
	challenge = base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(hash[:])

	return challenge, verifier, nil
}

// createSecureToken creates a JWT token containing encrypted user context
func (s *SecureOAuthService) createSecureToken(userID string, state *OAuthState) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"state":   state.State,
		"nonce":   state.Nonce,
		"exp":     time.Now().Add(10 * time.Minute).Unix(),
		"iat":     time.Now().Unix(),
		"iss":     "vaultke-oauth",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.jwtSecret)
}

// buildSecureAuthURL constructs the OAuth URL with security parameters
func (s *SecureOAuthService) buildSecureAuthURL(state, challenge, nonce string) string {
	baseURL := "https://accounts.google.com/o/oauth2/v2/auth"

	params := url.Values{}
	params.Add("client_id", s.clientID)
	params.Add("redirect_uri", s.redirectURI)
	params.Add("response_type", "code")
	params.Add("scope", "https://www.googleapis.com/auth/drive.file")
	params.Add("access_type", "offline")
	params.Add("prompt", "consent")

	// Security parameters
	params.Add("state", state)
	params.Add("nonce", nonce)
	params.Add("code_challenge", challenge)
	params.Add("code_challenge_method", "S256")

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// cleanupExpiredStates removes expired OAuth states from global storage
func cleanupExpiredStates() {
	globalStatesMutex.Lock()
	defer globalStatesMutex.Unlock()

	now := time.Now()
	for state, oauthState := range globalOAuthStates {
		if now.After(oauthState.ExpiresAt) {
			delete(globalOAuthStates, state)
			fmt.Printf("🧹 Cleaned up expired OAuth state: %s\n", state[:10]+"...")
		}
	}
}

// InitiateGoogleDriveAuth starts the secure OAuth flow
func (s *SecureOAuthService) InitiateGoogleDriveAuth(c *gin.Context, userID string) {
	// Clean up expired states first
	cleanupExpiredStates()

	// Generate secure parameters
	state, err := s.generateSecureState()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to generate secure state",
		})
		return
	}

	nonce, err := s.generateNonce()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to generate nonce",
		})
		return
	}

	challenge, verifier, err := s.generatePKCEChallenge()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to generate PKCE challenge",
		})
		return
	}

	// Create OAuth state object
	oauthState := &OAuthState{
		UserID:    userID,
		State:     state,
		Nonce:     nonce,
		Challenge: challenge,
		Verifier:  verifier,
		Method:    "S256",
		ExpiresAt: time.Now().Add(10 * time.Minute),
	}

	// Store state securely in global storage
	globalStatesMutex.Lock()
	globalOAuthStates[state] = oauthState
	globalStatesMutex.Unlock()

	fmt.Printf("🔐 OAuth state stored: %s (expires: %v)\n", state[:10]+"...", oauthState.ExpiresAt.Format("15:04:05"))

	// Create secure token for additional validation
	secureToken, err := s.createSecureToken(userID, oauthState)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create secure token",
		})
		return
	}

	// Build secure OAuth URL
	authURL := s.buildSecureAuthURL(state, challenge, nonce)

	// Return response without exposing sensitive data
	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "Google Drive authorization URL generated securely",
		"auth_url":     authURL,
		"secure_token": secureToken,
		"expires_in":   600, // 10 minutes
	})
}

// validateSecureToken validates the JWT secure token
func (s *SecureOAuthService) validateSecureToken(tokenString string, state *OAuthState) error {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtSecret, nil
	})

	if err != nil {
		return err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// Validate claims
		if claims["user_id"] != state.UserID ||
			claims["state"] != state.State ||
			claims["nonce"] != state.Nonce {
			return fmt.Errorf("token claims don't match state")
		}
		return nil
	}

	return fmt.Errorf("invalid token")
}

// HandleGoogleCallback processes the OAuth callback securely
func (s *SecureOAuthService) HandleGoogleCallback(c *gin.Context) {
	// Extract parameters
	code := c.Query("code")
	state := c.Query("state")
	receivedError := c.Query("error")

	fmt.Printf("🔄 OAuth callback received - State: %s, Code: %s\n",
		func() string { if len(state) > 10 { return state[:10]+"..." } else { return state } }(),
		func() string { if code != "" { return "present" } else { return "missing" } }())

	// Check for OAuth errors
	if receivedError != "" {
		c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
			"error": fmt.Sprintf("OAuth error: %s", receivedError),
		})
		return
	}

	// Validate required parameters
	if code == "" || state == "" {
		c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
			"error": "Missing required OAuth parameters",
		})
		return
	}

	// Retrieve and validate state from global storage
	globalStatesMutex.RLock()
	oauthState, exists := globalOAuthStates[state]
	stateCount := len(globalOAuthStates)
	globalStatesMutex.RUnlock()

	fmt.Printf("🔍 State validation - Exists: %v, Total states: %d\n", exists, stateCount)

	if !exists {
		fmt.Printf("❌ OAuth state not found: %s\n", state[:10]+"...")
		c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
			"error": "Invalid or expired OAuth state",
		})
		return
	}

	// Check state expiration
	if time.Now().After(oauthState.ExpiresAt) {
		globalStatesMutex.Lock()
		delete(globalOAuthStates, state)
		globalStatesMutex.Unlock()
		c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
			"error": "OAuth state expired",
		})
		return
	}

	// Validate secure token if provided
	secureToken := c.GetHeader("X-Secure-Token")
	if secureToken != "" {
		if err := s.validateSecureToken(secureToken, oauthState); err != nil {
			c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
				"error": "Invalid secure token",
			})
			return
		}
	}

	// Exchange code for tokens using PKCE
	tokens, err := s.exchangeCodeForTokens(code, oauthState)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
			"error": "Failed to exchange code for tokens: " + err.Error(),
		})
		return
	}

	// Clean up state from global storage
	globalStatesMutex.Lock()
	delete(globalOAuthStates, state)
	globalStatesMutex.Unlock()

	// Store tokens securely for the user
	if err := s.storeUserTokens(c, oauthState.UserID, tokens); err != nil {
		c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
			"error": "Failed to store user tokens: " + err.Error(),
		})
		return
	}

	// Get app URL from environment
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8081" // fallback
	}

	// Show success page
	c.HTML(http.StatusOK, "oauth_success_secure.html", gin.H{
		"message": "Google Drive connected successfully!",
		"note":    "Your connection is now active. You can create backups from the mobile app.",
		"app_url": appURL,
	})
}

// exchangeCodeForTokens exchanges authorization code for access tokens with PKCE
func (s *SecureOAuthService) exchangeCodeForTokens(code string, state *OAuthState) (*oauth2.Token, error) {
	// Create OAuth2 config
	config := &oauth2.Config{
		ClientID:     s.clientID,
		ClientSecret: s.clientSecret,
		RedirectURL:  s.redirectURI,
		Scopes:       []string{"https://www.googleapis.com/auth/drive.file"},
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://accounts.google.com/o/oauth2/auth",
			TokenURL: "https://oauth2.googleapis.com/token",
		},
	}

	// Create context with PKCE verifier
	ctx := context.WithValue(context.Background(), oauth2.HTTPClient, &http.Client{
		Timeout: 30 * time.Second,
	})

	// Exchange code for token with PKCE verifier
	token, err := config.Exchange(ctx, code, oauth2.SetAuthURLParam("code_verifier", state.Verifier))
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for token: %w", err)
	}

	return token, nil
}

// storeUserTokens securely stores user's OAuth tokens
func (s *SecureOAuthService) storeUserTokens(c *gin.Context, userID string, token *oauth2.Token) error {
	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		return fmt.Errorf("database not available in context")
	}

	// Create Google Drive service and store tokens
	driveService := services.NewGoogleDriveService(db.(*sql.DB))
	expiresIn := int(token.Expiry.Sub(time.Now()).Seconds())

	err := driveService.StoreUserTokens(userID, token.AccessToken, token.RefreshToken, expiresIn)
	if err != nil {
		return fmt.Errorf("failed to store tokens in database: %w", err)
	}

	fmt.Printf("✅ Tokens securely stored for user: %s\n", userID)
	return nil
}

// StoreGoogleDriveTokens stores the user's Google Drive OAuth tokens
func StoreGoogleDriveTokens(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var tokens GoogleDriveTokens
	if err := c.ShouldBindJSON(&tokens); err != nil {
		fmt.Printf("Error binding JSON: %v\n", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid token data: " + err.Error(),
		})
		return
	}

	// Add timestamp for logging
	tokens.Timestamp = time.Now()

	// Log the token storage attempt (without sensitive data)
	fmt.Printf("Storing Google Drive tokens for user: %s at %v\n", userID, tokens.Timestamp)

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Store tokens securely (encrypted)
	err := driveService.StoreUserTokens(userID, tokens.AccessToken, tokens.RefreshToken, tokens.ExpiresIn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to store Google Drive tokens: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Google Drive tokens stored successfully",
	})
}

// DisconnectGoogleDrive revokes and removes the user's Google Drive tokens
func DisconnectGoogleDrive(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Revoke tokens and remove from database
	err := driveService.DisconnectUser(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to disconnect Google Drive: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Google Drive disconnected successfully",
	})
}

// CreateGoogleDriveBackup creates a backup of user data to Google Drive
func CreateGoogleDriveBackup(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Create backup
	backupResult, err := driveService.CreateUserBackup(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create backup: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "Backup created successfully",
		"backup_id": backupResult.BackupID,
		"file_size": backupResult.FileSize,
		"timestamp": backupResult.Timestamp,
	})
}

// RestoreGoogleDriveBackup restores user data from Google Drive backup
func RestoreGoogleDriveBackup(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Restore backup
	restoreResult, err := driveService.RestoreUserBackup(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to restore backup: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "Backup restored successfully",
		"restored_items": restoreResult.RestoredItems,
		"timestamp":      restoreResult.Timestamp,
	})
}

// GetGoogleDriveBackupInfo gets information about the user's backup
func GetGoogleDriveBackupInfo(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Get backup info
	backupInfo, err := driveService.GetUserBackupInfo(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get backup info: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"connected":   backupInfo.Connected,
		"lastBackup":  backupInfo.LastBackup,
		"backupSize":  backupInfo.BackupSize,
		"backupCount": backupInfo.BackupCount,
	})
}

// GetGoogleDriveStatus checks if user has Google Drive connected
func GetGoogleDriveStatus(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Check connection status
	connected, err := driveService.IsUserConnected(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to check Google Drive status: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"connected": connected,
	})
}

// GetGoogleDriveAuthURL generates secure OAuth URL for frontend
func GetGoogleDriveAuthURL(c *gin.Context) {
	// Extract user ID from authenticated context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Invalid user context",
		})
		return
	}

	// Get credentials from environment
	clientID := os.Getenv("GOOGLE_DRIVE_CLIENT_ID")
	clientSecret := os.Getenv("GOOGLE_DRIVE_CLIENT_SECRET")
	redirectURL := os.Getenv("GOOGLE_DRIVE_REDIRECT_URL")
	jwtSecret := os.Getenv("JWT_SECRET")

	if clientID == "" || clientSecret == "" {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Google Drive not configured on server",
		})
		return
	}

	if redirectURL == "" {
		redirectURL = "http://localhost:8080/api/v1/auth/google/callback" // fallback
	}

	if jwtSecret == "" {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "JWT secret not configured",
		})
		return
	}

	// Create secure OAuth service
	oauthService := NewSecureOAuthService(clientID, clientSecret, redirectURL, []byte(jwtSecret))

	// Generate secure OAuth URL
	oauthService.InitiateGoogleDriveAuth(c, userIDStr)
}

// InitiateGoogleDriveAuth starts the OAuth flow for Google Drive (public endpoint)
func InitiateGoogleDriveAuth(c *gin.Context) {
	// This is a public endpoint that doesn't require authentication
	// It should be used for testing or when user authentication isn't available

	// For security, we'll require a user_id parameter or return an error
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "user_id parameter is required for public OAuth initiation",
			"message": "Please use the authenticated endpoint /api/v1/users/google-drive/auth-url instead",
		})
		return
	}

	// Get credentials from environment
	clientID := os.Getenv("GOOGLE_DRIVE_CLIENT_ID")
	clientSecret := os.Getenv("GOOGLE_DRIVE_CLIENT_SECRET")
	redirectURL := os.Getenv("GOOGLE_DRIVE_REDIRECT_URL")
	jwtSecret := os.Getenv("JWT_SECRET")

	if clientID == "" || clientSecret == "" {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Google Drive not configured on server",
		})
		return
	}

	if redirectURL == "" {
		redirectURL = "http://localhost:8080/api/v1/auth/google/callback" // fallback
	}

	if jwtSecret == "" {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "JWT secret not configured",
		})
		return
	}

	// Create secure OAuth service
	oauthService := NewSecureOAuthService(clientID, clientSecret, redirectURL, []byte(jwtSecret))

	// Generate secure OAuth URL
	oauthService.InitiateGoogleDriveAuth(c, userID)
}

// HandleGoogleDriveCallback handles the OAuth callback securely
func HandleGoogleDriveCallback(c *gin.Context) {
	// Get credentials from environment
	clientID := os.Getenv("GOOGLE_DRIVE_CLIENT_ID")
	clientSecret := os.Getenv("GOOGLE_DRIVE_CLIENT_SECRET")
	redirectURL := os.Getenv("GOOGLE_DRIVE_REDIRECT_URL")
	jwtSecret := os.Getenv("JWT_SECRET")

	if clientID == "" || clientSecret == "" {
		c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
			"error": "Google Drive credentials not configured on server",
		})
		return
	}

	if redirectURL == "" {
		redirectURL = "http://localhost:8080/api/v1/auth/google/callback" // fallback
	}

	if jwtSecret == "" {
		c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
			"error": "JWT secret not configured on server",
		})
		return
	}

	// Create secure OAuth service
	oauthService := NewSecureOAuthService(clientID, clientSecret, redirectURL, []byte(jwtSecret))

	// Handle callback securely
	oauthService.HandleGoogleCallback(c)
}
