package api

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
	"vaultke-backend/internal/services"
)

// GoogleDriveTokens represents the OAuth tokens for Google Drive
type GoogleDriveTokens struct {
	AccessToken  string    `json:"access_token" db:"access_token"`
	RefreshToken string    `json:"refresh_token" db:"refresh_token"`
	ExpiresIn    int       `json:"expires_in" db:"expires_in"`
	TokenType    string    `json:"token_type" db:"token_type"`
	Timestamp    time.Time `json:"timestamp"`
}

// StoreGoogleDriveTokens stores the user's Google Drive OAuth tokens
func StoreGoogleDriveTokens(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var tokens GoogleDriveTokens
	if err := c.Should<PERSON>(&tokens); err != nil {
		fmt.Printf("Error binding JSON: %v\n", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid token data: " + err.Error(),
		})
		return
	}

	// Add timestamp for logging
	tokens.Timestamp = time.Now()

	// Log the token storage attempt (without sensitive data)
	fmt.Printf("Storing Google Drive tokens for user: %s at %v\n", userID, tokens.Timestamp)

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Fix: Ensure we have a valid expiry time
	expiresIn := tokens.ExpiresIn
	if expiresIn <= 0 || expiresIn < 300 {
		// If no expiry or less than 5 minutes, set to 1 hour
		expiresIn = 3600
		fmt.Printf("⚠️ Manual token expiry invalid (%d), setting to 1 hour\n", tokens.ExpiresIn)
	}

	// Store tokens securely (encrypted)
	err := driveService.StoreUserTokens(userID, tokens.AccessToken, tokens.RefreshToken, expiresIn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to store Google Drive tokens: " + err.Error(),
		})
		return
	}

	fmt.Printf("✅ Manual tokens stored for user: %s with %d seconds expiry\n", userID, expiresIn)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Google Drive tokens stored successfully",
	})
}

// DisconnectGoogleDrive revokes and removes the user's Google Drive tokens
func DisconnectGoogleDrive(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Revoke tokens and remove from database
	err := driveService.DisconnectUser(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to disconnect Google Drive: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Google Drive disconnected successfully",
	})
}

// CreateGoogleDriveBackup creates a backup of user data to Google Drive
func CreateGoogleDriveBackup(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Create backup
	backupResult, err := driveService.CreateUserBackup(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create backup: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "Backup created successfully",
		"backup_id": backupResult.BackupID,
		"file_size": backupResult.FileSize,
		"timestamp": backupResult.Timestamp,
	})
}

// RestoreGoogleDriveBackup restores user data from Google Drive backup
func RestoreGoogleDriveBackup(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Restore backup
	restoreResult, err := driveService.RestoreUserBackup(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to restore backup: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "Backup restored successfully",
		"restored_items": restoreResult.RestoredItems,
		"timestamp":      restoreResult.Timestamp,
	})
}

// GetGoogleDriveBackupInfo gets information about the user's backup
func GetGoogleDriveBackupInfo(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Get backup info
	backupInfo, err := driveService.GetUserBackupInfo(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get backup info: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"connected":   backupInfo.Connected,
		"lastBackup":  backupInfo.LastBackup,
		"backupSize":  backupInfo.BackupSize,
		"backupCount": backupInfo.BackupCount,
	})
}

// GetGoogleDriveStatus checks if user has Google Drive connected
func GetGoogleDriveStatus(c *gin.Context) {
	userID := c.GetString("userID")
	fmt.Printf("🔍 GetGoogleDriveStatus called for user: %s\n", userID)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Create Google Drive service
	driveService := services.NewGoogleDriveService(db.(*sql.DB))

	// Check connection status
	connected, err := driveService.IsUserConnected(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to check Google Drive status: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"connected": connected,
	})
}

// GetGoogleDriveAuthURL generates OAuth URL for frontend
func GetGoogleDriveAuthURL(c *gin.Context) {
	// Get credentials from environment
	clientID := os.Getenv("GOOGLE_DRIVE_CLIENT_ID")
	redirectURL := os.Getenv("GOOGLE_DRIVE_REDIRECT_URL")

	if clientID == "" {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Google Drive not configured on server",
		})
		return
	}

	if redirectURL == "" {
		redirectURL = "http://localhost:8080/api/v1/auth/google/callback" // fallback
	}

	// Build OAuth URL
	authURL := fmt.Sprintf(
		"https://accounts.google.com/o/oauth2/v2/auth?"+
			"client_id=%s&"+
			"redirect_uri=%s&"+
			"response_type=code&"+
			"scope=%s&"+
			"access_type=offline&"+
			"prompt=consent",
		clientID,
		redirectURL,
		"https://www.googleapis.com/auth/drive.file",
	)

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"auth_url": authURL,
		"client_id": clientID,
		"message":  "Use this URL to connect Google Drive",
	})
}

// InitiateGoogleDriveAuth starts the OAuth flow for Google Drive (legacy endpoint)
func InitiateGoogleDriveAuth(c *gin.Context) {
	// Redirect to the new endpoint
	GetGoogleDriveAuthURL(c)
}

// HandleGoogleDriveCallback handles the OAuth callback
func HandleGoogleDriveCallback(c *gin.Context) {
	fmt.Printf("🔍 OAuth callback triggered with code: %s\n", c.Query("code")[:20]+"...")

	code := c.Query("code")
	if code == "" {
		c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
			"error": "Missing authorization code",
		})
		return
	}

	// Exchange authorization code for tokens
	clientID := os.Getenv("GOOGLE_DRIVE_CLIENT_ID")
	clientSecret := os.Getenv("GOOGLE_DRIVE_CLIENT_SECRET")
	redirectURL := os.Getenv("GOOGLE_DRIVE_REDIRECT_URL")

	if clientID == "" || clientSecret == "" {
		c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
			"error": "Google Drive credentials not configured on server",
		})
		return
	}

	// Create OAuth2 config
	config := &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		RedirectURL:  redirectURL,
		Scopes:       []string{"https://www.googleapis.com/auth/drive.file"},
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://accounts.google.com/o/oauth2/auth",
			TokenURL: "https://oauth2.googleapis.com/token",
		},
	}

	// Exchange code for token
	token, err := config.Exchange(context.Background(), code)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
			"error": "Failed to exchange authorization code for tokens: " + err.Error(),
		})
		return
	}

	// Log successful token exchange (without exposing token values)
	fmt.Printf("✅ OAuth token exchange successful for Google Drive\n")

	// Store tokens automatically for the user
	// Note: In production, you'd need to identify the user from the OAuth state parameter
	// For now, we'll store for the admin user as a demo
	userID := "c9fca28d-595f-4cf5-b3d2-b02472d21621" // TODO: Get from OAuth state parameter

	// Get database from context (if available)
	db, exists := c.Get("db")
	fmt.Printf("🔍 Database context exists: %v\n", exists)
	if exists {
		// Create Google Drive service and store tokens
		driveService := services.NewGoogleDriveService(db.(*sql.DB))

		// Fix: Ensure we have a valid expiry time (Google tokens typically last 1 hour)
		var expiresIn int
		if token.Expiry.IsZero() || token.Expiry.Before(time.Now()) {
			// If no expiry or already expired, set to 1 hour (3600 seconds)
			expiresIn = 3600
			fmt.Printf("⚠️ Token expiry invalid or missing, setting to 1 hour\n")
		} else {
			expiresIn = int(token.Expiry.Sub(time.Now()).Seconds())
			// Ensure minimum 5 minutes remaining
			if expiresIn < 300 {
				expiresIn = 3600 // Reset to 1 hour if less than 5 minutes
				fmt.Printf("⚠️ Token expires too soon (%d seconds), extending to 1 hour\n", expiresIn)
			}
		}

		fmt.Printf("🔍 Token details - Expiry: %v, ExpiresIn: %d seconds\n", token.Expiry, expiresIn)

		err = driveService.StoreUserTokens(userID, token.AccessToken, token.RefreshToken, expiresIn)
		if err != nil {
			fmt.Printf("❌ Failed to auto-store tokens: %v\n", err)
			c.HTML(http.StatusInternalServerError, "oauth_error.html", gin.H{
				"error": "Failed to store authentication tokens: " + err.Error(),
			})
			return
		}

		fmt.Printf("✅ Tokens automatically stored for user: %s with %d seconds expiry\n", userID, expiresIn)
	} else {
		fmt.Printf("❌ Database context not available, cannot store tokens\n")
	}

	// Get app URL from environment
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8081" // fallback
	}

	// SECURITY: Never display tokens in browser
	// Show success page with secure messaging
	c.HTML(http.StatusOK, "oauth_success_secure.html", gin.H{
		"message": "Google Drive connected successfully!",
		"note":    "Your connection is now active. You can create backups from the mobile app.",
		"app_url": appURL,
	})
}

// DebugGoogleDriveTokens shows stored tokens for debugging (remove in production)
func DebugGoogleDriveTokens(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Query tokens
	query := `
		SELECT user_id, expires_at, updated_at,
		       CASE WHEN expires_at > datetime('now') THEN 'valid' ELSE 'expired' END as status
		FROM google_drive_tokens
		WHERE user_id = ?
	`

	var storedUserID, expiresAt, updatedAt, status string
	err := db.(*sql.DB).QueryRow(query, userID).Scan(&storedUserID, &expiresAt, &updatedAt, &status)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "No tokens found for user",
			"user_id": userID,
		})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to query tokens: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"user_id":    storedUserID,
		"expires_at": expiresAt,
		"updated_at": updatedAt,
		"status":     status,
		"current_time": time.Now().Format("2006-01-02 15:04:05"),
	})
}
