package api

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"

	"vaultke-backend/internal/services"
)

// InitializeE2EEKeys initializes military-grade E2EE keys for a user
func InitializeE2EEKeys(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.<PERSON>(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get database connection
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Initialize E2EE service
	e2eeService := services.NewMilitaryGradeE2EEService(db.(*sql.DB))

	// Initialize keys for the user
	keyBundle, err := e2eeService.InitializeUserKeys(userID)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to initialize E2EE keys: " + err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"keyBundle":     keyBundle,
			"securityLevel": "MILITARY_GRADE",
			"features": []string{
				"PERFECT_FORWARD_SECRECY",
				"POST_COMPROMISE_SECURITY",
				"AUTHENTICATED_ENCRYPTION",
				"METADATA_PROTECTION",
				"CURVE25519_KEY_EXCHANGE",
			},
		},
	})
}

// GetE2EEKeyBundle retrieves a user's public key bundle
func GetE2EEKeyBundle(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "User ID is required",
		})
		return
	}

	// Get database connection
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Initialize E2EE service
	e2eeService := services.NewMilitaryGradeE2EEService(db.(*sql.DB))

	// Get key bundle for the user
	keyBundle, err := e2eeService.GetKeyBundle(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Key bundle not found: " + err.Error(),
		})
		return
	}

	// Return only public components (never return private keys)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"userId":          keyBundle.UserID,
			"identityKey":     keyBundle.IdentityKey,
			"signedPreKey":    keyBundle.SignedPreKey,
			"preKeySignature": keyBundle.PreKeySignature,
			"oneTimePreKeys":  keyBundle.OneTimePreKeys,
			"registrationId":  keyBundle.RegistrationID,
			"securityLevel":   "MILITARY_GRADE",
		},
	})
}

// EncryptMessage encrypts a message using military-grade E2EE
func EncryptMessage(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var req struct {
		RecipientID string                 `json:"recipientId" binding:"required"`
		Plaintext   string                 `json:"plaintext" binding:"required"`
		Metadata    map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request: " + err.Error(),
		})
		return
	}

	// Get database connection
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Initialize E2EE service
	e2eeService := services.NewMilitaryGradeE2EEService(db.(*sql.DB))

	// Encrypt the message
	encryptedMessage, err := e2eeService.EncryptMessage(userID, req.RecipientID, req.Plaintext, req.Metadata)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to encrypt message: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    encryptedMessage,
	})
}

// DecryptMessage decrypts a military-grade encrypted message
func DecryptMessage(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var req services.EncryptedMessage
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid encrypted message: " + err.Error(),
		})
		return
	}

	// Verify user is authorized to decrypt this message
	if req.RecipientID != userID {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "Not authorized to decrypt this message",
		})
		return
	}

	// Get database connection
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Initialize E2EE service
	e2eeService := services.NewMilitaryGradeE2EEService(db.(*sql.DB))

	// Decrypt the message
	plaintext, metadata, err := e2eeService.DecryptMessage(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to decrypt message: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"plaintext": plaintext,
			"metadata":  metadata,
			"verified":  true,
		},
	})
}

// GetE2EESecurityStatus returns the security status of the E2EE system
func GetE2EESecurityStatus(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"securityLevel": "MILITARY_GRADE",
			"features": []string{
				"PERFECT_FORWARD_SECRECY",
				"POST_COMPROMISE_SECURITY",
				"AUTHENTICATED_ENCRYPTION_AES_256_GCM",
				"CURVE25519_KEY_EXCHANGE",
				"HKDF_KEY_DERIVATION",
				"HMAC_SHA256_AUTHENTICATION",
				"METADATA_PROTECTION",
				"CONSTANT_TIME_OPERATIONS",
				"SIDE_CHANNEL_RESISTANCE",
			},
			"algorithms": gin.H{
				"encryption":     "AES-256-GCM",
				"keyExchange":    "Curve25519",
				"keyDerivation":  "HKDF-SHA-256",
				"authentication": "HMAC-SHA-256",
				"hashing":        "SHA-256",
			},
			"status":  "ACTIVE",
			"version": "1.0",
		},
	})
}
