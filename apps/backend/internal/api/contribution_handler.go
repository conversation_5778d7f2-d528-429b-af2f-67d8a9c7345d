package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Contribution handlers
func GetContributions(c *gin.Context) {
	chamaID := c.Query("chamaId")
	if chamaID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "chamaId parameter is required",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    []interface{}{},
		"message": "No contributions found",
	})
}

func MakeContribution(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var req struct {
		ChamaID       string  `json:"chamaId" binding:"required" validate:"required,uuid"`
		Amount        float64 `json:"amount" binding:"required" validate:"required,amount"`
		Description   string  `json:"description" validate:"max=200,safe_text,no_sql_injection,no_xss"`
		Type          string  `json:"type" validate:"alphanumeric"` // "regular", "penalty", "special"
		PaymentMethod string  `json:"paymentMethod" validate:"alphanumeric,max=50"` // "wallet" or "mpesa"
		MpesaReference string `json:"mpesaReference,omitempty"` // For M-Pesa payments
		Status        string  `json:"status,omitempty"` // For pending M-Pesa payments
		IsAnonymous   bool    `json:"isAnonymous,omitempty"` // For anonymous contributions in contribution groups
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	// fmt.Printf("🔍 Making contribution: User ID: %v, Chama ID: %s, Amount: %.2f\n", userID, req.ChamaID, req.Amount)

	// Enhanced validation
	if req.Amount <= 0 || req.Amount > 10000000 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Amount must be between 1 and 10,000,000 KES",
		})
		return
	}

	// Validate contribution type
	validTypes := map[string]bool{
		"regular": true,
		"penalty": true,
		"special": true,
		"":        true, // Allow empty (defaults to regular)
	}
	if !validTypes[req.Type] {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid contribution type. Must be 'regular', 'penalty', or 'special'",
		})
		return
	}

	// Sanitize inputs
	req.ChamaID = sanitizeInput(req.ChamaID)
	req.Description = sanitizeInput(req.Description)
	req.Type = sanitizeInput(req.Type)
	req.PaymentMethod = sanitizeInput(req.PaymentMethod)

	// Get database connection
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Set default payment method if not provided
	if req.PaymentMethod == "" {
		req.PaymentMethod = "wallet"
	}

	// Start transaction
	tx, err := db.(*sql.DB).Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to start transaction",
		})
		return
	}
	defer tx.Rollback()

	// Handle different payment methods
	if req.PaymentMethod == "wallet" {
		// Check if user has sufficient balance in personal wallet
		var personalBalance float64
		err = tx.QueryRow(`
			SELECT COALESCE(balance, 0)
			FROM wallets
			WHERE owner_id = ? AND type = 'personal'
		`, userID).Scan(&personalBalance)
		if err != nil {
			if err == sql.ErrNoRows {
				// User doesn't have a personal wallet, create one with 0 balance
				fmt.Printf("⚠️ User %s doesn't have a personal wallet, creating one\n", userID)
				_, err = tx.Exec(`
					INSERT INTO wallets (id, owner_id, type, balance, created_at, updated_at)
					VALUES (?, ?, 'personal', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
				`, "wallet-"+userID.(string), userID)
				if err != nil {
					fmt.Printf("❌ Error creating wallet for user %s: %v\n", userID, err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"success": false,
						"error":   "Failed to create user wallet",
					})
					return
				}
				personalBalance = 0
			} else {
				fmt.Printf("❌ Error checking wallet balance for user %s: %v\n", userID, err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   "Failed to check wallet balance",
				})
				return
			}
		}

		fmt.Printf("💰 User %s wallet balance: %.2f, attempting to deduct: %.2f\n", userID, personalBalance, req.Amount)

		if personalBalance < req.Amount {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Insufficient balance in personal wallet",
			})
			return
		}

		// Deduct from personal wallet
		result, err := tx.Exec(`
			UPDATE wallets
			SET balance = balance - ?, updated_at = CURRENT_TIMESTAMP
			WHERE owner_id = ? AND type = 'personal'
		`, req.Amount, userID)
		if err != nil {
			fmt.Printf("❌ Error deducting from wallet for user %s: %v\n", userID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Failed to deduct from personal wallet",
			})
			return
		}

		rowsAffected, _ := result.RowsAffected()
		fmt.Printf("✅ Wallet deduction: %d rows affected for user %s\n", rowsAffected, userID)
	} else if req.PaymentMethod == "mpesa" {
		// For M-Pesa payments, we don't deduct from wallet
		// The M-Pesa callback will handle the actual payment processing
		// This is just creating a contribution record
		fmt.Printf("Creating M-Pesa contribution record with reference: %s\n", req.MpesaReference)
	}

	// Add to chama wallet
	fmt.Printf("💰 Adding %.2f to chama %s wallet\n", req.Amount, req.ChamaID)
	result, err := tx.Exec(`
		UPDATE wallets
		SET balance = balance + ?, updated_at = CURRENT_TIMESTAMP
		WHERE owner_id = ? AND type = 'chama'
	`, req.Amount, req.ChamaID)
	if err != nil {
		fmt.Printf("❌ Error updating chama wallet: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update chama wallet: " + err.Error(),
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		// If chama wallet doesn't exist, create it
		fmt.Printf("⚠️ Chama %s wallet doesn't exist, creating it with balance %.2f\n", req.ChamaID, req.Amount)
		_, err = tx.Exec(`
			INSERT INTO wallets (id, owner_id, type, balance, created_at, updated_at)
			VALUES (?, ?, 'chama', ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		`, "wallet-"+req.ChamaID, req.ChamaID, req.Amount)
		if err != nil {
			fmt.Printf("❌ Error creating chama wallet: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Failed to create chama wallet: " + err.Error(),
			})
			return
		}
		fmt.Printf("✅ Created chama wallet with balance %.2f\n", req.Amount)
	} else {
		fmt.Printf("✅ Updated chama wallet: %d rows affected\n", rowsAffected)
	}

	// Update chama's total_funds field to match wallet balance
	_, err = tx.Exec(`
		UPDATE chamas
		SET total_funds = (
			SELECT COALESCE(balance, 0)
			FROM wallets
			WHERE owner_id = ? AND type = 'chama'
		), updated_at = CURRENT_TIMESTAMP
		WHERE id = ?
	`, req.ChamaID, req.ChamaID)
	if err != nil {
		fmt.Printf("Warning: Failed to update chama total_funds: %v\n", err)
		// Don't fail the transaction for this, just log the warning
	}

	// Record the transaction
	transactionID := fmt.Sprintf("txn-%d", time.Now().UnixNano())
	contributionType := req.Type
	if contributionType == "" {
		contributionType = "regular"
	}

	// Set transaction status based on payment method
	transactionStatus := "completed"
	if req.PaymentMethod == "mpesa" {
		transactionStatus = "pending" // M-Pesa payments start as pending
	}

	// Create metadata to store anonymous flag
	metadata := make(map[string]interface{})
	if req.IsAnonymous {
		metadata["isAnonymous"] = true
		metadata["displayName"] = "Anonymous"
	}
	metadataJSON, _ := json.Marshal(metadata)

	// Always use the actual user ID for initiated_by to satisfy foreign key constraint
	// The anonymous flag is stored in metadata
	transactionInitiator := userID

	// Prepare transaction insert query with optional M-Pesa reference and anonymous support
	var insertQuery string
	var insertArgs []interface{}

	// Note: Wallet balance updates are handled above, transaction record is for audit purposes

	if req.PaymentMethod == "mpesa" && req.MpesaReference != "" {
		insertQuery = `
			INSERT INTO transactions (
				id, type, amount, currency, description, status, payment_method,
				reference, initiated_by, recipient_id, metadata, created_at, updated_at
			) VALUES (?, 'contribution', ?, 'KES', ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		`
		insertArgs = []interface{}{transactionID, req.Amount, req.Description, transactionStatus, req.PaymentMethod, req.MpesaReference, transactionInitiator, req.ChamaID, string(metadataJSON)}
	} else {
		insertQuery = `
			INSERT INTO transactions (
				id, type, amount, currency, description, status, payment_method,
				initiated_by, recipient_id, metadata, created_at, updated_at
			) VALUES (?, 'contribution', ?, 'KES', ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		`
		insertArgs = []interface{}{transactionID, req.Amount, req.Description, transactionStatus, req.PaymentMethod, transactionInitiator, req.ChamaID, string(metadataJSON)}
	}

	fmt.Printf("🔍 Executing transaction insert query with %d args\n", len(insertArgs))
	fmt.Printf("📝 Query: %s\n", insertQuery)
	fmt.Printf("📊 Args: %+v\n", insertArgs)

	_, err = tx.Exec(insertQuery, insertArgs...)
	if err != nil {
		fmt.Printf("❌ Transaction insert failed: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to record transaction: " + err.Error(),
		})
		return
	}

	fmt.Printf("✅ Transaction recorded successfully: %s\n", transactionID)

	// Update member's total contributions only for completed transactions (wallet payments)
	if transactionStatus == "completed" {
		fmt.Printf("✅ Updating member contributions for user %s in chama %s\n", userID, req.ChamaID)

		_, err = tx.Exec(`
			UPDATE chama_members
			SET total_contributions = total_contributions + ?,
			    last_contribution = CURRENT_TIMESTAMP
			WHERE chama_id = ? AND user_id = ?
		`, req.Amount, req.ChamaID, userID)
		if err != nil {
			fmt.Printf("❌ Error updating member contributions: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Failed to update member contributions",
			})
			return
		}

		fmt.Printf("✅ Successfully updated contributions for user %s\n", userID)
	}

	// Log final wallet balances before committing
	if req.PaymentMethod == "wallet" {
		var finalUserBalance, finalChamaBalance float64
		tx.QueryRow("SELECT COALESCE(balance, 0) FROM wallets WHERE owner_id = ? AND type = 'personal'", userID).Scan(&finalUserBalance)
		tx.QueryRow("SELECT COALESCE(balance, 0) FROM wallets WHERE owner_id = ? AND type = 'chama'", req.ChamaID).Scan(&finalChamaBalance)
		fmt.Printf("📊 Final balances - User %s: %.2f, Chama %s: %.2f\n", userID, finalUserBalance, req.ChamaID, finalChamaBalance)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		fmt.Printf("❌ Failed to commit transaction: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to commit transaction: " + err.Error(),
		})
		return
	}

	fmt.Printf("✅ Transaction committed successfully\n")

	// Return success response with appropriate message
	var message string
	if req.PaymentMethod == "mpesa" {
		message = "M-Pesa contribution initiated successfully"
	} else {
		message = "Contribution made successfully"
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": message,
		"data": map[string]interface{}{
			"id":            transactionID,
			"chamaId":       req.ChamaID,
			"amount":        req.Amount,
			"type":          contributionType,
			"description":   req.Description,
			"paymentMethod": req.PaymentMethod,
			"status":        transactionStatus,
			"contributedBy": userID,
			"createdAt":     time.Now().Format(time.RFC3339),
		},
	})
}

func GetContribution(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Get contribution endpoint - coming soon",
	})
}
