package services

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"vaultke-backend/internal/models"

	"github.com/google/uuid"
)

// SharesService handles shares-related business logic
type SharesService struct {
	db *sql.DB
}

// NewSharesService creates a new shares service
func NewSharesService(db *sql.DB) *SharesService {
	return &SharesService{db: db}
}

// CreateShares creates new shares for a member
func (s *SharesService) CreateShares(chamaID string, req *models.CreateShareRequest) (*models.Share, error) {
	// Validate member is part of the chama
	if !s.isMemberOfChama(req.MemberID, chamaID) {
		return nil, fmt.Errorf("member is not part of this chama")
	}

	// Generate unique ID
	shareID := uuid.New().String()
	now := time.Now()

	share := &models.Share{
		ID:                shareID,
		ChamaID:           chamaID,
		MemberID:          req.MemberID,
		ShareType:         req.ShareType,
		SharesOwned:       req.SharesCount,
		ShareValue:        req.ShareValue,
		PurchaseDate:      req.PurchaseDate,
		CertificateNumber: req.CertificateNumber,
		Status:            models.ShareStatusActive,
		CreatedAt:         now,
		UpdatedAt:         now,
	}

	// Calculate total value
	share.CalculateTotalValue()

	// Insert into database
	query := `
		INSERT INTO shares (
			id, chama_id, member_id, share_type, shares_owned, share_value, 
			total_value, purchase_date, certificate_number, status, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		share.ID,
		share.ChamaID,
		share.MemberID,
		share.ShareType,
		share.SharesOwned,
		share.ShareValue,
		share.TotalValue,
		share.PurchaseDate,
		share.CertificateNumber,
		share.Status,
		share.CreatedAt,
		share.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create shares: %w", err)
	}

	// Create share transaction record
	_, err = s.createShareTransaction(chamaID, &models.CreateShareTransactionRequest{
		ToMemberID:      &req.MemberID,
		TransactionType: models.ShareTransactionPurchase,
		SharesCount:     req.SharesCount,
		ShareValue:      req.ShareValue,
		TransactionDate: req.PurchaseDate,
		Description:     stringPtr("Initial share purchase"),
	})

	if err != nil {
		log.Printf("Warning: Failed to create share transaction record: %v", err)
	}

	log.Printf("Created shares %s for member %s in chama %s", shareID, req.MemberID, chamaID)
	return share, nil
}

// GetChamaShares retrieves all shares for a chama
func (s *SharesService) GetChamaShares(chamaID string, limit, offset int) ([]models.ShareWithMemberInfo, error) {
	query := `
		SELECT s.id, s.chama_id, s.member_id, s.share_type, s.shares_owned, s.share_value,
			   s.total_value, s.purchase_date, s.certificate_number, s.status, 
			   s.created_at, s.updated_at, u.first_name, u.last_name, u.email
		FROM shares s
		JOIN users u ON s.member_id = u.id
		WHERE s.chama_id = ? AND s.status = 'active'
		ORDER BY s.created_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := s.db.Query(query, chamaID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get chama shares: %w", err)
	}
	defer rows.Close()

	var shares []models.ShareWithMemberInfo
	for rows.Next() {
		var share models.ShareWithMemberInfo
		var firstName, lastName string

		err := rows.Scan(
			&share.ID,
			&share.ChamaID,
			&share.MemberID,
			&share.ShareType,
			&share.SharesOwned,
			&share.ShareValue,
			&share.TotalValue,
			&share.PurchaseDate,
			&share.CertificateNumber,
			&share.Status,
			&share.CreatedAt,
			&share.UpdatedAt,
			&firstName,
			&lastName,
			&share.MemberEmail,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan share: %w", err)
		}

		share.MemberName = firstName + " " + lastName
		shares = append(shares, share)
	}

	return shares, nil
}

// GetMemberShares retrieves all shares for a specific member in a chama
func (s *SharesService) GetMemberShares(chamaID, memberID string) ([]models.Share, error) {
	query := `
		SELECT id, chama_id, member_id, share_type, shares_owned, share_value,
			   total_value, purchase_date, certificate_number, status, created_at, updated_at
		FROM shares 
		WHERE chama_id = ? AND member_id = ? AND status = 'active'
		ORDER BY purchase_date DESC
	`

	rows, err := s.db.Query(query, chamaID, memberID)
	if err != nil {
		return nil, fmt.Errorf("failed to get member shares: %w", err)
	}
	defer rows.Close()

	var shares []models.Share
	for rows.Next() {
		var share models.Share
		err := rows.Scan(
			&share.ID,
			&share.ChamaID,
			&share.MemberID,
			&share.ShareType,
			&share.SharesOwned,
			&share.ShareValue,
			&share.TotalValue,
			&share.PurchaseDate,
			&share.CertificateNumber,
			&share.Status,
			&share.CreatedAt,
			&share.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan share: %w", err)
		}
		shares = append(shares, share)
	}

	return shares, nil
}

// GetChamaSharesSummary retrieves aggregated share information for a chama
func (s *SharesService) GetChamaSharesSummary(chamaID string) ([]models.ShareSummary, error) {
	query := `
		SELECT s.member_id, u.first_name, u.last_name, 
			   SUM(s.shares_owned) as total_shares, 
			   SUM(s.total_value) as total_value,
			   MAX(s.purchase_date) as last_purchase
		FROM shares s
		JOIN users u ON s.member_id = u.id
		WHERE s.chama_id = ? AND s.status = 'active'
		GROUP BY s.member_id, u.first_name, u.last_name
		ORDER BY total_shares DESC
	`

	rows, err := s.db.Query(query, chamaID)
	if err != nil {
		log.Printf("Error querying shares summary for chama %s: %v", chamaID, err)
		return nil, fmt.Errorf("failed to get chama shares summary: %w", err)
	}
	defer rows.Close()

	var summaries []models.ShareSummary
	for rows.Next() {
		var summary models.ShareSummary
		var firstName, lastName string
		var lastPurchase sql.NullTime

		err := rows.Scan(
			&summary.MemberID,
			&firstName,
			&lastName,
			&summary.TotalShares,
			&summary.TotalValue,
			&lastPurchase,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan share summary: %w", err)
		}

		summary.MemberName = firstName + " " + lastName
		if lastPurchase.Valid {
			summary.LastPurchase = &lastPurchase.Time
		}

		// Get detailed share types for this member
		shareTypes, err := s.GetMemberShares(chamaID, summary.MemberID)
		if err != nil {
			log.Printf("Warning: Failed to get share types for member %s: %v", summary.MemberID, err)
		} else {
			summary.ShareTypes = shareTypes
		}

		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// UpdateShares updates existing shares
func (s *SharesService) UpdateShares(shareID string, req *models.UpdateShareRequest) (*models.Share, error) {
	// Get existing share
	existing, err := s.getShareByID(shareID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.SharesOwned != nil {
		existing.SharesOwned = *req.SharesOwned
	}
	if req.ShareValue != nil {
		existing.ShareValue = *req.ShareValue
	}
	if req.CertificateNumber != nil {
		existing.CertificateNumber = req.CertificateNumber
	}
	if req.Status != nil {
		existing.Status = *req.Status
	}

	// Recalculate total value
	existing.CalculateTotalValue()
	existing.UpdatedAt = time.Now()

	// Update in database
	query := `
		UPDATE shares 
		SET shares_owned = ?, share_value = ?, total_value = ?, 
			certificate_number = ?, status = ?, updated_at = ?
		WHERE id = ?
	`

	_, err = s.db.Exec(
		query,
		existing.SharesOwned,
		existing.ShareValue,
		existing.TotalValue,
		existing.CertificateNumber,
		existing.Status,
		existing.UpdatedAt,
		shareID,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to update shares: %w", err)
	}

	log.Printf("Updated shares %s", shareID)
	return existing, nil
}

// Helper functions
func (s *SharesService) getShareByID(shareID string) (*models.Share, error) {
	query := `
		SELECT id, chama_id, member_id, share_type, shares_owned, share_value,
			   total_value, purchase_date, certificate_number, status, created_at, updated_at
		FROM shares WHERE id = ?
	`

	var share models.Share
	err := s.db.QueryRow(query, shareID).Scan(
		&share.ID,
		&share.ChamaID,
		&share.MemberID,
		&share.ShareType,
		&share.SharesOwned,
		&share.ShareValue,
		&share.TotalValue,
		&share.PurchaseDate,
		&share.CertificateNumber,
		&share.Status,
		&share.CreatedAt,
		&share.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("share not found")
		}
		return nil, fmt.Errorf("failed to get share: %w", err)
	}

	return &share, nil
}

func (s *SharesService) isMemberOfChama(memberID, chamaID string) bool {
	query := `SELECT 1 FROM chama_members WHERE user_id = ? AND chama_id = ? AND is_active = TRUE`
	var exists int
	err := s.db.QueryRow(query, memberID, chamaID).Scan(&exists)
	return err == nil
}

func (s *SharesService) createShareTransaction(chamaID string, req *models.CreateShareTransactionRequest) (*models.ShareTransaction, error) {
	transactionID := uuid.New().String()
	now := time.Now()

	transaction := &models.ShareTransaction{
		ID:              transactionID,
		ChamaID:         chamaID,
		FromMemberID:    req.FromMemberID,
		ToMemberID:      req.ToMemberID,
		TransactionType: req.TransactionType,
		SharesCount:     req.SharesCount,
		ShareValue:      req.ShareValue,
		TotalAmount:     float64(req.SharesCount) * req.ShareValue,
		TransactionDate: req.TransactionDate,
		Status:          models.ShareTransactionCompleted,
		Description:     req.Description,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	query := `
		INSERT INTO share_transactions (
			id, chama_id, from_member_id, to_member_id, transaction_type,
			shares_count, share_value, total_amount, transaction_date, status,
			description, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(
		query,
		transaction.ID,
		transaction.ChamaID,
		transaction.FromMemberID,
		transaction.ToMemberID,
		transaction.TransactionType,
		transaction.SharesCount,
		transaction.ShareValue,
		transaction.TotalAmount,
		transaction.TransactionDate,
		transaction.Status,
		transaction.Description,
		transaction.CreatedAt,
		transaction.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create share transaction: %w", err)
	}

	return transaction, nil
}

// GetShareTransactions retrieves share transactions for a chama
func (s *SharesService) GetShareTransactions(chamaID string, limit, offset int) ([]models.ShareTransaction, error) {
	query := `
		SELECT id, chama_id, from_member_id, to_member_id, transaction_type,
			   shares_count, share_value, total_amount, transaction_date, status,
			   approved_by, description, created_at, updated_at
		FROM share_transactions
		WHERE chama_id = ?
		ORDER BY transaction_date DESC
		LIMIT ? OFFSET ?
	`

	rows, err := s.db.Query(query, chamaID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get share transactions: %w", err)
	}
	defer rows.Close()

	var transactions []models.ShareTransaction
	for rows.Next() {
		var transaction models.ShareTransaction
		err := rows.Scan(
			&transaction.ID,
			&transaction.ChamaID,
			&transaction.FromMemberID,
			&transaction.ToMemberID,
			&transaction.TransactionType,
			&transaction.SharesCount,
			&transaction.ShareValue,
			&transaction.TotalAmount,
			&transaction.TransactionDate,
			&transaction.Status,
			&transaction.ApprovedBy,
			&transaction.Description,
			&transaction.CreatedAt,
			&transaction.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan share transaction: %w", err)
		}
		transactions = append(transactions, transaction)
	}

	return transactions, nil
}

func stringPtr(s string) *string {
	return &s
}
