package services

import (
	"crypto/rand"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// PasswordResetService handles password reset functionality
type PasswordResetService struct {
	db           *sql.DB
	emailService *EmailService
}

// PasswordResetToken represents a password reset token
type PasswordResetToken struct {
	ID        string    `json:"id" db:"id"`
	UserID    string    `json:"userId" db:"user_id"`
	Token     string    `json:"token" db:"token"`
	ExpiresAt time.Time `json:"expiresAt" db:"expires_at"`
	Used      bool      `json:"used" db:"used"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
}

// NewPasswordResetService creates a new password reset service
func NewPasswordResetService(db *sql.DB, emailService *EmailService) *PasswordResetService {
	return &PasswordResetService{
		db:           db,
		emailService: emailService,
	}
}

// InitializePasswordResetTable creates the password reset tokens table if it doesn't exist
func (s *PasswordResetService) InitializePasswordResetTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS password_reset_tokens (
		id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
		user_id TEXT NOT NULL,
		token TEXT NOT NULL UNIQUE,
		expires_at DATETIME NOT NULL,
		used BOOLEAN DEFAULT FALSE,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
	)`

	_, err := s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create password_reset_tokens table: %w", err)
	}

	// Create index for faster lookups
	indexQuery := `CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON password_reset_tokens(token)`
	_, err = s.db.Exec(indexQuery)
	if err != nil {
		return fmt.Errorf("failed to create password reset tokens index: %w", err)
	}

	return nil
}

// GenerateResetToken generates a secure random token
func (s *PasswordResetService) GenerateResetToken() (string, error) {
	// Generate 6-digit numeric code for user-friendly experience
	bytes := make([]byte, 3)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to 6-digit number
	token := fmt.Sprintf("%06d", int(bytes[0])<<16|int(bytes[1])<<8|int(bytes[2]))
	return token[:6], nil
}

// CreatePasswordResetToken creates a new password reset token for a user
func (s *PasswordResetService) CreatePasswordResetToken(userID string) (*PasswordResetToken, error) {
	// Generate token
	token, err := s.GenerateResetToken()
	if err != nil {
		return nil, err
	}

	// Set expiry to 10 minutes from now
	expiresAt := time.Now().Add(10 * time.Minute)

	// Invalidate any existing tokens for this user
	_, err = s.db.Exec("UPDATE password_reset_tokens SET used = TRUE WHERE user_id = ? AND used = FALSE", userID)
	if err != nil {
		return nil, fmt.Errorf("failed to invalidate existing tokens: %w", err)
	}

	// Insert new token
	query := `
	INSERT INTO password_reset_tokens (user_id, token, expires_at)
	VALUES (?, ?, ?)
	`

	result, err := s.db.Exec(query, userID, token, expiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create password reset token: %w", err)
	}

	// Get the inserted ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get inserted token ID: %w", err)
	}

	resetToken := &PasswordResetToken{
		ID:        fmt.Sprintf("%d", id),
		UserID:    userID,
		Token:     token,
		ExpiresAt: expiresAt,
		Used:      false,
		CreatedAt: time.Now(),
	}

	return resetToken, nil
}

// ValidateResetToken validates a password reset token
func (s *PasswordResetService) ValidateResetToken(token string) (*PasswordResetToken, error) {
	query := `
	SELECT id, user_id, token, expires_at, used, created_at
	FROM password_reset_tokens
	WHERE token = ? AND used = FALSE
	`

	var resetToken PasswordResetToken
	err := s.db.QueryRow(query, token).Scan(
		&resetToken.ID,
		&resetToken.UserID,
		&resetToken.Token,
		&resetToken.ExpiresAt,
		&resetToken.Used,
		&resetToken.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("invalid or expired reset token")
		}
		return nil, fmt.Errorf("failed to validate reset token: %w", err)
	}

	// Check if token has expired
	if time.Now().After(resetToken.ExpiresAt) {
		return nil, fmt.Errorf("reset token has expired")
	}

	return &resetToken, nil
}

// UseResetToken marks a reset token as used
func (s *PasswordResetService) UseResetToken(token string) error {
	query := `UPDATE password_reset_tokens SET used = TRUE WHERE token = ?`
	result, err := s.db.Exec(query, token)
	if err != nil {
		return fmt.Errorf("failed to mark token as used: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get affected rows: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("token not found")
	}

	return nil
}

// SendPasswordResetEmail sends a password reset email to the user
func (s *PasswordResetService) SendPasswordResetEmail(userEmail, userName, resetToken string) error {
	if s.emailService == nil {
		return fmt.Errorf("email service not configured")
	}

	return s.emailService.SendPasswordResetEmail(userEmail, resetToken, userName)
}

// CleanupExpiredTokens removes expired password reset tokens
func (s *PasswordResetService) CleanupExpiredTokens() error {
	query := `DELETE FROM password_reset_tokens WHERE expires_at < ? OR used = TRUE`
	_, err := s.db.Exec(query, time.Now())
	if err != nil {
		return fmt.Errorf("failed to cleanup expired tokens: %w", err)
	}
	return nil
}

// GetUserByIdentifier finds a user by email or phone number
func (s *PasswordResetService) GetUserByIdentifier(identifier string) (string, string, string, error) {
	// Clean the identifier
	identifier = strings.TrimSpace(identifier)

	var userID, email, name string
	var query string

	// Check if identifier looks like an email
	if strings.Contains(identifier, "@") {
		query = `SELECT id, email, COALESCE(first_name || ' ' || last_name, first_name, email) as name FROM users WHERE email = ? AND status = 'active'`
	} else {
		// Assume it's a phone number
		query = `SELECT id, email, COALESCE(first_name || ' ' || last_name, first_name, phone) as name FROM users WHERE phone = ? AND status = 'active'`
	}

	err := s.db.QueryRow(query, identifier).Scan(&userID, &email, &name)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", "", "", fmt.Errorf("user not found")
		}
		return "", "", "", fmt.Errorf("failed to find user: %w", err)
	}

	return userID, email, name, nil
}

// RequestPasswordReset handles the complete password reset request process
func (s *PasswordResetService) RequestPasswordReset(identifier string) error {
	// Find user by email or phone
	userID, email, name, err := s.GetUserByIdentifier(identifier)
	if err != nil {
		return err
	}

	// Create reset token
	resetToken, err := s.CreatePasswordResetToken(userID)
	if err != nil {
		return fmt.Errorf("failed to create reset token: %w", err)
	}

	// Send email
	err = s.SendPasswordResetEmail(email, name, resetToken.Token)
	if err != nil {
		return fmt.Errorf("failed to send reset email: %w", err)
	}

	return nil
}

// ResetPassword resets a user's password using a valid token
func (s *PasswordResetService) ResetPassword(token, newPassword string) error {
	// Validate token
	resetToken, err := s.ValidateResetToken(token)
	if err != nil {
		return err
	}

	// Hash the new password using bcrypt
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update the password_hash column (not password)
	query := `UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
	_, err = s.db.Exec(query, string(hashedPassword), resetToken.UserID)
	if err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	// Mark token as used
	err = s.UseResetToken(token)
	if err != nil {
		return fmt.Errorf("failed to mark token as used: %w", err)
	}

	return nil
}
