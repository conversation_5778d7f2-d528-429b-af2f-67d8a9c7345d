package services

import (
	"fmt"
	"net/smtp"
	"os"
	"time"
)

// EmailService handles email sending functionality
type EmailService struct {
	smtpHost     string
	smtpPort     string
	smtpUsername string
	smtpPassword string
	fromEmail    string
}

// NewEmailService creates a new email service
func NewEmailService() *EmailService {
	// Get password and trim quotes if present
	password := os.Getenv("SMTP_PASSWORD")
	fmt.Printf("Raw password from env: '%s' (length: %d)\n", password, len(password))

	if len(password) >= 2 && password[0] == '"' && password[len(password)-1] == '"' {
		password = password[1 : len(password)-1]
		fmt.Printf("Trimmed quotes from password: '%s' (length: %d)\n", password, len(password))
	}

	return &EmailService{
		smtpHost:     os.Getenv("SMTP_HOST"),
		smtpPort:     os.Getenv("SMTP_PORT"),
		smtpUsername: os.<PERSON>env("SMTP_USERNAME"),
		smtpPassword: password,
		fromEmail:    os.Getenv("SMTP_USERNAME"), // Use SMTP username as from email
	}
}

// SendPasswordResetEmail sends a password reset email to the user
func (s *EmailService) SendPasswordResetEmail(toEmail, resetToken, userName string) error {
	// Validate configuration
	if s.smtpHost == "" || s.smtpPort == "" || s.smtpUsername == "" || s.smtpPassword == "" {
		fmt.Printf("Email service configuration missing: host=%s, port=%s, username=%s, password_set=%t\n",
			s.smtpHost, s.smtpPort, s.smtpUsername, s.smtpPassword != "")
		return fmt.Errorf("email service not configured properly")
	}

	fmt.Printf("Email service configuration loaded:\n")
	fmt.Printf("  Host: %s\n", s.smtpHost)
	fmt.Printf("  Port: %s\n", s.smtpPort)
	fmt.Printf("  Username: %s\n", s.smtpUsername)
	fmt.Printf("  Password: '%s' (length: %d)\n", s.smtpPassword, len(s.smtpPassword))
	fmt.Printf("Sending password reset email to: %s with token: %s\n", toEmail, resetToken)

	// Create reset URL (you can customize this based on your frontend)
	resetURL := fmt.Sprintf("https://vaultke.com/reset-password?token=%s", resetToken)

	// Email subject and body
	subject := "VaultKe - Password Reset Request"
	body := s.generatePasswordResetEmailBody(userName, resetURL, resetToken)

	// Create email message in the exact format as your working app
	message := fmt.Sprintf("To: %s\r\nSubject: %s\r\nContent-Type: text/html; charset=UTF-8\r\n\r\n%s",
		toEmail, subject, body)

	// Send email
	err := s.sendEmail(toEmail, message)
	if err != nil {
		fmt.Printf("Failed to send email: %v\n", err)
		return err
	}

	fmt.Printf("Email sent successfully to: %s\n", toEmail)
	return nil
}

// sendEmail sends an email using SMTP - exact same approach as your working code
func (s *EmailService) sendEmail(toEmail, message string) error {
	// Set up authentication - exact same as your working code
	auth := smtp.PlainAuth("", s.smtpUsername, s.smtpPassword, s.smtpHost)

	// SMTP server address
	addr := fmt.Sprintf("%s:%s", s.smtpHost, s.smtpPort)

	fmt.Printf("Attempting to send email via SMTP server: %s\n", addr)
	fmt.Printf("From: %s, To: %s\n", s.fromEmail, toEmail)
	fmt.Printf("Auth details - Username: %s, Password: '%s' (length: %d)\n", s.smtpUsername, s.smtpPassword, len(s.smtpPassword))

	// Send email - exact same method as your working code
	err := smtp.SendMail(addr, auth, s.fromEmail, []string{toEmail}, []byte(message))
	if err != nil {
		fmt.Printf("SMTP error: %v\n", err)
		return fmt.Errorf("failed to send email: %w", err)
	}

	fmt.Printf("✅ Email sent successfully!\n")
	return nil
}

// SendChamaInvitationEmail sends a chama invitation email to the invitee
func (s *EmailService) SendChamaInvitationEmail(toEmail, chamaName, inviterName, message, invitationToken string) error {
	// Validate configuration
	if s.smtpHost == "" || s.smtpPort == "" || s.smtpUsername == "" || s.smtpPassword == "" {
		fmt.Printf("Email service configuration missing for chama invitation\n")
		return fmt.Errorf("email service not configured properly")
	}

	fmt.Printf("Sending chama invitation email to: %s for chama: %s\n", toEmail, chamaName)

	// Create invitation URL
	invitationURL := fmt.Sprintf("https://vaultke.com/chama-invitation?token=%s", invitationToken)

	// Email subject and body
	subject := fmt.Sprintf("VaultKe - Invitation to Join %s Chama", chamaName)
	body := s.generateChamaInvitationEmailBody(chamaName, inviterName, message, invitationURL, invitationToken)

	// Create email message in the exact format as your working app
	emailMessage := fmt.Sprintf("To: %s\r\nSubject: %s\r\nContent-Type: text/html; charset=UTF-8\r\n\r\n%s",
		toEmail, subject, body)

	// Send email
	err := s.sendEmail(toEmail, emailMessage)
	if err != nil {
		fmt.Printf("Failed to send chama invitation email: %v\n", err)
		return err
	}

	fmt.Printf("Chama invitation email sent successfully to: %s\n", toEmail)
	return nil
}

// generatePasswordResetEmailBody generates the HTML email body for password reset
func (s *EmailService) generatePasswordResetEmailBody(userName, resetURL, resetToken string) string {
	// If userName is empty, use a generic greeting
	if userName == "" {
		userName = "VaultKe User"
	}

	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VaultKe - Password Reset</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .token { background: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 18px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 VaultKe Password Reset</h1>
        </div>
        <div class="content">
            <h2>Hello %s,</h2>
            <p>We received a request to reset your VaultKe account password. If you made this request, you can reset your password using the button below:</p>
            
            <div style="text-align: center;">
                <a href="%s" class="button">Reset Your Password</a>
            </div>
            
            <p>Or copy and paste this reset code in the VaultKe app:</p>
            <div class="token">%s</div>
            
            <div class="warning">
                <strong>⚠️ Important Security Information:</strong>
                <ul>
                    <li>This reset link will expire in 1 hour for security</li>
                    <li>If you didn't request this reset, please ignore this email</li>
                    <li>Never share this reset code with anyone</li>
                    <li>VaultKe will never ask for your password via email</li>
                </ul>
            </div>
            
            <p>If you're having trouble with the button above, you can also reset your password by:</p>
            <ol>
                <li>Opening the VaultKe app</li>
                <li>Going to "Forgot Password"</li>
                <li>Entering the reset code: <strong>%s</strong></li>
            </ol>
            
            <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>
            
            <p>Best regards,<br>The VaultKe Team</p>
        </div>
        <div class="footer">
            <p>This email was sent by VaultKe - Your trusted chama finance companion</p>
            <p>© %d VaultKe. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`, userName, resetURL, resetToken, resetToken, time.Now().Year())
}

// generateChamaInvitationEmailBody generates the HTML email body for chama invitation
func (s *EmailService) generateChamaInvitationEmailBody(chamaName, inviterName, message, invitationURL, invitationToken string) string {
	// If message is empty, use a default message
	if message == "" {
		message = fmt.Sprintf("You have been invited to join %s chama. Join us to start saving and investing together!", chamaName)
	}

	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VaultKe - Chama Invitation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .chama-info { background: #e2e8f0; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .token { background: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 16px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 VaultKe Chama Invitation</h1>
        </div>
        <div class="content">
            <h2>You're Invited to Join %s!</h2>
            <p><strong>%s</strong> has invited you to join their chama on VaultKe.</p>

            <div class="chama-info">
                <h3>📋 Invitation Details:</h3>
                <p><strong>Chama Name:</strong> %s</p>
                <p><strong>Invited by:</strong> %s</p>
                <p><strong>Message:</strong> %s</p>
            </div>

            <div style="text-align: center;">
                <a href="%s" class="button">Accept Invitation</a>
            </div>

            <p>Or copy and paste this invitation code in the VaultKe app:</p>
            <div class="token">%s</div>

            <div class="warning">
                <strong>⚠️ Important Information:</strong>
                <ul>
                    <li>This invitation will expire in 7 days</li>
                    <li>You need a VaultKe account to join the chama</li>
                    <li>If you don't have an account, download the VaultKe app first</li>
                    <li>Only accept invitations from people you trust</li>
                </ul>
            </div>

            <h3>🤔 What is a Chama?</h3>
            <p>A chama is a group savings and investment club where members contribute money regularly and support each other financially. VaultKe makes it easy to:</p>
            <ul>
                <li>💰 Track contributions and payments</li>
                <li>📊 Monitor group finances</li>
                <li>🤝 Manage loans and investments</li>
                <li>📱 Communicate with members</li>
            </ul>

            <p>If you didn't expect this invitation, you can safely ignore this email.</p>

            <p>Best regards,<br>The VaultKe Team</p>
        </div>
        <div class="footer">
            <p>This email was sent by VaultKe - Your trusted chama finance companion</p>
            <p>© %d VaultKe. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`, chamaName, inviterName, chamaName, inviterName, message, invitationURL, invitationToken, time.Now().Year())
}

// SendTestEmail sends a test email to verify configuration
func (s *EmailService) SendTestEmail(toEmail string) error {
	subject := "VaultKe - Email Service Test"
	body := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>VaultKe Email Test</title>
</head>
<body>
    <h2>✅ Email Service Working!</h2>
    <p>This is a test email from VaultKe to verify that the email service is configured correctly.</p>
    <p>If you received this email, the SMTP configuration is working properly.</p>
    <p>Best regards,<br>The VaultKe Team</p>
</body>
</html>`

	message := fmt.Sprintf("From: %s\r\n", s.fromEmail)
	message += fmt.Sprintf("To: %s\r\n", toEmail)
	message += fmt.Sprintf("Subject: %s\r\n", subject)
	message += "MIME-Version: 1.0\r\n"
	message += "Content-Type: text/html; charset=UTF-8\r\n"
	message += "\r\n"
	message += body

	return s.sendEmail(toEmail, message)
}
