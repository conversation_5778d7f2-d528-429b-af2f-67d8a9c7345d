-- =====================================================
-- VaultKe Notification System Database Schema
-- =====================================================

-- 1. Notification Sounds Table
CREATE TABLE notification_sounds (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT DEFAULT 0,
    duration_seconds DECIMAL(5,2) DEFAULT 0.00,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_is_default (is_default),
    INDEX idx_is_active (is_active)
);

-- 2. User Notification Preferences Table
CREATE TABLE user_notification_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    
    -- Sound Settings
    notification_sound_id INT DEFAULT NULL,
    sound_enabled BOOLEAN DEFAULT TRUE,
    vibration_enabled BOOLEAN DEFAULT TRUE,
    volume_level TINYINT DEFAULT 80 CHECK (volume_level BETWEEN 0 AND 100),
    
    -- Notification Types
    chama_notifications BOOLEAN DEFAULT TRUE,
    transaction_notifications BOOLEAN DEFAULT TRUE,
    reminder_notifications BOOLEAN DEFAULT TRUE,
    system_notifications BOOLEAN DEFAULT TRUE,
    marketing_notifications BOOLEAN DEFAULT FALSE,
    
    -- Timing Settings
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '07:00:00',
    timezone VARCHAR(50) DEFAULT 'Africa/Nairobi',
    
    -- Advanced Settings
    notification_frequency ENUM('immediate', 'batched_15min', 'batched_1hour', 'daily_digest') DEFAULT 'immediate',
    priority_only_during_quiet BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (notification_sound_id) REFERENCES notification_sounds(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_preferences (user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_sound_enabled (sound_enabled)
);

-- 3. Notifications Table (Enhanced)
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    
    -- Content
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('chama', 'transaction', 'reminder', 'system', 'marketing', 'alert') NOT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    
    -- Categorization
    category VARCHAR(100) DEFAULT NULL, -- e.g., 'payment_due', 'meeting_reminder', 'deposit_received'
    reference_type VARCHAR(50) DEFAULT NULL, -- e.g., 'chama', 'transaction', 'user'
    reference_id INT DEFAULT NULL,
    
    -- Status & Delivery
    status ENUM('pending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'pending',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    
    -- Scheduling
    scheduled_for TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    
    -- Metadata
    data JSON DEFAULT NULL, -- Additional data for the notification
    sound_played BOOLEAN DEFAULT FALSE,
    retry_count TINYINT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_for (scheduled_for),
    INDEX idx_is_read (is_read),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_user_type_status (user_id, type, status)
);

-- 4. Notification Templates Table
CREATE TABLE notification_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('chama', 'transaction', 'reminder', 'system', 'marketing', 'alert') NOT NULL,
    category VARCHAR(100) NOT NULL,
    
    -- Template Content
    title_template VARCHAR(255) NOT NULL,
    message_template TEXT NOT NULL,
    
    -- Settings
    default_priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    requires_sound BOOLEAN DEFAULT TRUE,
    requires_vibration BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    variables JSON DEFAULT NULL, -- Available template variables
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type_category (type, category),
    INDEX idx_is_active (is_active)
);

-- 5. Notification Delivery Log Table
CREATE TABLE notification_delivery_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    notification_id INT NOT NULL,
    user_id INT NOT NULL,
    
    -- Delivery Details
    delivery_method ENUM('push', 'sms', 'email', 'in_app') NOT NULL,
    status ENUM('pending', 'sent', 'delivered', 'failed', 'bounced') NOT NULL,
    
    -- Timing
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    
    -- Error Handling
    error_message TEXT NULL,
    retry_count TINYINT DEFAULT 0,
    
    -- Metadata
    device_info JSON DEFAULT NULL,
    
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_notification_id (notification_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_delivery_method (delivery_method)
);

-- 6. User Reminders Table
CREATE TABLE user_reminders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    
    -- Reminder Content
    title VARCHAR(255) NOT NULL,
    description TEXT DEFAULT NULL,
    
    -- Timing
    reminder_datetime TIMESTAMP NOT NULL,
    timezone VARCHAR(50) DEFAULT 'Africa/Nairobi',
    
    -- Recurrence
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern ENUM('daily', 'weekly', 'monthly', 'yearly') DEFAULT NULL,
    recurrence_interval INT DEFAULT 1, -- Every X days/weeks/months/years
    recurrence_end_date DATE DEFAULT NULL,
    
    -- Settings
    sound_enabled BOOLEAN DEFAULT TRUE,
    vibration_enabled BOOLEAN DEFAULT TRUE,
    custom_sound_id INT DEFAULT NULL,
    
    -- Status
    status ENUM('active', 'completed', 'cancelled', 'snoozed') DEFAULT 'active',
    snooze_until TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    -- Metadata
    category VARCHAR(100) DEFAULT 'personal',
    priority ENUM('low', 'normal', 'high') DEFAULT 'normal',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (custom_sound_id) REFERENCES notification_sounds(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_reminder_datetime (reminder_datetime),
    INDEX idx_status (status),
    INDEX idx_user_active_reminders (user_id, status, reminder_datetime)
);

-- =====================================================
-- Insert Default Data
-- =====================================================

-- Default notification sounds
INSERT INTO notification_sounds (name, file_path, is_default, is_active) VALUES
('Default Ring', '/notification_sound/ring.mp3', TRUE, TRUE),
('Gentle Bell', '/notification_sound/bell.mp3', FALSE, TRUE),
('Alert Tone', '/notification_sound/alert.mp3', FALSE, TRUE),
('Chime', '/notification_sound/chime.mp3', FALSE, TRUE),
('Silent', '', FALSE, TRUE);

-- Default notification templates
INSERT INTO notification_templates (name, type, category, title_template, message_template, default_priority, variables) VALUES
('chama_payment_due', 'chama', 'payment_due', 'Payment Due: {chama_name}', 'Your contribution of KSh {amount} for {chama_name} is due on {due_date}.', 'high', '["chama_name", "amount", "due_date"]'),
('transaction_received', 'transaction', 'deposit_received', 'Payment Received', 'You have received KSh {amount} from {sender_name}.', 'normal', '["amount", "sender_name"]'),
('meeting_reminder', 'reminder', 'meeting', 'Meeting Reminder: {chama_name}', 'Don\'t forget about the {chama_name} meeting scheduled for {meeting_time}.', 'high', '["chama_name", "meeting_time"]'),
('system_maintenance', 'system', 'maintenance', 'System Maintenance', 'VaultKe will undergo maintenance from {start_time} to {end_time}. Some features may be unavailable.', 'normal', '["start_time", "end_time"]');
