package database

import (
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"
)

// MigrationManager handles database migrations
type MigrationManager struct {
	db *sql.DB
}

// NewMigrationManager creates a new migration manager
func NewMigrationManager(db *sql.DB) *MigrationManager {
	return &MigrationManager{db: db}
}

// RunMigrations executes all pending migrations
func (m *MigrationManager) RunMigrations() error {
	log.Println("🚀 Starting database migrations...")

	// Create migrations table if it doesn't exist
	if err := m.createMigrationsTable(); err != nil {
		return fmt.Errorf("failed to create migrations table: %w", err)
	}

	// Run notification system migration
	if err := m.runMigration("create_notification_system_tables", m.createNotificationSystemTables); err != nil {
		return fmt.Errorf("failed to run notification system migration: %w", err)
	}

	// Insert default data
	if err := m.runMigration("insert_default_notification_data", m.insertDefaultNotificationData); err != nil {
		return fmt.Errorf("failed to insert default notification data: %w", err)
	}

	log.Println("✅ All migrations completed successfully!")
	return nil
}

// createMigrationsTable creates the migrations tracking table
func (m *MigrationManager) createMigrationsTable() error {
	query := `
		CREATE TABLE IF NOT EXISTS migrations (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			migration VARCHAR(255) NOT NULL UNIQUE,
			executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`
	_, err := m.db.Exec(query)
	return err
}

// runMigration executes a migration if it hasn't been run before
func (m *MigrationManager) runMigration(name string, migrationFunc func() error) error {
	// Check if migration has already been run
	var count int
	err := m.db.QueryRow("SELECT COUNT(*) FROM migrations WHERE migration = ?", name).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		log.Printf("⏭️  Migration '%s' already executed, skipping...", name)
		return nil
	}

	log.Printf("🔄 Running migration: %s", name)

	// Start transaction
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Execute migration
	if err := migrationFunc(); err != nil {
		return fmt.Errorf("migration failed: %w", err)
	}

	// Record migration as completed
	_, err = tx.Exec("INSERT INTO migrations (migration) VALUES (?)", name)
	if err != nil {
		return err
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return err
	}

	log.Printf("✅ Migration '%s' completed successfully!", name)
	return nil
}

// createNotificationSystemTables creates all notification system tables
func (m *MigrationManager) createNotificationSystemTables() error {
	migrations := []string{
		// notification_sounds table
		`CREATE TABLE IF NOT EXISTS notification_sounds (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(100) NOT NULL,
			file_path VARCHAR(255) NOT NULL,
			file_size INTEGER DEFAULT 0,
			duration_seconds REAL DEFAULT 0.00,
			is_default BOOLEAN DEFAULT 0,
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,

		// user_notification_preferences table
		`CREATE TABLE IF NOT EXISTS user_notification_preferences (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			user_id TEXT NOT NULL,
			notification_sound_id INTEGER DEFAULT NULL,
			sound_enabled BOOLEAN DEFAULT 1,
			vibration_enabled BOOLEAN DEFAULT 1,
			volume_level INTEGER DEFAULT 80 CHECK (volume_level BETWEEN 0 AND 100),
			chama_notifications BOOLEAN DEFAULT 1,
			transaction_notifications BOOLEAN DEFAULT 1,
			reminder_notifications BOOLEAN DEFAULT 1,
			system_notifications BOOLEAN DEFAULT 1,
			marketing_notifications BOOLEAN DEFAULT 0,
			quiet_hours_enabled BOOLEAN DEFAULT 0,
			quiet_hours_start TIME DEFAULT '22:00:00',
			quiet_hours_end TIME DEFAULT '07:00:00',
			timezone VARCHAR(50) DEFAULT 'Africa/Nairobi',
			notification_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (notification_frequency IN ('immediate', 'batched_15min', 'batched_1hour', 'daily_digest')),
			priority_only_during_quiet BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
			FOREIGN KEY (notification_sound_id) REFERENCES notification_sounds(id) ON DELETE SET NULL,
			UNIQUE(user_id)
		)`,

		// Enhanced notifications table (add columns if table exists)
		`CREATE TABLE IF NOT EXISTS notifications_new (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			user_id TEXT NOT NULL,
			title VARCHAR(255) NOT NULL,
			message TEXT NOT NULL,
			type VARCHAR(20) NOT NULL CHECK (type IN ('chama', 'transaction', 'reminder', 'system', 'marketing', 'alert')),
			priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
			category VARCHAR(100) DEFAULT NULL,
			reference_type VARCHAR(50) DEFAULT NULL,
			reference_id INTEGER DEFAULT NULL,
			status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'read', 'failed')),
			is_read BOOLEAN DEFAULT 0,
			read_at DATETIME NULL,
			scheduled_for DATETIME DEFAULT CURRENT_TIMESTAMP,
			sent_at DATETIME NULL,
			delivered_at DATETIME NULL,
			data TEXT DEFAULT NULL,
			sound_played BOOLEAN DEFAULT 0,
			retry_count INTEGER DEFAULT 0,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
		)`,

		// notification_templates table
		`CREATE TABLE IF NOT EXISTS notification_templates (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(100) NOT NULL UNIQUE,
			type VARCHAR(20) NOT NULL CHECK (type IN ('chama', 'transaction', 'reminder', 'system', 'marketing', 'alert')),
			category VARCHAR(100) NOT NULL,
			title_template VARCHAR(255) NOT NULL,
			message_template TEXT NOT NULL,
			default_priority VARCHAR(10) DEFAULT 'normal' CHECK (default_priority IN ('low', 'normal', 'high', 'urgent')),
			requires_sound BOOLEAN DEFAULT 1,
			requires_vibration BOOLEAN DEFAULT 1,
			variables TEXT DEFAULT NULL,
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,

		// notification_delivery_log table
		`CREATE TABLE IF NOT EXISTS notification_delivery_log (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			notification_id INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			delivery_method VARCHAR(20) NOT NULL CHECK (delivery_method IN ('push', 'sms', 'email', 'in_app')),
			status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
			attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			delivered_at DATETIME NULL,
			error_message TEXT NULL,
			retry_count INTEGER DEFAULT 0,
			device_info TEXT DEFAULT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
		)`,

		// user_reminders table
		`CREATE TABLE IF NOT EXISTS user_reminders (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			user_id TEXT NOT NULL,
			title VARCHAR(255) NOT NULL,
			description TEXT DEFAULT NULL,
			reminder_datetime DATETIME NOT NULL,
			timezone VARCHAR(50) DEFAULT 'Africa/Nairobi',
			is_recurring BOOLEAN DEFAULT 0,
			recurrence_pattern VARCHAR(20) DEFAULT NULL CHECK (recurrence_pattern IN ('daily', 'weekly', 'monthly', 'yearly')),
			recurrence_interval INTEGER DEFAULT 1,
			recurrence_end_date DATE DEFAULT NULL,
			sound_enabled BOOLEAN DEFAULT 1,
			vibration_enabled BOOLEAN DEFAULT 1,
			custom_sound_id INTEGER DEFAULT NULL,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'snoozed')),
			snooze_until DATETIME NULL,
			completed_at DATETIME NULL,
			category VARCHAR(100) DEFAULT 'personal',
			priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high')),
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
			FOREIGN KEY (custom_sound_id) REFERENCES notification_sounds(id) ON DELETE SET NULL
		)`,
	}

	// Execute all migrations
	for _, migration := range migrations {
		if _, err := m.db.Exec(migration); err != nil {
			return fmt.Errorf("failed to execute migration: %w", err)
		}
	}

	// Handle existing notifications table migration
	if err := m.migrateExistingNotificationsTable(); err != nil {
		return fmt.Errorf("failed to migrate existing notifications table: %w", err)
	}

	// Create indexes
	if err := m.createIndexes(); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	return nil
}

// migrateExistingNotificationsTable handles migration of existing notifications table
func (m *MigrationManager) migrateExistingNotificationsTable() error {
	// Check if old notifications table exists
	var count int
	err := m.db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='notifications'").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// Get existing table structure
		rows, err := m.db.Query("PRAGMA table_info(notifications)")
		if err != nil {
			return err
		}
		defer rows.Close()

		existingColumns := make(map[string]bool)
		for rows.Next() {
			var cid int
			var name, dataType string
			var notNull, pk int
			var defaultValue sql.NullString

			err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
			if err != nil {
				return err
			}
			existingColumns[name] = true
		}

		// Build SELECT query based on existing columns (user_id is already TEXT)
		selectParts := []string{
			"CAST(id AS INTEGER) as id",
			"user_id",
			"title",
			"message",
			"CASE WHEN type IS NULL THEN 'system' ELSE type END as type",
			"COALESCE(is_read, 0) as is_read",
		}

		// Add read_at if it exists, otherwise NULL
		if existingColumns["read_at"] {
			selectParts = append(selectParts, "read_at")
		} else {
			selectParts = append(selectParts, "NULL as read_at")
		}

		selectParts = append(selectParts, "created_at")

		// Add updated_at if it exists, otherwise use created_at
		if existingColumns["updated_at"] {
			selectParts = append(selectParts, "COALESCE(updated_at, created_at) as updated_at")
		} else {
			selectParts = append(selectParts, "created_at as updated_at")
		}

		selectQuery := strings.Join(selectParts, ", ")

		// Copy data from old table to new table
		copyQuery := fmt.Sprintf(`
			INSERT OR IGNORE INTO notifications_new
			(id, user_id, title, message, type, is_read, read_at, created_at, updated_at)
			SELECT %s FROM notifications
		`, selectQuery)

		_, err = m.db.Exec(copyQuery)
		if err != nil {
			return fmt.Errorf("failed to copy data: %w", err)
		}

		// Drop old table
		if _, err = m.db.Exec("DROP TABLE notifications"); err != nil {
			return fmt.Errorf("failed to drop old table: %w", err)
		}

		log.Println("✅ Migrated existing notifications table data")
	}

	// Rename new table to notifications
	_, err = m.db.Exec("ALTER TABLE notifications_new RENAME TO notifications")
	if err != nil {
		// If rename fails, the table might already be named correctly
		log.Println("Note: notifications table rename failed, might already be correct")
	}

	return nil
}

// createIndexes creates database indexes for better performance
func (m *MigrationManager) createIndexes() error {
	indexes := []string{
		// notification_sounds indexes
		"CREATE INDEX IF NOT EXISTS idx_notification_sounds_is_default ON notification_sounds(is_default)",
		"CREATE INDEX IF NOT EXISTS idx_notification_sounds_is_active ON notification_sounds(is_active)",

		// user_notification_preferences indexes
		"CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_user_id ON user_notification_preferences(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_sound_enabled ON user_notification_preferences(sound_enabled)",

		// notifications indexes
		"CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status)",
		"CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)",
		"CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority)",
		"CREATE INDEX IF NOT EXISTS idx_notifications_scheduled_for ON notifications(scheduled_for)",
		"CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)",
		"CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read)",

		// notification_templates indexes
		"CREATE INDEX IF NOT EXISTS idx_notification_templates_type_category ON notification_templates(type, category)",
		"CREATE INDEX IF NOT EXISTS idx_notification_templates_is_active ON notification_templates(is_active)",

		// notification_delivery_log indexes
		"CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_notification_id ON notification_delivery_log(notification_id)",
		"CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_user_id ON notification_delivery_log(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_status ON notification_delivery_log(status)",

		// user_reminders indexes
		"CREATE INDEX IF NOT EXISTS idx_user_reminders_user_id ON user_reminders(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_user_reminders_reminder_datetime ON user_reminders(reminder_datetime)",
		"CREATE INDEX IF NOT EXISTS idx_user_reminders_status ON user_reminders(status)",
		"CREATE INDEX IF NOT EXISTS idx_user_reminders_user_active ON user_reminders(user_id, status, reminder_datetime)",
	}

	for _, index := range indexes {
		if _, err := m.db.Exec(index); err != nil {
			log.Printf("Warning: Failed to create index: %v", err)
		}
	}

	return nil
}

// insertDefaultNotificationData inserts default sounds and templates
func (m *MigrationManager) insertDefaultNotificationData() error {
	// Insert default notification sounds
	if err := m.insertDefaultSounds(); err != nil {
		return fmt.Errorf("failed to insert default sounds: %w", err)
	}

	// Insert default notification templates
	if err := m.insertDefaultTemplates(); err != nil {
		return fmt.Errorf("failed to insert default templates: %w", err)
	}

	return nil
}

// insertDefaultSounds inserts default notification sounds
func (m *MigrationManager) insertDefaultSounds() error {
	// Check if sounds already exist
	var count int
	err := m.db.QueryRow("SELECT COUNT(*) FROM notification_sounds").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		log.Println("ℹ️  Notification sounds already exist, skipping default sound insertion.")
		return nil
	}

	sounds := []struct {
		name      string
		filePath  string
		isDefault bool
		isActive  bool
	}{
		{"Default Ring", "/notification_sound/ring.mp3", true, true},
		{"Gentle Bell", "/notification_sound/bell.mp3", false, true},
		{"Alert Tone", "/notification_sound/alert.mp3", false, true},
		{"Chime", "/notification_sound/chime.mp3", false, true},
		{"Silent", "", false, true},
	}

	stmt, err := m.db.Prepare(`
		INSERT INTO notification_sounds (name, file_path, is_default, is_active, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return err
	}
	defer stmt.Close()

	now := time.Now()
	for _, sound := range sounds {
		_, err = stmt.Exec(sound.name, sound.filePath, sound.isDefault, sound.isActive, now, now)
		if err != nil {
			return err
		}
	}

	log.Println("✅ Default notification sounds inserted successfully!")
	return nil
}

// insertDefaultTemplates inserts default notification templates
func (m *MigrationManager) insertDefaultTemplates() error {
	// Check if templates already exist
	var count int
	err := m.db.QueryRow("SELECT COUNT(*) FROM notification_templates").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		log.Println("ℹ️  Notification templates already exist, skipping default template insertion.")
		return nil
	}

	templates := []struct {
		name            string
		notifType       string
		category        string
		titleTemplate   string
		messageTemplate string
		defaultPriority string
		variables       string
	}{
		{
			"chama_payment_due", "chama", "payment_due",
			"Payment Due: {chama_name}",
			"Your contribution of KSh {amount} for {chama_name} is due on {due_date}.",
			"high", `["chama_name", "amount", "due_date"]`,
		},
		{
			"transaction_received", "transaction", "deposit_received",
			"Payment Received",
			"You have received KSh {amount} from {sender_name}.",
			"normal", `["amount", "sender_name"]`,
		},
		{
			"meeting_reminder", "reminder", "meeting",
			"Meeting Reminder: {chama_name}",
			"Don't forget about the {chama_name} meeting scheduled for {meeting_time}.",
			"high", `["chama_name", "meeting_time"]`,
		},
		{
			"system_maintenance", "system", "maintenance",
			"System Maintenance",
			"VaultKe will undergo maintenance from {start_time} to {end_time}. Some features may be unavailable.",
			"normal", `["start_time", "end_time"]`,
		},
	}

	stmt, err := m.db.Prepare(`
		INSERT INTO notification_templates 
		(name, type, category, title_template, message_template, default_priority, variables, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return err
	}
	defer stmt.Close()

	now := time.Now()
	for _, template := range templates {
		_, err = stmt.Exec(
			template.name, template.notifType, template.category,
			template.titleTemplate, template.messageTemplate,
			template.defaultPriority, template.variables, now, now,
		)
		if err != nil {
			return err
		}
	}

	log.Println("✅ Default notification templates inserted successfully!")
	return nil
}

// GetMigrationStatus returns the status of all migrations
func (m *MigrationManager) GetMigrationStatus() ([]map[string]interface{}, error) {
	rows, err := m.db.Query("SELECT migration, executed_at FROM migrations ORDER BY executed_at ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var migrations []map[string]interface{}
	for rows.Next() {
		var migration string
		var executedAt time.Time

		if err := rows.Scan(&migration, &executedAt); err != nil {
			return nil, err
		}

		migrations = append(migrations, map[string]interface{}{
			"migration":   migration,
			"executed_at": executedAt,
		})
	}

	return migrations, nil
}

// IsNotificationSystemReady checks if the notification system is properly set up
func (m *MigrationManager) IsNotificationSystemReady() (bool, error) {
	// Check if default sound exists
	var defaultSoundCount int
	err := m.db.QueryRow("SELECT COUNT(*) FROM notification_sounds WHERE is_default = 1").Scan(&defaultSoundCount)
	if err != nil {
		return false, err
	}

	// Check if templates exist
	var templateCount int
	err = m.db.QueryRow("SELECT COUNT(*) FROM notification_templates").Scan(&templateCount)
	if err != nil {
		return false, err
	}

	return defaultSoundCount > 0 && templateCount > 0, nil
}
