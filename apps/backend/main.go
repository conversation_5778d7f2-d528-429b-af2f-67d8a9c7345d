package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	"vaultke-backend/internal/api"
	"vaultke-backend/internal/config"
	"vaultke-backend/internal/database"
	"vaultke-backend/internal/middleware"
	"vaultke-backend/internal/services"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// Run database migrations
	if err := database.Migrate(db); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	// Initialize Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Load HTML templates for OAuth pages
	router.LoadHTMLGlob("templates/*")

	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Smart CORS middleware - handles localhost and production URLs
	router.Use(func(c *gin.Context) {
		origin := c.GetHeader("Origin")

		// Define allowed origins for different environments
		allowedOrigins := []string{
			// Localhost development
			"http://localhost:3000",
			"http://localhost:8081",
			"http://localhost:19006",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8081",
			"http://127.0.0.1:19006",
			// Tunnel URLs (update these with your current tunnel URLs)
			"https://4xfzba-ip-41-72-200-10.tunnelmole.net",
			// Production URLs (add your production domains here)
			"https://vaultke.com",
			"https://app.vaultke.com",
		}

		// Check if origin is allowed
		allowedOrigin := "*" // Default to allow all for development
		if origin != "" {
			for _, allowed := range allowedOrigins {
				if origin == allowed {
					allowedOrigin = origin
					break
				}
			}
		}

		// Set CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Origin", allowedOrigin)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "false")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control, X-CSRF-Token, X-File-Name, X-File-Size, X-Timezone, X-Language, X-Screen-Resolution, X-Device-Type, X-Device-Name, X-Browser-Name, X-OS-Name, X-Connection-Type")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Authorization")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")

		// Handle preflight OPTIONS requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Disable rate limiting for development
	if os.Getenv("DISABLE_RATE_LIMITING") != "true" {
		securityConfig := middleware.DefaultSecurityConfig()
		router.Use(middleware.SecurityMiddleware(securityConfig))
	}

	router.Use(middleware.InputValidationMiddleware())
	router.Use(middleware.FileUploadSecurityMiddleware())

	// IP debugging middleware for tunneled environments
	router.Use(func(c *gin.Context) {
		// Only log for non-OPTIONS requests to avoid spam
		if c.Request.Method != "OPTIONS" {
			// fmt.Printf("🌐 Request from %s %s - ClientIP: %s, X-Real-IP: %s, X-Forwarded-For: %s, CF-Connecting-IP: %s, Remote-Addr: %s\n",
			// 	c.Request.Method, c.Request.URL.Path,
			// 	c.ClientIP(),
			// 	c.GetHeader("X-Real-IP"),
			// 	c.GetHeader("X-Forwarded-For"),
			// 	c.GetHeader("CF-Connecting-IP"),
			// 	c.Request.RemoteAddr,
			// )
		}
		c.Next()
	})

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "VaultKe API is running",
			"version": "1.0.0",
		})
	})

	// Legal pages for Google OAuth
	router.GET("/privacy-policy", func(c *gin.Context) {
		c.HTML(http.StatusOK, "privacy-policy.html", nil)
	})
	router.GET("/terms-of-service", func(c *gin.Context) {
		c.HTML(http.StatusOK, "terms-of-service.html", nil)
	})

	// Serve static files (uploaded images)
	router.Static("/uploads", "./uploads")

	// Serve notification sound files
	router.Static("/notification_sound", "./notification_sound")

	// Initialize services
	authService := services.NewAuthService(cfg.JWTSecret, cfg.JWTExpiration)
	authMiddleware := middleware.NewAuthMiddleware(authService)
	wsService := services.NewWebSocketService()

	// Initialize email service
	emailService := services.NewEmailService()

	// Initialize password reset service
	passwordResetService := services.NewPasswordResetService(db, emailService)

	// Initialize password reset table
	if err := passwordResetService.InitializePasswordResetTable(); err != nil {
		log.Fatalf("Failed to initialize password reset table: %v", err)
	}

	// Initialize LiveKit meeting service
	api.InitializeMeetingService(db)

	// Initialize notification scheduler for reminders
	notificationScheduler := services.NewNotificationScheduler(db)
	notificationScheduler.Start()

	// Initialize scheduler service for meeting auto-unlock
	// Note: You'll need to get the meeting service instance to pass here
	// For now, we'll initialize it separately in the API package

	// Initialize handlers
	authHandlers := api.NewAuthHandlers(db, cfg.JWTSecret, cfg.JWTExpiration)
	reminderHandlers := api.NewReminderHandlers(db)
	sharesHandlers := api.NewSharesHandlers(db)
	dividendsHandlers := api.NewDividendsHandlers(db)
	pollsHandlers := api.NewPollsHandlers(db)
	disbursementHandlers := api.NewDisbursementHandlers(db)
	reportsHandlers := api.NewFinancialReportsHandlers(db)
	deliveryContactsHandlers := api.NewDeliveryContactsHandlers(db)
	userSearchHandlers := api.NewUserSearchHandlers(db)
	receiptHandlers := api.NewReceiptHandlers(db)
	moneyRequestHandlers := api.NewMoneyRequestHandlers(db)

	// Database middleware to inject db into context
	dbMiddleware := func(c *gin.Context) {
		c.Set("db", db)
		c.Next()
	}

	// Configuration middleware to inject config into context
	configMiddleware := func(c *gin.Context) {
		c.Set("config", cfg)
		c.Next()
	}

	// WebSocket middleware to inject wsService into context
	wsMiddleware := func(c *gin.Context) {
		c.Set("wsService", wsService)
		c.Next()
	}

	// Password reset middleware to inject passwordResetService into context
	passwordResetMiddleware := func(c *gin.Context) {
		c.Set("passwordResetService", passwordResetService)
		c.Next()
	}

	// API routes
	apiGroup := router.Group("/api/v1")
	{
		// Authentication routes with stricter rate limiting
		auth := apiGroup.Group("/auth")
		auth.Use(middleware.AuthRateLimitMiddleware()) // Stricter rate limiting for auth endpoints
		auth.Use(dbMiddleware)                         // Add database context for login session recording
		auth.Use(passwordResetMiddleware)              // Add password reset service to context
		{
			auth.POST("/register", authHandlers.Register)
			auth.POST("/login", authHandlers.Login)
			auth.POST("/logout", authMiddleware.AuthRequired(), authHandlers.Logout)
			auth.POST("/refresh", authHandlers.RefreshToken)
			auth.POST("/verify-email", authMiddleware.AuthRequired(), authHandlers.VerifyEmail)
			auth.POST("/verify-phone", authMiddleware.AuthRequired(), authHandlers.VerifyPhone)
			auth.POST("/forgot-password", authHandlers.ForgotPassword)
			auth.POST("/reset-password", authHandlers.ResetPassword)
			auth.POST("/test-email", authHandlers.TestEmail) // For development testing
		}

		// WebSocket route (handles auth internally)
		apiGroup.GET("/ws", wsService.HandleWebSocket)

		// Public marketplace routes (no authentication required for browsing)
		publicMarketplace := apiGroup.Group("/marketplace")
		publicMarketplace.Use(dbMiddleware)
		publicMarketplace.Use(wsMiddleware)
		{
			publicProducts := publicMarketplace.Group("/products")
			{
				publicProducts.GET("/", api.GetProducts)     // ✅ Public - browse products
				publicProducts.GET("/all", api.GetProducts)  // ✅ Public - get all products (same as above but with higher default limit)
				publicProducts.GET("/:id", api.GetProduct)   // ✅ Public - view product details
			}

			// Public categories endpoint
			publicMarketplace.GET("/categories", api.GetMarketplaceCategories) // ✅ Public - get categories with counts
		}

		// Public payment routes (no authentication required for callbacks)
		publicPayments := apiGroup.Group("/payments")
		publicPayments.Use(dbMiddleware)
		publicPayments.Use(configMiddleware)
		{
			publicPayments.POST("/mpesa/callback", api.HandleMpesaCallback)
		}

		// Public Google Drive OAuth routes (no authentication required)
		publicAuth := apiGroup.Group("/auth")
		{
			publicAuth.GET("/google/drive", api.InitiateGoogleDriveAuth)
			publicAuth.GET("/google/callback", api.HandleGoogleDriveCallback)
		}

		// Protected routes
		protected := apiGroup.Group("/")
		protected.Use(authMiddleware.AuthRequired())
		protected.Use(dbMiddleware)
		protected.Use(configMiddleware)
		protected.Use(wsMiddleware)
		{
			// User routes
			users := protected.Group("/users")
			{
				users.GET("/", api.GetUsers)
				users.GET("/admin/all", api.GetAllUsersForAdmin)         // Admin endpoint to get all users
				users.GET("/admin/statistics", api.GetAdminStatistics)   // Admin statistics endpoint
				users.GET("/admin/analytics", api.GetSystemAnalytics)    // System analytics endpoint
				users.GET("/profile", authHandlers.GetProfile)
				users.PUT("/profile", authHandlers.UpdateProfile)
				users.GET("/statistics", api.GetUserStatistics) // User statistics endpoint

				// Google Drive backup routes
				users.GET("/google-drive/auth-url", api.GetGoogleDriveAuthURL)
				users.POST("/google-drive/store-tokens", api.StoreGoogleDriveTokens)
				users.POST("/google-drive/disconnect", api.DisconnectGoogleDrive)
				users.POST("/google-drive/backup", api.CreateGoogleDriveBackup)
				users.POST("/google-drive/restore", api.RestoreGoogleDriveBackup)
				users.GET("/google-drive/backup-info", api.GetGoogleDriveBackupInfo)
				users.GET("/google-drive/status", api.GetGoogleDriveStatus)
				users.GET("/google-drive/debug-tokens", api.DebugGoogleDriveTokens) // Debug endpoint

				// User settings routes
				users.GET("/privacy-settings", api.GetPrivacySettings)
				users.PUT("/privacy-settings", api.UpdatePrivacySettings)
				users.GET("/security-settings", api.GetSecuritySettings)
				users.PUT("/security-settings", api.UpdateSecuritySettings)
				users.GET("/preferences", api.GetUserPreferences)
				users.PUT("/preferences", api.UpdateUserPreferences)

				users.POST("/avatar", api.UploadAvatar)
				users.PUT("/:id/role", api.AdminUpdateUserRole) // Admin endpoint to update user role
				users.PUT("/:id/status", api.UpdateUserStatus)  // Admin endpoint to update user status
				users.DELETE("/:id", api.DeleteUser)            // Admin endpoint to delete user
			}

			// Chama routes
			chamas := protected.Group("/chamas")
			{
				chamas.GET("/", api.GetChamas)
				chamas.GET("/admin/all", api.GetAllChamasForAdmin) // Admin endpoint to get all chamas
				chamas.POST("/", api.CreateChama)
				chamas.GET("/my", api.GetUserChamas)
				chamas.GET("/:id", api.GetChama)
				chamas.PUT("/:id", api.UpdateChama)
				chamas.DELETE("/:id", api.DeleteChama)
				chamas.GET("/:id/members", api.GetChamaMembers)
				chamas.GET("/:id/members/:userId/role", api.GetMemberRole)
				chamas.POST("/:id/join", api.JoinChama)
				chamas.POST("/:id/leave", api.LeaveChama)
				chamas.GET("/:id/transactions", api.GetChamaTransactions)
				chamas.GET("/:id/statistics", api.GetChamaStatistics)

				// Invitation routes
				chamas.POST("/:id/invite", api.SendChamaInvitation)
				chamas.GET("/:id/invitations/sent", api.GetChamaSentInvitations)
				chamas.GET("/invitations", api.GetUserInvitations)
				chamas.POST("/invitations/:id/respond", api.RespondToInvitation)
				chamas.POST("/invitations/:id/cancel", api.CancelInvitation)
				chamas.POST("/invitations/:id/resend", api.ResendInvitation)
			}

			// Wallet routes
			wallets := protected.Group("/wallets")
			{
				wallets.GET("/", api.GetWallets)
				wallets.GET("/balance", api.GetWalletBalance)
				wallets.GET("/transactions", api.GetUserTransactions)
				wallets.GET("/:id", api.GetWallet)
				wallets.GET("/:id/transactions", api.GetWalletTransactions)
				wallets.POST("/transfer", api.TransferMoney)
				wallets.POST("/deposit", api.DepositMoney)
				wallets.POST("/withdraw", api.WithdrawMoney)
			}

			// Money request routes (part of wallet functionality)
			wallet := protected.Group("/wallet")
			{
				wallet.POST("/create-money-request", moneyRequestHandlers.CreateMoneyRequest)
				wallet.POST("/send-money-request", moneyRequestHandlers.SendMoneyRequest)
				wallet.GET("/recent-contacts", moneyRequestHandlers.GetRecentContacts)
			}

			// Receipt routes
			receipts := protected.Group("/receipts")
			{
				receipts.GET("/transactions/:transactionId", receiptHandlers.GetTransactionReceipt)
				receipts.GET("/transactions/:transactionId/download", receiptHandlers.DownloadTransactionReceipt)
			}

			// Protected marketplace routes (authentication required)
			marketplace := protected.Group("/marketplace")
			{
				products := marketplace.Group("/products")
				{
					products.GET("/manage/all", api.GetAllProducts)           // ✅ Auth required - get all products for management (including out of stock)
					products.GET("/:id/details", api.GetProductWithOwnership) // ✅ Enhanced product details with ownership
					products.POST("/", api.CreateProduct)                     // ✅ Auth required - create product
					products.PUT("/:id", api.UpdateProduct)                   // ✅ Auth required - update product
					products.DELETE("/:id", api.DeleteProduct)                // ✅ Auth required - delete product
				}

				marketplace.GET("/cart", api.GetCart)               // ✅ Auth required - view cart
				marketplace.POST("/cart", api.AddToCart)            // ✅ Auth required - add to cart
				marketplace.DELETE("/cart/:id", api.RemoveFromCart) // ✅ Auth required - remove from cart

				marketplace.GET("/wishlist", api.GetWishlist)                      // ✅ Auth required - view wishlist
				marketplace.POST("/wishlist", api.AddToWishlist)                   // ✅ Auth required - add to wishlist
				marketplace.DELETE("/wishlist/:productId", api.RemoveFromWishlist) // ✅ Auth required - remove from wishlist

				orders := marketplace.Group("/orders")
				{
					orders.GET("/", api.GetOrders)                                                 // ✅ Auth required - view orders
					orders.POST("/", api.CreateOrder)                                              // ✅ Auth required - create order
					orders.GET("/:id", api.GetOrder)                                               // ✅ Auth required - view order details
					orders.PUT("/:id", api.UpdateOrder)                                            // ✅ Auth required - update order
					orders.PUT("/:id/status", api.UpdateOrderStatus)                               // ✅ Auth required - update order status
					orders.POST("/:id/assign-delivery", api.AssignDeliveryPerson)                  // ✅ Auth required - assign delivery (legacy)
					orders.POST("/assign-delivery-to-products", api.AssignDeliveryPersonToProduct) // ✅ Auth required - assign delivery to specific products
				}

				// Analytics routes
				analytics := marketplace.Group("/analytics")
				{
					analytics.GET("/seller", api.GetSellerAnalytics) // ✅ Auth required - seller analytics
					analytics.GET("/buyer", api.GetBuyerStats)       // ✅ Auth required - buyer stats
				}

				// Delivery routes
				deliveries := marketplace.Group("/deliveries")
				{
					deliveries.GET("/", api.GetDeliveries)                  // ✅ Auth required - view deliveries
					deliveries.POST("/:id/accept", api.AcceptDelivery)      // ✅ Auth required - accept delivery
					deliveries.PUT("/:id/status", api.UpdateDeliveryStatus) // ✅ Auth required - update delivery status
				}

				marketplace.GET("/reviews/product/:productId", api.GetReviews)                  // ✅ Auth required - view product reviews
				marketplace.GET("/reviews/product/:productId/stats", api.GetProductReviewStats) // ✅ Auth required - get review stats
				marketplace.POST("/reviews", api.CreateReview)                                  // ✅ Auth required - create review
				marketplace.GET("/reviews/my", api.GetMyReviews)                                // ✅ Auth required - get user's reviews
			}

			// Payment routes
			payments := protected.Group("/payments")
			{
				payments.POST("/mpesa/stk", api.InitiateMpesaSTK)
				payments.GET("/mpesa/status/:checkoutRequestId", api.GetMpesaTransactionStatus)
				payments.POST("/bank-transfer", api.InitiateBankTransfer)
			}

			// Chat routes
			chat := protected.Group("/chat")
			{
				chat.GET("/rooms", api.GetChatRooms)
				chat.POST("/rooms", api.CreateChatRoom)
				chat.GET("/rooms/:id", api.GetChatRoom)
				chat.POST("/rooms/:id/join", api.JoinChatRoom) // Added missing join endpoint
				chat.GET("/rooms/:id/members", api.GetChatRoomMembers)
				chat.DELETE("/rooms/:id", api.DeleteChatRoom)
				chat.POST("/rooms/:id/clear", api.ClearChatRoom)
				chat.GET("/rooms/:id/messages", api.GetChatMessages)
				chat.POST("/rooms/:id/messages", api.SendMessage)
				chat.PUT("/rooms/:id/read", api.MarkMessagesAsRead)
			}

			// Notifications routes
			notifications := protected.Group("/notifications")
			{
				notifications.GET("/", api.GetNotifications)
				notifications.GET("/unread-count", api.GetUnreadNotificationCount)
				notifications.PUT("/:id/read", api.MarkNotificationAsRead)
				notifications.POST("/read-all", api.MarkAllNotificationsAsRead)
				notifications.DELETE("/:id", api.DeleteNotification)

				// Notification preferences routes
				notifications.GET("/preferences", api.GetNotificationPreferences)
				notifications.PUT("/preferences", api.UpdateNotificationPreferences)
				notifications.GET("/sounds", api.GetAvailableNotificationSounds)
				notifications.POST("/test-sound", api.TestNotificationSound)

				// Legacy notification settings routes (for backward compatibility)
				notifications.GET("/settings", api.GetNotificationSettings)
				notifications.PUT("/settings", api.UpdateNotificationSettings)

				// Chama invitation response routes
				notifications.POST("/invitations/:id/accept", api.AcceptChamaInvitation)
				notifications.POST("/invitations/:id/reject", api.RejectChamaInvitation)
			}

			// Support routes
			support := protected.Group("/support")
			{
				support.POST("/requests", api.CreateSupportRequest)
				support.GET("/requests", api.GetSupportRequests)
				support.PUT("/requests/:id", api.UpdateSupportRequest)
				support.POST("/test-request", api.CreateTestSupportRequest) // Test endpoint
			}

			// Security routes
			auth := protected.Group("/auth")
			{
				auth.POST("/change-password", api.ChangePassword)
				auth.GET("/login-history", api.GetLoginHistory)
				auth.POST("/logout-all-devices", api.LogoutAllDevices)
				auth.POST("/logout-device/:sessionId", api.LogoutSpecificDevice)
			}

			// Learning routes
			learning := protected.Group("/learning")
			{
				// Public learning routes (require authentication but not admin)
				learning.GET("/categories", api.GetLearningCategories)
				learning.GET("/categories/:id", api.GetLearningCategory)
				learning.GET("/courses", api.GetLearningCourses)
				learning.GET("/courses/:id", api.GetLearningCourse)
				learning.POST("/courses/:id/start", api.StartCourse)
				learning.POST("/courses/:id/submit-quiz", api.SubmitQuizResults)

				// Learning content upload routes (require authentication)
				learning.POST("/upload/image", api.UploadLearningImage)
				learning.POST("/upload/video", api.UploadLearningVideo)
				learning.POST("/upload/document", api.UploadLearningDocument)
				learning.POST("/validate-video-url", api.ValidateVideoURL)

				// Admin learning routes
				admin := learning.Group("/admin")
				admin.Use(func(c *gin.Context) {
					userRole := c.GetString("userRole")
					if userRole != "admin" {
						c.JSON(http.StatusForbidden, gin.H{
							"success": false,
							"error":   "Admin access required",
						})
						c.Abort()
						return
					}
					c.Next()
				})
				{
					// Category management
					admin.POST("/categories", api.CreateLearningCategory)
					admin.PUT("/categories/:id", api.UpdateLearningCategory)
					admin.DELETE("/categories/:id", api.DeleteLearningCategory)

					// Course management
					admin.POST("/courses", api.CreateLearningCourse)
					admin.PUT("/courses/:id", api.UpdateLearningCourse)
					admin.DELETE("/courses/:id", api.DeleteLearningCourse)
				}
			}

			// Reminder routes
			reminders := protected.Group("/reminders")
			{
				reminders.POST("/", reminderHandlers.CreateReminder)
				reminders.GET("/", reminderHandlers.GetUserReminders)
				reminders.GET("/:id", reminderHandlers.GetReminder)
				reminders.PUT("/:id", reminderHandlers.UpdateReminder)
				reminders.DELETE("/:id", reminderHandlers.DeleteReminder)
				reminders.POST("/:id/toggle", reminderHandlers.ToggleReminder)
			}

			// Shares routes
			shares := protected.Group("/chamas/:id/shares")
			{
				shares.POST("/", sharesHandlers.CreateShares)
				shares.GET("/", sharesHandlers.GetChamaShares)
				shares.GET("/summary", sharesHandlers.GetChamaSharesSummary)
				shares.GET("/transactions", sharesHandlers.GetShareTransactions)
				shares.GET("/members/:memberId", sharesHandlers.GetMemberShares)
				shares.PUT("/:shareId", sharesHandlers.UpdateShares)
			}

			// Dividends routes
			dividends := protected.Group("/chamas/:id/dividends")
			{
				dividends.POST("/", dividendsHandlers.DeclareDividend)
				dividends.GET("/", dividendsHandlers.GetChamaDividendDeclarations)
				dividends.GET("/:declarationId", dividendsHandlers.GetDividendDeclarationDetails)
				dividends.POST("/:declarationId/approve", dividendsHandlers.ApproveDividend)
				dividends.POST("/:declarationId/process", dividendsHandlers.ProcessDividendPayments)
				dividends.GET("/members/:memberId/history", dividendsHandlers.GetMemberDividendHistory)
				dividends.GET("/my-history", dividendsHandlers.GetMyDividendHistory)
			}

			// Polls and Voting routes (new system - currently broken due to table conflicts)
			polls := protected.Group("/chamas/:id/polls")
			{
				polls.POST("/", pollsHandlers.CreatePoll)
				polls.GET("/", pollsHandlers.GetChamaPolls)
				polls.GET("/active", pollsHandlers.GetActivePolls)
				polls.GET("/results", pollsHandlers.GetPollResults)
				polls.GET("/:pollId", pollsHandlers.GetPollDetails)
				polls.POST("/:pollId/vote", pollsHandlers.CastVote)
				polls.POST("/role-escalation", pollsHandlers.CreateRoleEscalationPoll)
				polls.GET("/members", pollsHandlers.GetChamaMembers)
			}

			// Vote routes (using old vote system - working)
			votes := protected.Group("/chamas/:id/votes")
			{
				votes.POST("/", api.CreateVote)
				votes.GET("/", api.GetChamaVotes)
				votes.GET("/active", api.GetActiveVotes)
				votes.GET("/results", api.GetVoteResults)
				votes.GET("/:voteId", api.GetVoteDetails)
				votes.POST("/:voteId/vote", api.CastVoteOnItem)
				votes.POST("/role-escalation", api.CreateRoleEscalationVote)
			}

			// Disbursement and Account Management routes
			disbursements := protected.Group("/chamas/:id")
			{
				disbursements.GET("/disbursements", disbursementHandlers.GetDisbursementBatches)
				disbursements.POST("/disbursements/:batchId/process", disbursementHandlers.ProcessDisbursementBatch)
				disbursements.POST("/disbursements/:batchId/approve", disbursementHandlers.ApproveDisbursementBatch)
				disbursements.GET("/transparency", disbursementHandlers.GetTransparencyLog)
			}

			// Financial Reports routes
			reports := protected.Group("/chamas/:id")
			{
				reports.GET("/reports", reportsHandlers.GetFinancialReports)
				reports.POST("/reports", reportsHandlers.GenerateFinancialReport)
				reports.GET("/reports/:reportId/download", reportsHandlers.DownloadFinancialReport)
			}

			// User Search routes
			userSearch := protected.Group("/users")
			{
				userSearch.GET("/search", userSearchHandlers.SearchUsers)
				userSearch.GET("/search/advanced", userSearchHandlers.SearchUsersAdvanced)
				userSearch.GET("/:userId/profile", userSearchHandlers.GetUserProfile)
			}

			// Marketplace roles routes
			marketplaceRoles := protected.Group("/marketplace")
			{
				marketplaceRoles.GET("/user-roles/:userId", userSearchHandlers.CheckMarketplaceRoles)
			}

			// Marketplace Delivery Contacts routes
			deliveryContacts := protected.Group("/marketplace")
			{
				deliveryContacts.GET("/delivery-contacts", deliveryContactsHandlers.GetDeliveryContacts)
				deliveryContacts.POST("/delivery-contacts", deliveryContactsHandlers.CreateDeliveryContact)
				deliveryContacts.PUT("/delivery-contacts/:contactId", deliveryContactsHandlers.UpdateDeliveryContact)
				deliveryContacts.DELETE("/delivery-contacts/:contactId", deliveryContactsHandlers.DeleteDeliveryContact)

				// Auto-seller detection routes
				deliveryContacts.GET("/products/user/:userId", api.GetUserProducts)
				deliveryContacts.POST("/auto-register-seller", api.AutoRegisterAsSeller)
				deliveryContacts.POST("/auto-register-buyer", api.AutoRegisterAsBuyer)
			}

			// Contributions routes
			contributions := protected.Group("/contributions")
			{
				contributions.GET("", api.GetContributions)
				contributions.POST("", api.MakeContribution)
				contributions.GET("/:id", api.GetContribution)
			}

			// Meetings routes
			meetings := protected.Group("/meetings")
			{
				meetings.GET("/", api.GetMeetings)
				meetings.POST("/", api.CreateMeeting)
				meetings.POST("/livekit", api.CreateMeetingWithLiveKit)   // New LiveKit-enabled meeting creation
				meetings.POST("/calendar", api.CreateMeetingWithCalendar) // New meeting with calendar integration
				meetings.GET("/:id", api.GetMeeting)
				meetings.PUT("/:id", api.UpdateMeeting)
				meetings.DELETE("/:id", api.DeleteMeeting)
				meetings.POST("/:id/join", api.JoinMeeting)
				meetings.POST("/:id/join-livekit", api.JoinMeetingWithLiveKit)      // New LiveKit join endpoint
				meetings.POST("/:id/join-jitsi", api.JoinMeetingWithJitsi)          // New Jitsi Meet join endpoint
				meetings.GET("/:id/preview", api.PreviewMeeting)                    // New meeting preview for chairperson/secretary
				meetings.POST("/:id/start", api.StartMeeting)                       // New start meeting endpoint
				meetings.POST("/:id/end", api.EndMeeting)                           // New end meeting endpoint
				meetings.POST("/:id/attendance", api.MarkAttendance)                // New attendance marking
				meetings.GET("/:id/attendance", api.GetMeetingAttendance)           // New attendance retrieval
				meetings.POST("/:id/documents", api.UploadMeetingDocument)          // Upload meeting documents
				meetings.GET("/:id/documents", api.GetMeetingDocuments)             // Get meeting documents
				meetings.DELETE("/:id/documents/:docId", api.DeleteMeetingDocument) // Delete meeting document
				meetings.POST("/:id/minutes", api.SaveMeetingMinutes)               // Save meeting minutes/notes
				meetings.GET("/:id/minutes", api.GetMeetingMinutes)                 // Get meeting minutes/notes
				meetings.GET("/test-livekit", api.TestLiveKitConnection)            // Test LiveKit configuration
			}

			// Merry-Go-Round routes
			merryGoRounds := protected.Group("/merry-go-rounds")
			{
				merryGoRounds.GET("/", api.GetMerryGoRounds)
				merryGoRounds.POST("/", api.CreateMerryGoRound)
				merryGoRounds.GET("/:id", api.GetMerryGoRound)
				merryGoRounds.PUT("/:id", api.UpdateMerryGoRound)
				merryGoRounds.DELETE("/:id", api.DeleteMerryGoRound)
				merryGoRounds.POST("/:id/join", api.JoinMerryGoRound)
			}

			// Welfare routes
			welfare := protected.Group("/welfare")
			{
				welfare.GET("/", api.GetWelfareRequests)
				welfare.POST("/", api.CreateWelfareRequest)
				welfare.GET("/:id", api.GetWelfareRequest)
				welfare.PUT("/:id", api.UpdateWelfareRequest)
				welfare.DELETE("/:id", api.DeleteWelfareRequest)
				welfare.POST("/:id/vote", api.VoteOnWelfareRequest)
				welfare.POST("/contribute", api.ContributeToWelfare)
				welfare.GET("/:id/contributions", api.GetWelfareContributions)
			}

			// Loan routes
			loans := protected.Group("/loans")
			{
				loans.GET("/", api.GetLoanApplications)
				loans.POST("/apply", api.CreateLoanApplication)
				loans.GET("/:id", api.GetLoanApplication)
				loans.PUT("/:id", api.UpdateLoanApplication)
				loans.DELETE("/:id", api.DeleteLoanApplication)
				loans.POST("/:id/approve", api.ApproveLoan)
				loans.POST("/:id/reject", api.RejectLoan)
				loans.POST("/:id/disburse", api.DisburseLoan)
				loans.GET("/guarantor-requests", api.GetGuarantorRequests)
				loans.POST("/guarantors/:guarantorId/respond", api.RespondToGuarantorRequest)
			}
		}
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// Configure server to handle both IPv4 and IPv6
	server := &http.Server{
		Addr:    ":" + port,
		Handler: router,
		// Add timeouts for better stability
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	log.Printf("VaultKe API server starting on port %s", port)
	log.Printf("Server accessible via:")
	log.Printf("  - http://localhost:%s", port)
	log.Printf("  - http://127.0.0.1:%s", port)
	log.Printf("  - http://[::1]:%s", port)

	// Graceful shutdown
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Server failed to start:", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Stop notification scheduler
	notificationScheduler.Stop()

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server shutdown complete")
}
