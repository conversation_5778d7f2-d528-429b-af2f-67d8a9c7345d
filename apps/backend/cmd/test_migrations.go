package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/vaultke/backend/internal/database"
	_ "github.com/mattn/go-sqlite3"
)

func main() {
	log.Println("🧪 Testing VaultKe Database Migration System...")

	// Create a test database
	testDBPath := "test_vaultke.db"
	
	// Remove existing test database
	if err := os.Remove(testDBPath); err != nil && !os.IsNotExist(err) {
		log.Printf("Warning: Failed to remove existing test database: %v", err)
	}

	// Initialize test database
	db, err := initializeTestDatabase(testDBPath)
	if err != nil {
		log.Fatalf("❌ Failed to initialize test database: %v", err)
	}
	defer db.Close()
	defer os.Remove(testDBPath) // Clean up

	// Test 1: Run migrations for the first time
	log.Println("\n🔄 Test 1: Running migrations for the first time...")
	if err := database.Migrate(db); err != nil {
		log.Fatalf("❌ First migration run failed: %v", err)
	}
	log.Println("✅ First migration run completed successfully!")

	// Test 2: Run migrations again (should skip already applied migrations)
	log.Println("\n🔄 Test 2: Running migrations again (should skip applied migrations)...")
	if err := database.Migrate(db); err != nil {
		log.Fatalf("❌ Second migration run failed: %v", err)
	}
	log.Println("✅ Second migration run completed successfully!")

	// Test 3: Check migration status
	log.Println("\n🔄 Test 3: Checking migration status...")
	migrations, err := database.GetMigrationStatus(db)
	if err != nil {
		log.Fatalf("❌ Failed to get migration status: %v", err)
	}
	log.Printf("✅ Found %d applied migrations", len(migrations))

	// Test 4: Verify notification system is ready
	log.Println("\n🔄 Test 4: Verifying notification system...")
	ready, err := database.IsNotificationSystemReady(db)
	if err != nil {
		log.Fatalf("❌ Failed to check notification system: %v", err)
	}
	if !ready {
		log.Fatalf("❌ Notification system is not ready")
	}
	log.Println("✅ Notification system is ready!")

	// Test 5: Verify table structures
	log.Println("\n🔄 Test 5: Verifying table structures...")
	if err := verifyTableStructures(db); err != nil {
		log.Fatalf("❌ Table structure verification failed: %v", err)
	}
	log.Println("✅ All table structures verified!")

	// Test 6: Test data integrity
	log.Println("\n🔄 Test 6: Testing data integrity...")
	if err := testDataIntegrity(db); err != nil {
		log.Fatalf("❌ Data integrity test failed: %v", err)
	}
	log.Println("✅ Data integrity test passed!")

	log.Println("\n🎉 All migration tests passed successfully!")
	log.Println("✅ The unified migration system is working correctly!")
}

func initializeTestDatabase(dbPath string) (*sql.DB, error) {
	// Ensure directory exists
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Open database connection
	db, err := sql.Open("sqlite3", dbPath+"?_foreign_keys=on")
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

func verifyTableStructures(db *sql.DB) error {
	// List of critical tables that should exist
	criticalTables := []string{
		"schema_migrations",
		"users",
		"chamas",
		"notifications",
		"notification_sounds",
		"user_notification_preferences",
		"notification_templates",
		"notification_delivery_log",
		"user_reminders",
	}

	for _, table := range criticalTables {
		var count int
		err := db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", table).Scan(&count)
		if err != nil {
			return fmt.Errorf("failed to check table %s: %w", table, err)
		}
		if count == 0 {
			return fmt.Errorf("table %s does not exist", table)
		}
		log.Printf("   ✅ Table %s exists", table)
	}

	return nil
}

func testDataIntegrity(db *sql.DB) error {
	// Test 1: Check if default notification sounds exist
	var soundCount int
	err := db.QueryRow("SELECT COUNT(*) FROM notification_sounds WHERE is_default = 1").Scan(&soundCount)
	if err != nil {
		return fmt.Errorf("failed to check default sounds: %w", err)
	}
	if soundCount == 0 {
		return fmt.Errorf("no default notification sounds found")
	}
	log.Printf("   ✅ Found %d default notification sound(s)", soundCount)

	// Test 2: Check if notification templates exist
	var templateCount int
	err = db.QueryRow("SELECT COUNT(*) FROM notification_templates").Scan(&templateCount)
	if err != nil {
		return fmt.Errorf("failed to check templates: %w", err)
	}
	if templateCount == 0 {
		return fmt.Errorf("no notification templates found")
	}
	log.Printf("   ✅ Found %d notification template(s)", templateCount)

	// Test 3: Verify foreign key constraints work
	// Try to insert a notification with invalid user_id (should fail)
	_, err = db.Exec(`
		INSERT INTO notifications (user_id, title, message, type) 
		VALUES ('invalid-user-id', 'Test', 'Test message', 'system')
	`)
	if err == nil {
		return fmt.Errorf("foreign key constraint not working - invalid user_id was accepted")
	}
	log.Println("   ✅ Foreign key constraints are working")

	return nil
}
