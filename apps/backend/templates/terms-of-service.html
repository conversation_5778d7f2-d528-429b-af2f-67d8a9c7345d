<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VaultKe - Terms of Service</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 { color: #2c3e50; }
        h2 { color: #34495e; margin-top: 30px; }
        .last-updated { color: #7f8c8d; font-style: italic; }
    </style>
</head>
<body>
    <h1>VaultKe Terms of Service</h1>
    <p class="last-updated">Last updated: July 25, 2025</p>

    <h2>1. Acceptance of Terms</h2>
    <p>By accessing and using VaultKe, you accept and agree to be bound by the terms and provision of this agreement.</p>

    <h2>2. Description of Service</h2>
    <p>VaultKe is a chama (group savings and investment) management platform that helps groups manage their finances, meetings, and member activities.</p>

    <h2>3. User Responsibilities</h2>
    <p>You are responsible for maintaining the confidentiality of your account and password and for restricting access to your account.</p>

    <h2>4. Financial Transactions</h2>
    <p>VaultKe facilitates financial transactions between chama members. Users are responsible for the accuracy of their financial information.</p>

    <h2>5. Google Drive Integration</h2>
    <p>By connecting Google Drive, you grant VaultKe permission to create and manage backup files in your Google Drive account.</p>

    <h2>6. Limitation of Liability</h2>
    <p>VaultKe shall not be liable for any indirect, incidental, special, consequential, or punitive damages.</p>

    <h2>7. Contact Information</h2>
    <p>For questions about these Terms of Service, please contact <NAME_EMAIL></p>

    <p><em>This is a development version. Full terms of service will be available when the app is publicly released.</em></p>
</body>
</html>
