<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VaultKe - Google Drive Connected</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #333;
        }

        .container {
            background: #ffffff;
            border-radius: 20px;
            padding: 48px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            max-width: 520px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #45a049, #2196F3);
        }

        .header {
            margin-bottom: 32px;
        }

        .logo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 20px;
            margin-bottom: 24px;
            box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
        }

        .logo::before {
            content: '🔒';
            font-size: 36px;
        }

        h1 {
            color: #1a1a1a;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            font-weight: 400;
        }
        .card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
            position: relative;
        }

        .card-success {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #4CAF50;
        }

        .card-info {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-color: #2196F3;
        }

        .card-warning {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            border-color: #f59e0b;
        }

        .card-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            margin-right: 12px;
            font-size: 16px;
            vertical-align: top;
        }

        .card-success .card-icon {
            background: #4CAF50;
            color: white;
        }

        .card-info .card-icon {
            background: #2196F3;
            color: white;
        }

        .card-warning .card-icon {
            background: #f59e0b;
            color: white;
        }

        .card-title {
            font-weight: 600;
            font-size: 16px;
            color: #1a1a1a;
            margin-bottom: 8px;
            display: inline-block;
            vertical-align: top;
            width: calc(100% - 44px);
        }

        .card-content {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
            margin-left: 44px;
        }

        .steps {
            list-style: none;
            padding: 0;
            margin: 12px 0 0 0;
        }

        .steps li {
            padding: 8px 0;
            position: relative;
            padding-left: 24px;
        }

        .steps li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 8px;
            background: #2196F3;
            color: white;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        .steps {
            counter-reset: step-counter;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin-top: 32px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .footer {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo"></div>
            <h1>Google Drive Connected!</h1>
            <p class="subtitle">Your VaultKe account is now securely linked to Google Drive</p>
        </div>

        <div class="card card-success">
            <div class="card-icon">🔒</div>
            <div class="card-title">Secure Connection Established</div>
            <div class="card-content">
                Your authentication has been processed securely. No sensitive tokens are displayed on this page for your protection.
            </div>
        </div>

        <div class="card card-info">
            <div class="card-icon">📱</div>
            <div class="card-title">Next Steps</div>
            <div class="card-content">
                <ol class="steps">
                    <li>Click "Return to VaultKe App" below (or wait for auto-redirect)</li>
                    <li>Navigate to Settings → Backup & Sync</li>
                    <li>Your Google Drive connection is now active</li>
                    <li>Create your first secure backup</li>
                </ol>
            </div>
        </div>

        <div class="card card-warning">
            <div class="card-icon">⚠️</div>
            <div class="card-title">Security Reminder</div>
            <div class="card-content">
                VaultKe will never ask you to manually copy authentication tokens. Always keep your login credentials private and secure.
            </div>
        </div>

        <button class="btn" onclick="returnToApp()">
            Return to VaultKe App
        </button>

        <div class="footer">
            VaultKe - Secure Chama Management Platform<br>
            Redirecting to app in <span id="countdown">30</span> seconds
        </div>
    </div>

    <script>
        // App URL configuration (from backend)
        const APP_URL = '{{.app_url}}' || 'http://localhost:8081';

        // Function to return to app
        function returnToApp() {
            try {
                // Try to redirect to the app
                window.location.href = APP_URL;
            } catch (error) {
                // Fallback: close window if redirect fails
                window.close();
            }
        }

        // Countdown timer
        let countdown = 30;
        const countdownElement = document.getElementById('countdown');

        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;

            if (countdown <= 0) {
                clearInterval(timer);
                returnToApp();
            }
        }, 1000);

        // Enhanced security measures
        document.addEventListener('DOMContentLoaded', function() {
            // Clear browser history for security
            if (window.history && window.history.replaceState) {
                window.history.replaceState({}, document.title, window.location.pathname);
            }

            // Disable right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });

            // Disable text selection for security
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
            });

            // Disable F12, Ctrl+Shift+I, Ctrl+U
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' ||
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.key === 'u')) {
                    e.preventDefault();
                }
            });
        });

        // Focus management
        window.addEventListener('blur', function() {
            // Return to app if user switches away (security measure)
            setTimeout(() => {
                if (document.hidden) {
                    returnToApp();
                }
            }, 5000);
        });
    </script>
</body>
</html>
