# Environment Configuration
ENVIRONMENT=development
PORT=8080

# Database Configuration
DATABASE_URL=vaultke.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRATION=86400

# M-Pesa Configuration
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_PASSKEY=your_mpesa_passkey
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_CALLBACK_URL=https://c500-41-72-200-10.ngrok-free.app/api/v1/payments/mpesa/callback

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Google Drive Configuration
GOOGLE_DRIVE_CLIENT_ID=************-apj801tf38k25daiisnqt70f8m7j2o43.apps.googleusercontent.com
GOOGLE_DRIVE_CLIENT_SECRET=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
GOOGLE_DRIVE_REDIRECT_URL=http://localhost:8080/auth/google/callback
GOOGLE_DRIVE_ENCRYPTION_KEY=vaultke-google-drive-encryption-key-32

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# SMS Configuration (Africa's Talking)
AT_USERNAME=your_africas_talking_username
AT_API_KEY=your_africas_talking_api_key
AT_SENDER=VaultKe

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# LiveKit Configuration
LIVEKIT_WS_URL=wss://livemeetup-ges53s7d.livekit.cloud
LIVEKIT_API_KEY=APIggdTE7b6sLCm
LIVEKIT_API_SECRET=zFLzLgJlGlhYNX1OIPpreTLLA5DtOnVNrbv53fBfFZuB
