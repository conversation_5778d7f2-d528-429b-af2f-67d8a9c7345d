package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"vaultke-backend/internal/api"
	"vaultke-backend/test/helpers"
)

type UserBasicTestSuite struct {
	suite.Suite
	testDB *helpers.TestDatabase
	router *gin.Engine
}

func (suite *UserBasicTestSuite) SetupSuite() {
	suite.testDB = helpers.SetupTestDatabase()

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// Add middleware
	suite.router.Use(func(c *gin.Context) {
		c.Set("userID", "test-user-123")
		c.Set("db", suite.testDB.DB)
		c.Next()
	})

	// Setup user routes
	apiGroup := suite.router.Group("/api/v1")
	{
		apiGroup.GET("/users", api.GetUsers)
		apiGroup.GET("/profile", api.GetProfile)
		apiGroup.PUT("/profile", api.UpdateProfile)
		apiGroup.POST("/avatar", api.UploadAvatar)
		apiGroup.PUT("/users/:id/role", api.AdminUpdateUserRole)
		apiGroup.PUT("/users/:id/status", api.UpdateUserStatus)
		apiGroup.DELETE("/users/:id", api.DeleteUser)
		apiGroup.GET("/users/admin/all", api.GetAllUsersForAdmin)
	}
}

func (suite *UserBasicTestSuite) TearDownSuite() {
	if suite.testDB != nil {
		suite.testDB.Close()
	}
}

func (suite *UserBasicTestSuite) SetupTest() {
	// Clean up test data before each test
	if suite.testDB != nil {
		suite.testDB.CleanupTestData()
	}
	// Insert test data
	suite.insertTestData()
}

func (suite *UserBasicTestSuite) insertTestData() {
	// Create test user
	err := suite.testDB.CreateTestUser(helpers.TestUser{
		ID:       "test-user-123",
		Email:    "<EMAIL>",
		Phone:    "+254712345678",
		Role:     "user",
		Password: "password123",
	})
	suite.NoError(err)

	// Create admin user
	err = suite.testDB.CreateTestUser(helpers.TestUser{
		ID:       "admin-user-123",
		Email:    "<EMAIL>",
		Phone:    "+254712345679",
		Role:     "admin",
		Password: "password123",
	})
	suite.NoError(err)
}

func (suite *UserBasicTestSuite) TestGetUsers() {
	req, _ := http.NewRequest("GET", "/api/v1/users", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["data"])
}

func (suite *UserBasicTestSuite) TestGetUsersWithPagination() {
	req, _ := http.NewRequest("GET", "/api/v1/users?limit=10&offset=0", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["data"])
}

func (suite *UserBasicTestSuite) TestGetProfile() {
	req, _ := http.NewRequest("GET", "/api/v1/profile", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	// Response should be valid JSON
	assert.NotNil(suite.T(), response)
	// Check if success field exists and is boolean
	if success, exists := response["success"]; exists {
		assert.IsType(suite.T(), true, success)
	}
}

func (suite *UserBasicTestSuite) TestUpdateProfile() {
	suite.Run("successful_profile_update", func() {
		updateData := map[string]interface{}{
			"firstName": "Updated",
			"lastName":  "User",
			"bio":       "Updated bio",
		}
		jsonData, _ := json.Marshal(updateData)

		req, _ := http.NewRequest("PUT", "/api/v1/profile", bytes.NewReader(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(suite.T(), http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response should be valid JSON
		assert.NotNil(suite.T(), response)
		// Check if success field exists and is boolean
		if success, exists := response["success"]; exists {
			assert.IsType(suite.T(), true, success)
		}
	})

	suite.Run("profile_update_with_invalid_data", func() {
		updateData := map[string]interface{}{
			"email": "invalid-email", // Invalid email format
		}
		jsonData, _ := json.Marshal(updateData)

		req, _ := http.NewRequest("PUT", "/api/v1/profile", bytes.NewReader(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// The endpoint might return 200 or 400 depending on validation implementation
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusBadRequest)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response should have success field
		_, hasSuccess := response["success"]
		assert.True(suite.T(), hasSuccess)
	})
}

func (suite *UserBasicTestSuite) TestUploadAvatar() {
	suite.Run("upload_avatar_without_file", func() {
		req, _ := http.NewRequest("POST", "/api/v1/avatar", nil)
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// The endpoint might return 200 or 400 depending on implementation
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusBadRequest)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response should have success field
		_, hasSuccess := response["success"]
		assert.True(suite.T(), hasSuccess)
	})
}

func (suite *UserBasicTestSuite) TestGetAllUsersForAdmin() {
	suite.Run("admin_get_all_users", func() {
		req, _ := http.NewRequest("GET", "/api/v1/users/admin/all", nil)
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// This might return 403 if not admin, or 200 if successful
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusForbidden)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response structure should be valid regardless of success/failure
		_, hasSuccess := response["success"]
		assert.True(suite.T(), hasSuccess)
	})
}

func (suite *UserBasicTestSuite) TestAdminUpdateUserRole() {
	suite.Run("update_user_role", func() {
		updateData := map[string]interface{}{
			"role": "moderator",
		}
		jsonData, _ := json.Marshal(updateData)

		req, _ := http.NewRequest("PUT", "/api/v1/users/test-user-123/role", bytes.NewReader(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// This might return various status codes depending on implementation
		assert.True(suite.T(), w.Code >= 200 && w.Code < 500, "Expected status code between 200-499, got %d", w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response should be valid JSON
		assert.NotNil(suite.T(), response)
		// Check if success field exists and is boolean
		if success, exists := response["success"]; exists {
			assert.IsType(suite.T(), true, success)
		}
	})
}

func (suite *UserBasicTestSuite) TestUpdateUserStatus() {
	suite.Run("update_user_status", func() {
		updateData := map[string]interface{}{
			"status": "suspended",
		}
		jsonData, _ := json.Marshal(updateData)

		req, _ := http.NewRequest("PUT", "/api/v1/users/test-user-123/status", bytes.NewReader(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// This might return 403 if not admin, or 200 if successful
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusForbidden)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response structure should be valid regardless of success/failure
		_, hasSuccess := response["success"]
		assert.True(suite.T(), hasSuccess)
	})
}

func (suite *UserBasicTestSuite) TestDeleteUser() {
	suite.Run("delete_user", func() {
		req, _ := http.NewRequest("DELETE", "/api/v1/users/test-user-123", nil)
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// This might return 403 if not admin, or 200 if successful
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusForbidden)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		// Response structure should be valid regardless of success/failure
		_, hasSuccess := response["success"]
		assert.True(suite.T(), hasSuccess)
	})
}

// Additional comprehensive API handler tests for coverage
func (suite *UserBasicTestSuite) TestAuthHandlers() {
	suite.T().Run("register_endpoint", func(t *testing.T) {
		body := gin.H{
			"email":     "<EMAIL>",
			"password":  "password123",
			"firstName": "New",
			"lastName":  "User",
			"phone":     "+254700000999",
		}

		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/register", body, nil)
		// Should return some response (success or error)
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("login_endpoint", func(t *testing.T) {
		body := gin.H{
			"email":    "<EMAIL>",
			"password": "password123",
		}

		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/login", body, nil)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("logout_endpoint", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer test-token"}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/logout", nil, headers)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("refresh_token_endpoint", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer test-token"}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/refresh", nil, headers)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("forgot_password_endpoint", func(t *testing.T) {
		body := gin.H{"email": "<EMAIL>"}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/forgot-password", body, nil)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("reset_password_endpoint", func(t *testing.T) {
		body := gin.H{
			"token":    "reset-token",
			"password": "newpassword123",
		}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/reset-password", body, nil)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("verify_email_endpoint", func(t *testing.T) {
		body := gin.H{"token": "verify-token"}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/verify-email", body, nil)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("verify_phone_endpoint", func(t *testing.T) {
		body := gin.H{"token": "verify-token"}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/auth/verify-phone", body, nil)
		// Should return some response
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})
}

func (suite *UserBasicTestSuite) TestUserManagementHandlers() {
	suite.T().Run("get_users_with_filters", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer admin-token"}
		w := helpers.MakeRequest(suite.router, "GET", "/api/v1/users?page=1&limit=10&search=test", nil, headers)
		assert.Equal(t, 200, w.Code)
	})

	suite.T().Run("get_users_with_pagination", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer admin-token"}
		w := helpers.MakeRequest(suite.router, "GET", "/api/v1/users?page=2&limit=5", nil, headers)
		assert.Equal(t, 200, w.Code)
	})

	suite.T().Run("update_profile_with_all_fields", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer user-token"}
		body := gin.H{
			"firstName":   "Updated",
			"lastName":    "Name",
			"bio":         "Updated bio",
			"occupation":  "Developer",
			"dateOfBirth": "1990-01-01",
		}

		w := helpers.MakeRequest(suite.router, "PUT", "/api/v1/users/profile", body, headers)
		assert.Equal(t, 200, w.Code)
	})

	suite.T().Run("admin_operations_comprehensive", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer admin-token"}

		// Test role update
		body := gin.H{"role": "moderator"}
		w := helpers.MakeRequest(suite.router, "PUT", "/api/v1/admin/users/test-user-123/role", body, headers)
		assert.True(t, w.Code >= 200 && w.Code < 500)

		// Test status update
		body = gin.H{"status": "suspended"}
		w = helpers.MakeRequest(suite.router, "PUT", "/api/v1/admin/users/test-user-123/status", body, headers)
		assert.True(t, w.Code >= 200 && w.Code < 500)

		// Test user deletion
		w = helpers.MakeRequest(suite.router, "DELETE", "/api/v1/admin/users/test-user-123", nil, headers)
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})
}

func (suite *UserBasicTestSuite) TestSecurityHandlers() {
	suite.T().Run("change_password", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer user-token"}
		body := gin.H{
			"currentPassword": "password123",
			"newPassword":     "newpassword123",
		}

		w := helpers.MakeRequest(suite.router, "PUT", "/api/v1/security/change-password", body, headers)
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("get_login_history", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer user-token"}
		w := helpers.MakeRequest(suite.router, "GET", "/api/v1/security/login-history", nil, headers)
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})

	suite.T().Run("logout_all_devices", func(t *testing.T) {
		headers := map[string]string{"Authorization": "Bearer user-token"}
		w := helpers.MakeRequest(suite.router, "POST", "/api/v1/security/logout-all", nil, headers)
		assert.True(t, w.Code >= 200 && w.Code < 500)
	})
}

func TestUserBasicSuite(t *testing.T) {
	suite.Run(t, new(UserBasicTestSuite))
}
