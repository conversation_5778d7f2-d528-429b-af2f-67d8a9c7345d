{"name": "vaultke-backend", "version": "1.0.0", "description": "VaultKe Backend API Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["<PERSON><PERSON>", "api", "backend", "marketplace", "chama"], "author": "VaultKe Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "qrcode": "^1.5.4"}, "devDependencies": {"nodemon": "^3.0.1"}}