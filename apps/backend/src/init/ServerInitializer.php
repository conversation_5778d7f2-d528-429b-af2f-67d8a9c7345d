<?php

namespace App\Init;

use PDO;
use PDOException;
use Exception;
use App\Database\DatabaseMigrator;

class ServerInitializer
{
    private PDO $db;
    private DatabaseMigrator $migrator;
    private string $dbPath;

    public function __construct(string $dbPath = null)
    {
        $this->dbPath = $dbPath ?: __DIR__ . '/../../vaultke.db';
        $this->initializeDatabase();
        $this->migrator = new DatabaseMigrator($this->db);
    }

    /**
     * Initialize database connection
     */
    private function initializeDatabase(): void
    {
        try {
            $this->db = new PDO("sqlite:{$this->dbPath}");
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            
            // Enable foreign key constraints
            $this->db->exec('PRAGMA foreign_keys = ON');
            
        } catch (PDOException $e) {
            throw new Exception("Failed to connect to database: " . $e->getMessage());
        }
    }

    /**
     * Initialize the server - run all necessary setup
     */
    public function initialize(): void
    {
        echo "🚀 Initializing VaultKe Server...\n";
        echo "📍 Database: {$this->dbPath}\n\n";

        try {
            // Step 1: Run database migrations
            echo "📊 Setting up database...\n";
            $this->migrator->migrate();

            // Step 2: Initialize notification preferences for existing users
            echo "👥 Setting up notification preferences for existing users...\n";
            $this->migrator->initializeForExistingUsers();

            // Step 3: Verify system integrity
            echo "🔍 Verifying system integrity...\n";
            $this->verifySystemIntegrity();

            // Step 4: Create notification sound directory
            echo "🔊 Setting up notification sounds...\n";
            $this->setupNotificationSounds();

            echo "\n🎉 Server initialization completed successfully!\n";
            $this->displaySystemStatus();

        } catch (Exception $e) {
            echo "\n❌ Server initialization failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * Verify system integrity
     */
    private function verifySystemIntegrity(): void
    {
        $integrity = $this->migrator->verifyIntegrity();

        foreach ($integrity as $component => $status) {
            if ($component === 'error') {
                throw new Exception("Integrity check failed: " . $status);
            }

            $statusIcon = $status['status'] === 'OK' ? '✅' : 
                         ($status['status'] === 'EMPTY' ? '⚠️' : '❌');
            
            echo "  {$statusIcon} {$component}: {$status['status']}";
            
            if (isset($status['count'])) {
                echo " ({$status['count']} records)";
            }
            
            if (isset($status['missing_columns']) && !empty($status['missing_columns'])) {
                echo " - Missing: " . implode(', ', $status['missing_columns']);
            }
            
            echo "\n";
        }
    }

    /**
     * Setup notification sounds directory and files
     */
    private function setupNotificationSounds(): void
    {
        $soundsDir = __DIR__ . '/../../notification_sound';
        
        // Create directory if it doesn't exist
        if (!is_dir($soundsDir)) {
            if (!mkdir($soundsDir, 0755, true)) {
                throw new Exception("Failed to create notification sounds directory: {$soundsDir}");
            }
            echo "  📁 Created notification sounds directory\n";
        }

        // Check if ring.mp3 exists
        $ringPath = $soundsDir . '/ring.mp3';
        if (!file_exists($ringPath)) {
            echo "  ⚠️  Warning: ring.mp3 not found at {$ringPath}\n";
            echo "     Please copy your ring.mp3 file to this location.\n";
        } else {
            echo "  🔊 Default notification sound (ring.mp3) found\n";
        }

        // Set proper permissions
        if (is_dir($soundsDir)) {
            chmod($soundsDir, 0755);
            
            $soundFiles = glob($soundsDir . '/*.mp3');
            foreach ($soundFiles as $file) {
                chmod($file, 0644);
            }
        }
    }

    /**
     * Display system status
     */
    private function displaySystemStatus(): void
    {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "📊 VAULTKE NOTIFICATION SYSTEM STATUS\n";
        echo str_repeat("=", 50) . "\n";

        // Migration status
        $migrations = $this->migrator->status();
        echo "🔄 Migrations: " . count($migrations) . " completed\n";
        foreach ($migrations as $migration) {
            echo "   ✅ {$migration['migration']} ({$migration['executed_at']})\n";
        }

        // Default sound status
        $defaultSound = $this->migrator->getDefaultNotificationSound();
        if ($defaultSound) {
            echo "\n🔊 Default Notification Sound:\n";
            echo "   📛 Name: {$defaultSound['name']}\n";
            echo "   📁 Path: {$defaultSound['file_path']}\n";
            if ($defaultSound['duration_seconds']) {
                echo "   ⏱️  Duration: {$defaultSound['duration_seconds']}s\n";
            }
        }

        // Available sounds
        $sounds = $this->migrator->getAvailableNotificationSounds();
        echo "\n🎵 Available Notification Sounds: " . count($sounds) . "\n";
        foreach ($sounds as $sound) {
            $icon = $sound['is_default'] ? '🔊' : '🎵';
            echo "   {$icon} {$sound['name']} ({$sound['file_path']})\n";
        }

        // System readiness
        $isReady = $this->migrator->isNotificationSystemReady();
        $readyIcon = $isReady ? '✅' : '❌';
        $readyStatus = $isReady ? 'READY' : 'NOT READY';
        
        echo "\n🚀 System Status: {$readyIcon} {$readyStatus}\n";

        if ($isReady) {
            echo "\n🎉 The notification system is ready to use!\n";
            echo "   • Users can set notification preferences\n";
            echo "   • Sounds will be played from /notification_sound/ring.mp3\n";
            echo "   • Reminders and notifications are fully functional\n";
        } else {
            echo "\n⚠️  The notification system needs attention:\n";
            echo "   • Check if default sounds are properly configured\n";
            echo "   • Ensure notification templates are loaded\n";
        }

        echo "\n" . str_repeat("=", 50) . "\n";
    }

    /**
     * Get database connection
     */
    public function getDatabase(): PDO
    {
        return $this->db;
    }

    /**
     * Get migrator instance
     */
    public function getMigrator(): DatabaseMigrator
    {
        return $this->migrator;
    }

    /**
     * Quick health check
     */
    public function healthCheck(): array
    {
        return [
            'database_connected' => $this->db !== null,
            'notification_system_ready' => $this->migrator->isNotificationSystemReady(),
            'default_sound_available' => $this->migrator->getDefaultNotificationSound() !== null,
            'sounds_count' => count($this->migrator->getAvailableNotificationSounds()),
            'migrations_count' => count($this->migrator->status())
        ];
    }

    /**
     * Reset notification system (for development/testing)
     */
    public function resetNotificationSystem(): void
    {
        echo "🔄 Resetting notification system...\n";
        
        try {
            // Rollback migration
            $this->migrator->rollback();
            
            // Re-run migration
            $this->migrator->migrate();
            
            // Re-initialize for existing users
            $this->migrator->initializeForExistingUsers();
            
            echo "✅ Notification system reset completed!\n";
            
        } catch (Exception $e) {
            echo "❌ Reset failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}
