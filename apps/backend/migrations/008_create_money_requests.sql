-- Create money_requests table for QR code and direct money requests
CREATE TABLE IF NOT EXISTS money_requests (
    id TEXT PRIMARY KEY,
    requester_id TEXT NOT NULL,
    target_user_id TEXT NULL, -- NULL for QR code requests, set for direct requests
    amount REAL NOT NULL,
    reason TEXT,
    request_type TEXT NOT NULL CHECK (request_type IN ('qr_code', 'direct')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled', 'expired')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    completed_at DATETIME NULL,

    FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for money_requests
CREATE INDEX IF NOT EXISTS idx_money_requests_requester_id ON money_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_money_requests_target_user_id ON money_requests(target_user_id);
CREATE INDEX IF NOT EXISTS idx_money_requests_status ON money_requests(status);
CREATE INDEX IF NOT EXISTS idx_money_requests_created_at ON money_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_money_requests_expires_at ON money_requests(expires_at);

-- Create notifications table for in-app notifications
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    type TEXT NOT NULL, -- 'money_request', 'payment_received', etc.
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data TEXT NULL, -- Additional data for the notification (JSON as TEXT)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_read INTEGER DEFAULT 0, -- SQLite uses INTEGER for boolean (0=false, 1=true)
    read_at DATETIME NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for notifications
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Add recipient_id to transactions table if it doesn't exist
-- This helps track who received money for recent contacts feature
-- Note: SQLite doesn't support ADD COLUMN IF NOT EXISTS, so we'll use a different approach

-- Check if recipient_id column exists, if not add it
PRAGMA table_info(transactions);

-- Add recipient_id column (this will fail silently if column already exists)
-- In SQLite, we need to handle this differently in the application code
-- For now, we'll assume the column doesn't exist and add it
-- ALTER TABLE transactions ADD COLUMN recipient_id TEXT;

-- Create index for recipient_id
-- CREATE INDEX IF NOT EXISTS idx_transactions_recipient_id ON transactions(recipient_id);
