-- Migration: Create E2EE (End-to-End Encryption) Tables
-- Description: Creates tables for military-grade Signal Protocol E2EE implementation
-- Version: 009
-- Date: 2025-07-09

-- Table for storing user key bundles (identity keys, pre-keys, etc.)
CREATE TABLE IF NOT EXISTS e2ee_key_bundles (
    user_id VARCHAR(36) PRIMARY KEY,
    identity_key TEXT NOT NULL COMMENT 'Base64-encoded Curve25519 public identity key',
    signed_pre_key TEXT NOT NULL COMMENT 'Base64-encoded signed pre-key',
    pre_key_signature TEXT NOT NULL COMMENT 'Base64-encoded signature of signed pre-key',
    one_time_pre_keys JSON NOT NULL COMMENT 'Array of base64-encoded one-time pre-keys',
    registration_id BIGINT NOT NULL COMMENT 'Unique registration ID for this key bundle',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_e2ee_key_bundles_user_id (user_id),
    INDEX idx_e2ee_key_bundles_registration_id (registration_id),
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Stores cryptographic key bundles for Signal Protocol E2EE';

-- Table for storing cryptographic sessions between users
CREATE TABLE IF NOT EXISTS e2ee_sessions (
    id VARCHAR(128) PRIMARY KEY COMMENT 'Unique session identifier',
    user_a_id VARCHAR(36) NOT NULL COMMENT 'First user in the session',
    user_b_id VARCHAR(36) NOT NULL COMMENT 'Second user in the session',
    shared_secret TEXT NOT NULL COMMENT 'Base64-encoded shared secret for key derivation',
    sending_chain TEXT NOT NULL COMMENT 'Base64-encoded sending chain key',
    receiving_chain TEXT NOT NULL COMMENT 'Base64-encoded receiving chain key',
    message_number BIGINT DEFAULT 0 COMMENT 'Current message number for perfect forward secrecy',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_e2ee_sessions_users (user_a_id, user_b_id),
    INDEX idx_e2ee_sessions_last_used (last_used),
    INDEX idx_e2ee_sessions_created_at (created_at),
    
    -- Unique constraint to prevent duplicate sessions
    UNIQUE KEY unique_session_users (LEAST(user_a_id, user_b_id), GREATEST(user_a_id, user_b_id)),
    
    -- Foreign key constraints
    FOREIGN KEY (user_a_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (user_b_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Stores cryptographic sessions for Signal Protocol E2EE';

-- Table for storing encrypted messages with military-grade security
CREATE TABLE IF NOT EXISTS e2ee_messages (
    id VARCHAR(128) PRIMARY KEY COMMENT 'Unique message identifier',
    session_id VARCHAR(128) NOT NULL COMMENT 'Session this message belongs to',
    sender_id VARCHAR(36) NOT NULL COMMENT 'User who sent the message',
    recipient_id VARCHAR(36) NOT NULL COMMENT 'User who should receive the message',
    room_id VARCHAR(36) NOT NULL COMMENT 'Chat room this message belongs to',
    ciphertext LONGTEXT NOT NULL COMMENT 'Base64-encoded encrypted message content',
    auth_tag TEXT NOT NULL COMMENT 'Base64-encoded authentication tag',
    iv TEXT NOT NULL COMMENT 'Base64-encoded initialization vector',
    message_number BIGINT NOT NULL COMMENT 'Message number in the session',
    security_level VARCHAR(32) DEFAULT 'MILITARY_GRADE' COMMENT 'Security level of encryption',
    integrity_hash VARCHAR(64) NOT NULL COMMENT 'SHA-256 hash for integrity verification',
    metadata JSON COMMENT 'Protected metadata (padded for traffic analysis resistance)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL COMMENT 'When the message was delivered',
    read_at TIMESTAMP NULL COMMENT 'When the message was read',
    
    -- Indexes for performance
    INDEX idx_e2ee_messages_session (session_id),
    INDEX idx_e2ee_messages_sender (sender_id),
    INDEX idx_e2ee_messages_recipient (recipient_id),
    INDEX idx_e2ee_messages_room (room_id),
    INDEX idx_e2ee_messages_created_at (created_at),
    INDEX idx_e2ee_messages_message_number (message_number),
    INDEX idx_e2ee_messages_security_level (security_level),
    
    -- Foreign key constraints
    FOREIGN KEY (session_id) REFERENCES e2ee_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Stores encrypted messages with military-grade security';

-- Table for tracking security events and potential attacks
CREATE TABLE IF NOT EXISTS e2ee_security_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL COMMENT 'User associated with the security event',
    event_type VARCHAR(64) NOT NULL COMMENT 'Type of security event',
    event_details JSON COMMENT 'Detailed information about the event',
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
    ip_address VARCHAR(45) COMMENT 'IP address associated with the event',
    user_agent TEXT COMMENT 'User agent string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance and security monitoring
    INDEX idx_e2ee_security_events_user (user_id),
    INDEX idx_e2ee_security_events_type (event_type),
    INDEX idx_e2ee_security_events_severity (severity),
    INDEX idx_e2ee_security_events_created_at (created_at),
    INDEX idx_e2ee_security_events_ip (ip_address),
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Tracks security events for E2EE system monitoring';

-- Table for storing skipped message keys (for out-of-order message handling)
CREATE TABLE IF NOT EXISTS e2ee_skipped_keys (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128) NOT NULL COMMENT 'Session this skipped key belongs to',
    message_number BIGINT NOT NULL COMMENT 'Message number for this key',
    message_key TEXT NOT NULL COMMENT 'Base64-encoded message key',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL COMMENT 'When this key expires',
    
    -- Indexes for performance
    INDEX idx_e2ee_skipped_keys_session (session_id),
    INDEX idx_e2ee_skipped_keys_message_number (message_number),
    INDEX idx_e2ee_skipped_keys_expires_at (expires_at),
    
    -- Unique constraint
    UNIQUE KEY unique_session_message_number (session_id, message_number),
    
    -- Foreign key constraint
    FOREIGN KEY (session_id) REFERENCES e2ee_sessions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Stores skipped message keys for out-of-order message handling';

-- Add E2EE-related columns to existing chat_messages table
ALTER TABLE chat_messages 
ADD COLUMN IF NOT EXISTS is_encrypted BOOLEAN DEFAULT FALSE COMMENT 'Whether this message is E2EE encrypted',
ADD COLUMN IF NOT EXISTS e2ee_message_id VARCHAR(128) NULL COMMENT 'Reference to e2ee_messages table',
ADD COLUMN IF NOT EXISTS security_level VARCHAR(32) NULL COMMENT 'Security level if encrypted',
ADD INDEX idx_chat_messages_encrypted (is_encrypted),
ADD INDEX idx_chat_messages_e2ee_id (e2ee_message_id),
ADD INDEX idx_chat_messages_security_level (security_level);

-- Add foreign key constraint for E2EE message reference
ALTER TABLE chat_messages 
ADD CONSTRAINT fk_chat_messages_e2ee_message 
FOREIGN KEY (e2ee_message_id) REFERENCES e2ee_messages(id) ON DELETE SET NULL;

-- Create stored procedure for cleaning up expired sessions and keys
DELIMITER //
CREATE PROCEDURE CleanupExpiredE2EEData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Clean up expired skipped keys
    DELETE FROM e2ee_skipped_keys 
    WHERE expires_at < NOW();
    
    -- Clean up old sessions (older than 30 days and not used in 7 days)
    DELETE FROM e2ee_sessions 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND last_used < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Clean up old security events (older than 90 days)
    DELETE FROM e2ee_security_events 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    COMMIT;
END //
DELIMITER ;

-- Create event to automatically run cleanup procedure daily
CREATE EVENT IF NOT EXISTS e2ee_daily_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL CleanupExpiredE2EEData();

-- Insert initial security event types for monitoring
INSERT IGNORE INTO e2ee_security_events (user_id, event_type, event_details, severity) VALUES
('system', 'E2EE_SYSTEM_INITIALIZED', '{"message": "Military-grade E2EE system initialized", "version": "1.0"}', 'LOW');

-- Create view for session statistics
CREATE OR REPLACE VIEW e2ee_session_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as sessions_created,
    COUNT(DISTINCT user_a_id) + COUNT(DISTINCT user_b_id) as unique_users,
    AVG(message_number) as avg_messages_per_session
FROM e2ee_sessions 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Create view for security monitoring
CREATE OR REPLACE VIEW e2ee_security_summary AS
SELECT 
    event_type,
    severity,
    COUNT(*) as event_count,
    MAX(created_at) as last_occurrence
FROM e2ee_security_events 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY event_type, severity
ORDER BY severity DESC, event_count DESC;
