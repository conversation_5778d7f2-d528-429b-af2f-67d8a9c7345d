-- Seed basic chat data for testing
-- Run this to create some basic chat rooms and messages

-- 1. Create some basic chat rooms
INSERT OR IGNORE INTO chat_rooms (id, name, type, created_by, is_active, created_at, updated_at) VALUES
('general-chat', 'General Discussion', 'group', 'samuel-user-id', true, datetime('now'), datetime('now')),
('support-chat', 'Customer Support', 'support', 'samuel-user-id', true, datetime('now'), datetime('now'));

-- 2. Add user to these chat rooms
INSERT OR IGNORE INTO chat_room_members (id, room_id, user_id, role, joined_at, is_active) VALUES
('member-general-samuel', 'general-chat', 'samuel-user-id', 'member', datetime('now'), true),
('member-support-samuel', 'support-chat', 'samuel-user-id', 'member', datetime('now'), true);

-- 3. Add some sample messages
INSERT OR IGNORE INTO chat_messages (id, room_id, sender_id, message, content, type, created_at, updated_at) VALUES
('msg-general-1', 'general-chat', 'samuel-user-id', 'Welcome to VaultKe General Discussion!', 'Welcome to VaultKe General Discussion!', 'text', datetime('now', '-1 hour'), datetime('now', '-1 hour')),
('msg-general-2', 'general-chat', 'samuel-user-id', 'Feel free to chat here about anything.', 'Feel free to chat here about anything.', 'text', datetime('now', '-30 minutes'), datetime('now', '-30 minutes')),
('msg-support-1', 'support-chat', 'samuel-user-id', 'Hello! How can we help you today?', 'Hello! How can we help you today?', 'text', datetime('now', '-2 hours'), datetime('now', '-2 hours')),
('msg-support-2', 'support-chat', 'samuel-user-id', 'Our support team is available 24/7.', 'Our support team is available 24/7.', 'text', datetime('now', '-1 hour'), datetime('now', '-1 hour'));

-- 4. Update last message info in chat rooms
UPDATE chat_rooms SET 
    last_message = 'Feel free to chat here about anything.',
    last_message_at = datetime('now', '-30 minutes')
WHERE id = 'general-chat';

UPDATE chat_rooms SET 
    last_message = 'Our support team is available 24/7.',
    last_message_at = datetime('now', '-1 hour')
WHERE id = 'support-chat';

-- 5. Verify the data
SELECT 'Chat Rooms Created' as status, COUNT(*) as count FROM chat_rooms WHERE is_active = true;
SELECT 'Chat Members Created' as status, COUNT(*) as count FROM chat_room_members WHERE is_active = true;
SELECT 'Chat Messages Created' as status, COUNT(*) as count FROM chat_messages WHERE is_deleted = false;
