-- Debug script to check chat database state
-- Run this to see what's causing the timeout

-- 1. Check total number of chat rooms
SELECT 'Total Chat Rooms' as metric, COUNT(*) as count FROM chat_rooms WHERE is_active = true;

-- 2. Check total number of chat room members
SELECT 'Total Chat Room Members' as metric, COUNT(*) as count FROM chat_room_members WHERE is_active = true;

-- 3. Check total number of chat messages
SELECT 'Total Chat Messages' as metric, COUNT(*) as count FROM chat_messages WHERE is_deleted = false;

-- 4. Check chat rooms per user (this might be the issue)
SELECT 
    'Chat Rooms per User' as metric,
    user_id,
    COUNT(*) as room_count
FROM chat_room_members 
WHERE is_active = true 
GROUP BY user_id 
ORDER BY room_count DESC 
LIMIT 10;

-- 5. Check participants per room (this might be the issue)
SELECT 
    'Participants per Room' as metric,
    room_id,
    COUNT(*) as participant_count
FROM chat_room_members 
WHERE is_active = true 
GROUP BY room_id 
ORDER BY participant_count DESC 
LIMIT 10;

-- 6. Check messages per room
SELECT 
    'Messages per Room' as metric,
    room_id,
    COUNT(*) as message_count
FROM chat_messages 
WHERE is_deleted = false 
GROUP BY room_id 
ORDER BY message_count DESC 
LIMIT 10;

-- 7. Check for any specific user's chat rooms (replace 'user-id' with actual user ID)
SELECT 
    r.id,
    r.name,
    r.type,
    r.created_at,
    (SELECT COUNT(*) FROM chat_room_members WHERE room_id = r.id AND is_active = true) as member_count,
    (SELECT COUNT(*) FROM chat_messages WHERE room_id = r.id AND is_deleted = false) as message_count
FROM chat_rooms r
INNER JOIN chat_room_members m ON r.id = m.room_id
WHERE m.user_id = 'samuel-user-id' -- Replace with actual user ID
AND m.is_active = true 
AND r.is_active = true
ORDER BY r.last_message_at DESC, r.created_at DESC;

-- 8. Check for any problematic queries
SELECT 
    'Rooms with many participants' as issue,
    room_id,
    COUNT(*) as participant_count
FROM chat_room_members 
WHERE is_active = true 
GROUP BY room_id 
HAVING COUNT(*) > 100
ORDER BY participant_count DESC;

-- 9. Check for rooms with many messages
SELECT 
    'Rooms with many messages' as issue,
    room_id,
    COUNT(*) as message_count
FROM chat_messages 
WHERE is_deleted = false 
GROUP BY room_id 
HAVING COUNT(*) > 1000
ORDER BY message_count DESC;

-- 10. Sample chat room data
SELECT 
    'Sample Chat Rooms' as info,
    id,
    name,
    type,
    created_at,
    last_message_at
FROM chat_rooms 
WHERE is_active = true 
ORDER BY created_at DESC 
LIMIT 5;
