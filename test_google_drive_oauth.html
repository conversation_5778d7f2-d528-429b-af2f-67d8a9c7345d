<!DOCTYPE html>
<html>
<head>
    <title>Test Google Drive OAuth for VaultKe</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .url { background: #e3f2fd; padding: 10px; border-radius: 3px; word-break: break-all; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #45a049; }
        .tokens { background: #fff3e0; padding: 15px; border-radius: 5px; margin: 10px 0; }
        textarea { width: 100%; height: 100px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🔐 VaultKe Secure Google Drive OAuth Test</h1>

    <div class="step" style="background: #e8f5e9; border: 1px solid #4caf50;">
        <h3>🛡️ Security Features Enabled</h3>
        <ul>
            <li>✅ CSRF Protection (Secure State Parameter)</li>
            <li>✅ PKCE Implementation (Code Challenge/Verifier)</li>
            <li>✅ Nonce Protection (Replay Attack Prevention)</li>
            <li>✅ JWT Secure Token (Additional Validation)</li>
            <li>✅ Time-based Expiration (10-minute window)</li>
        </ul>
    </div>

    <div class="step">
        <h3>Step 1: Start Secure OAuth Flow</h3>
        <p>Click the button below to start secure Google Drive authentication:</p>
        <button onclick="startOAuth()">Start Secure Google Drive OAuth</button>
        <div id="authUrl" class="url" style="display:none;"></div>
    </div>

    <div class="step">
        <h3>Step 2: Complete Authentication</h3>
        <p>After clicking the OAuth button above, you'll be redirected to Google. Complete the authentication and you'll get tokens.</p>
    </div>

    <div class="step">
        <h3>Step 3: Test Backup</h3>
        <p>Once you have real tokens, test the backup:</p>
        <button onclick="testBackup()">Test Google Drive Backup</button>
        <div id="backupResult"></div>
    </div>

    <div class="tokens">
        <h3>📋 Manual Token Entry (if needed)</h3>
        <p>If you got tokens manually, paste them here:</p>
        <label>Access Token:</label>
        <textarea id="accessToken" placeholder="Paste access token here"></textarea>
        <label>Refresh Token:</label>
        <textarea id="refreshToken" placeholder="Paste refresh token here"></textarea>
        <label>Expires In (seconds):</label>
        <input type="number" id="expiresIn" value="3600" style="width: 100px;">
        <br><br>
        <button onclick="storeManualTokens()">Store These Tokens</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        const CLIENT_ID = '700521271518-apj801tf38k25daiisnqt70f8m7j2o43.apps.googleusercontent.com';

        async function startOAuth() {
            try {
                // Use a test user ID for demonstration
                const testUserID = 'c9fca28d-595f-4cf5-b3d2-b02472d21621';

                const response = await fetch(`${API_BASE}/auth/google/drive?user_id=${testUserID}`);
                const result = await response.json();

                if (result.success && result.auth_url) {
                    document.getElementById('authUrl').innerHTML = `
                        <div style="background: #e8f5e9; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h3>🔒 Secure OAuth URL Generated</h3>
                            <p><strong>Auth URL:</strong></p>
                            <a href="${result.auth_url}" target="_blank" style="word-break: break-all;">${result.auth_url}</a>
                            <p><strong>Secure Token:</strong> ${result.secure_token ? '✅ Generated' : '❌ Missing'}</p>
                            <p><strong>Expires In:</strong> ${result.expires_in} seconds</p>
                            <p><em>Click the link above to complete secure authentication</em></p>
                        </div>
                    `;
                    document.getElementById('authUrl').style.display = 'block';

                    // Store secure token for callback validation
                    if (result.secure_token) {
                        localStorage.setItem('oauth_secure_token', result.secure_token);
                    }

                    // Also open in new window
                    window.open(result.auth_url, '_blank');
                } else {
                    document.getElementById('authUrl').innerHTML = `
                        <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h3>❌ OAuth Failed</h3>
                            <p><strong>Error:</strong> ${result.error || 'Unknown error'}</p>
                            <p><strong>Message:</strong> ${result.message || 'No additional information'}</p>
                        </div>
                    `;
                    document.getElementById('authUrl').style.display = 'block';
                }
            } catch (error) {
                document.getElementById('authUrl').innerHTML = `
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                document.getElementById('authUrl').style.display = 'block';
            }
        }

        async function storeManualTokens() {
            const accessToken = document.getElementById('accessToken').value;
            const refreshToken = document.getElementById('refreshToken').value;
            const expiresIn = parseInt(document.getElementById('expiresIn').value);

            if (!accessToken || !refreshToken) {
                alert('Please enter both access token and refresh token');
                return;
            }

            try {
                // You'll need to add your auth token here
                const authToken = prompt('Enter your VaultKe auth token (from mobile app):');
                if (!authToken) {
                    alert('Auth token is required');
                    return;
                }

                const response = await fetch(`${API_BASE}/users/google-drive/store-tokens`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        access_token: accessToken,
                        refresh_token: refreshToken,
                        expires_in: expiresIn,
                        token_type: 'Bearer'
                    })
                });

                const result = await response.json();
                if (result.success) {
                    alert('✅ Tokens stored successfully! Now try the backup.');
                } else {
                    alert('❌ Failed to store tokens: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                alert('Error storing tokens: ' + error.message);
            }
        }

        async function testBackup() {
            try {
                const authToken = prompt('Enter your VaultKe auth token (from mobile app):');
                if (!authToken) {
                    alert('Auth token is required');
                    return;
                }

                document.getElementById('backupResult').innerHTML = '⏳ Creating backup...';

                const response = await fetch(`${API_BASE}/users/google-drive/backup`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const result = await response.json();
                if (result.success) {
                    document.getElementById('backupResult').innerHTML = `
                        <div style="color: green;">
                            ✅ Backup created successfully!<br>
                            <strong>File:</strong> ${result.backup_id || 'N/A'}<br>
                            <strong>Size:</strong> ${result.file_size || 'N/A'} bytes<br>
                            <strong>Time:</strong> ${result.timestamp || 'N/A'}
                        </div>
                    `;
                } else {
                    document.getElementById('backupResult').innerHTML = `
                        <div style="color: red;">
                            ❌ Backup failed: ${result.error || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('backupResult').innerHTML = `
                    <div style="color: red;">
                        ❌ Error: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
