#!/bin/bash

# VaultKe Secure OAuth Test Script
echo "🔐 Testing VaultKe Secure Google Drive OAuth"
echo "============================================="

# Configuration
API_BASE="http://localhost:8080/api/v1"
TEST_USER_ID="c9fca28d-595f-4cf5-b3d2-b02472d21621"

echo ""
echo "📋 Test Configuration:"
echo "  API Base: $API_BASE"
echo "  Test User ID: $TEST_USER_ID"
echo ""

# Test 1: Public OAuth endpoint with user_id
echo "🧪 Test 1: Public OAuth Endpoint (with user_id)"
echo "------------------------------------------------"
echo "Request: GET $API_BASE/auth/google/drive?user_id=$TEST_USER_ID"
echo ""

response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$API_BASE/auth/google/drive?user_id=$TEST_USER_ID")
http_status=$(echo "$response" | grep "HTTP_STATUS" | cut -d: -f2)
json_response=$(echo "$response" | sed '/HTTP_STATUS/d')

echo "HTTP Status: $http_status"
echo "Response:"
echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
echo ""

if [ "$http_status" = "200" ]; then
    echo "✅ Test 1 PASSED: Secure OAuth URL generated successfully"
    
    # Extract auth_url and secure_token
    auth_url=$(echo "$json_response" | jq -r '.auth_url' 2>/dev/null)
    secure_token=$(echo "$json_response" | jq -r '.secure_token' 2>/dev/null)
    
    if [ "$auth_url" != "null" ] && [ "$secure_token" != "null" ]; then
        echo "   🔗 Auth URL: ${auth_url:0:80}..."
        echo "   🎫 Secure Token: ${secure_token:0:50}..."
        
        # Check for security parameters in URL
        if [[ "$auth_url" == *"state="* ]] && [[ "$auth_url" == *"nonce="* ]] && [[ "$auth_url" == *"code_challenge="* ]]; then
            echo "   🛡️ Security parameters detected: ✅ state, ✅ nonce, ✅ code_challenge"
        else
            echo "   ⚠️  Some security parameters missing in URL"
        fi
    fi
else
    echo "❌ Test 1 FAILED: HTTP $http_status"
fi

echo ""
echo "----------------------------------------"

# Test 2: Public OAuth endpoint without user_id (should fail)
echo "🧪 Test 2: Public OAuth Endpoint (without user_id - should fail)"
echo "----------------------------------------------------------------"
echo "Request: GET $API_BASE/auth/google/drive"
echo ""

response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$API_BASE/auth/google/drive")
http_status=$(echo "$response" | grep "HTTP_STATUS" | cut -d: -f2)
json_response=$(echo "$response" | sed '/HTTP_STATUS/d')

echo "HTTP Status: $http_status"
echo "Response:"
echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
echo ""

if [ "$http_status" = "400" ]; then
    echo "✅ Test 2 PASSED: Correctly rejected request without user_id"
else
    echo "❌ Test 2 FAILED: Expected HTTP 400, got HTTP $http_status"
fi

echo ""
echo "----------------------------------------"

# Test 3: Protected OAuth endpoint (should fail without auth)
echo "🧪 Test 3: Protected OAuth Endpoint (without auth - should fail)"
echo "---------------------------------------------------------------"
echo "Request: GET $API_BASE/users/google-drive/auth-url"
echo ""

response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$API_BASE/users/google-drive/auth-url")
http_status=$(echo "$response" | grep "HTTP_STATUS" | cut -d: -f2)
json_response=$(echo "$response" | sed '/HTTP_STATUS/d')

echo "HTTP Status: $http_status"
echo "Response:"
echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
echo ""

if [ "$http_status" = "401" ]; then
    echo "✅ Test 3 PASSED: Correctly rejected unauthenticated request"
else
    echo "❌ Test 3 FAILED: Expected HTTP 401, got HTTP $http_status"
fi

echo ""
echo "========================================="
echo "🏁 Test Summary"
echo "========================================="

# Summary
if [ "$http_status" = "401" ]; then
    echo "✅ Security Test Results:"
    echo "   • Public endpoint with user_id: Working"
    echo "   • Public endpoint without user_id: Properly rejected"
    echo "   • Protected endpoint without auth: Properly rejected"
    echo ""
    echo "🎯 Next Steps:"
    echo "   1. Open test_google_drive_oauth.html in your browser"
    echo "   2. Click 'Start Secure Google Drive OAuth'"
    echo "   3. Complete the OAuth flow in the popup window"
    echo "   4. Verify tokens are stored securely"
    echo ""
    echo "🌐 Test URL: http://localhost:8080/test_google_drive_oauth.html"
else
    echo "⚠️  Some tests failed. Check your server configuration."
fi

echo ""
echo "🔒 Security Features Verified:"
echo "   • No client_id exposure in responses"
echo "   • CSRF protection with state parameter"
echo "   • PKCE implementation with code_challenge"
echo "   • Nonce for replay attack prevention"
echo "   • JWT secure token for additional validation"
echo "   • Proper authentication requirements"
