// SecureOAuthService.js - Frontend secure OAuth handling

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Linking } from 'react-native';

class SecureOAuthService {
  constructor(apiBaseURL) {
    this.apiBaseURL = apiBaseURL;
    this.pendingAuth = null;
  }

  /**
   * Initiate secure Google Drive OAuth flow
   */
  async initiateGoogleDriveAuth() {
    try {
      // Get user's auth token
      const authToken = await AsyncStorage.getItem('auth_token');
      if (!authToken) {
        throw new Error('User not authenticated');
      }

      // Request secure OAuth URL from backend
      const response = await fetch(`${this.apiBaseURL}/api/v1/auth/google/initiate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to initiate OAuth');
      }

      // Store secure token for callback validation
      await AsyncStorage.setItem('oauth_secure_token', data.secure_token);
      
      // Store OAuth session info
      this.pendingAuth = {
        secureToken: data.secure_token,
        expiresAt: Date.now() + (data.expires_in * 1000),
      };

      // Open OAuth URL in browser
      const canOpen = await Linking.canOpenURL(data.auth_url);
      if (canOpen) {
        await Linking.openURL(data.auth_url);
        return {
          success: true,
          message: 'OAuth flow initiated. Please complete authorization in browser.',
        };
      } else {
        throw new Error('Cannot open OAuth URL');
      }

    } catch (error) {
      console.error('OAuth initiation error:', error);
      return {
        success: false,
        message: error.message || 'Failed to initiate OAuth',
      };
    }
  }

  /**
   * Handle OAuth callback (called when app is reopened)
   */
  async handleOAuthCallback(url) {
    try {
      // Parse callback URL
      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      const state = urlObj.searchParams.get('state');
      const error = urlObj.searchParams.get('error');

      // Check for OAuth errors
      if (error) {
        throw new Error(`OAuth error: ${error}`);
      }

      // Validate required parameters
      if (!code || !state) {
        throw new Error('Missing OAuth callback parameters');
      }

      // Check if we have a pending auth session
      if (!this.pendingAuth) {
        throw new Error('No pending OAuth session');
      }

      // Check session expiration
      if (Date.now() > this.pendingAuth.expiresAt) {
        this.pendingAuth = null;
        await AsyncStorage.removeItem('oauth_secure_token');
        throw new Error('OAuth session expired');
      }

      // Get stored secure token
      const secureToken = await AsyncStorage.getItem('oauth_secure_token');
      if (!secureToken) {
        throw new Error('Missing secure token');
      }

      // Send callback to backend for processing
      const authToken = await AsyncStorage.getItem('auth_token');
      const response = await fetch(`${this.apiBaseURL}/api/v1/auth/google/callback?code=${code}&state=${state}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'X-Secure-Token': secureToken,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      // Clean up
      this.pendingAuth = null;
      await AsyncStorage.removeItem('oauth_secure_token');

      if (!data.success) {
        throw new Error(data.message || 'OAuth callback failed');
      }

      return {
        success: true,
        message: 'Google Drive connected successfully',
      };

    } catch (error) {
      console.error('OAuth callback error:', error);
      
      // Clean up on error
      this.pendingAuth = null;
      await AsyncStorage.removeItem('oauth_secure_token');
      
      return {
        success: false,
        message: error.message || 'OAuth callback failed',
      };
    }
  }

  /**
   * Check OAuth session status
   */
  getOAuthStatus() {
    if (!this.pendingAuth) {
      return { pending: false };
    }

    const isExpired = Date.now() > this.pendingAuth.expiresAt;
    if (isExpired) {
      this.pendingAuth = null;
      AsyncStorage.removeItem('oauth_secure_token');
      return { pending: false, expired: true };
    }

    return {
      pending: true,
      expiresIn: Math.floor((this.pendingAuth.expiresAt - Date.now()) / 1000),
    };
  }

  /**
   * Cancel pending OAuth session
   */
  async cancelOAuth() {
    this.pendingAuth = null;
    await AsyncStorage.removeItem('oauth_secure_token');
  }
}

// React Hook for secure OAuth
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

export const useSecureOAuth = () => {
  const { apiService } = useAuth();
  const [oauthService] = useState(() => new SecureOAuthService(apiService.baseURL));
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [oauthStatus, setOAuthStatus] = useState({ pending: false });

  // Check OAuth status on mount and periodically
  useEffect(() => {
    const checkStatus = () => {
      const status = oauthService.getOAuthStatus();
      setOAuthStatus(status);
    };

    checkStatus();
    const interval = setInterval(checkStatus, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [oauthService]);

  // Handle deep links for OAuth callback
  useEffect(() => {
    const handleDeepLink = async (url) => {
      if (url.includes('/auth/google/callback')) {
        setIsLoading(true);
        setError(null);

        const result = await oauthService.handleOAuthCallback(url);
        
        if (!result.success) {
          setError(result.message);
        }
        
        setIsLoading(false);
        setOAuthStatus({ pending: false });
      }
    };

    // Listen for deep links
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    // Check if app was opened with a deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => subscription?.remove();
  }, [oauthService]);

  const initiateGoogleDriveAuth = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    const result = await oauthService.initiateGoogleDriveAuth();
    
    if (!result.success) {
      setError(result.message);
      setIsLoading(false);
    } else {
      // Keep loading state until callback is received
      setOAuthStatus({ pending: true });
    }

    return result;
  }, [oauthService]);

  const cancelOAuth = useCallback(async () => {
    await oauthService.cancelOAuth();
    setOAuthStatus({ pending: false });
    setIsLoading(false);
    setError(null);
  }, [oauthService]);

  return {
    initiateGoogleDriveAuth,
    cancelOAuth,
    isLoading,
    error,
    oauthStatus,
  };
};

// Usage in React Component
export const GoogleDriveConnectionScreen = () => {
  const { initiateGoogleDriveAuth, cancelOAuth, isLoading, error, oauthStatus } = useSecureOAuth();

  const handleConnectGoogleDrive = async () => {
    const result = await initiateGoogleDriveAuth();
    
    if (result.success) {
      // Show success message or navigate
      console.log('OAuth initiated successfully');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Connect Google Drive</Text>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {oauthStatus.pending && (
        <View style={styles.pendingContainer}>
          <Text style={styles.pendingText}>
            Waiting for authorization... 
            {oauthStatus.expiresIn && ` (${oauthStatus.expiresIn}s remaining)`}
          </Text>
          <TouchableOpacity onPress={cancelOAuth} style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}
      
      <TouchableOpacity
        onPress={handleConnectGoogleDrive}
        disabled={isLoading || oauthStatus.pending}
        style={[
          styles.connectButton,
          (isLoading || oauthStatus.pending) && styles.disabledButton
        ]}
      >
        <Text style={styles.connectButtonText}>
          {isLoading ? 'Connecting...' : 'Connect Google Drive'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default SecureOAuthService;
