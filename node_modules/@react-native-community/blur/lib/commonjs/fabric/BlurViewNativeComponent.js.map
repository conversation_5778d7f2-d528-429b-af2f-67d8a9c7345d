{"version": 3, "names": ["codegenNativeComponent", "excludedPlatforms"], "sources": ["BlurViewNativeComponent.ts"], "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps, HostComponent, ColorValue } from 'react-native';\nimport type {\n  WithDefault,\n  Int32,\n} from 'react-native/Libraries/Types/CodegenTypes';\n\ninterface NativeProps extends ViewProps {\n  blurType?: WithDefault<\n    | 'dark'\n    | 'light'\n    | 'xlight'\n    | 'prominent'\n    | 'regular'\n    | 'extraDark'\n    | 'chromeMaterial'\n    | 'material'\n    | 'thickMaterial'\n    | 'thinMaterial'\n    | 'ultraThinMaterial'\n    | 'chromeMaterialDark'\n    | 'materialDark'\n    | 'thickMaterialDark'\n    | 'thinMaterialDark'\n    | 'ultraThinMaterialDark'\n    | 'chromeMaterialLight'\n    | 'materialLight'\n    | 'thickMaterialLight'\n    | 'thinMaterialLight'\n    | 'ultraThinMaterialLight',\n    'dark'\n  >;\n  blurAmount?: WithDefault<Int32, 10>;\n  reducedTransparencyFallbackColor?: ColorValue;\n}\n\nexport default codegenNativeComponent<NativeProps>('BlurView', {\n  excludedPlatforms: ['android'],\n}) as HostComponent<NativeProps>;\n"], "mappings": ";;;;;;;AAAA;;;;eAoCe,IAAAA,+BAAA,EAAoC,UAApC,EAAgD;EAC7DC,iBAAiB,EAAE,CAAC,SAAD;AAD0C,CAAhD,C"}