{"version": 3, "names": ["BlurView", "BlurViewUntyped", "Vibrancy<PERSON>iew", "VibrancyViewUntyped"], "sources": ["index.tsx"], "sourcesContent": ["import BlurViewUntyped from './components/BlurView';\nimport VibrancyViewUntyped from './components/VibrancyView';\nimport type { View } from 'react-native'\n\nimport type { BlurViewProps as BlurViewPropsIOS } from './components/BlurView.ios';\nimport type { BlurViewProps as BlurViewPropsAndroid } from './components/BlurView.android';\nimport type { VibrancyViewProps as VibrancyViewPropsIOS } from './components/VibrancyView.ios';\n\ntype BlurViewProps = BlurViewPropsIOS | BlurViewPropsAndroid;\ntype VibrancyViewProps = VibrancyViewPropsIOS;\n\nconst BlurView = BlurViewUntyped as React.ForwardRefExoticComponent<BlurViewProps & React.RefAttributes<View>>\nconst VibrancyView = VibrancyViewUntyped as React.ForwardRefExoticComponent<VibrancyViewProps & React.RefAttributes<View>>\n\nexport { BlurView, VibrancyView };\nexport type { BlurViewProps, VibrancyViewProps };"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAUA,MAAMA,QAAQ,GAAGC,iBAAjB;;AACA,MAAMC,YAAY,GAAGC,qBAArB"}