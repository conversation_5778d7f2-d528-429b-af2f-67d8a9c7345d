{"version": 3, "names": ["OVERLAY_COLORS", "light", "xlight", "dark", "BlurView", "forwardRef", "ref", "downsampleFactor", "blurRadius", "blurAmount", "blurType", "overlayColor", "enabled", "autoUpdate", "children", "style", "rest", "useEffect", "DeviceEventEmitter", "addListener", "message", "Error", "removeAllListeners", "getOverlayColor", "getBlurRadius", "equivalentBlurRadius", "Math", "round", "getDownsampleFactor", "StyleSheet", "compose", "styles", "transparent", "create", "backgroundColor"], "sources": ["BlurView.android.tsx"], "sourcesContent": ["import React, { forwardRef, useEffect } from 'react';\nimport {\n  View,\n  DeviceEventEmitter,\n  StyleSheet,\n  ViewProps,\n  ViewStyle,\n} from 'react-native';\nimport NativeBlurView from '../fabric/BlurViewNativeComponentAndroid';\n\nconst OVERLAY_COLORS = {\n  light: 'rgba(255, 255, 255, 0.2)',\n  xlight: 'rgba(255, 255, 255, 0.75)',\n  dark: 'rgba(16, 12, 12, 0.64)',\n};\n\nexport type BlurViewProps = ViewProps & {\n  blurAmount?: number;\n  blurType?: 'dark' | 'light' | 'xlight';\n  blurRadius?: number;\n  downsampleFactor?: number;\n  overlayColor?: string;\n  enabled?: boolean;\n  autoUpdate?: boolean;\n};\n\nconst BlurView = forwardRef<View, BlurViewProps>(\n  (\n    {\n      downsampleFactor,\n      blurRadius,\n      blurAmount = 10,\n      blurType = 'dark',\n      overlayColor,\n      enabled,\n      autoUpdate,\n      children,\n      style,\n      ...rest\n    },\n    ref\n  ) => {\n    useEffect(() => {\n      DeviceEventEmitter.addListener('ReactNativeBlurError', (message) => {\n        throw new Error(`[ReactNativeBlur]: ${message}`);\n      });\n\n      return () => {\n        DeviceEventEmitter.removeAllListeners('ReactNativeBlurError');\n      };\n    }, []);\n\n    const getOverlayColor = () => {\n      if (overlayColor != null) {\n        return overlayColor;\n      }\n\n      return OVERLAY_COLORS[blurType] || OVERLAY_COLORS.dark;\n    };\n\n    const getBlurRadius = () => {\n      if (blurRadius != null) {\n        if (blurRadius > 25) {\n          throw new Error(\n            `[ReactNativeBlur]: blurRadius cannot be greater than 25! (was: ${blurRadius})`\n          );\n        }\n        return blurRadius;\n      }\n\n      // iOS seems to use a slightly different blurring algorithm (or scale?).\n      // Android blurRadius + downsampleFactor is approximately 80% of blurAmount.\n      const equivalentBlurRadius = Math.round(blurAmount * 0.8);\n\n      if (equivalentBlurRadius > 25) {\n        return 25;\n      }\n      return equivalentBlurRadius;\n    };\n\n    const getDownsampleFactor = () => {\n      if (downsampleFactor != null) {\n        return downsampleFactor;\n      }\n\n      return blurRadius;\n    };\n\n    return (\n      <NativeBlurView\n        {...rest}\n        ref={ref}\n        blurRadius={getBlurRadius()}\n        downsampleFactor={getDownsampleFactor()}\n        overlayColor={getOverlayColor()}\n        blurAmount={blurAmount}\n        blurType={blurType}\n        enabled={enabled}\n        autoUpdate={autoUpdate}\n        pointerEvents=\"none\"\n        style={StyleSheet.compose(styles.transparent, style)}\n      >\n        {children}\n      </NativeBlurView>\n    );\n  }\n);\n\nconst styles = StyleSheet.create<{ transparent: ViewStyle }>({\n  transparent: { backgroundColor: 'transparent' },\n});\n\nexport default BlurView;\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAOA;;;;;;;;;;AAEA,MAAMA,cAAc,GAAG;EACrBC,KAAK,EAAE,0BADc;EAErBC,MAAM,EAAE,2BAFa;EAGrBC,IAAI,EAAE;AAHe,CAAvB;AAgBA,MAAMC,QAAQ,gBAAG,IAAAC,iBAAA,EACf,OAaEC,GAbF,KAcK;EAAA,IAbH;IACEC,gBADF;IAEEC,UAFF;IAGEC,UAAU,GAAG,EAHf;IAIEC,QAAQ,GAAG,MAJb;IAKEC,YALF;IAMEC,OANF;IAOEC,UAPF;IAQEC,QARF;IASEC,KATF;IAUE,GAAGC;EAVL,CAaG;EACH,IAAAC,gBAAA,EAAU,MAAM;IACdC,+BAAA,CAAmBC,WAAnB,CAA+B,sBAA/B,EAAwDC,OAAD,IAAa;MAClE,MAAM,IAAIC,KAAJ,CAAW,sBAAqBD,OAAQ,EAAxC,CAAN;IACD,CAFD;;IAIA,OAAO,MAAM;MACXF,+BAAA,CAAmBI,kBAAnB,CAAsC,sBAAtC;IACD,CAFD;EAGD,CARD,EAQG,EARH;;EAUA,MAAMC,eAAe,GAAG,MAAM;IAC5B,IAAIZ,YAAY,IAAI,IAApB,EAA0B;MACxB,OAAOA,YAAP;IACD;;IAED,OAAOX,cAAc,CAACU,QAAD,CAAd,IAA4BV,cAAc,CAACG,IAAlD;EACD,CAND;;EAQA,MAAMqB,aAAa,GAAG,MAAM;IAC1B,IAAIhB,UAAU,IAAI,IAAlB,EAAwB;MACtB,IAAIA,UAAU,GAAG,EAAjB,EAAqB;QACnB,MAAM,IAAIa,KAAJ,CACH,kEAAiEb,UAAW,GADzE,CAAN;MAGD;;MACD,OAAOA,UAAP;IACD,CARyB,CAU1B;IACA;;;IACA,MAAMiB,oBAAoB,GAAGC,IAAI,CAACC,KAAL,CAAWlB,UAAU,GAAG,GAAxB,CAA7B;;IAEA,IAAIgB,oBAAoB,GAAG,EAA3B,EAA+B;MAC7B,OAAO,EAAP;IACD;;IACD,OAAOA,oBAAP;EACD,CAlBD;;EAoBA,MAAMG,mBAAmB,GAAG,MAAM;IAChC,IAAIrB,gBAAgB,IAAI,IAAxB,EAA8B;MAC5B,OAAOA,gBAAP;IACD;;IAED,OAAOC,UAAP;EACD,CAND;;EAQA,oBACE,6BAAC,uCAAD,eACMQ,IADN;IAEE,GAAG,EAAEV,GAFP;IAGE,UAAU,EAAEkB,aAAa,EAH3B;IAIE,gBAAgB,EAAEI,mBAAmB,EAJvC;IAKE,YAAY,EAAEL,eAAe,EAL/B;IAME,UAAU,EAAEd,UANd;IAOE,QAAQ,EAAEC,QAPZ;IAQE,OAAO,EAAEE,OARX;IASE,UAAU,EAAEC,UATd;IAUE,aAAa,EAAC,MAVhB;IAWE,KAAK,EAAEgB,uBAAA,CAAWC,OAAX,CAAmBC,MAAM,CAACC,WAA1B,EAAuCjB,KAAvC;EAXT,IAaGD,QAbH,CADF;AAiBD,CA/Ec,CAAjB;;AAkFA,MAAMiB,MAAM,GAAGF,uBAAA,CAAWI,MAAX,CAA8C;EAC3DD,WAAW,EAAE;IAAEE,eAAe,EAAE;EAAnB;AAD8C,CAA9C,CAAf;;eAIe9B,Q"}