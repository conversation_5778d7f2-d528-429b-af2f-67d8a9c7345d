{"version": 3, "names": ["Vibrancy<PERSON>iew", "React", "Component", "render", "console", "error"], "sources": ["VibrancyView.android.tsx"], "sourcesContent": ["import React from 'react';\n\nclass VibrancyView extends React.Component {\n  render() {\n    console.error('VibrancyView is not implemented on Android');\n    return null;\n  }\n}\n\nexport default VibrancyView;\n"], "mappings": ";;;;;;;AAAA;;;;AAEA,MAAMA,YAAN,SAA2BC,cAAA,CAAMC,SAAjC,CAA2C;EACzCC,MAAM,GAAG;IACPC,OAAO,CAACC,KAAR,CAAc,4CAAd;IACA,OAAO,IAAP;EACD;;AAJwC;;eAO5BL,Y"}