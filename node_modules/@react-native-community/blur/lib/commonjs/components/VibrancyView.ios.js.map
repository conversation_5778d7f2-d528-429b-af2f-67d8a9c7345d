{"version": 3, "names": ["Vibrancy<PERSON>iew", "forwardRef", "ref", "style", "rest", "StyleSheet", "compose", "styles", "transparent", "create", "backgroundColor"], "sources": ["VibrancyView.ios.tsx"], "sourcesContent": ["import React, { forwardRef } from 'react';\nimport { StyleSheet, ViewProps, ViewStyle } from 'react-native';\nimport NativeVibrancyView from '../fabric/VibrancyViewNativeComponent';\nimport type { BlurViewProps } from './BlurView.ios';\n\nexport type VibrancyViewProps = ViewProps & {\n  blurType?: BlurViewProps['blurType'];\n  blurAmount: number;\n  reducedTransparencyFallbackColor?: string;\n};\n\nconst VibrancyView = forwardRef<any, VibrancyViewProps>(\n  ({ style, ...rest }, ref) => (\n    <NativeVibrancyView\n      {...rest}\n      ref={ref}\n      style={StyleSheet.compose(styles.transparent, style)}\n    />\n  )\n);\n\nconst styles = StyleSheet.create<{ transparent: ViewStyle }>({\n  transparent: { backgroundColor: 'transparent' },\n});\n\nexport default VibrancyView;\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;;;;;AASA,MAAMA,YAAY,gBAAG,IAAAC,iBAAA,EACnB,OAAqBC,GAArB;EAAA,IAAC;IAAEC,KAAF;IAAS,GAAGC;EAAZ,CAAD;EAAA,oBACE,6BAAC,oCAAD,eACMA,IADN;IAEE,GAAG,EAAEF,GAFP;IAGE,KAAK,EAAEG,uBAAA,CAAWC,OAAX,CAAmBC,MAAM,CAACC,WAA1B,EAAuCL,KAAvC;EAHT,GADF;AAAA,CADmB,CAArB;;AAUA,MAAMI,MAAM,GAAGF,uBAAA,CAAWI,MAAX,CAA8C;EAC3DD,WAAW,EAAE;IAAEE,eAAe,EAAE;EAAnB;AAD8C,CAA9C,CAAf;;eAIeV,Y"}