"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _react = _interopRequireDefault(require("react"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class VibrancyView extends _react.default.Component {
  render() {
    console.error('VibrancyView is not implemented on Android');
    return null;
  }

}

var _default = VibrancyView;
exports.default = _default;
//# sourceMappingURL=VibrancyView.android.js.map