{"version": 3, "names": ["BlurView", "forwardRef", "ref", "blurType", "blurAmount", "style", "rest", "StyleSheet", "compose", "styles", "transparent", "create", "backgroundColor"], "sources": ["BlurView.ios.tsx"], "sourcesContent": ["import React, { forwardRef } from 'react';\nimport { StyleSheet, ViewProps, ViewStyle, View } from 'react-native';\nimport NativeBlurView from '../fabric/BlurViewNativeComponent';\n\ntype BlurType =\n  | 'dark'\n  | 'light'\n  | 'xlight'\n  | 'prominent'\n  | 'regular'\n  | 'extraDark'\n  | 'chromeMaterial'\n  | 'material'\n  | 'thickMaterial'\n  | 'thinMaterial'\n  | 'ultraThinMaterial'\n  | 'chromeMaterialDark'\n  | 'materialDark'\n  | 'thickMaterialDark'\n  | 'thinMaterialDark'\n  | 'ultraThinMaterialDark'\n  | 'chromeMaterialLight'\n  | 'materialLight'\n  | 'thickMaterialLight'\n  | 'thinMaterialLight'\n  | 'ultraThinMaterialLight';\n\nexport type BlurViewProps = ViewProps & {\n  blurType?: BlurType;\n  blurAmount?: number;\n  reducedTransparencyFallbackColor?: string;\n};\n\nconst BlurView = forwardRef<View, BlurViewProps>(\n  ({ blurType = 'dark', blurAmount = 10, style, ...rest }, ref) => (\n    <NativeBlurView\n      ref={ref}\n      style={StyleSheet.compose(styles.transparent, style)}\n      blurType={blurType}\n      blurAmount={blurAmount}\n      {...rest}\n    />\n  )\n);\n\nconst styles = StyleSheet.create<{ transparent: ViewStyle }>({\n  transparent: { backgroundColor: 'transparent' },\n});\n\nexport default BlurView;\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;;;;;AA+BA,MAAMA,QAAQ,gBAAG,IAAAC,iBAAA,EACf,OAAyDC,GAAzD;EAAA,IAAC;IAAEC,QAAQ,GAAG,MAAb;IAAqBC,UAAU,GAAG,EAAlC;IAAsCC,KAAtC;IAA6C,GAAGC;EAAhD,CAAD;EAAA,oBACE,6BAAC,gCAAD;IACE,GAAG,EAAEJ,GADP;IAEE,KAAK,EAAEK,uBAAA,CAAWC,OAAX,CAAmBC,MAAM,CAACC,WAA1B,EAAuCL,KAAvC,CAFT;IAGE,QAAQ,EAAEF,QAHZ;IAIE,UAAU,EAAEC;EAJd,GAKME,IALN,EADF;AAAA,CADe,CAAjB;;AAYA,MAAMG,MAAM,GAAGF,uBAAA,CAAWI,MAAX,CAA8C;EAC3DD,WAAW,EAAE;IAAEE,eAAe,EAAE;EAAnB;AAD8C,CAA9C,CAAf;;eAIeZ,Q"}