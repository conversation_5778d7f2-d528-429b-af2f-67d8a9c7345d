{"version": 3, "names": ["codegenNativeComponent", "excludedPlatforms"], "sources": ["BlurViewNativeComponentAndroid.ts"], "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewP<PERSON>, HostComponent, ColorValue } from 'react-native';\nimport type {\n  WithDefault,\n  Int32,\n} from 'react-native/Libraries/Types/CodegenTypes';\n\ninterface NativeProps extends ViewProps {\n  blurAmount?: WithDefault<Int32, 10>;\n  blurType?: WithDefault<'dark' | 'light' | 'xlight', 'dark'>;\n  blurRadius?: Int32;\n  downsampleFactor?: Int32;\n  overlayColor?: ColorValue;\n  enabled?: boolean;\n  autoUpdate?: boolean;\n}\n\nexport default codegenNativeComponent<NativeProps>('AndroidBlurView', {\n  excludedPlatforms: ['iOS'],\n}) as HostComponent<NativeProps>;\n"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,yDAAnC;AAiBA,eAAeA,sBAAsB,CAAc,iBAAd,EAAiC;EACpEC,iBAAiB,EAAE,CAAC,KAAD;AADiD,CAAjC,CAArC"}