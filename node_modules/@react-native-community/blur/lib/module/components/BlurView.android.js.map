{"version": 3, "names": ["React", "forwardRef", "useEffect", "DeviceEventEmitter", "StyleSheet", "NativeBlurView", "OVERLAY_COLORS", "light", "xlight", "dark", "BlurView", "ref", "downsampleFactor", "blurRadius", "blurAmount", "blurType", "overlayColor", "enabled", "autoUpdate", "children", "style", "rest", "addListener", "message", "Error", "removeAllListeners", "getOverlayColor", "getBlurRadius", "equivalentBlurRadius", "Math", "round", "getDownsampleFactor", "compose", "styles", "transparent", "create", "backgroundColor"], "sources": ["BlurView.android.tsx"], "sourcesContent": ["import React, { forwardRef, useEffect } from 'react';\nimport {\n  View,\n  DeviceEventEmitter,\n  StyleSheet,\n  ViewProps,\n  ViewStyle,\n} from 'react-native';\nimport NativeBlurView from '../fabric/BlurViewNativeComponentAndroid';\n\nconst OVERLAY_COLORS = {\n  light: 'rgba(255, 255, 255, 0.2)',\n  xlight: 'rgba(255, 255, 255, 0.75)',\n  dark: 'rgba(16, 12, 12, 0.64)',\n};\n\nexport type BlurViewProps = ViewProps & {\n  blurAmount?: number;\n  blurType?: 'dark' | 'light' | 'xlight';\n  blurRadius?: number;\n  downsampleFactor?: number;\n  overlayColor?: string;\n  enabled?: boolean;\n  autoUpdate?: boolean;\n};\n\nconst BlurView = forwardRef<View, BlurViewProps>(\n  (\n    {\n      downsampleFactor,\n      blurRadius,\n      blurAmount = 10,\n      blurType = 'dark',\n      overlayColor,\n      enabled,\n      autoUpdate,\n      children,\n      style,\n      ...rest\n    },\n    ref\n  ) => {\n    useEffect(() => {\n      DeviceEventEmitter.addListener('ReactNativeBlurError', (message) => {\n        throw new Error(`[ReactNativeBlur]: ${message}`);\n      });\n\n      return () => {\n        DeviceEventEmitter.removeAllListeners('ReactNativeBlurError');\n      };\n    }, []);\n\n    const getOverlayColor = () => {\n      if (overlayColor != null) {\n        return overlayColor;\n      }\n\n      return OVERLAY_COLORS[blurType] || OVERLAY_COLORS.dark;\n    };\n\n    const getBlurRadius = () => {\n      if (blurRadius != null) {\n        if (blurRadius > 25) {\n          throw new Error(\n            `[ReactNativeBlur]: blurRadius cannot be greater than 25! (was: ${blurRadius})`\n          );\n        }\n        return blurRadius;\n      }\n\n      // iOS seems to use a slightly different blurring algorithm (or scale?).\n      // Android blurRadius + downsampleFactor is approximately 80% of blurAmount.\n      const equivalentBlurRadius = Math.round(blurAmount * 0.8);\n\n      if (equivalentBlurRadius > 25) {\n        return 25;\n      }\n      return equivalentBlurRadius;\n    };\n\n    const getDownsampleFactor = () => {\n      if (downsampleFactor != null) {\n        return downsampleFactor;\n      }\n\n      return blurRadius;\n    };\n\n    return (\n      <NativeBlurView\n        {...rest}\n        ref={ref}\n        blurRadius={getBlurRadius()}\n        downsampleFactor={getDownsampleFactor()}\n        overlayColor={getOverlayColor()}\n        blurAmount={blurAmount}\n        blurType={blurType}\n        enabled={enabled}\n        autoUpdate={autoUpdate}\n        pointerEvents=\"none\"\n        style={StyleSheet.compose(styles.transparent, style)}\n      >\n        {children}\n      </NativeBlurView>\n    );\n  }\n);\n\nconst styles = StyleSheet.create<{ transparent: ViewStyle }>({\n  transparent: { backgroundColor: 'transparent' },\n});\n\nexport default BlurView;\n"], "mappings": ";;AAAA,OAAOA,KAAP,IAAgBC,UAAhB,EAA4BC,SAA5B,QAA6C,OAA7C;AACA,SAEEC,kBAFF,EAGEC,UAHF,QAMO,cANP;AAOA,OAAOC,cAAP,MAA2B,0CAA3B;AAEA,MAAMC,cAAc,GAAG;EACrBC,KAAK,EAAE,0BADc;EAErBC,MAAM,EAAE,2BAFa;EAGrBC,IAAI,EAAE;AAHe,CAAvB;AAgBA,MAAMC,QAAQ,gBAAGT,UAAU,CACzB,OAaEU,GAbF,KAcK;EAAA,IAbH;IACEC,gBADF;IAEEC,UAFF;IAGEC,UAAU,GAAG,EAHf;IAIEC,QAAQ,GAAG,MAJb;IAKEC,YALF;IAMEC,OANF;IAOEC,UAPF;IAQEC,QARF;IASEC,KATF;IAUE,GAAGC;EAVL,CAaG;EACHnB,SAAS,CAAC,MAAM;IACdC,kBAAkB,CAACmB,WAAnB,CAA+B,sBAA/B,EAAwDC,OAAD,IAAa;MAClE,MAAM,IAAIC,KAAJ,CAAW,sBAAqBD,OAAQ,EAAxC,CAAN;IACD,CAFD;IAIA,OAAO,MAAM;MACXpB,kBAAkB,CAACsB,kBAAnB,CAAsC,sBAAtC;IACD,CAFD;EAGD,CARQ,EAQN,EARM,CAAT;;EAUA,MAAMC,eAAe,GAAG,MAAM;IAC5B,IAAIV,YAAY,IAAI,IAApB,EAA0B;MACxB,OAAOA,YAAP;IACD;;IAED,OAAOV,cAAc,CAACS,QAAD,CAAd,IAA4BT,cAAc,CAACG,IAAlD;EACD,CAND;;EAQA,MAAMkB,aAAa,GAAG,MAAM;IAC1B,IAAId,UAAU,IAAI,IAAlB,EAAwB;MACtB,IAAIA,UAAU,GAAG,EAAjB,EAAqB;QACnB,MAAM,IAAIW,KAAJ,CACH,kEAAiEX,UAAW,GADzE,CAAN;MAGD;;MACD,OAAOA,UAAP;IACD,CARyB,CAU1B;IACA;;;IACA,MAAMe,oBAAoB,GAAGC,IAAI,CAACC,KAAL,CAAWhB,UAAU,GAAG,GAAxB,CAA7B;;IAEA,IAAIc,oBAAoB,GAAG,EAA3B,EAA+B;MAC7B,OAAO,EAAP;IACD;;IACD,OAAOA,oBAAP;EACD,CAlBD;;EAoBA,MAAMG,mBAAmB,GAAG,MAAM;IAChC,IAAInB,gBAAgB,IAAI,IAAxB,EAA8B;MAC5B,OAAOA,gBAAP;IACD;;IAED,OAAOC,UAAP;EACD,CAND;;EAQA,oBACE,oBAAC,cAAD,eACMQ,IADN;IAEE,GAAG,EAAEV,GAFP;IAGE,UAAU,EAAEgB,aAAa,EAH3B;IAIE,gBAAgB,EAAEI,mBAAmB,EAJvC;IAKE,YAAY,EAAEL,eAAe,EAL/B;IAME,UAAU,EAAEZ,UANd;IAOE,QAAQ,EAAEC,QAPZ;IAQE,OAAO,EAAEE,OARX;IASE,UAAU,EAAEC,UATd;IAUE,aAAa,EAAC,MAVhB;IAWE,KAAK,EAAEd,UAAU,CAAC4B,OAAX,CAAmBC,MAAM,CAACC,WAA1B,EAAuCd,KAAvC;EAXT,IAaGD,QAbH,CADF;AAiBD,CA/EwB,CAA3B;AAkFA,MAAMc,MAAM,GAAG7B,UAAU,CAAC+B,MAAX,CAA8C;EAC3DD,WAAW,EAAE;IAAEE,eAAe,EAAE;EAAnB;AAD8C,CAA9C,CAAf;AAIA,eAAe1B,QAAf"}