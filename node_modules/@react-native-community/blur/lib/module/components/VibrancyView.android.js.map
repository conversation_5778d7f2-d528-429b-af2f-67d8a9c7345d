{"version": 3, "names": ["React", "Vibrancy<PERSON>iew", "Component", "render", "console", "error"], "sources": ["VibrancyView.android.tsx"], "sourcesContent": ["import React from 'react';\n\nclass VibrancyView extends React.Component {\n  render() {\n    console.error('VibrancyView is not implemented on Android');\n    return null;\n  }\n}\n\nexport default VibrancyView;\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,OAAlB;;AAEA,MAAMC,YAAN,SAA2BD,KAAK,CAACE,SAAjC,CAA2C;EACzCC,MAAM,GAAG;IACPC,OAAO,CAACC,KAAR,CAAc,4CAAd;IACA,OAAO,IAAP;EACD;;AAJwC;;AAO3C,eAAeJ,YAAf"}