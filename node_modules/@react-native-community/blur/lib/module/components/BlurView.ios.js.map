{"version": 3, "names": ["React", "forwardRef", "StyleSheet", "NativeBlurView", "BlurView", "ref", "blurType", "blurAmount", "style", "rest", "compose", "styles", "transparent", "create", "backgroundColor"], "sources": ["BlurView.ios.tsx"], "sourcesContent": ["import React, { forwardRef } from 'react';\nimport { StyleSheet, ViewProps, ViewStyle, View } from 'react-native';\nimport NativeBlurView from '../fabric/BlurViewNativeComponent';\n\ntype BlurType =\n  | 'dark'\n  | 'light'\n  | 'xlight'\n  | 'prominent'\n  | 'regular'\n  | 'extraDark'\n  | 'chromeMaterial'\n  | 'material'\n  | 'thickMaterial'\n  | 'thinMaterial'\n  | 'ultraThinMaterial'\n  | 'chromeMaterialDark'\n  | 'materialDark'\n  | 'thickMaterialDark'\n  | 'thinMaterialDark'\n  | 'ultraThinMaterialDark'\n  | 'chromeMaterialLight'\n  | 'materialLight'\n  | 'thickMaterialLight'\n  | 'thinMaterialLight'\n  | 'ultraThinMaterialLight';\n\nexport type BlurViewProps = ViewProps & {\n  blurType?: BlurType;\n  blurAmount?: number;\n  reducedTransparencyFallbackColor?: string;\n};\n\nconst BlurView = forwardRef<View, BlurViewProps>(\n  ({ blurType = 'dark', blurAmount = 10, style, ...rest }, ref) => (\n    <NativeBlurView\n      ref={ref}\n      style={StyleSheet.compose(styles.transparent, style)}\n      blurType={blurType}\n      blurAmount={blurAmount}\n      {...rest}\n    />\n  )\n);\n\nconst styles = StyleSheet.create<{ transparent: ViewStyle }>({\n  transparent: { backgroundColor: 'transparent' },\n});\n\nexport default BlurView;\n"], "mappings": ";;AAAA,OAAOA,KAAP,IAAgBC,UAAhB,QAAkC,OAAlC;AACA,SAASC,UAAT,QAAuD,cAAvD;AACA,OAAOC,cAAP,MAA2B,mCAA3B;AA+BA,MAAMC,QAAQ,gBAAGH,UAAU,CACzB,OAAyDI,GAAzD;EAAA,IAAC;IAAEC,QAAQ,GAAG,MAAb;IAAqBC,UAAU,GAAG,EAAlC;IAAsCC,KAAtC;IAA6C,GAAGC;EAAhD,CAAD;EAAA,oBACE,oBAAC,cAAD;IACE,GAAG,EAAEJ,GADP;IAEE,KAAK,EAAEH,UAAU,CAACQ,OAAX,CAAmBC,MAAM,CAACC,WAA1B,EAAuCJ,KAAvC,CAFT;IAGE,QAAQ,EAAEF,QAHZ;IAIE,UAAU,EAAEC;EAJd,GAKME,IALN,EADF;AAAA,CADyB,CAA3B;AAYA,MAAME,MAAM,GAAGT,UAAU,CAACW,MAAX,CAA8C;EAC3DD,WAAW,EAAE;IAAEE,eAAe,EAAE;EAAnB;AAD8C,CAA9C,CAAf;AAIA,eAAeV,QAAf"}