{"version": 3, "names": ["React", "forwardRef", "StyleSheet", "NativeVibrancyView", "Vibrancy<PERSON>iew", "ref", "style", "rest", "compose", "styles", "transparent", "create", "backgroundColor"], "sources": ["VibrancyView.ios.tsx"], "sourcesContent": ["import React, { forwardRef } from 'react';\nimport { StyleSheet, ViewProps, ViewStyle } from 'react-native';\nimport NativeVibrancyView from '../fabric/VibrancyViewNativeComponent';\nimport type { BlurViewProps } from './BlurView.ios';\n\nexport type VibrancyViewProps = ViewProps & {\n  blurType?: BlurViewProps['blurType'];\n  blurAmount: number;\n  reducedTransparencyFallbackColor?: string;\n};\n\nconst VibrancyView = forwardRef<any, VibrancyViewProps>(\n  ({ style, ...rest }, ref) => (\n    <NativeVibrancyView\n      {...rest}\n      ref={ref}\n      style={StyleSheet.compose(styles.transparent, style)}\n    />\n  )\n);\n\nconst styles = StyleSheet.create<{ transparent: ViewStyle }>({\n  transparent: { backgroundColor: 'transparent' },\n});\n\nexport default VibrancyView;\n"], "mappings": ";;AAAA,OAAOA,KAAP,IAAgBC,UAAhB,QAAkC,OAAlC;AACA,SAASC,UAAT,QAAiD,cAAjD;AACA,OAAOC,kBAAP,MAA+B,uCAA/B;AASA,MAAMC,YAAY,gBAAGH,UAAU,CAC7B,OAAqBI,GAArB;EAAA,IAAC;IAAEC,KAAF;IAAS,GAAGC;EAAZ,CAAD;EAAA,oBACE,oBAAC,kBAAD,eACMA,IADN;IAEE,GAAG,EAAEF,GAFP;IAGE,KAAK,EAAEH,UAAU,CAACM,OAAX,CAAmBC,MAAM,CAACC,WAA1B,EAAuCJ,KAAvC;EAHT,GADF;AAAA,CAD6B,CAA/B;AAUA,MAAMG,MAAM,GAAGP,UAAU,CAACS,MAAX,CAA8C;EAC3DD,WAAW,EAAE;IAAEE,eAAe,EAAE;EAAnB;AAD8C,CAA9C,CAAf;AAIA,eAAeR,YAAf"}