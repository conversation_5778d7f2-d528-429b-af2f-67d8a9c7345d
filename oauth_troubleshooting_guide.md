# OAuth Authentication Troubleshooting Guide

## 🚨 **Current Issues Identified**

### **Issue 1: Mobile App Authentication Failure**
```
🔐 Authentication failed, clearing invalid token
🚨 API Request failed: Error: User not authenticated
GET http://localhost:8080/api/v1/users/google-drive/auth-url [HTTP/1.1 401 Unauthorized]
```

### **Issue 2: Browser Missing user_id Parameter**
```
error: "user_id parameter is required for public OAuth initiation"
message: "Please use the authenticated endpoint /api/v1/users/google-drive/auth-url instead"
```

## 🔧 **Solutions Applied**

### **✅ Fix 1: Enhanced Public Endpoint**
Updated the public endpoint to handle browser users more gracefully:

```go
// Now returns HTML error page for browsers
if strings.Contains(c.GetHeader("Accept"), "text/html") {
    c.HTML(http.StatusBadRequest, "oauth_error.html", gin.H{
        "error": "Missing user_id parameter. Please use the test page: http://localhost:8080/test_google_drive_oauth.html",
    })
    return
}
```

### **✅ Fix 2: Added Debug Endpoints**
Added debug endpoints to troubleshoot authentication:

```
GET /api/v1/debug/auth-status          (public - no auth required)
GET /api/v1/debug/auth-status-protected (protected - auth required)
```

## 🔍 **Troubleshooting Steps**

### **Step 1: Test Authentication Status**

#### **Test Public Debug Endpoint**
```bash
curl "http://localhost:8080/api/v1/debug/auth-status"
```

**Expected Response:**
```json
{
  "status": "unauthenticated",
  "message": "User authentication failed",
  "headers": {
    "authorization": "missing",
    "user_agent": "curl/7.68.0"
  }
}
```

#### **Test Protected Debug Endpoint (Mobile App Token)**
```bash
# Use the same token your mobile app is using
curl -H "Authorization: Bearer YOUR_MOBILE_APP_TOKEN" \
     "http://localhost:8080/api/v1/debug/auth-status-protected"
```

**If Token is Valid:**
```json
{
  "status": "authenticated",
  "message": "User is properly authenticated",
  "context": {
    "user_id": "c9fca28d-595f-4cf5-b3d2-b02472d21621",
    "user_id_exists": true
  }
}
```

**If Token is Invalid:**
```json
{
  "status": "unauthenticated",
  "message": "User authentication failed",
  "suggestions": [
    "Check if Authorization header is present",
    "Verify JWT token is valid and not expired",
    "Ensure token format is 'Bearer <token>'",
    "Check if authentication middleware is working"
  ]
}
```

### **Step 2: Fix Mobile App Authentication**

#### **Option A: Login Again in Mobile App**
1. **Open mobile app**
2. **Go to login screen**
3. **Login with valid credentials**
4. **Try Google Drive connection again**

#### **Option B: Check Token Storage**
In your mobile app, check if the token is properly stored:

```javascript
// Check stored token
import AsyncStorage from '@react-native-async-storage/async-storage';

const checkToken = async () => {
  const token = await AsyncStorage.getItem('auth_token');
  console.log('Stored token:', token ? 'present' : 'missing');
  
  if (token) {
    // Test token validity
    const response = await fetch('http://localhost:8080/api/v1/debug/auth-status-protected', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    const result = await response.json();
    console.log('Token validation:', result);
  }
};
```

### **Step 3: Browser OAuth Testing**

#### **Use the Test Page**
```
http://localhost:8080/test_google_drive_oauth.html
```

#### **Or Use Direct URL with user_id**
```
http://localhost:8080/api/v1/auth/google/drive?user_id=c9fca28d-595f-4cf5-b3d2-b02472d21621
```

## 🎯 **Quick Fixes**

### **For Mobile App (401 Error)**
```javascript
// In your mobile app, add token refresh logic
const makeAuthenticatedRequest = async (url, options = {}) => {
  let token = await AsyncStorage.getItem('auth_token');
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
    },
  });
  
  if (response.status === 401) {
    // Token expired, try to refresh or redirect to login
    console.log('Token expired, redirecting to login');
    // Clear invalid token
    await AsyncStorage.removeItem('auth_token');
    // Redirect to login screen
    navigation.navigate('Login');
    throw new Error('Authentication required');
  }
  
  return response;
};
```

### **For Browser Testing**
Use the test HTML page:
```
http://localhost:8080/test_google_drive_oauth.html
```

## 🔄 **Complete OAuth Flow Test**

### **Step 1: Restart Backend**
```bash
cd apps/backend
go run main.go
```

### **Step 2: Test Debug Endpoints**
```bash
# Test public debug
curl "http://localhost:8080/api/v1/debug/auth-status"

# Test with mobile app token (replace with actual token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8080/api/v1/debug/auth-status-protected"
```

### **Step 3: Test OAuth Flow**
```bash
# Generate OAuth URL
curl "http://localhost:8080/api/v1/auth/google/drive?user_id=c9fca28d-595f-4cf5-b3d2-b02472d21621"

# Copy auth_url from response and open in browser
# Complete Google authentication
# Should see success page
```

### **Step 4: Test Mobile App**
1. **Login to mobile app** with valid credentials
2. **Go to settings** → Google Drive
3. **Click "Connect Google Drive"**
4. **Should work without 401 error**

## 📋 **Expected Server Logs**

When everything works correctly, you should see:
```
🔐 OAuth state stored: M2jaYcZpof... (expires: 17:15:30)
🔄 OAuth callback received - State: M2jaYcZpof..., Code: present
🔍 State validation - Exists: true, Total states: 1
✅ Tokens securely stored for user: c9fca28d-595f-4cf5-b3d2-b02472d21621
```

## 🚀 **Next Steps**

1. **Restart your backend server** to apply the fixes
2. **Test the debug endpoints** to identify the authentication issue
3. **Fix mobile app authentication** (likely need to login again)
4. **Test the complete OAuth flow** using the test page
5. **Verify mobile app integration** works

The OAuth state persistence issue has been fixed, and now we need to resolve the mobile app authentication issue. Use the debug endpoints to identify exactly what's wrong with the mobile app's token! 🔍
