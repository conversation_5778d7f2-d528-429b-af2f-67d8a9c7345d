erDiagram
    %% VaultKe Database Schema - Entity Relationship Diagram
    
    %% Core User Management
    users {
        TEXT id PK
        TEXT email UK
        TEXT phone UK
        TEXT password_hash
        TEXT first_name
        TEXT last_name
        TEXT avatar
        TEXT role
        TEXT status
        TEXT county
        TEXT town
        TEXT business_type
        REAL rating
        INTEGER total_ratings
        DATETIM<PERSON> created_at
        DATETIME updated_at
    }
    
    %% Chama (Group) Management
    chamas {
        TEXT id PK
        TEXT name
        TEXT description
        TEXT rules
        TEXT category
        TEXT permissions
        TEXT creator_id FK
        INTEGER member_count
        REAL total_contributions
        TEXT status
        DATETIME created_at
        DATETIME updated_at
    }
    
    chama_members {
        TEXT id PK
        TEXT chama_id FK
        TEXT user_id FK
        TEXT role
        TEXT status
        DATETIM<PERSON> joined_at
        DATETIME updated_at
    }
    
    chama_invitations {
        TEXT id PK
        TEXT chama_id FK
        TEXT inviter_id FK
        TEXT invitee_email
        TEXT invitee_phone
        TEXT role
        TEXT status
        TEXT token
        DATETIME expires_at
        DATETIME created_at
    }
    
    %% Financial Management
    wallets {
        TEXT id PK
        TEXT user_id FK
        TEXT chama_id FK
        REAL balance
        TEXT currency
        TEXT status
        D<PERSON>ETIM<PERSON> created_at
        DATETIME updated_at
    }
    
    transactions {
        TEXT id PK
        TEXT from_wallet_id FK
        TEXT to_wallet_id FK
        TEXT user_id FK
        TEXT chama_id FK
        REAL amount
        TEXT currency
        TEXT type
        TEXT category
        TEXT description
        TEXT reference
        TEXT status
        TEXT mpesa_receipt
        DATETIME created_at
    }
    
    %% Loans & Credit
    loans {
        TEXT id PK
        TEXT user_id FK
        TEXT chama_id FK
        REAL amount
        REAL interest_rate
        INTEGER duration_months
        TEXT purpose
        TEXT status
        DATETIME applied_at
        DATETIME approved_at
        DATETIME due_date
    }
    
    guarantors {
        TEXT id PK
        TEXT loan_id FK
        TEXT user_id FK
        TEXT status
        DATETIME created_at
    }
    
    loan_payments {
        TEXT id PK
        TEXT loan_id FK
        REAL amount
        TEXT type
        DATETIME payment_date
        DATETIME created_at
    }
    
    %% Marketplace
    products {
        TEXT id PK
        TEXT seller_id FK
        TEXT name
        TEXT description
        REAL price
        TEXT currency
        TEXT category
        TEXT images
        INTEGER stock_quantity
        TEXT status
        DATETIME created_at
        DATETIME updated_at
    }
    
    orders {
        TEXT id PK
        TEXT buyer_id FK
        REAL total_amount
        TEXT currency
        TEXT status
        TEXT delivery_address
        TEXT delivery_contact
        DATETIME created_at
        DATETIME updated_at
    }
    
    order_items {
        TEXT id PK
        TEXT order_id FK
        TEXT product_id FK
        INTEGER quantity
        REAL unit_price
        REAL total_price
        TEXT delivery_person_id FK
        TEXT status
    }
    
    cart_items {
        TEXT id PK
        TEXT user_id FK
        TEXT product_id FK
        INTEGER quantity
        DATETIME created_at
    }
    
    product_reviews {
        TEXT id PK
        TEXT product_id FK
        TEXT user_id FK
        INTEGER rating
        TEXT comment
        DATETIME created_at
    }
    
    wishlist {
        TEXT id PK
        TEXT user_id FK
        TEXT product_id FK
        DATETIME created_at
    }
    
    %% Enhanced Notification System
    notifications {
        INTEGER id PK
        TEXT user_id FK
        TEXT title
        TEXT message
        TEXT type
        TEXT priority
        TEXT category
        TEXT reference_type
        INTEGER reference_id
        TEXT status
        BOOLEAN is_read
        DATETIME read_at
        DATETIME scheduled_for
        DATETIME sent_at
        DATETIME delivered_at
        TEXT data
        BOOLEAN sound_played
        INTEGER retry_count
        DATETIME created_at
        DATETIME updated_at
    }
    
    notification_sounds {
        INTEGER id PK
        TEXT name
        TEXT file_path
        INTEGER file_size
        REAL duration_seconds
        BOOLEAN is_default
        BOOLEAN is_active
        DATETIME created_at
        DATETIME updated_at
    }
    
    user_notification_preferences {
        INTEGER id PK
        TEXT user_id FK
        INTEGER notification_sound_id FK
        BOOLEAN sound_enabled
        BOOLEAN vibration_enabled
        INTEGER volume_level
        BOOLEAN chama_notifications
        BOOLEAN transaction_notifications
        BOOLEAN reminder_notifications
        BOOLEAN system_notifications
        BOOLEAN marketing_notifications
        BOOLEAN quiet_hours_enabled
        TIME quiet_hours_start
        TIME quiet_hours_end
        TEXT timezone
        TEXT notification_frequency
        BOOLEAN priority_only_during_quiet
        DATETIME created_at
        DATETIME updated_at
    }
    
    notification_templates {
        INTEGER id PK
        TEXT name UK
        TEXT type
        TEXT category
        TEXT title_template
        TEXT message_template
        TEXT default_priority
        BOOLEAN requires_sound
        BOOLEAN requires_vibration
        TEXT variables
        BOOLEAN is_active
        DATETIME created_at
        DATETIME updated_at
    }
    
    %% Meetings & Communication
    meetings {
        TEXT id PK
        TEXT chama_id FK
        TEXT creator_id FK
        TEXT title
        TEXT description
        TEXT agenda
        DATETIME scheduled_at
        INTEGER duration_minutes
        TEXT meeting_type
        TEXT room_name
        TEXT status
        DATETIME created_at
    }
    
    meeting_attendance {
        TEXT id PK
        TEXT meeting_id FK
        TEXT user_id FK
        DATETIME joined_at
        DATETIME left_at
        TEXT status
    }
    
    chat_rooms {
        TEXT id PK
        TEXT chama_id FK
        TEXT name
        TEXT type
        TEXT creator_id FK
        DATETIME created_at
    }
    
    chat_messages {
        TEXT id PK
        TEXT room_id FK
        TEXT sender_id FK
        TEXT message
        TEXT message_type
        TEXT file_url
        TEXT file_name
        INTEGER file_size
        BOOLEAN is_edited
        DATETIME created_at
        DATETIME updated_at
    }
    
    %% Learning System
    learning_categories {
        TEXT id PK
        TEXT name
        TEXT description
        TEXT icon
        INTEGER sort_order
        BOOLEAN is_active
        DATETIME created_at
    }
    
    learning_courses {
        TEXT id PK
        TEXT category_id FK
        TEXT title
        TEXT description
        TEXT content
        TEXT content_type
        TEXT video_url
        TEXT image_url
        TEXT document_url
        INTEGER duration_minutes
        TEXT difficulty_level
        INTEGER sort_order
        BOOLEAN is_active
        DATETIME created_at
    }
    
    quiz_results {
        TEXT id PK
        TEXT user_id FK
        TEXT course_id FK
        INTEGER score
        INTEGER total_questions
        REAL percentage
        TEXT answers
        DATETIME completed_at
    }
    
    %% Relationships
    users ||--o{ chama_members : "belongs to"
    users ||--o{ chamas : "creates"
    users ||--o{ wallets : "owns"
    users ||--o{ transactions : "makes"
    users ||--o{ loans : "applies for"
    users ||--o{ products : "sells"
    users ||--o{ orders : "places"
    users ||--o{ notifications : "receives"
    users ||--o{ user_notification_preferences : "has"
    
    chamas ||--o{ chama_members : "has"
    chamas ||--o{ chama_invitations : "sends"
    chamas ||--o{ wallets : "has"
    chamas ||--o{ meetings : "hosts"
    chamas ||--o{ chat_rooms : "has"
    
    wallets ||--o{ transactions : "from/to"
    
    loans ||--o{ guarantors : "has"
    loans ||--o{ loan_payments : "receives"
    
    products ||--o{ order_items : "included in"
    products ||--o{ cart_items : "added to"
    products ||--o{ product_reviews : "reviewed"
    products ||--o{ wishlist : "wished for"
    
    orders ||--o{ order_items : "contains"
    
    meetings ||--o{ meeting_attendance : "tracks"
    
    chat_rooms ||--o{ chat_messages : "contains"
    
    learning_categories ||--o{ learning_courses : "contains"
    learning_courses ||--o{ quiz_results : "generates"
    
    notification_sounds ||--o{ user_notification_preferences : "used in"
    notification_templates ||--o{ notifications : "generates"
