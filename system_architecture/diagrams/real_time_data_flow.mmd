sequenceDiagram
    participant User1 as 👤 User 1
    participant Mobile1 as 📱 Mobile App 1
    participant WSClient1 as 🔄 WebSocket Client 1
    participant WSServer as 🔄 WebSocket Server
    participant EventBus as 📡 Event Bus
    participant NotifSvc as 🔔 Notification Service
    participant TransSvc as 💳 Transaction Service
    participant ChamaSvc as 👥 Chama Service
    participant DB as 🗄️ Database
    participant WSClient2 as 🔄 WebSocket Client 2
    participant Mobile2 as 📱 Mobile App 2
    participant User2 as 👤 User 2
    
    %% WebSocket Connection Establishment
    Note over User1, User2: WebSocket Connection Setup
    
    User1->>Mobile1: Launch app
    Mobile1->>WSClient1: Initialize WebSocket
    WSClient1->>WSServer: Connect with auth token
    Note right of WSServer: Validate JWT token<br/>Extract user ID<br/>Store connection mapping
    WSServer-->>WSClient1: Connection established
    WSClient1-->>Mobile1: Connected
    
    User2->>Mobile2: Launch app
    Mobile2->>WSClient2: Initialize WebSocket
    WSClient2->>WSServer: Connect with auth token
    WSServer-->>WSClient2: Connection established
    WSClient2-->>Mobile2: Connected
    
    %% Real-time Transaction Notification
    Note over User1, User2: Real-time Transaction Flow
    
    User1->>Mobile1: Send money to User2
    Mobile1->>TransSvc: POST /api/v1/transactions/transfer
    TransSvc->>DB: Process transaction
    DB-->>TransSvc: Transaction completed
    
    %% Event Broadcasting
    TransSvc->>EventBus: Emit "transaction.completed"
    Note right of EventBus: Event: {<br/>  type: "transaction.completed",<br/>  data: {<br/>    transaction_id: "txn123",<br/>    from_user: "user1",<br/>    to_user: "user2",<br/>    amount: 1000,<br/>    currency: "KES"<br/>  }<br/>}
    
    EventBus->>NotifSvc: Handle transaction event
    NotifSvc->>NotifSvc: Create notifications for both users
    
    %% Send to sender
    NotifSvc->>WSServer: Send notification to User1
    Note right of WSServer: Message: {<br/>  type: "notification",<br/>  category: "transaction",<br/>  title: "Money Sent",<br/>  message: "You sent KES 1,000 to User2",<br/>  data: { transaction_id: "txn123" }<br/>}
    WSServer->>WSClient1: Forward notification
    WSClient1->>Mobile1: Handle notification
    Mobile1->>Mobile1: Update wallet balance
    Mobile1->>Mobile1: Show notification toast
    Mobile1-->>User1: "Money sent successfully"
    
    %% Send to recipient
    NotifSvc->>WSServer: Send notification to User2
    Note right of WSServer: Message: {<br/>  type: "notification",<br/>  category: "transaction",<br/>  title: "Money Received",<br/>  message: "You received KES 1,000 from User1",<br/>  data: { transaction_id: "txn123" }<br/>}
    WSServer->>WSClient2: Forward notification
    WSClient2->>Mobile2: Handle notification
    Mobile2->>Mobile2: Update wallet balance
    Mobile2->>Mobile2: Show notification toast
    Mobile2->>Mobile2: Play notification sound
    Mobile2-->>User2: "Money received!"
    
    %% Real-time Chat Messages
    Note over User1, User2: Real-time Chat Flow
    
    User1->>Mobile1: Send chat message in Chama
    Mobile1->>ChamaSvc: POST /api/v1/chat/rooms/{id}/messages
    Note right of ChamaSvc: {<br/>  "message": "Hello everyone!",<br/>  "message_type": "text",<br/>  "room_id": "room123"<br/>}
    
    ChamaSvc->>DB: Store chat message
    DB-->>ChamaSvc: Message stored
    
    ChamaSvc->>EventBus: Emit "chat.message.sent"
    Note right of EventBus: Event: {<br/>  type: "chat.message.sent",<br/>  data: {<br/>    message_id: "msg456",<br/>    room_id: "room123",<br/>    sender_id: "user1",<br/>    message: "Hello everyone!",<br/>    timestamp: "2023-12-25T12:00:00Z"<br/>  }<br/>}
    
    EventBus->>WSServer: Broadcast to room members
    WSServer->>WSServer: Get room member connections
    Note right of WSServer: Query active connections<br/>for room members<br/>(excluding sender)
    
    %% Broadcast to all room members except sender
    WSServer->>WSClient2: Send chat message
    Note right of WSClient2: Message: {<br/>  type: "chat_message",<br/>  room_id: "room123",<br/>  sender: {<br/>    id: "user1",<br/>    name: "User One",<br/>    avatar: "avatar_url"<br/>  },<br/>  message: "Hello everyone!",<br/>  timestamp: "2023-12-25T12:00:00Z"<br/>}
    
    WSClient2->>Mobile2: Handle chat message
    Mobile2->>Mobile2: Update chat UI
    Mobile2->>Mobile2: Show message bubble
    Mobile2-->>User2: Display new message
    
    %% Real-time Chama Updates
    Note over User1, User2: Real-time Chama Activity
    
    User1->>Mobile1: Make chama contribution
    Mobile1->>ChamaSvc: POST /api/v1/chamas/{id}/contribute
    ChamaSvc->>DB: Process contribution
    DB-->>ChamaSvc: Contribution recorded
    
    ChamaSvc->>EventBus: Emit "chama.contribution.made"
    Note right of EventBus: Event: {<br/>  type: "chama.contribution.made",<br/>  data: {<br/>    chama_id: "chama123",<br/>    contributor_id: "user1",<br/>    amount: 5000,<br/>    new_total: 50000<br/>  }<br/>}
    
    EventBus->>WSServer: Broadcast to chama members
    WSServer->>WSClient2: Send chama update
    Note right of WSClient2: Message: {<br/>  type: "chama_update",<br/>  chama_id: "chama123",<br/>  update_type: "contribution",<br/>  message: "User One contributed KES 5,000",<br/>  new_total: 50000<br/>}
    
    WSClient2->>Mobile2: Handle chama update
    Mobile2->>Mobile2: Update chama balance display
    Mobile2->>Mobile2: Update member contribution list
    Mobile2-->>User2: Show updated chama stats
    
    %% Connection Management & Reconnection
    Note over User1, User2: Connection Management
    
    WSClient1->>WSClient1: Detect connection loss
    WSClient1->>WSClient1: Start reconnection timer
    
    loop Reconnection Attempts
        WSClient1->>WSServer: Attempt reconnection
        alt Connection Successful
            WSServer-->>WSClient1: Reconnected
            WSClient1->>WSServer: Request missed messages
            Note right of WSServer: Get messages since<br/>last_seen_timestamp
            WSServer-->>WSClient1: Send missed messages
            WSClient1->>Mobile1: Process missed messages
            Mobile1-->>User1: Sync complete
        else Connection Failed
            WSClient1->>WSClient1: Exponential backoff delay
            Note left of WSClient1: Wait: 1s, 2s, 4s, 8s...
        end
    end
    
    %% Heartbeat & Keep-Alive
    Note over User1, User2: Connection Health Monitoring
    
    loop Every 30 seconds
        WSClient1->>WSServer: Send ping
        WSServer-->>WSClient1: Send pong
        
        WSClient2->>WSServer: Send ping
        WSServer-->>WSClient2: Send pong
    end
    
    alt No Pong Received
        WSClient1->>WSClient1: Mark connection as dead
        WSClient1->>WSClient1: Trigger reconnection
    end
    
    %% Offline Message Queuing
    Note over User1, User2: Offline Message Handling
    
    WSClient2->>WSClient2: Detect offline state
    User2->>Mobile2: Send message while offline
    Mobile2->>Mobile2: Queue message locally
    Note right of Mobile2: Store in local queue:<br/>{<br/>  type: "chat_message",<br/>  data: {...},<br/>  timestamp: "...",<br/>  retry_count: 0<br/>}
    
    WSClient2->>WSServer: Reconnect
    WSServer-->>WSClient2: Connection restored
    Mobile2->>Mobile2: Process queued messages
    Mobile2->>ChamaSvc: Send queued message
    ChamaSvc-->>Mobile2: Message sent
    Mobile2->>Mobile2: Remove from queue
    
    %% Real-time Presence Updates
    Note over User1, User2: User Presence System
    
    WSClient1->>WSServer: Update presence (online)
    WSServer->>EventBus: Emit "user.presence.changed"
    EventBus->>WSServer: Broadcast to relevant users
    WSServer->>WSClient2: Send presence update
    Note right of WSClient2: Message: {<br/>  type: "presence_update",<br/>  user_id: "user1",<br/>  status: "online",<br/>  last_seen: "2023-12-25T12:00:00Z"<br/>}
    
    WSClient2->>Mobile2: Update user status
    Mobile2->>Mobile2: Show green dot next to User1
    Mobile2-->>User2: User1 is online
    
    %% Background/Foreground Handling
    Note over User1, User2: App State Management
    
    Mobile1->>Mobile1: App goes to background
    Mobile1->>WSClient1: Reduce heartbeat frequency
    WSClient1->>WSServer: Update presence (away)
    
    Mobile1->>Mobile1: App returns to foreground
    Mobile1->>WSClient1: Resume normal heartbeat
    WSClient1->>WSServer: Update presence (online)
    WSClient1->>WSServer: Request missed updates
    WSServer-->>WSClient1: Send missed notifications
    
    %% Error Handling & Recovery
    Note over User1, User2: Error Handling
    
    WSServer->>WSClient1: Send malformed message
    WSClient1->>WSClient1: Parse error
    WSClient1->>WSClient1: Log error & continue
    WSClient1->>WSServer: Send error acknowledgment
    
    WSClient1->>WSServer: Send message
    Note over WSServer: Server error
    WSServer-->>WSClient1: Error response
    WSClient1->>Mobile1: Handle error
    Mobile1->>Mobile1: Show retry option
    Mobile1-->>User1: "Message failed to send. Retry?"
