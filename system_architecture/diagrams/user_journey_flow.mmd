graph TD
    %% VaultKe Complete User Journey Flow
    
    subgraph "User Onboarding Journey"
        Start[👤 New User Visits App] --> Download[📱 Downloads VaultKe App]
        Download --> Launch[🚀 Launches App]
        Launch --> Welcome[👋 Welcome Screen]
        Welcome --> Choice{Choose Action}
        
        Choice -->|New User| Register[📝 Registration Flow]
        Choice -->|Existing User| Login[🔑 Login Flow]
        Choice -->|Guest Mode| GuestMode[👁️ Browse as Guest]
        
        Register --> RegForm[📋 Fill Registration Form]
        RegForm --> Verify[📧 Email/SMS Verification]
        Verify --> Profile[👤 Complete Profile Setup]
        Profile --> Preferences[⚙️ Set Preferences]
        Preferences --> Dashboard[🏠 Enter Dashboard]
        
        Login --> LoginForm[🔑 Enter Credentials]
        LoginForm --> BiometricCheck{Biometric Available?}
        BiometricCheck -->|Yes| BiometricAuth[👆 Biometric Login]
        BiometricCheck -->|No| PasswordAuth[🔒 Password Login]
        BiometricAuth --> Dashboard
        PasswordAuth --> Dashboard
        
        GuestMode --> LimitedAccess[👁️ Limited Feature Access]
        LimitedAccess --> PromptRegister[📝 Prompt to Register]
        PromptRegister --> Register
    end
    
    subgraph "Core App Experience"
        Dashboard --> MainFeatures{Select Feature}
        
        MainFeatures -->|Chamas| ChamaJourney[👥 Chama Management]
        MainFeatures -->|Wallet| WalletJourney[💰 Financial Management]
        MainFeatures -->|Marketplace| MarketJourney[🛒 Marketplace]
        MainFeatures -->|Learning| LearningJourney[📚 Learning Center]
        MainFeatures -->|Profile| ProfileJourney[👤 Profile Management]
        MainFeatures -->|Notifications| NotificationJourney[🔔 Notifications]
    end
    
    subgraph "Chama Management Journey"
        ChamaJourney --> ChamaAction{Chama Action}
        
        ChamaAction -->|Create New| CreateChama[➕ Create New Chama]
        ChamaAction -->|Join Existing| JoinChama[🤝 Join Chama]
        ChamaAction -->|View My Chamas| ViewChamas[📋 View Chama List]
        
        CreateChama --> ChamaDetails[📝 Enter Chama Details]
        ChamaDetails --> ChamaRules[📜 Set Rules & Permissions]
        ChamaRules --> InviteMembers[📧 Invite Members]
        InviteMembers --> ChamaCreated[✅ Chama Created]
        ChamaCreated --> ChamaManagement[⚙️ Manage Chama]
        
        JoinChama --> SearchChama[🔍 Search/Browse Chamas]
        SearchChama --> RequestJoin[🙋 Request to Join]
        RequestJoin --> WaitApproval[⏳ Wait for Approval]
        WaitApproval --> JoinApproved[✅ Join Approved]
        JoinApproved --> ChamaParticipation[🤝 Participate in Chama]
        
        ViewChamas --> SelectChama[👆 Select Chama]
        SelectChama --> ChamaDetails2[📊 View Chama Details]
        ChamaDetails2 --> ChamaActions{Chama Actions}
        
        ChamaActions -->|Contribute| MakeContribution[💰 Make Contribution]
        ChamaActions -->|View Members| ViewMembers[👥 View Members]
        ChamaActions -->|Schedule Meeting| ScheduleMeeting[📅 Schedule Meeting]
        ChamaActions -->|Chat| ChamaChat[💬 Group Chat]
        ChamaActions -->|View Finances| ViewFinances[📊 Financial Overview]
    end
    
    subgraph "Financial Management Journey"
        WalletJourney --> WalletAction{Wallet Action}
        
        WalletAction -->|View Balance| CheckBalance[💰 Check Balance]
        WalletAction -->|Send Money| SendMoney[📤 Send Money]
        WalletAction -->|Request Money| RequestMoney[📥 Request Money]
        WalletAction -->|Transaction History| ViewHistory[📋 View History]
        WalletAction -->|Add Money| AddMoney[➕ Add Money]
        
        SendMoney --> SelectRecipient[👤 Select Recipient]
        SelectRecipient --> EnterAmount[💰 Enter Amount]
        EnterAmount --> ConfirmTransfer[✅ Confirm Transfer]
        ConfirmTransfer --> AuthorizePayment[🔐 Authorize Payment]
        AuthorizePayment --> ProcessPayment[⚡ Process Payment]
        ProcessPayment --> PaymentComplete[✅ Payment Complete]
        PaymentComplete --> SendNotification[🔔 Send Notifications]
        
        AddMoney --> PaymentMethod{Payment Method}
        PaymentMethod -->|M-Pesa| MpesaPayment[📱 M-Pesa Payment]
        PaymentMethod -->|Bank Transfer| BankTransfer[🏦 Bank Transfer]
        PaymentMethod -->|Card Payment| CardPayment[💳 Card Payment]
        
        MpesaPayment --> MpesaSTK[📱 STK Push]
        MpesaSTK --> MpesaPIN[🔢 Enter M-Pesa PIN]
        MpesaPIN --> MpesaConfirm[✅ M-Pesa Confirmation]
        MpesaConfirm --> WalletUpdated[💰 Wallet Updated]
    end
    
    subgraph "Marketplace Journey"
        MarketJourney --> MarketAction{Market Action}
        
        MarketAction -->|Browse Products| BrowseProducts[🔍 Browse Products]
        MarketAction -->|Sell Product| SellProduct[📦 List Product]
        MarketAction -->|View Cart| ViewCart[🛍️ View Cart]
        MarketAction -->|Order History| OrderHistory[📋 Order History]
        
        BrowseProducts --> ProductCategories[📂 Select Category]
        ProductCategories --> ProductList[📋 View Products]
        ProductList --> ProductDetails[📱 Product Details]
        ProductDetails --> ProductAction{Product Action}
        
        ProductAction -->|Add to Cart| AddToCart[🛍️ Add to Cart]
        ProductAction -->|Buy Now| BuyNow[💳 Buy Now]
        ProductAction -->|Add to Wishlist| AddWishlist[❤️ Add to Wishlist]
        ProductAction -->|Contact Seller| ContactSeller[📞 Contact Seller]
        
        AddToCart --> ContinueShopping{Continue Shopping?}
        ContinueShopping -->|Yes| ProductList
        ContinueShopping -->|No| ViewCart
        
        ViewCart --> CartActions{Cart Actions}
        CartActions -->|Update Quantity| UpdateCart[🔢 Update Quantity]
        CartActions -->|Remove Item| RemoveItem[🗑️ Remove Item]
        CartActions -->|Checkout| Checkout[💳 Checkout]
        
        Checkout --> DeliveryInfo[📍 Delivery Information]
        DeliveryInfo --> PaymentInfo[💳 Payment Information]
        PaymentInfo --> OrderSummary[📋 Order Summary]
        OrderSummary --> PlaceOrder[✅ Place Order]
        PlaceOrder --> OrderConfirmation[📧 Order Confirmation]
        OrderConfirmation --> TrackOrder[📦 Track Order]
        
        SellProduct --> ProductInfo[📝 Product Information]
        ProductInfo --> ProductImages[📸 Upload Images]
        ProductImages --> ProductPricing[💰 Set Price]
        ProductPricing --> ProductListing[📋 Review Listing]
        ProductListing --> PublishProduct[✅ Publish Product]
        PublishProduct --> ManageListings[⚙️ Manage Listings]
    end
    
    subgraph "Learning Journey"
        LearningJourney --> LearningAction{Learning Action}
        
        LearningAction -->|Browse Courses| BrowseCourses[📚 Browse Courses]
        LearningAction -->|My Progress| ViewProgress[📊 View Progress]
        LearningAction -->|Certificates| ViewCertificates[🏆 View Certificates]
        
        BrowseCourses --> CourseCategories[📂 Course Categories]
        CourseCategories --> CourseList[📋 Course List]
        CourseList --> CourseDetails[📖 Course Details]
        CourseDetails --> EnrollCourse[✅ Enroll in Course]
        EnrollCourse --> StartLearning[🎓 Start Learning]
        
        StartLearning --> LessonContent[📝 Lesson Content]
        LessonContent --> LessonComplete[✅ Complete Lesson]
        LessonComplete --> NextLesson{Next Lesson?}
        NextLesson -->|Yes| LessonContent
        NextLesson -->|No| TakeQuiz[📝 Take Quiz]
        TakeQuiz --> QuizResults[📊 Quiz Results]
        QuizResults --> CourseComplete[🎓 Course Complete]
        CourseComplete --> Certificate[🏆 Receive Certificate]
    end
    
    subgraph "Profile & Settings Journey"
        ProfileJourney --> ProfileAction{Profile Action}
        
        ProfileAction -->|Edit Profile| EditProfile[✏️ Edit Profile]
        ProfileAction -->|Security Settings| SecuritySettings[🔒 Security Settings]
        ProfileAction -->|Notification Settings| NotificationSettings[🔔 Notification Settings]
        ProfileAction -->|Privacy Settings| PrivacySettings[🛡️ Privacy Settings]
        ProfileAction -->|Help & Support| HelpSupport[❓ Help & Support]
        
        EditProfile --> UpdateInfo[📝 Update Information]
        UpdateInfo --> UploadAvatar[📸 Upload Avatar]
        UploadAvatar --> SaveProfile[💾 Save Profile]
        
        SecuritySettings --> ChangePassword[🔑 Change Password]
        ChangePassword --> EnableBiometric[👆 Enable Biometric]
        EnableBiometric --> TwoFactorAuth[📱 Two-Factor Auth]
        
        NotificationSettings --> NotificationTypes[🔔 Notification Types]
        NotificationTypes --> SoundSettings[🔊 Sound Settings]
        SoundSettings --> QuietHours[🌙 Quiet Hours]
        
        HelpSupport --> FAQ[❓ FAQ]
        FAQ --> ContactSupport[📞 Contact Support]
        ContactSupport --> TicketCreated[🎫 Support Ticket]
    end
    
    subgraph "Notification Journey"
        NotificationJourney --> NotificationAction{Notification Action}
        
        NotificationAction -->|View All| ViewNotifications[📋 View All Notifications]
        NotificationAction -->|Mark as Read| MarkRead[✅ Mark as Read]
        NotificationAction -->|Settings| NotificationSettings
        
        ViewNotifications --> NotificationDetails[📖 Notification Details]
        NotificationDetails --> NotificationResponse{Response Required?}
        NotificationResponse -->|Yes| TakeAction[⚡ Take Action]
        NotificationResponse -->|No| MarkRead
        
        TakeAction --> ActionComplete[✅ Action Complete]
        ActionComplete --> UpdateNotification[🔄 Update Notification]
    end
    
    %% Return flows
    ChamaManagement --> Dashboard
    ChamaParticipation --> Dashboard
    WalletUpdated --> Dashboard
    PaymentComplete --> Dashboard
    TrackOrder --> Dashboard
    ManageListings --> Dashboard
    Certificate --> Dashboard
    SaveProfile --> Dashboard
    TicketCreated --> Dashboard
    UpdateNotification --> Dashboard
    
    %% Styling
    classDef onboarding fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef chama fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef financial fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef marketplace fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef learning fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef profile fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef notification fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef core fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    
    class Start,Download,Launch,Welcome,Register,Login,GuestMode,Dashboard onboarding
    class ChamaJourney,CreateChama,JoinChama,ViewChamas,ChamaManagement,ChamaParticipation chama
    class WalletJourney,SendMoney,AddMoney,MpesaPayment,PaymentComplete,WalletUpdated financial
    class MarketJourney,BrowseProducts,SellProduct,Checkout,TrackOrder,ManageListings marketplace
    class LearningJourney,BrowseCourses,StartLearning,Certificate learning
    class ProfileJourney,EditProfile,SecuritySettings,SaveProfile profile
    class NotificationJourney,ViewNotifications,UpdateNotification notification
    class MainFeatures,Choice core
