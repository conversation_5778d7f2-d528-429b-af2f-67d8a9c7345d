graph TB
    %% VaultKe System-wide Data Flow Diagram
    
    subgraph "Client Layer - Data Sources"
        UserInput[👤 User Input<br/>- Form Data<br/>- Touch Events<br/>- Voice Commands<br/>- Camera/Gallery]
        
        DeviceData[📱 Device Data<br/>- Location<br/>- Contacts<br/>- Biometric Data<br/>- Push Tokens]
        
        CachedData[💾 Cached Data<br/>- User Profile<br/>- App Settings<br/>- Offline Queue<br/>- Media Cache]
    end
    
    subgraph "Mobile App - Data Processing"
        UIComponents[🖼️ UI Components<br/>- Input Validation<br/>- Data Formatting<br/>- State Updates<br/>- Event Handling]
        
        StateManagement[🔄 State Management<br/>- Context API<br/>- Local State<br/>- Derived State<br/>- State Persistence]
        
        DataServices[🔧 Data Services<br/>- API Client<br/>- WebSocket Client<br/>- Storage Service<br/>- Sync Service]
    end
    
    subgraph "Network Layer - Data Transmission"
        HTTPRequests[🌐 HTTP Requests<br/>- REST API Calls<br/>- File Uploads<br/>- Authentication<br/>- Error Handling]
        
        WebSocketData[🔄 WebSocket Data<br/>- Real-time Updates<br/>- Chat Messages<br/>- Notifications<br/>- Live Events]
        
        PushNotifications[🔔 Push Notifications<br/>- Remote Notifications<br/>- Background Updates<br/>- Badge Updates<br/>- Sound Triggers]
    end
    
    subgraph "Backend API - Data Gateway"
        APIGateway[🚪 API Gateway<br/>- Request Routing<br/>- Rate Limiting<br/>- CORS Handling<br/>- Response Formatting]
        
        AuthMiddleware[🔐 Auth Middleware<br/>- Token Validation<br/>- User Context<br/>- Permission Check<br/>- Session Management]
        
        ValidationLayer[✅ Validation Layer<br/>- Input Sanitization<br/>- Schema Validation<br/>- Business Rules<br/>- Data Transformation]
    end
    
    subgraph "Service Layer - Business Logic"
        AuthService[🔐 Auth Service<br/>- User Authentication<br/>- Token Management<br/>- Session Handling<br/>- Password Security]
        
        UserService[👤 User Service<br/>- Profile Management<br/>- Preferences<br/>- Avatar Processing<br/>- Account Settings]
        
        ChamaService[👥 Chama Service<br/>- Group Management<br/>- Member Operations<br/>- Role Management<br/>- Activity Tracking]
        
        WalletService[💰 Wallet Service<br/>- Balance Management<br/>- Transaction Validation<br/>- Currency Handling<br/>- Account Linking]
        
        TransactionService[💳 Transaction Service<br/>- Payment Processing<br/>- Transfer Logic<br/>- Fee Calculation<br/>- Receipt Generation]
        
        NotificationService[🔔 Notification Service<br/>- Message Creation<br/>- Template Processing<br/>- Delivery Routing<br/>- Status Tracking]
        
        MarketplaceService[🛒 Marketplace Service<br/>- Product Management<br/>- Order Processing<br/>- Inventory Tracking<br/>- Review System]
        
        LearningService[📚 Learning Service<br/>- Course Management<br/>- Progress Tracking<br/>- Quiz Processing<br/>- Certificate Generation]
    end
    
    subgraph "Data Access Layer"
        RepositoryLayer[🗄️ Repository Layer<br/>- Data Abstraction<br/>- Query Building<br/>- Result Mapping<br/>- Connection Management]
        
        CacheLayer[⚡ Cache Layer<br/>- Redis Cache<br/>- Memory Cache<br/>- Query Cache<br/>- Session Cache]
        
        DatabaseConnections[🔌 DB Connections<br/>- Connection Pool<br/>- Transaction Management<br/>- Query Optimization<br/>- Error Handling]
    end
    
    subgraph "Data Storage"
        PrimaryDB[(🗄️ SQLite Database<br/>- User Data<br/>- Chama Data<br/>- Financial Data<br/>- Transaction Logs)]
        
        FileStorage[📁 File Storage<br/>- User Avatars<br/>- Product Images<br/>- Documents<br/>- Media Files]
        
        BackupStorage[☁️ Backup Storage<br/>- Google Drive<br/>- Database Backups<br/>- File Backups<br/>- Recovery Points]
    end
    
    subgraph "External Data Sources"
        MpesaAPI[💳 M-Pesa API<br/>- Payment Requests<br/>- Transaction Status<br/>- Account Balance<br/>- Callback Data]
        
        SMSGateway[📱 SMS Gateway<br/>- Message Delivery<br/>- Delivery Reports<br/>- Bulk Messaging<br/>- Status Updates]
        
        EmailService[📧 Email Service<br/>- Email Delivery<br/>- Template Rendering<br/>- Bounce Handling<br/>- Analytics Data]
        
        PushService[🔔 Push Service<br/>- Notification Delivery<br/>- Device Registration<br/>- Delivery Analytics<br/>- Platform Routing]
    end
    
    subgraph "Real-time Data Flow"
        WebSocketServer[🔄 WebSocket Server<br/>- Connection Management<br/>- Message Broadcasting<br/>- Event Distribution<br/>- Client Synchronization]
        
        EventBus[📡 Event Bus<br/>- Event Publishing<br/>- Event Subscription<br/>- Event Routing<br/>- Event Persistence]
        
        RealtimeUpdates[⚡ Real-time Updates<br/>- Live Notifications<br/>- Chat Messages<br/>- Balance Updates<br/>- Status Changes]
    end
    
    %% Data Flow Connections
    
    %% Client to Mobile App
    UserInput --> UIComponents
    DeviceData --> UIComponents
    CachedData --> StateManagement
    UIComponents --> StateManagement
    StateManagement --> DataServices
    
    %% Mobile App to Network
    DataServices --> HTTPRequests
    DataServices --> WebSocketData
    DataServices <--> CachedData
    
    %% Network to Backend
    HTTPRequests --> APIGateway
    WebSocketData --> WebSocketServer
    PushNotifications --> DataServices
    
    %% Backend Processing
    APIGateway --> AuthMiddleware
    AuthMiddleware --> ValidationLayer
    ValidationLayer --> AuthService
    ValidationLayer --> UserService
    ValidationLayer --> ChamaService
    ValidationLayer --> WalletService
    ValidationLayer --> TransactionService
    ValidationLayer --> NotificationService
    ValidationLayer --> MarketplaceService
    ValidationLayer --> LearningService
    
    %% Service to Data Access
    AuthService --> RepositoryLayer
    UserService --> RepositoryLayer
    ChamaService --> RepositoryLayer
    WalletService --> RepositoryLayer
    TransactionService --> RepositoryLayer
    NotificationService --> RepositoryLayer
    MarketplaceService --> RepositoryLayer
    LearningService --> RepositoryLayer
    
    %% Data Access to Storage
    RepositoryLayer --> DatabaseConnections
    RepositoryLayer --> CacheLayer
    DatabaseConnections --> PrimaryDB
    UserService --> FileStorage
    MarketplaceService --> FileStorage
    
    %% Backup Flow
    PrimaryDB --> BackupStorage
    FileStorage --> BackupStorage
    
    %% External Service Integration
    TransactionService --> MpesaAPI
    NotificationService --> SMSGateway
    NotificationService --> EmailService
    NotificationService --> PushService
    
    %% Real-time Data Flow
    WebSocketServer --> RealtimeUpdates
    NotificationService --> EventBus
    TransactionService --> EventBus
    ChamaService --> EventBus
    EventBus --> WebSocketServer
    RealtimeUpdates --> WebSocketData
    
    %% Cache Flow
    CacheLayer <--> RepositoryLayer
    CacheLayer <--> UserService
    CacheLayer <--> ChamaService
    
    %% Response Flow (Data flowing back)
    PrimaryDB -.-> RepositoryLayer
    RepositoryLayer -.-> AuthService
    RepositoryLayer -.-> UserService
    RepositoryLayer -.-> ChamaService
    RepositoryLayer -.-> WalletService
    RepositoryLayer -.-> TransactionService
    RepositoryLayer -.-> NotificationService
    RepositoryLayer -.-> MarketplaceService
    RepositoryLayer -.-> LearningService
    
    AuthService -.-> ValidationLayer
    UserService -.-> ValidationLayer
    ChamaService -.-> ValidationLayer
    WalletService -.-> ValidationLayer
    TransactionService -.-> ValidationLayer
    NotificationService -.-> ValidationLayer
    MarketplaceService -.-> ValidationLayer
    LearningService -.-> ValidationLayer
    
    ValidationLayer -.-> APIGateway
    APIGateway -.-> HTTPRequests
    HTTPRequests -.-> DataServices
    DataServices -.-> StateManagement
    StateManagement -.-> UIComponents
    
    %% External Service Responses
    MpesaAPI -.-> TransactionService
    SMSGateway -.-> NotificationService
    EmailService -.-> NotificationService
    PushService -.-> PushNotifications
    
    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef mobile fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef network fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef backend fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef service fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef data fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef external fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef realtime fill:#fafafa,stroke:#424242,stroke-width:2px
    
    class UserInput,DeviceData,CachedData client
    class UIComponents,StateManagement,DataServices mobile
    class HTTPRequests,WebSocketData,PushNotifications network
    class APIGateway,AuthMiddleware,ValidationLayer backend
    class AuthService,UserService,ChamaService,WalletService,TransactionService,NotificationService,MarketplaceService,LearningService service
    class RepositoryLayer,CacheLayer,DatabaseConnections data
    class PrimaryDB,FileStorage,BackupStorage storage
    class MpesaAPI,SMSGateway,EmailService,PushService external
    class WebSocketServer,EventBus,RealtimeUpdates realtime
