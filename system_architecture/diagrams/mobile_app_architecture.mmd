graph TB
    %% VaultKe Mobile App Architecture
    
    subgraph "App Entry Point"
        App[📱 App.js<br/>- App Provider<br/>- Navigation Container<br/>- Theme Provider<br/>- Error Boundary]
    end
    
    subgraph "Context Providers"
        AppContext[🌐 App Context<br/>- Global State<br/>- User Data<br/>- Settings<br/>- Cache Management]
        
        AuthContext[🔐 Auth Context<br/>- Authentication State<br/>- Token Management<br/>- User Session<br/>- Login/Logout]
        
        ThemeContext[🎨 Theme Context<br/>- Light/Dark Mode<br/>- Color Schemes<br/>- Typography<br/>- Spacing]
        
        NotificationContext[🔔 Notification Context<br/>- Push Tokens<br/>- Notification State<br/>- Sound Preferences<br/>- Badge Count]
    end
    
    subgraph "Navigation Structure"
        MainNavigator[🧭 Main Navigator<br/>- Authentication Check<br/>- Route Protection<br/>- Deep Linking]
        
        AuthStack[🔐 Auth Stack<br/>- Login Screen<br/>- Register Screen<br/>- Forgot Password<br/>- Reset Password]
        
        AppTabs[📑 App Tabs<br/>- Bottom Tab Navigator<br/>- Dashboard<br/>- Chamas<br/>- Wallet<br/>- Profile]
        
        ChamaStack[👥 Chama Stack<br/>- Chama List<br/>- Chama Details<br/>- Member Management<br/>- Meetings]
        
        WalletStack[💰 Wallet Stack<br/>- Wallet Overview<br/>- Transactions<br/>- Transfer Money<br/>- Transaction History]
        
        MarketStack[🛒 Market Stack<br/>- Product List<br/>- Product Details<br/>- Cart<br/>- Orders]
        
        LearningStack[📚 Learning Stack<br/>- Course Categories<br/>- Course List<br/>- Course Details<br/>- Progress]
    end
    
    subgraph "Screen Components"
        subgraph "Authentication Screens"
            LoginScreen[🔑 Login Screen<br/>- Email/Phone Input<br/>- Password Input<br/>- Biometric Login<br/>- Social Login]
            
            RegisterScreen[📝 Register Screen<br/>- User Details Form<br/>- Validation<br/>- Terms & Conditions<br/>- Email Verification]
        end
        
        subgraph "Main App Screens"
            DashboardScreen[🏠 Dashboard<br/>- Quick Stats<br/>- Recent Activity<br/>- Quick Actions<br/>- Notifications]
            
            ChamaListScreen[👥 Chama List<br/>- User's Chamas<br/>- Search & Filter<br/>- Create New<br/>- Join Requests]
            
            ChamaDetailsScreen[📊 Chama Details<br/>- Member List<br/>- Financial Summary<br/>- Meetings<br/>- Activities]
            
            WalletScreen[💰 Wallet<br/>- Balance Display<br/>- Quick Transfer<br/>- Transaction List<br/>- M-Pesa Integration]
            
            ProfileScreen[👤 Profile<br/>- User Information<br/>- Settings<br/>- Preferences<br/>- Security]
        end
        
        subgraph "Feature Screens"
            ProductListScreen[🛒 Products<br/>- Product Grid<br/>- Search & Filter<br/>- Categories<br/>- Wishlist]
            
            CartScreen[🛍️ Cart<br/>- Cart Items<br/>- Quantity Control<br/>- Checkout<br/>- Payment]
            
            CourseScreen[📚 Courses<br/>- Course Content<br/>- Video Player<br/>- Progress Tracking<br/>- Quizzes]
            
            NotificationScreen[🔔 Notifications<br/>- Notification List<br/>- Mark as Read<br/>- Settings<br/>- Sound Picker]
        end
    end
    
    subgraph "Shared Components"
        UIComponents[🧩 UI Components<br/>- Custom Button<br/>- Input Fields<br/>- Cards<br/>- Modals<br/>- Loading States]
        
        FormComponents[📝 Form Components<br/>- Validation<br/>- Error Handling<br/>- Field Types<br/>- Submission]
        
        MediaComponents[🖼️ Media Components<br/>- Image Viewer<br/>- Video Player<br/>- Audio Player<br/>- File Picker]
        
        NavigationComponents[🧭 Navigation<br/>- Header Components<br/>- Tab Bar<br/>- Drawer Menu<br/>- Back Button]
    end
    
    subgraph "Services Layer"
        APIService[🔧 API Service<br/>- HTTP Client<br/>- Request/Response<br/>- Error Handling<br/>- Token Management]
        
        AuthService[🔐 Auth Service<br/>- Login/Logout<br/>- Token Refresh<br/>- Biometric Auth<br/>- Session Management]
        
        StorageService[💾 Storage Service<br/>- AsyncStorage<br/>- Secure Storage<br/>- Cache Management<br/>- Data Persistence]
        
        NotificationService[🔔 Notification Service<br/>- Push Registration<br/>- Local Notifications<br/>- Sound Management<br/>- Badge Updates]
        
        WebSocketService[🔄 WebSocket Service<br/>- Real-time Connection<br/>- Message Handling<br/>- Reconnection Logic<br/>- Event Broadcasting]
    end
    
    subgraph "Utility Layer"
        Utils[🛠️ Utilities<br/>- Date Formatting<br/>- Currency Formatting<br/>- Validation Helpers<br/>- Constants]
        
        Helpers[🤝 Helpers<br/>- API Helpers<br/>- Navigation Helpers<br/>- Form Helpers<br/>- Error Helpers]
        
        Hooks[🎣 Custom Hooks<br/>- useAPI<br/>- useAuth<br/>- useNotifications<br/>- useWebSocket]
        
        Constants[📋 Constants<br/>- API Endpoints<br/>- Colors<br/>- Dimensions<br/>- Configuration]
    end
    
    subgraph "Device Integration"
        DeviceFeatures[📱 Device Features<br/>- Camera Access<br/>- Gallery Access<br/>- Contacts<br/>- Location Services]
        
        PushNotifications[🔔 Push Notifications<br/>- Expo Push<br/>- Token Registration<br/>- Background Handling<br/>- User Interaction]
        
        BiometricAuth[👆 Biometric Auth<br/>- Fingerprint<br/>- Face ID<br/>- Touch ID<br/>- Fallback PIN]
        
        FileSystem[📁 File System<br/>- File Upload<br/>- Image Processing<br/>- Document Handling<br/>- Cache Management]
    end
    
    %% App Flow
    App --> AppContext
    App --> AuthContext
    App --> ThemeContext
    App --> NotificationContext
    App --> MainNavigator
    
    %% Navigation Flow
    MainNavigator --> AuthStack
    MainNavigator --> AppTabs
    AppTabs --> ChamaStack
    AppTabs --> WalletStack
    AppTabs --> MarketStack
    AppTabs --> LearningStack
    
    %% Screen Connections
    AuthStack --> LoginScreen
    AuthStack --> RegisterScreen
    AppTabs --> DashboardScreen
    ChamaStack --> ChamaListScreen
    ChamaStack --> ChamaDetailsScreen
    WalletStack --> WalletScreen
    MarketStack --> ProductListScreen
    MarketStack --> CartScreen
    LearningStack --> CourseScreen
    AppTabs --> NotificationScreen
    AppTabs --> ProfileScreen
    
    %% Component Usage
    LoginScreen --> UIComponents
    RegisterScreen --> FormComponents
    DashboardScreen --> UIComponents
    ChamaDetailsScreen --> MediaComponents
    WalletScreen --> UIComponents
    ProfileScreen --> FormComponents
    
    %% Service Integration
    LoginScreen --> AuthService
    RegisterScreen --> APIService
    DashboardScreen --> APIService
    ChamaDetailsScreen --> WebSocketService
    WalletScreen --> APIService
    NotificationScreen --> NotificationService
    
    %% Context Usage
    AuthService --> AuthContext
    APIService --> AppContext
    NotificationService --> NotificationContext
    UIComponents --> ThemeContext
    
    %% Utility Usage
    APIService --> Utils
    FormComponents --> Helpers
    AuthService --> Hooks
    UIComponents --> Constants
    
    %% Device Integration
    AuthService --> BiometricAuth
    NotificationService --> PushNotifications
    ProfileScreen --> DeviceFeatures
    MediaComponents --> FileSystem
    
    %% Storage Integration
    AuthService --> StorageService
    AppContext --> StorageService
    NotificationService --> StorageService
    
    %% Styling
    classDef entry fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef context fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef navigation fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef screen fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef component fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef service fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef utility fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef device fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    
    class App entry
    class AppContext,AuthContext,ThemeContext,NotificationContext context
    class MainNavigator,AuthStack,AppTabs,ChamaStack,WalletStack,MarketStack,LearningStack navigation
    class LoginScreen,RegisterScreen,DashboardScreen,ChamaListScreen,ChamaDetailsScreen,WalletScreen,ProfileScreen,ProductListScreen,CartScreen,CourseScreen,NotificationScreen screen
    class UIComponents,FormComponents,MediaComponents,NavigationComponents component
    class APIService,AuthService,StorageService,NotificationService,WebSocketService service
    class Utils,Helpers,Hooks,Constants utility
    class DeviceFeatures,PushNotifications,BiometricAuth,FileSystem device
