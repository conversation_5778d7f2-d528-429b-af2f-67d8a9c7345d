sequenceDiagram
    participant Buyer as 👤 Buyer
    participant BuyerApp as 📱 Buyer App
    participant API as 🔧 Backend API
    participant MarketSvc as 🛒 Marketplace Service
    participant OrderSvc as 📦 Order Service
    participant WalletSvc as 💰 Wallet Service
    participant NotifSvc as 🔔 Notification Service
    participant DB as 🗄️ Database
    participant SellerApp as 📱 Seller App
    participant Seller as 👥 Seller
    participant DeliveryApp as 🚚 Delivery App
    participant DeliveryPerson as 🚴 Delivery Person
    
    %% Product Discovery Flow
    Note over Buyer, DeliveryPerson: Product Discovery & Browsing
    
    Buyer->>BuyerApp: Open Marketplace
    BuyerApp->>API: GET /api/v1/products?category=electronics
    API->>MarketSvc: Get products by category
    MarketSvc->>DB: Query products with filters
    Note right of DB: SELECT * FROM products<br/>WHERE category = 'electronics'<br/>AND status = 'active'<br/>ORDER BY created_at DESC
    
    DB-->>MarketSvc: Product list
    MarketSvc->>MarketSvc: Format product data
    MarketSvc-->>API: Products with seller info
    API-->>BuyerApp: 200 OK + product list
    BuyerApp->>BuyerApp: Display product grid
    BuyerApp-->>Buyer: Show available products
    
    %% Product Details & Reviews
    Buyer->>BuyerApp: Tap on product
    BuyerApp->>API: GET /api/v1/products/{id}
    API->>MarketSvc: Get product details
    MarketSvc->>DB: Get product with reviews
    Note right of DB: SELECT p.*, u.first_name, u.last_name,<br/>AVG(r.rating) as avg_rating,<br/>COUNT(r.id) as review_count<br/>FROM products p<br/>JOIN users u ON p.seller_id = u.id<br/>LEFT JOIN product_reviews r ON p.id = r.product_id<br/>WHERE p.id = 'product123'
    
    DB-->>MarketSvc: Product details + reviews
    MarketSvc-->>API: Complete product info
    API-->>BuyerApp: 200 OK + product details
    BuyerApp->>BuyerApp: Display product details
    BuyerApp-->>Buyer: Show product info & reviews
    
    %% Add to Cart Flow
    Buyer->>BuyerApp: Add to Cart (Quantity: 2)
    BuyerApp->>API: POST /api/v1/cart/add
    Note right of API: {<br/>  "product_id": "product123",<br/>  "quantity": 2<br/>}
    
    API->>MarketSvc: Add item to cart
    MarketSvc->>DB: Check product availability
    
    alt Product Available
        MarketSvc->>DB: Add/Update cart item
        Note right of DB: INSERT INTO cart_items<br/>(user_id, product_id, quantity)<br/>VALUES ('buyer123', 'product123', 2)<br/>ON CONFLICT UPDATE quantity
        
        DB-->>MarketSvc: Cart updated
        MarketSvc-->>API: Item added to cart
        API-->>BuyerApp: 200 OK + cart count
        BuyerApp->>BuyerApp: Update cart badge
        BuyerApp-->>Buyer: "Added to cart"
        
    else Product Out of Stock
        MarketSvc-->>API: Error: Out of stock
        API-->>BuyerApp: 400 Bad Request
        BuyerApp-->>Buyer: "Product out of stock"
    end
    
    %% Checkout Process
    Buyer->>BuyerApp: Go to Cart
    BuyerApp->>API: GET /api/v1/cart
    API->>MarketSvc: Get user cart
    MarketSvc->>DB: Get cart items with product details
    DB-->>MarketSvc: Cart items
    MarketSvc-->>API: Cart with totals
    API-->>BuyerApp: 200 OK + cart data
    BuyerApp-->>Buyer: Show cart items
    
    Buyer->>BuyerApp: Proceed to Checkout
    BuyerApp->>BuyerApp: Show checkout form
    Buyer->>BuyerApp: Enter delivery address
    Buyer->>BuyerApp: Select payment method
    Buyer->>BuyerApp: Confirm order
    
    BuyerApp->>API: POST /api/v1/orders
    Note right of API: {<br/>  "delivery_address": "123 Main St",<br/>  "delivery_contact": "0712345678",<br/>  "payment_method": "wallet",<br/>  "notes": "Call when arriving"<br/>}
    
    %% Order Processing
    API->>OrderSvc: Create order
    OrderSvc->>DB: BEGIN TRANSACTION
    
    %% Validate cart and create order
    OrderSvc->>DB: Get cart items
    OrderSvc->>DB: Validate product availability
    OrderSvc->>DB: Calculate totals
    OrderSvc->>DB: Create order record
    OrderSvc->>DB: Create order items
    
    %% Process payment
    OrderSvc->>WalletSvc: Process payment
    WalletSvc->>DB: Check buyer wallet balance
    
    alt Sufficient Balance
        WalletSvc->>DB: Debit buyer wallet
        WalletSvc->>DB: Create transaction record
        WalletSvc-->>OrderSvc: Payment successful
        
        OrderSvc->>DB: Update order status to 'paid'
        OrderSvc->>DB: Clear cart items
        OrderSvc->>DB: Update product stock
        OrderSvc->>DB: COMMIT TRANSACTION
        
        OrderSvc-->>API: Order created successfully
        API-->>BuyerApp: 201 Created + order details
        BuyerApp-->>Buyer: "Order placed successfully!"
        
        %% Notify seller
        OrderSvc->>NotifSvc: Send new order notification
        NotifSvc->>NotifSvc: Create seller notification
        NotifSvc-->>SellerApp: Real-time notification
        SellerApp-->>Seller: "New order received!"
        
    else Insufficient Balance
        WalletSvc-->>OrderSvc: Payment failed
        OrderSvc->>DB: ROLLBACK TRANSACTION
        OrderSvc-->>API: Error: Insufficient funds
        API-->>BuyerApp: 400 Bad Request
        BuyerApp-->>Buyer: "Insufficient wallet balance"
    end
    
    %% Order Fulfillment by Seller
    Note over Buyer, DeliveryPerson: Order Fulfillment Process
    
    Seller->>SellerApp: View new order
    SellerApp->>API: GET /api/v1/orders/{id}
    API->>OrderSvc: Get order details
    OrderSvc->>DB: Get order with items
    DB-->>OrderSvc: Order details
    OrderSvc-->>API: Complete order info
    API-->>SellerApp: 200 OK + order details
    SellerApp-->>Seller: Show order details
    
    Seller->>SellerApp: Accept order
    SellerApp->>API: PUT /api/v1/orders/{id}/status
    Note right of API: {<br/>  "status": "accepted",<br/>  "estimated_prep_time": 30<br/>}
    
    API->>OrderSvc: Update order status
    OrderSvc->>DB: Update order status
    OrderSvc->>NotifSvc: Send status update
    NotifSvc-->>BuyerApp: Real-time notification
    BuyerApp-->>Buyer: "Order accepted by seller"
    
    %% Seller prepares order
    Seller->>SellerApp: Mark as "Preparing"
    SellerApp->>API: PUT /api/v1/orders/{id}/status
    API->>OrderSvc: Update to preparing
    OrderSvc->>NotifSvc: Send update
    NotifSvc-->>BuyerApp: "Order is being prepared"
    
    %% Ready for pickup/delivery
    Seller->>SellerApp: Mark as "Ready for Pickup"
    SellerApp->>API: PUT /api/v1/orders/{id}/status
    API->>OrderSvc: Update to ready
    OrderSvc->>NotifSvc: Notify delivery person
    NotifSvc-->>DeliveryApp: "Order ready for pickup"
    NotifSvc-->>BuyerApp: "Order ready for delivery"
    
    %% Delivery Assignment
    DeliveryPerson->>DeliveryApp: Accept delivery
    DeliveryApp->>API: POST /api/v1/orders/{id}/assign-delivery
    Note right of API: {<br/>  "delivery_person_id": "delivery123"<br/>}
    
    API->>OrderSvc: Assign delivery person
    OrderSvc->>DB: Update order with delivery person
    OrderSvc->>NotifSvc: Send assignment notifications
    NotifSvc-->>BuyerApp: "Delivery person assigned"
    NotifSvc-->>SellerApp: "Delivery person assigned"
    
    %% Pickup from seller
    DeliveryPerson->>DeliveryApp: Arrived at seller
    DeliveryApp->>API: PUT /api/v1/orders/{id}/pickup-arrived
    API->>OrderSvc: Update status
    OrderSvc->>NotifSvc: Send notifications
    NotifSvc-->>SellerApp: "Delivery person arrived"
    
    DeliveryPerson->>DeliveryApp: Picked up order
    DeliveryApp->>API: PUT /api/v1/orders/{id}/picked-up
    API->>OrderSvc: Update to picked up
    OrderSvc->>NotifSvc: Send notifications
    NotifSvc-->>BuyerApp: "Order picked up, on the way"
    NotifSvc-->>SellerApp: "Order picked up"
    
    %% Delivery to buyer
    DeliveryPerson->>DeliveryApp: Arrived at buyer
    DeliveryApp->>API: PUT /api/v1/orders/{id}/delivery-arrived
    API->>OrderSvc: Update status
    OrderSvc->>NotifSvc: Send notification
    NotifSvc-->>BuyerApp: "Delivery person arrived"
    
    DeliveryPerson->>DeliveryApp: Delivered order
    DeliveryApp->>API: PUT /api/v1/orders/{id}/delivered
    Note right of API: {<br/>  "status": "delivered",<br/>  "delivery_proof": "photo_url",<br/>  "delivered_at": "2023-12-25T15:30:00Z"<br/>}
    
    %% Complete transaction
    API->>OrderSvc: Mark as delivered
    OrderSvc->>DB: BEGIN TRANSACTION
    OrderSvc->>DB: Update order status
    
    %% Release payment to seller (minus platform fee)
    OrderSvc->>WalletSvc: Transfer to seller
    WalletSvc->>DB: Credit seller wallet (90% of amount)
    WalletSvc->>DB: Credit platform wallet (10% fee)
    WalletSvc->>DB: Credit delivery person wallet (delivery fee)
    
    OrderSvc->>DB: COMMIT TRANSACTION
    OrderSvc->>NotifSvc: Send completion notifications
    NotifSvc-->>BuyerApp: "Order delivered successfully"
    NotifSvc-->>SellerApp: "Payment received"
    NotifSvc-->>DeliveryApp: "Delivery fee credited"
    
    %% Post-delivery actions
    BuyerApp-->>Buyer: "Rate your experience"
    Buyer->>BuyerApp: Submit rating & review
    BuyerApp->>API: POST /api/v1/products/{id}/review
    Note right of API: {<br/>  "rating": 5,<br/>  "comment": "Great product, fast delivery!",<br/>  "order_id": "order123"<br/>}
    
    API->>MarketSvc: Add product review
    MarketSvc->>DB: Store review
    MarketSvc->>NotifSvc: Notify seller of review
    NotifSvc-->>SellerApp: "New review received"
    
    %% Order tracking throughout process
    Note over Buyer, DeliveryPerson: Real-time Order Tracking
    
    loop Order Status Updates
        OrderSvc->>NotifSvc: Status change event
        NotifSvc->>NotifSvc: Create status notification
        NotifSvc-->>BuyerApp: WebSocket: order_status_update
        BuyerApp->>BuyerApp: Update order tracking UI
        BuyerApp-->>Buyer: Show current status
    end
    
    %% Error Handling Scenarios
    Note over Buyer, DeliveryPerson: Error Handling
    
    alt Order Cancellation by Buyer
        Buyer->>BuyerApp: Cancel order
        BuyerApp->>API: PUT /api/v1/orders/{id}/cancel
        API->>OrderSvc: Process cancellation
        
        alt Order not yet accepted
            OrderSvc->>WalletSvc: Refund full amount
            OrderSvc->>DB: Update order status to cancelled
            OrderSvc->>NotifSvc: Send cancellation notifications
        else Order already in progress
            OrderSvc-->>API: Error: Cannot cancel
            API-->>BuyerApp: 400 Bad Request
            BuyerApp-->>Buyer: "Order cannot be cancelled"
        end
    end
    
    alt Seller out of stock
        Seller->>SellerApp: Mark item unavailable
        SellerApp->>API: PUT /api/v1/orders/{id}/cancel
        API->>OrderSvc: Cancel due to unavailability
        OrderSvc->>WalletSvc: Process refund
        OrderSvc->>NotifSvc: Send notifications
        NotifSvc-->>BuyerApp: "Order cancelled - item unavailable"
    end
    
    alt Delivery issues
        DeliveryPerson->>DeliveryApp: Report delivery problem
        DeliveryApp->>API: POST /api/v1/orders/{id}/delivery-issue
        API->>OrderSvc: Handle delivery issue
        OrderSvc->>NotifSvc: Escalate to support
        NotifSvc-->>BuyerApp: "Delivery issue reported"
        NotifSvc-->>SellerApp: "Delivery issue with order"
    end
