graph TB
    %% VaultKe Frontend Component Hierarchy
    
    subgraph "App Root"
        App[📱 App.js<br/>- Error Boundary<br/>- App State Provider<br/>- Navigation Container<br/>- Theme Provider]
    end
    
    subgraph "Context Providers"
        AppProvider[🌐 AppProvider<br/>- Global App State<br/>- User Data<br/>- Settings<br/>- Cache Management]
        
        AuthProvider[🔐 AuthProvider<br/>- Authentication State<br/>- Token Management<br/>- User Session<br/>- Login Status]
        
        ThemeProvider[🎨 ThemeProvider<br/>- Light/Dark Mode<br/>- Color Schemes<br/>- Typography<br/>- Component Styles]
        
        NotificationProvider[🔔 NotificationProvider<br/>- Push Token<br/>- Notification State<br/>- Sound Settings<br/>- Badge Count]
        
        WebSocketProvider[🔄 WebSocketProvider<br/>- Connection State<br/>- Message Handling<br/>- Reconnection Logic<br/>- Event Broadcasting]
    end
    
    subgraph "Navigation Components"
        MainNavigator[🧭 MainNavigator<br/>- Route Protection<br/>- Deep Linking<br/>- Navigation State<br/>- Screen Transitions]
        
        AuthNavigator[🔐 AuthNavigator<br/>- Login Flow<br/>- Registration Flow<br/>- Password Reset<br/>- Onboarding]
        
        AppNavigator[📑 AppNavigator<br/>- Tab Navigation<br/>- Stack Navigation<br/>- Drawer Navigation<br/>- Modal Navigation]
        
        TabNavigator[📑 TabNavigator<br/>- Dashboard Tab<br/>- Chamas Tab<br/>- Wallet Tab<br/>- Profile Tab<br/>- More Tab]
    end
    
    subgraph "Screen Components"
        subgraph "Authentication Screens"
            LoginScreen[🔑 LoginScreen<br/>- Login Form<br/>- Biometric Login<br/>- Social Login<br/>- Forgot Password Link]
            
            RegisterScreen[📝 RegisterScreen<br/>- Registration Form<br/>- Field Validation<br/>- Terms Acceptance<br/>- Email Verification]
            
            ForgotPasswordScreen[🔒 ForgotPasswordScreen<br/>- Email Input<br/>- Reset Request<br/>- Verification Code<br/>- New Password]
            
            OnboardingScreen[👋 OnboardingScreen<br/>- Welcome Slides<br/>- Feature Introduction<br/>- Permission Requests<br/>- Setup Wizard]
        end
        
        subgraph "Main App Screens"
            DashboardScreen[🏠 DashboardScreen<br/>- Quick Stats<br/>- Recent Activity<br/>- Quick Actions<br/>- Notifications Panel]
            
            ChamaListScreen[👥 ChamaListScreen<br/>- Chama Cards<br/>- Search Bar<br/>- Filter Options<br/>- Create Button]
            
            ChamaDetailsScreen[📊 ChamaDetailsScreen<br/>- Chama Info<br/>- Member List<br/>- Financial Summary<br/>- Action Buttons]
            
            WalletScreen[💰 WalletScreen<br/>- Balance Display<br/>- Quick Actions<br/>- Transaction List<br/>- Add Money Button]
            
            ProfileScreen[👤 ProfileScreen<br/>- User Info<br/>- Settings Menu<br/>- Security Options<br/>- Help & Support]
        end
        
        subgraph "Feature Screens"
            TransactionScreen[💳 TransactionScreen<br/>- Transfer Form<br/>- Recipient Selection<br/>- Amount Input<br/>- Confirmation]
            
            ProductListScreen[🛒 ProductListScreen<br/>- Product Grid<br/>- Search & Filter<br/>- Category Tabs<br/>- Sort Options]
            
            ProductDetailsScreen[📱 ProductDetailsScreen<br/>- Product Images<br/>- Description<br/>- Reviews<br/>- Add to Cart]
            
            CartScreen[🛍️ CartScreen<br/>- Cart Items<br/>- Quantity Controls<br/>- Total Calculation<br/>- Checkout Button]
            
            CourseScreen[📚 CourseScreen<br/>- Course Content<br/>- Video Player<br/>- Progress Bar<br/>- Quiz Section]
            
            NotificationScreen[🔔 NotificationScreen<br/>- Notification List<br/>- Read/Unread Status<br/>- Action Buttons<br/>- Settings Link]
        end
        
        subgraph "Modal Screens"
            CameraModal[📸 CameraModal<br/>- Camera View<br/>- Capture Button<br/>- Gallery Access<br/>- Image Preview]
            
            PaymentModal[💳 PaymentModal<br/>- Payment Methods<br/>- M-Pesa Integration<br/>- Card Input<br/>- Processing State]
            
            ConfirmationModal[✅ ConfirmationModal<br/>- Action Confirmation<br/>- Details Display<br/>- Confirm/Cancel<br/>- Loading State]
            
            SettingsModal[⚙️ SettingsModal<br/>- Settings Form<br/>- Toggle Switches<br/>- Save/Cancel<br/>- Validation]
        end
    end
    
    subgraph "Shared UI Components"
        subgraph "Layout Components"
            SafeAreaWrapper[🛡️ SafeAreaWrapper<br/>- Safe Area Handling<br/>- Status Bar Config<br/>- Keyboard Avoidance<br/>- Platform Specific]
            
            ScreenContainer[📱 ScreenContainer<br/>- Screen Layout<br/>- Background<br/>- Padding<br/>- Scroll Handling]
            
            Header[📋 Header<br/>- Title Display<br/>- Back Button<br/>- Action Buttons<br/>- Search Bar]
            
            TabBar[📑 TabBar<br/>- Tab Items<br/>- Active State<br/>- Badge Display<br/>- Icon & Label]
        end
        
        subgraph "Form Components"
            CustomInput[📝 CustomInput<br/>- Text Input<br/>- Validation<br/>- Error Display<br/>- Placeholder]
            
            CustomButton[🔘 CustomButton<br/>- Button Styles<br/>- Loading State<br/>- Disabled State<br/>- Icon Support]
            
            Dropdown[📋 Dropdown<br/>- Option List<br/>- Search Filter<br/>- Multi-select<br/>- Custom Render]
            
            DatePicker[📅 DatePicker<br/>- Date Selection<br/>- Time Selection<br/>- Range Selection<br/>- Validation]
            
            ImagePicker[🖼️ ImagePicker<br/>- Camera Access<br/>- Gallery Access<br/>- Image Crop<br/>- Multiple Selection]
        end
        
        subgraph "Display Components"
            Card[🃏 Card<br/>- Content Container<br/>- Shadow/Border<br/>- Padding<br/>- Touch Handling]
            
            Avatar[👤 Avatar<br/>- Image Display<br/>- Fallback Initials<br/>- Size Variants<br/>- Status Indicator]
            
            Badge[🔴 Badge<br/>- Count Display<br/>- Color Variants<br/>- Position Options<br/>- Animation]
            
            ProgressBar[📊 ProgressBar<br/>- Progress Display<br/>- Color Themes<br/>- Animation<br/>- Labels]
            
            LoadingSpinner[⏳ LoadingSpinner<br/>- Loading Animation<br/>- Size Variants<br/>- Color Options<br/>- Overlay]
        end
        
        subgraph "Interactive Components"
            SwipeableRow[👆 SwipeableRow<br/>- Swipe Actions<br/>- Left/Right Actions<br/>- Animation<br/>- Threshold]
            
            PullToRefresh[🔄 PullToRefresh<br/>- Pull Detection<br/>- Refresh Trigger<br/>- Loading State<br/>- Animation]
            
            InfiniteScroll[📜 InfiniteScroll<br/>- Scroll Detection<br/>- Load More<br/>- Loading State<br/>- End Detection]
            
            SearchBar[🔍 SearchBar<br/>- Search Input<br/>- Filter Options<br/>- Clear Button<br/>- Suggestions]
        end
        
        subgraph "Feedback Components"
            Toast[🍞 Toast<br/>- Message Display<br/>- Auto Dismiss<br/>- Position Options<br/>- Action Button]
            
            Alert[⚠️ Alert<br/>- Alert Dialog<br/>- Confirmation<br/>- Custom Actions<br/>- Styling]
            
            BottomSheet[📋 BottomSheet<br/>- Slide Up Panel<br/>- Drag Handle<br/>- Content Area<br/>- Backdrop]
            
            Tooltip[💬 Tooltip<br/>- Help Text<br/>- Position Aware<br/>- Auto Hide<br/>- Custom Trigger]
        end
    end
    
    subgraph "Custom Hooks"
        useAuth[🔐 useAuth<br/>- Auth State<br/>- Login/Logout<br/>- Token Refresh<br/>- User Data]
        
        useAPI[🔧 useAPI<br/>- API Calls<br/>- Loading State<br/>- Error Handling<br/>- Caching]
        
        useWebSocket[🔄 useWebSocket<br/>- Connection State<br/>- Message Handling<br/>- Reconnection<br/>- Event Listeners]
        
        useNotifications[🔔 useNotifications<br/>- Notification State<br/>- Push Registration<br/>- Local Notifications<br/>- Badge Management]
        
        useTheme[🎨 useTheme<br/>- Theme State<br/>- Color Access<br/>- Dark Mode Toggle<br/>- Style Helpers]
        
        useStorage[💾 useStorage<br/>- Local Storage<br/>- Secure Storage<br/>- Cache Management<br/>- Data Persistence]
    end
    
    subgraph "Utility Components"
        ErrorBoundary[🛡️ ErrorBoundary<br/>- Error Catching<br/>- Fallback UI<br/>- Error Reporting<br/>- Recovery Options]
        
        NetworkStatus[📶 NetworkStatus<br/>- Connection State<br/>- Offline Banner<br/>- Retry Logic<br/>- Queue Management]
        
        PermissionHandler[🔐 PermissionHandler<br/>- Permission Requests<br/>- Status Checking<br/>- Fallback Handling<br/>- User Guidance]
        
        BiometricAuth[👆 BiometricAuth<br/>- Biometric Check<br/>- Authentication<br/>- Fallback Options<br/>- Error Handling]
    end
    
    %% Component Hierarchy Connections
    App --> AppProvider
    App --> AuthProvider
    App --> ThemeProvider
    App --> NotificationProvider
    App --> WebSocketProvider
    App --> MainNavigator
    
    MainNavigator --> AuthNavigator
    MainNavigator --> AppNavigator
    
    AuthNavigator --> LoginScreen
    AuthNavigator --> RegisterScreen
    AuthNavigator --> ForgotPasswordScreen
    AuthNavigator --> OnboardingScreen
    
    AppNavigator --> TabNavigator
    TabNavigator --> DashboardScreen
    TabNavigator --> ChamaListScreen
    TabNavigator --> WalletScreen
    TabNavigator --> ProfileScreen
    
    %% Screen to Component Usage
    LoginScreen --> CustomInput
    LoginScreen --> CustomButton
    LoginScreen --> SafeAreaWrapper
    LoginScreen --> useAuth
    
    DashboardScreen --> Card
    DashboardScreen --> ProgressBar
    DashboardScreen --> PullToRefresh
    DashboardScreen --> useAPI
    
    ChamaListScreen --> SearchBar
    ChamaListScreen --> InfiniteScroll
    ChamaListScreen --> Card
    ChamaListScreen --> useAPI
    
    WalletScreen --> Avatar
    WalletScreen --> Badge
    WalletScreen --> SwipeableRow
    WalletScreen --> useWebSocket
    
    TransactionScreen --> PaymentModal
    TransactionScreen --> ConfirmationModal
    TransactionScreen --> CustomInput
    TransactionScreen --> useAPI
    
    %% Modal Usage
    CameraModal --> ImagePicker
    PaymentModal --> Dropdown
    ConfirmationModal --> CustomButton
    SettingsModal --> CustomInput
    
    %% Shared Component Dependencies
    Card --> ThemeProvider
    CustomButton --> LoadingSpinner
    SearchBar --> CustomInput
    Toast --> ThemeProvider
    
    %% Hook Usage
    useAuth --> AuthProvider
    useAPI --> AppProvider
    useWebSocket --> WebSocketProvider
    useNotifications --> NotificationProvider
    useTheme --> ThemeProvider
    useStorage --> AppProvider
    
    %% Utility Component Usage
    App --> ErrorBoundary
    App --> NetworkStatus
    LoginScreen --> BiometricAuth
    CameraModal --> PermissionHandler
    
    %% Styling
    classDef app fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef provider fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef navigation fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef screen fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef component fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef hook fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef utility fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    
    class App app
    class AppProvider,AuthProvider,ThemeProvider,NotificationProvider,WebSocketProvider provider
    class MainNavigator,AuthNavigator,AppNavigator,TabNavigator navigation
    class LoginScreen,RegisterScreen,DashboardScreen,ChamaListScreen,WalletScreen,ProfileScreen,TransactionScreen,ProductListScreen,CartScreen,CourseScreen,NotificationScreen screen
    class SafeAreaWrapper,Header,Card,Avatar,CustomInput,CustomButton,Toast,Alert,SwipeableRow,PullToRefresh component
    class useAuth,useAPI,useWebSocket,useNotifications,useTheme,useStorage hook
    class ErrorBoundary,NetworkStatus,PermissionHandler,BiometricAuth utility
