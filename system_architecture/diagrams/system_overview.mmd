graph TB
    %% VaultKe System Overview Architecture Diagram
    
    subgraph "Client Layer"
        Mobile[📱 React Native Mobile App<br/>- iOS/Android/Web<br/>- React Navigation<br/>- Context API<br/>- WebSocket Client]
        Web[🌐 Web Interface<br/>- Expo Web<br/>- Progressive Web App<br/>- Responsive Design]
    end
    
    subgraph "Network Layer"
        LB[⚖️ Load Balancer<br/>- HTTPS Termination<br/>- Request Routing<br/>- Health Checks]
        CDN[🌍 CDN<br/>- Static Assets<br/>- Image Optimization<br/>- Global Distribution]
    end
    
    subgraph "Application Layer"
        API[🔧 Go Backend API<br/>- Gin Framework<br/>- JWT Authentication<br/>- RESTful Endpoints<br/>- WebSocket Server]
        
        subgraph "Core Services"
            AuthSvc[🔐 Auth Service<br/>- JWT Management<br/>- User Sessions<br/>- Role-Based Access]
            UserSvc[👤 User Service<br/>- Profile Management<br/>- Preferences<br/>- Settings]
            ChamaSvc[👥 Chama Service<br/>- Group Management<br/>- Member Roles<br/>- Invitations]
            WalletSvc[💰 Wallet Service<br/>- Transactions<br/>- Balances<br/>- Transfers]
            NotifSvc[🔔 Notification Service<br/>- Real-time Alerts<br/>- Push Notifications<br/>- Email/SMS]
            MarketSvc[🛒 Marketplace Service<br/>- Product Management<br/>- Orders<br/>- Reviews]
            LearnSvc[📚 Learning Service<br/>- Courses<br/>- Progress Tracking<br/>- Quizzes]
        end
        
        subgraph "Infrastructure Services"
            FileSvc[📁 File Service<br/>- Upload/Download<br/>- Image Processing<br/>- Static Serving]
            EmailSvc[📧 Email Service<br/>- SMTP Integration<br/>- Template Engine<br/>- Delivery Tracking]
            WSSvc[🔄 WebSocket Service<br/>- Real-time Updates<br/>- Connection Management<br/>- Event Broadcasting]
        end
    end
    
    subgraph "Data Layer"
        DB[(🗄️ SQLite Database<br/>- 50+ Tables<br/>- ACID Compliance<br/>- Migration System<br/>- Backup Integration)]
        
        subgraph "Data Domains"
            UserData[👤 Users & Auth<br/>- Accounts<br/>- Sessions<br/>- Permissions]
            ChamaData[👥 Chamas<br/>- Groups<br/>- Members<br/>- Meetings]
            FinData[💰 Financial<br/>- Wallets<br/>- Transactions<br/>- Loans]
            MarketData[🛒 Marketplace<br/>- Products<br/>- Orders<br/>- Reviews]
            LearnData[📚 Learning<br/>- Courses<br/>- Progress<br/>- Results]
            NotifData[🔔 Notifications<br/>- Messages<br/>- Preferences<br/>- Delivery Log]
        end
    end
    
    subgraph "External Services"
        MPesa[💳 M-Pesa API<br/>- Mobile Payments<br/>- STK Push<br/>- Callbacks]
        GDrive[☁️ Google Drive<br/>- Data Backup<br/>- File Storage<br/>- Sync]
        SMTP[📧 SMTP Server<br/>- Email Delivery<br/>- Templates<br/>- Tracking]
        SMS[📱 Africa's Talking<br/>- SMS Notifications<br/>- Bulk Messaging<br/>- Delivery Reports]
        LiveKit[🎥 LiveKit<br/>- Video Meetings<br/>- Real-time Communication<br/>- Recording]
    end
    
    subgraph "Monitoring & Security"
        Monitor[📊 Monitoring<br/>- Health Checks<br/>- Performance Metrics<br/>- Error Tracking]
        Security[🛡️ Security<br/>- Rate Limiting<br/>- CORS<br/>- Input Validation]
        Backup[💾 Backup System<br/>- Automated Backups<br/>- Point-in-time Recovery<br/>- Cloud Storage]
    end
    
    %% Client Connections
    Mobile --> LB
    Web --> LB
    Mobile -.-> CDN
    Web -.-> CDN
    
    %% Network Layer
    LB --> API
    CDN --> FileSvc
    
    %% API to Services
    API --> AuthSvc
    API --> UserSvc
    API --> ChamaSvc
    API --> WalletSvc
    API --> NotifSvc
    API --> MarketSvc
    API --> LearnSvc
    API --> FileSvc
    API --> EmailSvc
    API --> WSSvc
    
    %% Services to Database
    AuthSvc --> DB
    UserSvc --> DB
    ChamaSvc --> DB
    WalletSvc --> DB
    NotifSvc --> DB
    MarketSvc --> DB
    LearnSvc --> DB
    
    %% Database Domains
    DB --> UserData
    DB --> ChamaData
    DB --> FinData
    DB --> MarketData
    DB --> LearnData
    DB --> NotifData
    
    %% External Service Connections
    WalletSvc --> MPesa
    UserSvc --> GDrive
    EmailSvc --> SMTP
    NotifSvc --> SMS
    ChamaSvc --> LiveKit
    
    %% Infrastructure
    API --> Monitor
    API --> Security
    DB --> Backup
    Backup --> GDrive
    
    %% Real-time Connections
    WSSvc -.-> Mobile
    WSSvc -.-> Web
    NotifSvc --> WSSvc
    
    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef network fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef api fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef infra fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    
    class Mobile,Web client
    class LB,CDN network
    class API api
    class AuthSvc,UserSvc,ChamaSvc,WalletSvc,NotifSvc,MarketSvc,LearnSvc,FileSvc,EmailSvc,WSSvc service
    class DB,UserData,ChamaData,FinData,MarketData,LearnData,NotifData data
    class MPesa,GDrive,SMTP,SMS,LiveKit external
    class Monitor,Security,Backup infra
