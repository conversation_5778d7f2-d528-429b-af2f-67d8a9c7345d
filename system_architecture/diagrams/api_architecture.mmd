graph TB
    %% VaultKe API Architecture Diagram
    
    subgraph "Client Applications"
        Mobile[📱 React Native App]
        Web[🌐 Web Application]
        Admin[⚙️ Admin Panel]
    end
    
    subgraph "API Gateway Layer"
        Gateway[🚪 API Gateway<br/>- Rate Limiting<br/>- CORS<br/>- Request Validation<br/>- Response Formatting]
        Auth[🔐 Authentication Middleware<br/>- JWT Validation<br/>- Token Refresh<br/>- Role Verification]
        Logger[📝 Request Logger<br/>- Request/Response Logging<br/>- Performance Metrics<br/>- Error Tracking]
    end
    
    subgraph "API Endpoints - /api/v1"
        
        subgraph "Authentication APIs"
            AuthAPI[🔑 /auth/*<br/>POST /login<br/>POST /register<br/>POST /refresh<br/>POST /logout<br/>POST /forgot-password<br/>POST /reset-password]
        end
        
        subgraph "User Management APIs"
            UserAPI[👤 /users/*<br/>GET /profile<br/>PUT /profile<br/>POST /avatar<br/>GET /preferences<br/>PUT /preferences<br/>DELETE /account]
        end
        
        subgraph "Chama Management APIs"
            ChamaAPI[👥 /chamas/*<br/>GET /<br/>POST /<br/>GET /:id<br/>PUT /:id<br/>DELETE /:id<br/>GET /:id/members<br/>POST /:id/invite<br/>POST /:id/join<br/>DELETE /:id/leave]
        end
        
        subgraph "Financial APIs"
            WalletAPI[💰 /wallets/*<br/>GET /<br/>GET /:id<br/>GET /:id/transactions<br/>POST /transfer<br/>GET /balance]
            
            TransactionAPI[💳 /transactions/*<br/>GET /<br/>POST /<br/>GET /:id<br/>GET /history<br/>POST /mpesa/stk<br/>POST /mpesa/callback]
            
            LoanAPI[🏦 /loans/*<br/>GET /<br/>POST /<br/>GET /:id<br/>PUT /:id/approve<br/>POST /:id/payment<br/>GET /:id/schedule]
        end
        
        subgraph "Marketplace APIs"
            ProductAPI[🛒 /products/*<br/>GET /<br/>POST /<br/>GET /:id<br/>PUT /:id<br/>DELETE /:id<br/>POST /:id/review<br/>GET /categories]
            
            OrderAPI[📦 /orders/*<br/>GET /<br/>POST /<br/>GET /:id<br/>PUT /:id/status<br/>GET /history]
            
            CartAPI[🛍️ /cart/*<br/>GET /<br/>POST /add<br/>PUT /update<br/>DELETE /remove<br/>DELETE /clear]
        end
        
        subgraph "Communication APIs"
            NotificationAPI[🔔 /notifications/*<br/>GET /<br/>GET /unread-count<br/>PUT /:id/read<br/>PUT /mark-all-read<br/>GET /preferences<br/>PUT /preferences]
            
            MeetingAPI[🎥 /meetings/*<br/>GET /<br/>POST /<br/>GET /:id<br/>PUT /:id<br/>POST /:id/join<br/>POST /:id/leave<br/>GET /:id/attendance]
            
            ChatAPI[💬 /chat/*<br/>GET /rooms<br/>POST /rooms<br/>GET /rooms/:id/messages<br/>POST /rooms/:id/messages<br/>WebSocket /ws]
        end
        
        subgraph "Learning APIs"
            LearningAPI[📚 /learning/*<br/>GET /categories<br/>GET /courses<br/>GET /courses/:id<br/>POST /courses/:id/progress<br/>GET /progress<br/>POST /quiz/submit]
        end
        
        subgraph "File Management APIs"
            FileAPI[📁 /files/*<br/>POST /upload<br/>GET /:id<br/>DELETE /:id<br/>GET /uploads/*<br/>Static File Serving]
        end
        
        subgraph "Admin APIs"
            AdminAPI[⚙️ /admin/*<br/>GET /users<br/>GET /chamas<br/>GET /transactions<br/>GET /analytics<br/>PUT /user/:id/status<br/>GET /system/health]
        end
    end
    
    subgraph "Service Layer"
        AuthService[🔐 Auth Service]
        UserService[👤 User Service]
        ChamaService[👥 Chama Service]
        WalletService[💰 Wallet Service]
        TransactionService[💳 Transaction Service]
        LoanService[🏦 Loan Service]
        ProductService[🛒 Product Service]
        OrderService[📦 Order Service]
        NotificationService[🔔 Notification Service]
        MeetingService[🎥 Meeting Service]
        ChatService[💬 Chat Service]
        LearningService[📚 Learning Service]
        FileService[📁 File Service]
        AdminService[⚙️ Admin Service]
    end
    
    subgraph "Data Access Layer"
        Database[(🗄️ SQLite Database)]
        FileStorage[📁 File Storage]
        Cache[⚡ In-Memory Cache]
    end
    
    subgraph "External Services"
        MPesa[💳 M-Pesa API]
        Email[📧 SMTP Server]
        SMS[📱 SMS Gateway]
        GoogleDrive[☁️ Google Drive]
        LiveKit[🎥 LiveKit]
    end
    
    subgraph "WebSocket Services"
        WSServer[🔄 WebSocket Server<br/>- Real-time Notifications<br/>- Chat Messages<br/>- Live Updates<br/>- Connection Management]
    end
    
    %% Client to Gateway
    Mobile --> Gateway
    Web --> Gateway
    Admin --> Gateway
    
    %% Gateway Flow
    Gateway --> Auth
    Auth --> Logger
    Logger --> AuthAPI
    Logger --> UserAPI
    Logger --> ChamaAPI
    Logger --> WalletAPI
    Logger --> TransactionAPI
    Logger --> LoanAPI
    Logger --> ProductAPI
    Logger --> OrderAPI
    Logger --> CartAPI
    Logger --> NotificationAPI
    Logger --> MeetingAPI
    Logger --> ChatAPI
    Logger --> LearningAPI
    Logger --> FileAPI
    Logger --> AdminAPI
    
    %% API to Services
    AuthAPI --> AuthService
    UserAPI --> UserService
    ChamaAPI --> ChamaService
    WalletAPI --> WalletService
    TransactionAPI --> TransactionService
    LoanAPI --> LoanService
    ProductAPI --> ProductService
    OrderAPI --> OrderService
    CartAPI --> ProductService
    NotificationAPI --> NotificationService
    MeetingAPI --> MeetingService
    ChatAPI --> ChatService
    LearningAPI --> LearningService
    FileAPI --> FileService
    AdminAPI --> AdminService
    
    %% Services to Data
    AuthService --> Database
    UserService --> Database
    ChamaService --> Database
    WalletService --> Database
    TransactionService --> Database
    LoanService --> Database
    ProductService --> Database
    OrderService --> Database
    NotificationService --> Database
    MeetingService --> Database
    ChatService --> Database
    LearningService --> Database
    FileService --> FileStorage
    AdminService --> Database
    
    %% Caching
    UserService --> Cache
    ChamaService --> Cache
    ProductService --> Cache
    
    %% External Integrations
    TransactionService --> MPesa
    NotificationService --> Email
    NotificationService --> SMS
    UserService --> GoogleDrive
    MeetingService --> LiveKit
    
    %% WebSocket Connections
    ChatAPI --> WSServer
    NotificationAPI --> WSServer
    WSServer -.-> Mobile
    WSServer -.-> Web
    WSServer -.-> Admin
    
    %% Real-time Updates
    NotificationService --> WSServer
    ChatService --> WSServer
    TransactionService --> WSServer
    
    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef api fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef websocket fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    
    class Mobile,Web,Admin client
    class Gateway,Auth,Logger gateway
    class AuthAPI,UserAPI,ChamaAPI,WalletAPI,TransactionAPI,LoanAPI,ProductAPI,OrderAPI,CartAPI,NotificationAPI,MeetingAPI,ChatAPI,LearningAPI,FileAPI,AdminAPI api
    class AuthService,UserService,ChamaService,WalletService,TransactionService,LoanService,ProductService,OrderService,NotificationService,MeetingService,ChatService,LearningService,FileService,AdminService service
    class Database,FileStorage,Cache data
    class MPesa,Email,SMS,GoogleDrive,LiveKit external
    class WSServer websocket
