sequenceDiagram
    participant User as 👤 User
    participant Mobile as 📱 Mobile App
    participant API as 🔧 Backend API
    participant WalletSvc as 💰 Wallet Service
    participant TransSvc as 💳 Transaction Service
    participant DB as 🗄️ Database
    participant MPesa as 📱 M-Pesa API
    participant NotifSvc as 🔔 Notification Service
    participant Recipient as 👥 Recipient
    
    %% Money Transfer Flow
    Note over User, Recipient: P2P Money Transfer Flow
    
    User->>Mobile: Initiate "Send Money"
    Mobile->>Mobile: Show transfer form
    User->>Mobile: Enter recipient & amount
    Mobile->>Mobile: Validate input locally
    
    Mobile->>API: POST /api/v1/transactions/transfer
    Note right of API: {<br/>  "to_user_id": "user123",<br/>  "amount": 1000,<br/>  "currency": "KES",<br/>  "description": "Lunch money"<br/>}
    
    API->>WalletSvc: Validate sender wallet
    WalletSvc->>DB: Get sender wallet balance
    DB-->>WalletSvc: Wallet balance: 5000 KES
    
    alt Insufficient Balance
        WalletSvc-->>API: Error: Insufficient funds
        API-->>Mobile: 400 Bad Request
        Mobile-->>User: Show error message
    else Sufficient Balance
        WalletSvc->>WalletSvc: Validate recipient wallet
        WalletSvc->>DB: Get recipient wallet
        DB-->>WalletSvc: Recipient wallet found
        
        WalletSvc->>TransSvc: Process transfer request
        TransSvc->>TransSvc: Calculate fees (if any)
        TransSvc->>TransSvc: Generate transaction ID
        
        %% Database Transaction
        TransSvc->>DB: BEGIN TRANSACTION
        TransSvc->>DB: Debit sender wallet (-1000)
        TransSvc->>DB: Credit recipient wallet (+1000)
        TransSvc->>DB: Create transaction record
        TransSvc->>DB: COMMIT TRANSACTION
        
        DB-->>TransSvc: Transaction successful
        TransSvc-->>API: Transfer completed
        API-->>Mobile: 200 OK + transaction details
        
        %% Notifications
        TransSvc->>NotifSvc: Send transfer notifications
        NotifSvc->>NotifSvc: Create sender notification
        NotifSvc->>NotifSvc: Create recipient notification
        NotifSvc-->>Mobile: Real-time notification (sender)
        NotifSvc-->>Recipient: Real-time notification (recipient)
        
        Mobile-->>User: Transfer successful
    end
    
    %% M-Pesa Deposit Flow
    Note over User, MPesa: M-Pesa Deposit Flow
    
    User->>Mobile: Initiate "Add Money"
    Mobile->>Mobile: Show payment methods
    User->>Mobile: Select M-Pesa
    User->>Mobile: Enter amount (2000 KES)
    
    Mobile->>API: POST /api/v1/transactions/mpesa/deposit
    Note right of API: {<br/>  "amount": 2000,<br/>  "phone": "************",<br/>  "account_reference": "VaultKe"<br/>}
    
    API->>TransSvc: Process M-Pesa deposit
    TransSvc->>TransSvc: Generate transaction reference
    TransSvc->>DB: Create pending transaction
    
    TransSvc->>MPesa: STK Push request
    Note right of MPesa: {<br/>  "BusinessShortCode": "174379",<br/>  "Password": "encrypted",<br/>  "Timestamp": "**************",<br/>  "TransactionType": "CustomerPayBillOnline",<br/>  "Amount": 2000,<br/>  "PartyA": "************",<br/>  "PartyB": "174379",<br/>  "PhoneNumber": "************",<br/>  "CallBackURL": "https://api.vaultke.com/mpesa/callback",<br/>  "AccountReference": "VaultKe",<br/>  "TransactionDesc": "Wallet Deposit"<br/>}
    
    MPesa-->>TransSvc: STK Push response
    Note left of MPesa: {<br/>  "MerchantRequestID": "req123",<br/>  "CheckoutRequestID": "chk456",<br/>  "ResponseCode": "0",<br/>  "ResponseDescription": "Success"<br/>}
    
    TransSvc->>DB: Update transaction with M-Pesa IDs
    TransSvc-->>API: STK Push initiated
    API-->>Mobile: 200 OK + checkout request ID
    Mobile-->>User: Check phone for M-Pesa prompt
    
    %% User completes M-Pesa payment on phone
    User->>User: Enter M-Pesa PIN on phone
    
    %% M-Pesa Callback
    MPesa->>API: POST /api/v1/transactions/mpesa/callback
    Note right of API: {<br/>  "Body": {<br/>    "stkCallback": {<br/>      "MerchantRequestID": "req123",<br/>      "CheckoutRequestID": "chk456",<br/>      "ResultCode": 0,<br/>      "ResultDesc": "Success",<br/>      "CallbackMetadata": {<br/>        "Item": [<br/>          {"Name": "Amount", "Value": 2000},<br/>          {"Name": "MpesaReceiptNumber", "Value": "QGR7KLMN8P"},<br/>          {"Name": "TransactionDate", "Value": 20231225120530},<br/>          {"Name": "PhoneNumber", "Value": ************}<br/>        ]<br/>      }<br/>    }<br/>  }<br/>}
    
    API->>TransSvc: Process M-Pesa callback
    TransSvc->>DB: Find transaction by checkout ID
    
    alt Payment Successful (ResultCode = 0)
        TransSvc->>DB: BEGIN TRANSACTION
        TransSvc->>DB: Update transaction status to 'completed'
        TransSvc->>DB: Credit user wallet (+2000)
        TransSvc->>DB: Store M-Pesa receipt number
        TransSvc->>DB: COMMIT TRANSACTION
        
        TransSvc->>NotifSvc: Send deposit success notification
        NotifSvc-->>Mobile: Real-time notification
        Mobile-->>User: Deposit successful notification
        
    else Payment Failed (ResultCode != 0)
        TransSvc->>DB: Update transaction status to 'failed'
        TransSvc->>NotifSvc: Send deposit failure notification
        NotifSvc-->>Mobile: Real-time notification
        Mobile-->>User: Deposit failed notification
    end
    
    API-->>MPesa: 200 OK (acknowledge callback)
    
    %% Chama Contribution Flow
    Note over User, Recipient: Chama Contribution Flow
    
    User->>Mobile: Navigate to Chama
    Mobile->>Mobile: Show chama details
    User->>Mobile: Click "Contribute"
    Mobile->>Mobile: Show contribution form
    User->>Mobile: Enter contribution amount
    
    Mobile->>API: POST /api/v1/chamas/{id}/contribute
    Note right of API: {<br/>  "amount": 5000,<br/>  "contribution_type": "monthly",<br/>  "description": "December contribution"<br/>}
    
    API->>WalletSvc: Validate user wallet balance
    WalletSvc->>DB: Check user wallet balance
    
    alt Sufficient Balance
        API->>TransSvc: Process chama contribution
        TransSvc->>DB: BEGIN TRANSACTION
        TransSvc->>DB: Debit user wallet (-5000)
        TransSvc->>DB: Credit chama wallet (+5000)
        TransSvc->>DB: Create contribution record
        TransSvc->>DB: Update chama statistics
        TransSvc->>DB: COMMIT TRANSACTION
        
        TransSvc->>NotifSvc: Send contribution notifications
        NotifSvc->>NotifSvc: Notify chama members
        NotifSvc->>NotifSvc: Notify chama admins
        NotifSvc-->>Mobile: Real-time notification
        NotifSvc-->>Recipient: Notify other members
        
        API-->>Mobile: 200 OK + contribution details
        Mobile-->>User: Contribution successful
        
    else Insufficient Balance
        WalletSvc-->>API: Error: Insufficient funds
        API-->>Mobile: 400 Bad Request
        Mobile-->>User: Show insufficient funds error
        Mobile->>Mobile: Suggest adding money
    end
    
    %% Transaction History Query
    Note over User, DB: Transaction History Query
    
    User->>Mobile: View "Transaction History"
    Mobile->>API: GET /api/v1/transactions?limit=20&offset=0
    
    API->>TransSvc: Get user transactions
    TransSvc->>DB: Query user transactions
    Note right of DB: SELECT * FROM transactions<br/>WHERE user_id = 'user123'<br/>ORDER BY created_at DESC<br/>LIMIT 20 OFFSET 0
    
    DB-->>TransSvc: Transaction list
    TransSvc->>TransSvc: Format transaction data
    TransSvc-->>API: Formatted transactions
    API-->>Mobile: 200 OK + transactions
    Mobile->>Mobile: Display transaction list
    Mobile-->>User: Show transaction history
    
    %% Real-time Balance Updates
    Note over User, Recipient: Real-time Balance Updates
    
    TransSvc->>NotifSvc: Wallet balance changed
    NotifSvc->>NotifSvc: Create balance update event
    NotifSvc->>Mobile: WebSocket: balance_updated
    Mobile->>Mobile: Update wallet balance in UI
    Mobile-->>User: Show updated balance
    
    %% Error Handling Flow
    Note over User, DB: Error Handling & Recovery
    
    alt Database Transaction Fails
        TransSvc->>DB: BEGIN TRANSACTION
        TransSvc->>DB: Debit sender wallet
        DB-->>TransSvc: Error: Constraint violation
        TransSvc->>DB: ROLLBACK TRANSACTION
        TransSvc-->>API: Error: Transaction failed
        API-->>Mobile: 500 Internal Server Error
        Mobile-->>User: Transaction failed, please try again
        
        %% Retry mechanism
        Mobile->>Mobile: Show retry option
        User->>Mobile: Retry transaction
        Mobile->>API: Retry same request with idempotency key
    end
    
    alt Network Timeout
        Mobile->>API: Transaction request
        Note over API: Request timeout
        Mobile->>Mobile: Show network error
        Mobile->>Mobile: Queue for retry when online
        Mobile-->>User: Transaction queued for retry
    end
