graph TB
    %% VaultKe Enhanced Notification System Architecture
    
    subgraph "Trigger Sources"
        UserAction[👤 User Actions<br/>- Profile Updates<br/>- Settings Changes<br/>- Manual Triggers]
        ChamaEvents[👥 Chama Events<br/>- Member Joins/Leaves<br/>- Meetings Scheduled<br/>- Contributions Due]
        FinancialEvents[💰 Financial Events<br/>- Transactions<br/>- Loan Approvals<br/>- Payment Reminders]
        SystemEvents[⚙️ System Events<br/>- Maintenance<br/>- Updates<br/>- Security Alerts]
        ScheduledEvents[⏰ Scheduled Events<br/>- Recurring Reminders<br/>- Meeting Notifications<br/>- Payment Due Dates]
    end
    
    subgraph "Notification Engine"
        EventProcessor[🔄 Event Processor<br/>- Event Classification<br/>- Priority Assignment<br/>- Template Selection<br/>- User Preference Check]
        
        TemplateEngine[📝 Template Engine<br/>- Dynamic Content<br/>- Variable Substitution<br/>- Multi-language Support<br/>- Personalization]
        
        PreferenceFilter[🎛️ Preference Filter<br/>- User Settings Check<br/>- Quiet Hours Respect<br/>- Channel Preferences<br/>- Frequency Control]
        
        PriorityQueue[📋 Priority Queue<br/>- Urgent: Immediate<br/>- High: Within 5min<br/>- Normal: Batched<br/>- Low: Daily Digest]
    end
    
    subgraph "Delivery Channels"
        InAppNotif[📱 In-App Notifications<br/>- Real-time WebSocket<br/>- Badge Counts<br/>- Sound & Vibration<br/>- Rich Content]
        
        PushNotif[🔔 Push Notifications<br/>- Expo Push Service<br/>- iOS APNs<br/>- Android FCM<br/>- Background Delivery]
        
        EmailNotif[📧 Email Notifications<br/>- SMTP Delivery<br/>- HTML Templates<br/>- Attachment Support<br/>- Delivery Tracking]
        
        SMSNotif[📱 SMS Notifications<br/>- Africa's Talking<br/>- Bulk Messaging<br/>- Delivery Reports<br/>- Fallback Channel]
    end
    
    subgraph "Database Layer"
        NotificationDB[(🗄️ Notifications Table<br/>- Message Content<br/>- Delivery Status<br/>- Read Status<br/>- Metadata)]
        
        PreferencesDB[(⚙️ User Preferences<br/>- Channel Settings<br/>- Sound Preferences<br/>- Quiet Hours<br/>- Frequency Settings)]
        
        TemplatesDB[(📝 Templates<br/>- Message Templates<br/>- Variable Definitions<br/>- Multi-language<br/>- Category Mapping)]
        
        SoundsDB[(🔊 Notification Sounds<br/>- Audio Files<br/>- Default Settings<br/>- User Selections<br/>- File Metadata)]
        
        DeliveryLogDB[(📊 Delivery Log<br/>- Delivery Attempts<br/>- Success/Failure<br/>- Retry Counts<br/>- Performance Metrics)]
        
        RemindersDB[(⏰ User Reminders<br/>- Personal Reminders<br/>- Recurring Events<br/>- Snooze Settings<br/>- Completion Status)]
    end
    
    subgraph "External Services"
        ExpoService[📲 Expo Push Service<br/>- Push Token Management<br/>- Delivery Optimization<br/>- Platform Abstraction]
        
        SMTPServer[📧 SMTP Server<br/>- Email Delivery<br/>- Template Rendering<br/>- Bounce Handling]
        
        SMSGateway[📱 SMS Gateway<br/>- Africa's Talking API<br/>- Bulk SMS<br/>- Delivery Reports]
    end
    
    subgraph "Mobile App Integration"
        NotifReceiver[📱 Notification Receiver<br/>- Push Token Registration<br/>- Background Handling<br/>- User Interaction]
        
        SoundManager[🔊 Sound Manager<br/>- Audio Playback<br/>- Volume Control<br/>- Custom Sounds]
        
        BadgeManager[🔴 Badge Manager<br/>- Unread Count<br/>- App Icon Badge<br/>- Real-time Updates]
        
        UIComponents[🖼️ UI Components<br/>- Notification List<br/>- Settings Screen<br/>- Sound Picker]
    end
    
    subgraph "WebSocket Real-time"
        WSServer[🔄 WebSocket Server<br/>- Connection Management<br/>- Real-time Delivery<br/>- Connection Recovery]
        
        WSClient[📱 WebSocket Client<br/>- Auto Reconnection<br/>- Message Queuing<br/>- Offline Handling]
    end
    
    %% Event Flow
    UserAction --> EventProcessor
    ChamaEvents --> EventProcessor
    FinancialEvents --> EventProcessor
    SystemEvents --> EventProcessor
    ScheduledEvents --> EventProcessor
    
    %% Processing Flow
    EventProcessor --> TemplateEngine
    TemplateEngine --> PreferenceFilter
    PreferenceFilter --> PriorityQueue
    
    %% Database Interactions
    EventProcessor --> NotificationDB
    EventProcessor --> TemplatesDB
    PreferenceFilter --> PreferencesDB
    TemplateEngine --> TemplatesDB
    
    %% Delivery Flow
    PriorityQueue --> InAppNotif
    PriorityQueue --> PushNotif
    PriorityQueue --> EmailNotif
    PriorityQueue --> SMSNotif
    
    %% In-App Delivery
    InAppNotif --> WSServer
    WSServer --> WSClient
    WSClient --> NotifReceiver
    InAppNotif --> SoundsDB
    
    %% Push Notification Flow
    PushNotif --> ExpoService
    ExpoService --> NotifReceiver
    NotifReceiver --> SoundManager
    NotifReceiver --> BadgeManager
    
    %% Email Flow
    EmailNotif --> SMTPServer
    SMTPServer --> DeliveryLogDB
    
    %% SMS Flow
    SMSNotif --> SMSGateway
    SMSGateway --> DeliveryLogDB
    
    %% UI Integration
    NotifReceiver --> UIComponents
    SoundManager --> SoundsDB
    BadgeManager --> NotificationDB
    UIComponents --> PreferencesDB
    UIComponents --> RemindersDB
    
    %% Delivery Tracking
    InAppNotif --> DeliveryLogDB
    PushNotif --> DeliveryLogDB
    EmailNotif --> DeliveryLogDB
    SMSNotif --> DeliveryLogDB
    
    %% Feedback Loop
    DeliveryLogDB --> EventProcessor
    NotificationDB --> BadgeManager
    
    %% Styling
    classDef trigger fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef engine fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef delivery fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef external fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef mobile fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef websocket fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    
    class UserAction,ChamaEvents,FinancialEvents,SystemEvents,ScheduledEvents trigger
    class EventProcessor,TemplateEngine,PreferenceFilter,PriorityQueue engine
    class InAppNotif,PushNotif,EmailNotif,SMSNotif delivery
    class NotificationDB,PreferencesDB,TemplatesDB,SoundsDB,DeliveryLogDB,RemindersDB database
    class ExpoService,SMTPServer,SMSGateway external
    class NotifReceiver,SoundManager,BadgeManager,UIComponents mobile
    class WSServer,WSClient websocket
