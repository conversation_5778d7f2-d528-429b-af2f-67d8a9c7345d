sequenceDiagram
    participant User as 👤 User
    participant Mobile as 📱 Mobile App
    participant API as 🔧 Go Backend API
    participant Auth as 🔐 Auth Service
    participant DB as 🗄️ Database
    participant JWT as 🎫 JWT Service
    
    %% Registration Flow
    Note over User, JWT: User Registration Flow
    User->>Mobile: Enter registration details
    Mobile->>API: POST /api/v1/auth/register
    API->>Auth: Validate registration data
    Auth->>DB: Check email/phone uniqueness
    DB-->>Auth: Validation result
    
    alt Email/Phone already exists
        Auth-->>API: Error: User already exists
        API-->>Mobile: 409 Conflict
        Mobile-->>User: Show error message
    else Registration successful
        Auth->>DB: Create user record
        DB-->>Auth: User created
        Auth->>JWT: Generate access & refresh tokens
        JWT-->>Auth: Return tokens
        Auth-->>API: Registration successful + tokens
        API-->>Mobile: 201 Created + tokens
        Mobile->>Mobile: Store tokens securely
        Mobile-->>User: Registration successful
    end
    
    %% Login Flow
    Note over User, JWT: User Login Flow
    User->>Mobile: Enter login credentials
    Mobile->>API: POST /api/v1/auth/login
    API->>Auth: Validate credentials
    Auth->>DB: Query user by email/phone
    DB-->>Auth: User record
    
    alt Invalid credentials
        Auth-->>API: Error: Invalid credentials
        API-->>Mobile: 401 Unauthorized
        Mobile-->>User: Show error message
    else Valid credentials
        Auth->>Auth: Verify password hash
        Auth->>JWT: Generate access & refresh tokens
        JWT-->>Auth: Return tokens
        Auth->>DB: Update last login timestamp
        Auth-->>API: Login successful + tokens
        API-->>Mobile: 200 OK + tokens + user data
        Mobile->>Mobile: Store tokens securely
        Mobile->>Mobile: Update user context
        Mobile-->>User: Login successful
    end
    
    %% Authenticated Request Flow
    Note over User, JWT: Authenticated API Request
    User->>Mobile: Perform action requiring auth
    Mobile->>Mobile: Get stored access token
    Mobile->>API: API Request + Authorization header
    API->>Auth: Validate JWT token
    Auth->>JWT: Verify token signature & expiry
    
    alt Token invalid/expired
        JWT-->>Auth: Token invalid
        Auth-->>API: 401 Unauthorized
        API-->>Mobile: 401 Unauthorized
        Mobile->>Mobile: Attempt token refresh
        
        %% Token Refresh Flow
        Note over Mobile, JWT: Token Refresh Flow
        Mobile->>Mobile: Get stored refresh token
        Mobile->>API: POST /api/v1/auth/refresh
        API->>Auth: Validate refresh token
        Auth->>JWT: Verify refresh token
        
        alt Refresh token invalid/expired
            JWT-->>Auth: Refresh token invalid
            Auth-->>API: 401 Unauthorized
            API-->>Mobile: 401 Unauthorized
            Mobile->>Mobile: Clear stored tokens
            Mobile-->>User: Redirect to login
        else Refresh token valid
            JWT->>JWT: Generate new access token
            JWT-->>Auth: New access token
            Auth-->>API: 200 OK + new access token
            API-->>Mobile: New access token
            Mobile->>Mobile: Store new access token
            Mobile->>API: Retry original request
        end
        
    else Token valid
        JWT-->>Auth: Token valid + user claims
        Auth-->>API: User authenticated
        API->>API: Process request
        API-->>Mobile: Response data
        Mobile-->>User: Show result
    end
    
    %% Password Reset Flow
    Note over User, JWT: Password Reset Flow
    User->>Mobile: Request password reset
    Mobile->>API: POST /api/v1/auth/forgot-password
    API->>Auth: Generate reset token
    Auth->>DB: Store reset token with expiry
    Auth->>Auth: Send reset email/SMS
    Auth-->>API: Reset email sent
    API-->>Mobile: 200 OK
    Mobile-->>User: Check email/SMS for reset link
    
    User->>Mobile: Enter reset token + new password
    Mobile->>API: POST /api/v1/auth/reset-password
    API->>Auth: Validate reset token
    Auth->>DB: Check token validity & expiry
    
    alt Token invalid/expired
        DB-->>Auth: Token invalid
        Auth-->>API: Error: Invalid token
        API-->>Mobile: 400 Bad Request
        Mobile-->>User: Show error message
    else Token valid
        DB-->>Auth: Token valid
        Auth->>Auth: Hash new password
        Auth->>DB: Update user password
        Auth->>DB: Invalidate reset token
        Auth-->>API: Password updated
        API-->>Mobile: 200 OK
        Mobile-->>User: Password reset successful
    end
    
    %% Logout Flow
    Note over User, JWT: User Logout Flow
    User->>Mobile: Logout request
    Mobile->>API: POST /api/v1/auth/logout
    API->>Auth: Invalidate refresh token
    Auth->>DB: Remove/blacklist refresh token
    Auth-->>API: Logout successful
    API-->>Mobile: 200 OK
    Mobile->>Mobile: Clear stored tokens
    Mobile->>Mobile: Clear user context
    Mobile-->>User: Logout successful
    
    %% Multi-device Session Management
    Note over User, JWT: Multi-device Session Management
    User->>Mobile: Login on new device
    Mobile->>API: POST /api/v1/auth/login
    API->>Auth: Generate new session
    Auth->>DB: Store new refresh token
    Note over DB: Multiple refresh tokens<br/>per user (one per device)
    
    User->>Mobile: Logout from all devices
    Mobile->>API: POST /api/v1/auth/logout-all
    API->>Auth: Invalidate all user sessions
    Auth->>DB: Remove all user refresh tokens
    Auth-->>API: All sessions invalidated
    API-->>Mobile: 200 OK
    Mobile-->>User: Logged out from all devices
    
    %% Role-based Access Control
    Note over User, JWT: Role-based Access Control
    User->>Mobile: Access admin feature
    Mobile->>API: API Request + Authorization header
    API->>Auth: Validate token + check permissions
    Auth->>JWT: Extract user role from token
    
    alt Insufficient permissions
        JWT-->>Auth: User role insufficient
        Auth-->>API: 403 Forbidden
        API-->>Mobile: 403 Forbidden
        Mobile-->>User: Access denied
    else Sufficient permissions
        JWT-->>Auth: User authorized
        Auth-->>API: Access granted
        API->>API: Process admin request
        API-->>Mobile: Response data
        Mobile-->>User: Show admin data
    end
