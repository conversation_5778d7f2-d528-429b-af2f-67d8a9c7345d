graph TB
    %% VaultKe Error Handling & Recovery Flow
    
    subgraph "Error Sources"
        UserError[👤 User Errors<br/>- Invalid Input<br/>- Validation Failures<br/>- Business Rule Violations<br/>- Permission Denied]
        
        NetworkError[🌐 Network Errors<br/>- Connection Timeout<br/>- DNS Resolution<br/>- SSL/TLS Errors<br/>- Server Unreachable]
        
        ServerError[🔧 Server Errors<br/>- 500 Internal Error<br/>- Database Errors<br/>- Service Unavailable<br/>- Rate Limiting]
        
        ClientError[📱 Client Errors<br/>- JavaScript Errors<br/>- Memory Issues<br/>- Storage Full<br/>- Permission Denied]
        
        ExternalError[🔌 External Service Errors<br/>- M-Pesa API Errors<br/>- SMS Gateway Errors<br/>- Email Service Errors<br/>- Push Service Errors]
    end
    
    subgraph "Error Detection Layer"
        ErrorBoundary[🛡️ Error Boundary<br/>- React Error Catching<br/>- Component Error Isolation<br/>- Fallback UI Rendering<br/>- Error Reporting]
        
        APIErrorHandler[🔧 API Error Handler<br/>- HTTP Status Codes<br/>- Response Validation<br/>- Timeout Detection<br/>- Retry Logic]
        
        NetworkMonitor[📶 Network Monitor<br/>- Connection State<br/>- Offline Detection<br/>- Quality Assessment<br/>- Reconnection Events]
        
        ValidationEngine[✅ Validation Engine<br/>- Input Validation<br/>- Schema Validation<br/>- Business Rules<br/>- Data Integrity]
    end
    
    subgraph "Error Classification"
        ErrorClassifier[🏷️ Error Classifier<br/>- Error Type Detection<br/>- Severity Assessment<br/>- Recovery Strategy<br/>- User Impact Analysis]
        
        ErrorCategories{Error Categories}
        
        ErrorCategories -->|Critical| CriticalErrors[🚨 Critical Errors<br/>- App Crashes<br/>- Data Corruption<br/>- Security Breaches<br/>- Payment Failures]
        
        ErrorCategories -->|High| HighErrors[⚠️ High Priority<br/>- Feature Unavailable<br/>- Sync Failures<br/>- Authentication Issues<br/>- Transaction Errors]
        
        ErrorCategories -->|Medium| MediumErrors[⚡ Medium Priority<br/>- UI Glitches<br/>- Slow Performance<br/>- Minor Validation<br/>- Cosmetic Issues]
        
        ErrorCategories -->|Low| LowErrors[ℹ️ Low Priority<br/>- Informational<br/>- Warnings<br/>- Suggestions<br/>- Tips]
    end
    
    subgraph "Error Recovery Strategies"
        AutoRecovery[🔄 Automatic Recovery<br/>- Retry Mechanisms<br/>- Fallback Options<br/>- Cache Usage<br/>- Graceful Degradation]
        
        UserRecovery[👤 User-Assisted Recovery<br/>- Manual Retry<br/>- Alternative Actions<br/>- Input Correction<br/>- Help Guidance]
        
        SystemRecovery[⚙️ System Recovery<br/>- Service Restart<br/>- Connection Reset<br/>- Cache Clear<br/>- State Reset]
        
        EscalationRecovery[📞 Escalation Recovery<br/>- Support Contact<br/>- Bug Reporting<br/>- Manual Intervention<br/>- Rollback Procedures]
    end
    
    subgraph "Frontend Error Handling"
        ComponentError[🧩 Component Error<br/>- Error Boundary Catch<br/>- Fallback UI Display<br/>- Error State Management<br/>- Recovery Actions]
        
        APIError[🔧 API Error<br/>- Response Error Check<br/>- Status Code Handling<br/>- Error Message Display<br/>- Retry Options]
        
        ValidationError[✅ Validation Error<br/>- Field Validation<br/>- Form Error Display<br/>- Input Highlighting<br/>- Correction Guidance]
        
        NetworkErrorUI[📶 Network Error<br/>- Offline Banner<br/>- Retry Button<br/>- Queue Management<br/>- Sync Status]
    end
    
    subgraph "Backend Error Handling"
        MiddlewareError[🔧 Middleware Error<br/>- Request Validation<br/>- Authentication Errors<br/>- Rate Limiting<br/>- CORS Errors]
        
        ServiceError[⚙️ Service Error<br/>- Business Logic Errors<br/>- Database Errors<br/>- External API Errors<br/>- Transaction Failures]
        
        DatabaseError[🗄️ Database Error<br/>- Connection Errors<br/>- Query Errors<br/>- Constraint Violations<br/>- Transaction Rollback]
        
        ExternalAPIError[🔌 External API Error<br/>- M-Pesa Errors<br/>- SMS Errors<br/>- Email Errors<br/>- Timeout Handling]
    end
    
    subgraph "Error Response Flow"
        ErrorResponse[📤 Error Response<br/>- Standardized Format<br/>- Error Codes<br/>- User Messages<br/>- Debug Information]
        
        ErrorLogging[📝 Error Logging<br/>- Structured Logging<br/>- Error Context<br/>- Stack Traces<br/>- User Actions]
        
        ErrorMetrics[📊 Error Metrics<br/>- Error Rate Tracking<br/>- Performance Impact<br/>- User Experience<br/>- Trend Analysis]
        
        ErrorAlerts[🚨 Error Alerts<br/>- Critical Error Alerts<br/>- Threshold Monitoring<br/>- Team Notifications<br/>- Escalation Rules]
    end
    
    subgraph "Specific Error Scenarios"
        subgraph "Authentication Errors"
            TokenExpired[🔐 Token Expired<br/>- Detect Expired Token<br/>- Attempt Refresh<br/>- Redirect to Login<br/>- Clear User Data]
            
            InvalidCredentials[🚫 Invalid Credentials<br/>- Show Error Message<br/>- Highlight Fields<br/>- Suggest Recovery<br/>- Track Attempts]
            
            BiometricFailed[👆 Biometric Failed<br/>- Fallback to Password<br/>- Show Alternative<br/>- Guide User<br/>- Security Check]
        end
        
        subgraph "Transaction Errors"
            InsufficientFunds[💰 Insufficient Funds<br/>- Check Balance<br/>- Show Available Amount<br/>- Suggest Add Money<br/>- Alternative Payment]
            
            PaymentFailed[💳 Payment Failed<br/>- Show Error Reason<br/>- Retry Option<br/>- Alternative Method<br/>- Contact Support]
            
            TransactionTimeout[⏱️ Transaction Timeout<br/>- Check Status<br/>- Show Pending State<br/>- Retry Option<br/>- Status Updates]
        end
        
        subgraph "Network Errors"
            OfflineMode[📴 Offline Mode<br/>- Detect Offline<br/>- Show Offline Banner<br/>- Queue Actions<br/>- Sync When Online]
            
            SlowConnection[🐌 Slow Connection<br/>- Show Loading State<br/>- Optimize Requests<br/>- Reduce Data<br/>- User Feedback]
            
            ConnectionLost[📡 Connection Lost<br/>- Detect Disconnection<br/>- Attempt Reconnection<br/>- Show Status<br/>- Resume Operations]
        end
        
        subgraph "Data Errors"
            DataCorruption[🗄️ Data Corruption<br/>- Detect Corruption<br/>- Restore from Backup<br/>- Validate Data<br/>- User Notification]
            
            SyncConflict[🔄 Sync Conflict<br/>- Detect Conflicts<br/>- Show Options<br/>- User Resolution<br/>- Merge Strategy]
            
            StorageFull[💾 Storage Full<br/>- Check Storage<br/>- Clear Cache<br/>- User Guidance<br/>- Alternative Storage]
        end
    end
    
    subgraph "Recovery Actions"
        RetryMechanism[🔄 Retry Mechanism<br/>- Exponential Backoff<br/>- Max Retry Limit<br/>- Success Tracking<br/>- Failure Handling]
        
        FallbackOptions[🔀 Fallback Options<br/>- Alternative Endpoints<br/>- Cached Data<br/>- Offline Mode<br/>- Reduced Functionality]
        
        UserGuidance[👥 User Guidance<br/>- Clear Instructions<br/>- Step-by-step Help<br/>- Visual Indicators<br/>- Support Links]
        
        DataRecovery[💾 Data Recovery<br/>- Backup Restoration<br/>- Partial Recovery<br/>- Data Validation<br/>- User Confirmation]
    end
    
    subgraph "Monitoring & Analytics"
        ErrorTracking[📊 Error Tracking<br/>- Error Aggregation<br/>- Trend Analysis<br/>- Impact Assessment<br/>- Root Cause Analysis]
        
        UserImpact[👤 User Impact<br/>- Affected Users<br/>- Feature Usage<br/>- Success Rates<br/>- User Satisfaction]
        
        PerformanceImpact[⚡ Performance Impact<br/>- Response Times<br/>- Resource Usage<br/>- System Load<br/>- Scalability Issues]
        
        BusinessImpact[💼 Business Impact<br/>- Revenue Impact<br/>- User Retention<br/>- Feature Adoption<br/>- Support Costs]
    end
    
    %% Error Flow Connections
    UserError --> ErrorBoundary
    NetworkError --> NetworkMonitor
    ServerError --> APIErrorHandler
    ClientError --> ErrorBoundary
    ExternalError --> APIErrorHandler
    
    ErrorBoundary --> ErrorClassifier
    APIErrorHandler --> ErrorClassifier
    NetworkMonitor --> ErrorClassifier
    ValidationEngine --> ErrorClassifier
    
    ErrorClassifier --> ErrorCategories
    
    CriticalErrors --> EscalationRecovery
    HighErrors --> SystemRecovery
    MediumErrors --> UserRecovery
    LowErrors --> AutoRecovery
    
    AutoRecovery --> RetryMechanism
    UserRecovery --> UserGuidance
    SystemRecovery --> FallbackOptions
    EscalationRecovery --> DataRecovery
    
    %% Frontend Error Handling
    ComponentError --> ErrorResponse
    APIError --> ErrorResponse
    ValidationError --> ErrorResponse
    NetworkErrorUI --> ErrorResponse
    
    %% Backend Error Handling
    MiddlewareError --> ErrorLogging
    ServiceError --> ErrorLogging
    DatabaseError --> ErrorLogging
    ExternalAPIError --> ErrorLogging
    
    %% Response and Monitoring
    ErrorResponse --> ErrorLogging
    ErrorLogging --> ErrorMetrics
    ErrorMetrics --> ErrorAlerts
    ErrorAlerts --> ErrorTracking
    
    %% Specific Scenarios
    TokenExpired --> AutoRecovery
    InvalidCredentials --> UserRecovery
    InsufficientFunds --> UserGuidance
    PaymentFailed --> RetryMechanism
    OfflineMode --> FallbackOptions
    DataCorruption --> DataRecovery
    
    %% Analytics
    ErrorTracking --> UserImpact
    ErrorTracking --> PerformanceImpact
    ErrorTracking --> BusinessImpact
    
    %% Styling
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef detection fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef classification fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef recovery fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef frontend fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef backend fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef response fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef scenario fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef action fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef monitoring fill:#fafafa,stroke:#424242,stroke-width:2px
    
    class UserError,NetworkError,ServerError,ClientError,ExternalError error
    class ErrorBoundary,APIErrorHandler,NetworkMonitor,ValidationEngine detection
    class ErrorClassifier,ErrorCategories,CriticalErrors,HighErrors,MediumErrors,LowErrors classification
    class AutoRecovery,UserRecovery,SystemRecovery,EscalationRecovery recovery
    class ComponentError,APIError,ValidationError,NetworkErrorUI frontend
    class MiddlewareError,ServiceError,DatabaseError,ExternalAPIError backend
    class ErrorResponse,ErrorLogging,ErrorMetrics,ErrorAlerts response
    class TokenExpired,InvalidCredentials,InsufficientFunds,PaymentFailed,OfflineMode,DataCorruption scenario
    class RetryMechanism,FallbackOptions,UserGuidance,DataRecovery action
    class ErrorTracking,UserImpact,PerformanceImpact,BusinessImpact monitoring
