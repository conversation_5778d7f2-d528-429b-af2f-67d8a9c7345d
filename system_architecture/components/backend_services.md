# VaultKe Backend Services Architecture

## 🏗️ **Service Layer Overview**

The VaultKe backend follows a layered architecture pattern with clear separation of concerns:

```
Controllers → Services → Database
     ↓           ↓          ↓
   HTTP      Business    Data
  Layer       Logic     Access
```

## 🔧 **Core Services**

### **1. Authentication Service**
**Location**: `internal/services/auth.go`

**Responsibilities**:
- JWT token generation and validation
- Password hashing and verification
- User session management
- Multi-device session support
- Token refresh mechanism

**Key Methods**:
```go
func (s *AuthService) Login(email, password string) (*AuthResponse, error)
func (s *AuthService) Register(userData UserRegistration) (*User, error)
func (s *AuthService) RefreshToken(refreshToken string) (*TokenPair, error)
func (s *AuthService) ValidateToken(token string) (*UserClaims, error)
func (s *AuthService) Logout(userID, refreshToken string) error
```

**Security Features**:
- bcrypt password hashing with salt
- JWT with RS256 signing
- Refresh token rotation
- Session invalidation
- Rate limiting protection

### **2. User Service**
**Location**: `internal/services/user.go`

**Responsibilities**:
- User profile management
- Avatar upload and processing
- User preferences and settings
- Account verification
- Privacy controls

**Key Methods**:
```go
func (s *UserService) GetProfile(userID string) (*User, error)
func (s *UserService) UpdateProfile(userID string, updates UserUpdate) error
func (s *UserService) UploadAvatar(userID string, file multipart.File) error
func (s *UserService) GetPreferences(userID string) (*UserPreferences, error)
func (s *UserService) UpdatePreferences(userID string, prefs UserPreferences) error
```

### **3. Chama Service**
**Location**: `internal/services/chama.go`

**Responsibilities**:
- Chama (group) creation and management
- Member invitation and management
- Role-based permissions
- Group settings and rules
- Activity tracking

**Key Methods**:
```go
func (s *ChamaService) CreateChama(creatorID string, chamaData ChamaCreate) (*Chama, error)
func (s *ChamaService) InviteMember(chamaID, inviterID, email string) error
func (s *ChamaService) JoinChama(chamaID, userID, inviteToken string) error
func (s *ChamaService) GetMembers(chamaID string) ([]ChamaMember, error)
func (s *ChamaService) UpdateMemberRole(chamaID, memberID, newRole string) error
```

### **4. Wallet Service**
**Location**: `internal/services/wallet.go`

**Responsibilities**:
- Wallet creation and management
- Balance tracking
- Transaction validation
- Multi-currency support
- Wallet permissions

**Key Methods**:
```go
func (s *WalletService) CreateWallet(userID, chamaID string) (*Wallet, error)
func (s *WalletService) GetBalance(walletID string) (*Balance, error)
func (s *WalletService) GetTransactions(walletID string, filters TransactionFilter) ([]Transaction, error)
func (s *WalletService) ValidateTransaction(transaction TransactionRequest) error
```

### **5. Transaction Service**
**Location**: `internal/services/transaction.go`

**Responsibilities**:
- Transaction processing
- M-Pesa integration
- Transaction validation
- Fee calculation
- Transaction history

**Key Methods**:
```go
func (s *TransactionService) ProcessTransfer(transfer TransferRequest) (*Transaction, error)
func (s *TransactionService) InitiateMpesaPayment(payment MpesaPayment) (*MpesaResponse, error)
func (s *TransactionService) HandleMpesaCallback(callback MpesaCallback) error
func (s *TransactionService) GetTransactionHistory(userID string, filters Filter) ([]Transaction, error)
```

### **6. Notification Service**
**Location**: `internal/services/notification.go`

**Responsibilities**:
- Notification creation and delivery
- Template processing
- Multi-channel delivery (push, email, SMS)
- User preference handling
- Delivery tracking

**Key Methods**:
```go
func (s *NotificationService) SendNotification(notification NotificationRequest) error
func (s *NotificationService) SendBulkNotifications(notifications []NotificationRequest) error
func (s *NotificationService) GetUserNotifications(userID string, filters Filter) ([]Notification, error)
func (s *NotificationService) MarkAsRead(userID, notificationID string) error
func (s *NotificationService) UpdatePreferences(userID string, prefs NotificationPreferences) error
```

## 🔄 **Service Interactions**

### **Cross-Service Communication**
Services communicate through well-defined interfaces and dependency injection:

```go
type Services struct {
    Auth         *AuthService
    User         *UserService
    Chama        *ChamaService
    Wallet       *WalletService
    Transaction  *TransactionService
    Notification *NotificationService
    // ... other services
}
```

### **Event-Driven Architecture**
Services emit events for cross-cutting concerns:

```go
// Example: Transaction service emits events
func (s *TransactionService) ProcessTransfer(transfer TransferRequest) (*Transaction, error) {
    // Process transaction
    transaction, err := s.processTransfer(transfer)
    if err != nil {
        return nil, err
    }
    
    // Emit events
    s.eventBus.Emit("transaction.completed", transaction)
    s.eventBus.Emit("wallet.balance.updated", transaction.ToWalletID)
    
    return transaction, nil
}
```

## 🗄️ **Data Access Layer**

### **Repository Pattern**
Each service uses repositories for data access:

```go
type UserRepository interface {
    Create(user *User) error
    GetByID(id string) (*User, error)
    GetByEmail(email string) (*User, error)
    Update(id string, updates UserUpdate) error
    Delete(id string) error
}
```

### **Database Transactions**
Services handle database transactions for data consistency:

```go
func (s *TransactionService) ProcessTransfer(transfer TransferRequest) (*Transaction, error) {
    tx, err := s.db.Begin()
    if err != nil {
        return nil, err
    }
    defer tx.Rollback()
    
    // Debit from source wallet
    err = s.walletRepo.UpdateBalance(tx, transfer.FromWalletID, -transfer.Amount)
    if err != nil {
        return nil, err
    }
    
    // Credit to destination wallet
    err = s.walletRepo.UpdateBalance(tx, transfer.ToWalletID, transfer.Amount)
    if err != nil {
        return nil, err
    }
    
    // Create transaction record
    transaction, err := s.transactionRepo.Create(tx, transfer)
    if err != nil {
        return nil, err
    }
    
    return transaction, tx.Commit()
}
```

## 🔒 **Security & Validation**

### **Input Validation**
All services implement comprehensive input validation:

```go
func (s *UserService) UpdateProfile(userID string, updates UserUpdate) error {
    // Validate input
    if err := s.validator.Validate(updates); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }
    
    // Sanitize input
    updates = s.sanitizer.Sanitize(updates)
    
    // Business logic validation
    if err := s.validateBusinessRules(userID, updates); err != nil {
        return err
    }
    
    return s.userRepo.Update(userID, updates)
}
```

### **Authorization**
Services implement role-based access control:

```go
func (s *ChamaService) UpdateMemberRole(chamaID, memberID, newRole string, requesterID string) error {
    // Check if requester has permission
    hasPermission, err := s.hasPermission(requesterID, chamaID, "manage_members")
    if err != nil {
        return err
    }
    if !hasPermission {
        return ErrInsufficientPermissions
    }
    
    return s.chamaRepo.UpdateMemberRole(chamaID, memberID, newRole)
}
```

## 📊 **Performance Optimization**

### **Caching Strategy**
Services implement caching for frequently accessed data:

```go
func (s *UserService) GetProfile(userID string) (*User, error) {
    // Check cache first
    if user, found := s.cache.Get("user:" + userID); found {
        return user.(*User), nil
    }
    
    // Fetch from database
    user, err := s.userRepo.GetByID(userID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cache.Set("user:"+userID, user, 15*time.Minute)
    
    return user, nil
}
```

### **Database Optimization**
- Connection pooling
- Prepared statements
- Query optimization
- Index usage monitoring

## 🔍 **Monitoring & Logging**

### **Structured Logging**
All services implement structured logging:

```go
func (s *TransactionService) ProcessTransfer(transfer TransferRequest) (*Transaction, error) {
    logger := s.logger.WithFields(logrus.Fields{
        "service":      "transaction",
        "operation":    "process_transfer",
        "from_wallet":  transfer.FromWalletID,
        "to_wallet":    transfer.ToWalletID,
        "amount":       transfer.Amount,
        "request_id":   transfer.RequestID,
    })
    
    logger.Info("Processing transfer request")
    
    transaction, err := s.processTransfer(transfer)
    if err != nil {
        logger.WithError(err).Error("Transfer processing failed")
        return nil, err
    }
    
    logger.WithField("transaction_id", transaction.ID).Info("Transfer processed successfully")
    return transaction, nil
}
```

### **Metrics Collection**
Services expose metrics for monitoring:

```go
func (s *TransactionService) ProcessTransfer(transfer TransferRequest) (*Transaction, error) {
    start := time.Now()
    defer func() {
        s.metrics.RecordDuration("transaction.process_transfer", time.Since(start))
    }()
    
    s.metrics.IncrementCounter("transaction.process_transfer.attempts")
    
    transaction, err := s.processTransfer(transfer)
    if err != nil {
        s.metrics.IncrementCounter("transaction.process_transfer.errors")
        return nil, err
    }
    
    s.metrics.IncrementCounter("transaction.process_transfer.success")
    return transaction, nil
}
```

---

**Next Steps**: Review individual service implementations and API endpoint documentation for detailed usage examples.
