# VaultKe System Overview

## 🎯 **System Purpose**

VaultKe is a comprehensive financial management platform designed for Kenyan users, focusing on:
- **Chama Management**: Digital savings groups and investment clubs
- **Personal Finance**: Wallet management, transactions, and budgeting
- **Marketplace**: Product buying/selling with integrated payments
- **Learning**: Financial literacy and education content
- **Notifications**: Real-time updates and reminders

## 🏗️ **High-Level Architecture**

### **Client-Server Architecture**
```
┌─────────────────┐    HTTPS/WSS    ┌─────────────────┐    SQLite    ┌─────────────┐
│  React Native   │ ◄──────────────► │   Go Backend    │ ◄───────────► │  Database   │
│   Mobile App    │                  │   (Gin + JWT)   │               │  (SQLite)   │
└─────────────────┘                  └─────────────────┘               └─────────────┘
```

### **Core Components**

#### **1. Mobile Application (React Native)**
- **Platform**: Cross-platform (iOS, Android, Web)
- **State Management**: React Context API
- **Navigation**: React Navigation v6
- **UI Framework**: Custom components with theme support
- **Real-time**: WebSocket connections for live updates

#### **2. Backend API (Go)**
- **Framework**: Gin HTTP framework
- **Authentication**: JWT tokens with refresh mechanism
- **Database**: SQLite with custom migration system
- **Real-time**: WebSocket service for live notifications
- **File Storage**: Local file system with static serving

#### **3. Database (SQLite)**
- **Type**: Embedded SQL database
- **Migration System**: Custom Go-based migrations with tracking
- **Schema**: 50+ tables covering all business domains
- **Backup**: Google Drive integration for data backup

## 🔄 **Data Flow Architecture**

### **Request Flow**
1. **Mobile App** → HTTP/HTTPS → **Go Backend**
2. **Go Backend** → SQL → **SQLite Database**
3. **Database** → Results → **Go Backend**
4. **Go Backend** → JSON → **Mobile App**

### **Real-time Flow**
1. **Event Trigger** → **Go Backend**
2. **WebSocket Service** → **Connected Clients**
3. **Mobile App** → **UI Update**

## 🔐 **Security Architecture**

### **Authentication & Authorization**
- **JWT Tokens**: Access tokens (15min) + Refresh tokens (7 days)
- **Role-Based Access**: User, Admin, Chama roles
- **Session Management**: Multi-device support with logout capabilities
- **Password Security**: Bcrypt hashing with salt

### **API Security**
- **CORS**: Configurable origins for development/production
- **Rate Limiting**: Request throttling per user/IP
- **Input Validation**: Comprehensive request validation
- **SQL Injection**: Parameterized queries throughout

### **Data Protection**
- **Encryption**: Sensitive data encryption at rest
- **File Security**: Secure file upload with type validation
- **Privacy**: GDPR-compliant data handling
- **Audit Trail**: Comprehensive logging for security events

## 📱 **Mobile App Architecture**

### **Screen Structure**
```
App
├── Authentication (Login, Register, Forgot Password)
├── Dashboard (Overview, Quick Actions)
├── Chamas (List, Details, Members, Meetings)
├── Wallet (Balance, Transactions, Transfer)
├── Marketplace (Products, Cart, Orders)
├── Learning (Courses, Categories, Progress)
├── Profile (Settings, Preferences, Security)
└── Notifications (List, Preferences, Sounds)
```

### **Key Features**
- **Offline Support**: Local storage for critical data
- **Theme Support**: Light/Dark mode with system preference
- **Internationalization**: Multi-language support (English, Swahili)
- **Accessibility**: Screen reader and accessibility compliance
- **Performance**: Optimized rendering and memory management

## 🔧 **Backend Services Architecture**

### **Service Layer**
```
Controllers → Services → Database
     ↓           ↓          ↓
   HTTP      Business    Data
  Layer       Logic     Access
```

### **Core Services**
- **Auth Service**: JWT generation, validation, refresh
- **User Service**: User management, profiles, preferences
- **Chama Service**: Group management, members, roles
- **Wallet Service**: Transactions, balances, transfers
- **Notification Service**: Real-time notifications, preferences
- **File Service**: Upload, storage, serving
- **Email Service**: SMTP integration for notifications
- **WebSocket Service**: Real-time communication

## 🗄️ **Database Architecture**

### **Schema Organization**
- **Users & Auth**: User accounts, sessions, permissions
- **Chamas**: Groups, members, roles, invitations
- **Financial**: Wallets, transactions, loans, contributions
- **Marketplace**: Products, orders, reviews, delivery
- **Learning**: Courses, categories, progress, quizzes
- **Notifications**: Messages, preferences, sounds, delivery
- **Meetings**: Scheduling, attendance, documents, minutes
- **System**: Migrations, logs, configurations

### **Key Design Principles**
- **Normalization**: Proper relational design with foreign keys
- **Indexing**: Strategic indexes for performance
- **Constraints**: Data integrity through database constraints
- **Migrations**: Version-controlled schema changes
- **Backup**: Automated backup strategies

## 🌐 **Integration Architecture**

### **External Services**
- **M-Pesa**: Mobile money integration for payments
- **Google Drive**: Backup and restore functionality
- **Email SMTP**: Notification delivery
- **LiveKit**: Video conferencing for meetings
- **Africa's Talking**: SMS notifications

### **API Design**
- **RESTful**: Standard HTTP methods and status codes
- **Versioning**: API versioning strategy (/api/v1/)
- **Documentation**: Comprehensive API documentation
- **Error Handling**: Consistent error response format
- **Pagination**: Efficient data pagination

## 📊 **Performance & Scalability**

### **Current Scale**
- **Users**: Designed for 10K+ concurrent users
- **Data**: Optimized for millions of transactions
- **Storage**: Efficient SQLite with backup strategies
- **Response Time**: <200ms for most API calls

### **Scalability Strategy**
- **Horizontal Scaling**: Load balancer ready
- **Database**: Migration path to PostgreSQL
- **Caching**: Redis integration planned
- **CDN**: Static asset delivery optimization
- **Monitoring**: Comprehensive metrics and alerting

## 🔍 **Monitoring & Observability**

### **Logging**
- **Structured Logging**: JSON format with correlation IDs
- **Log Levels**: Debug, Info, Warn, Error with filtering
- **Audit Trail**: Security and business event logging
- **Performance**: Request/response timing and metrics

### **Health Checks**
- **API Health**: Endpoint health monitoring
- **Database**: Connection and query performance
- **External Services**: Third-party service availability
- **System Resources**: CPU, memory, disk usage

---

**Next Steps**: Review the detailed component documentation and system diagrams for deeper understanding of each subsystem.
